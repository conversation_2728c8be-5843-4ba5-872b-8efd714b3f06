# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/ab_testing_db?schema=public"

# Application Configuration
NODE_ENV="development"
LOG_LEVEL="info"

# Database Connection Pool Settings
DB_MAX_CONNECTIONS=10
DB_CONNECTION_TIMEOUT=5000
DB_QUERY_TIMEOUT=10000

# Prisma Configuration
PRISMA_LOG_LEVEL="info"

# Optional: For production deployments
# DATABASE_URL="***************************************************/ab_testing_prod?schema=public&sslmode=require"

# Optional: Shadow database for migrations (development only)
# SHADOW_DATABASE_URL="postgresql://username:password@localhost:5432/ab_testing_shadow?schema=public"

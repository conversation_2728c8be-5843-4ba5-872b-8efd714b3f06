# Docker Setup for A/B Testing Platform

This document provides comprehensive instructions for running the A/B Testing Platform using Docker Compose with PostgreSQL, Redis, and all supporting services.

## 🚀 Quick Start

### Prerequisites

- Docker Desktop 4.0+ or Docker Engine 20.10+
- Docker Compose 2.0+
- 4GB+ available RAM
- 10GB+ available disk space

### One-Command Setup

```bash
# Start the complete platform
./scripts/start.sh

# Or for production mode
./scripts/start.sh production
```

### Manual Setup

```bash
# 1. Copy environment configuration
cp .env.docker .env

# 2. Start all services
docker-compose up -d

# 3. Wait for services to be ready
docker-compose logs -f app
```

## 📊 Services Overview

### Core Services

| Service | Port | Description | Health Check |
|---------|------|-------------|--------------|
| **app** | 3003 | Node.js A/B Testing API | http://localhost:3003/health |
| **postgres** | 5432 | PostgreSQL Database | `pg_isready` |
| **redis** | 6379 | Redis Cache | `redis-cli ping` |
| **nginx** | 80, 8080 | Reverse Proxy & Static Files | http://localhost/health |

### Management Tools

| Service | Port | Description | Credentials |
|---------|------|-------------|-------------|
| **pgAdmin** | 5050 | Database Management | <EMAIL> / admin_password |
| **Redis Commander** | 8081 | Redis Management | admin / admin_password |
| **Grafana** | 3000 | Monitoring Dashboards | admin / admin_password |
| **Prometheus** | 9090 | Metrics Collection | No auth required |

## 🔧 Configuration

### Environment Variables

Key configuration options in `.env.docker`:

```bash
# Application
NODE_ENV=production
PORT=3003

# Database
DB_HOST=postgres
DB_NAME=ab_testing_platform
DB_USER=ab_testing_user
DB_PASSWORD=ab_testing_password

# Redis
REDIS_HOST=redis
REDIS_PASSWORD=redis_password

# Security
JWT_SECRET=your_super_secure_jwt_secret
SESSION_SECRET=your_super_secure_session_secret

# Features
ENABLE_AUDIT_LOGGING=true
ENABLE_METRICS=true
DEMO_MODE=true
```

### Development vs Production

#### Development Mode (default)
```bash
./scripts/start.sh
# or
docker-compose up -d
```

Features:
- Hot reload enabled
- Debug logging
- Development database
- Source code mounted as volume
- Node.js debugger port exposed (9229)

#### Production Mode
```bash
./scripts/start.sh production
# or
COMPOSE_FILE=docker-compose.yml docker-compose up -d
```

Features:
- Optimized build
- Production logging
- No source code mounting
- Security headers enabled
- Performance optimizations

## 🗄️ Database Setup

### Automatic Initialization

The PostgreSQL container automatically:

1. **Creates database schema** from `docker/postgres/init/01-create-database.sql`
2. **Loads sample data** from `docker/postgres/init/02-sample-data.sql`
3. **Sets up indexes** and performance optimizations
4. **Creates views** for analytics

### Sample Data Included

- **3 Tenants**: Acme Corp, Beta Company, Gamma Industries
- **6 Users**: Admin, Experimenter, Viewer, Analyst roles
- **5 Experiments**: Various types and statuses
- **10+ Variants**: A/B test configurations
- **100+ Events**: Conversion tracking data
- **Audit Logs**: Security and authorization logs

### Manual Database Operations

```bash
# Connect to database
docker-compose exec postgres psql -U ab_testing_user -d ab_testing_platform

# Backup database
docker-compose exec postgres pg_dump -U ab_testing_user ab_testing_platform > backup.sql

# Restore database
docker-compose exec -T postgres psql -U ab_testing_user -d ab_testing_platform < backup.sql

# View database logs
docker-compose logs postgres
```

## 🔄 Redis Cache

### Configuration

Redis is configured with:
- **Password protection**: `redis_password`
- **Persistence**: AOF enabled
- **Memory optimization**: Configured for caching
- **Connection pooling**: Ready for high load

### Redis Operations

```bash
# Connect to Redis
docker-compose exec redis redis-cli -a redis_password

# Monitor Redis commands
docker-compose exec redis redis-cli -a redis_password monitor

# View Redis info
docker-compose exec redis redis-cli -a redis_password info

# Clear cache
docker-compose exec redis redis-cli -a redis_password flushall
```

## 🌐 Nginx Reverse Proxy

### Features

- **Rate limiting**: API endpoints protected
- **SSL ready**: HTTPS configuration available
- **Static file serving**: Frontend assets
- **Compression**: Gzip enabled
- **Security headers**: XSS, CSRF protection
- **Load balancing**: Ready for multiple app instances

### Configuration Files

- `docker/nginx/nginx.conf`: Main configuration
- `docker/nginx/conf.d/default.conf`: Additional server blocks

### Custom SSL Setup

1. Place certificates in `docker/nginx/ssl/`
2. Uncomment SSL configuration in `docker/nginx/conf.d/default.conf`
3. Restart nginx: `docker-compose restart nginx`

## 📈 Monitoring & Observability

### Prometheus Metrics

Automatically collects metrics from:
- Application performance
- Database connections
- Redis operations
- Nginx requests
- System resources

### Grafana Dashboards

Pre-configured dashboards for:
- Application metrics
- Database performance
- Cache hit rates
- API response times
- Error rates

### Log Aggregation

All services log to Docker volumes:
```bash
# View all logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f app
docker-compose logs -f postgres
docker-compose logs -f redis
```

## 🧪 Testing the Platform

### Health Checks

```bash
# Application health
curl http://localhost:3003/health

# Database health
docker-compose exec postgres pg_isready -U ab_testing_user

# Redis health
docker-compose exec redis redis-cli -a redis_password ping

# Nginx health
curl http://localhost/health
```

### API Testing

```bash
# List experiments (admin)
curl -H "Authorization: Bearer admin_token" \
     http://localhost:3003/api/experiments

# Create experiment (experimenter)
curl -X POST \
     -H "Authorization: Bearer experimenter_token" \
     -H "Content-Type: application/json" \
     -d '{"name":"Docker Test","description":"Testing from Docker"}' \
     http://localhost:3003/api/experiments

# View analytics (viewer)
curl -H "Authorization: Bearer viewer_token" \
     http://localhost:3003/api/analytics/dashboard

# Test authorization (should fail)
curl -H "Authorization: Bearer viewer_token" \
     http://localhost:3003/api/users
```

### Load Testing

```bash
# Install Apache Bench
# macOS: brew install httpie
# Ubuntu: apt-get install apache2-utils

# Basic load test
ab -n 1000 -c 10 http://localhost:3003/health

# API load test with auth
ab -n 100 -c 5 -H "Authorization: Bearer admin_token" \
   http://localhost:3003/api/experiments
```

## 🛠️ Maintenance

### Backup & Restore

```bash
# Full backup
./scripts/backup.sh

# Database only
docker-compose exec postgres pg_dump -U ab_testing_user ab_testing_platform > db_backup.sql

# Redis backup
docker-compose exec redis redis-cli -a redis_password --rdb /data/dump.rdb
```

### Updates & Upgrades

```bash
# Pull latest images
docker-compose pull

# Rebuild application
docker-compose build app

# Rolling update (zero downtime)
docker-compose up -d --no-deps app
```

### Scaling

```bash
# Scale application instances
docker-compose up -d --scale app=3

# Scale with load balancer
docker-compose -f docker-compose.yml -f docker-compose.scale.yml up -d
```

## 🔧 Troubleshooting

### Common Issues

#### Application Won't Start
```bash
# Check logs
docker-compose logs app

# Check database connection
docker-compose exec app npm run db:test

# Restart services
docker-compose restart app postgres redis
```

#### Database Connection Issues
```bash
# Check PostgreSQL status
docker-compose exec postgres pg_isready -U ab_testing_user

# Check network connectivity
docker-compose exec app ping postgres

# Reset database
docker-compose down -v
docker-compose up -d
```

#### Redis Connection Issues
```bash
# Check Redis status
docker-compose exec redis redis-cli -a redis_password ping

# Check Redis logs
docker-compose logs redis

# Clear Redis data
docker-compose exec redis redis-cli -a redis_password flushall
```

#### Performance Issues
```bash
# Check resource usage
docker stats

# Check disk space
docker system df

# Clean up unused resources
docker system prune -a
```

### Debug Mode

```bash
# Start in debug mode
NODE_ENV=development docker-compose up -d

# Attach debugger
docker-compose exec app node --inspect-brk=0.0.0.0:9229 app.js

# View debug logs
docker-compose logs -f app | grep DEBUG
```

## 🚀 Production Deployment

### Security Checklist

- [ ] Change default passwords in `.env.docker`
- [ ] Enable SSL/HTTPS in Nginx
- [ ] Configure firewall rules
- [ ] Set up log rotation
- [ ] Enable monitoring alerts
- [ ] Configure backup strategy
- [ ] Review security headers

### Performance Optimization

- [ ] Tune PostgreSQL configuration
- [ ] Configure Redis memory limits
- [ ] Set up CDN for static assets
- [ ] Enable application caching
- [ ] Configure connection pooling
- [ ] Set up horizontal scaling

### Monitoring Setup

- [ ] Configure Prometheus alerts
- [ ] Set up Grafana notifications
- [ ] Enable log aggregation
- [ ] Configure health check monitoring
- [ ] Set up error tracking (Sentry)
- [ ] Configure performance monitoring

## 📞 Support

### Getting Help

1. **Check logs**: `docker-compose logs [service]`
2. **Review documentation**: This README and API docs
3. **Test health endpoints**: Verify all services are running
4. **Check GitHub issues**: Known problems and solutions

### Useful Commands

```bash
# Quick status check
docker-compose ps

# Resource usage
docker stats

# Clean up everything
./scripts/stop.sh --all

# Fresh start
./scripts/stop.sh --volumes && ./scripts/start.sh
```

---

## 🎉 Success!

If you see all services running and health checks passing, your A/B Testing Platform is ready for use!

- **Frontend**: http://localhost
- **API**: http://localhost:3003
- **Admin Tools**: http://localhost:5050 (pgAdmin)
- **Monitoring**: http://localhost:3000 (Grafana)

Happy testing! 🧪

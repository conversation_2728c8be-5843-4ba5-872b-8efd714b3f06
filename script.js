// Stock Graph Application
class StockGraph {
    constructor() {
        this.chart = null;
        this.currentSymbol = 'AAPL';
        this.currentRange = '1D';
        this.apiKey = 'demo'; // Replace with your Alpha Vantage API key
        this.isDemo = true; // Set to false when using real API
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadStockData(this.currentSymbol, this.currentRange);
    }

    setupEventListeners() {
        // Search button
        document.getElementById('searchBtn').addEventListener('click', () => {
            const symbol = document.getElementById('stockSymbol').value.trim().toUpperCase();
            if (symbol) {
                this.currentSymbol = symbol;
                this.loadStockData(symbol, this.currentRange);
            }
        });

        // Enter key in search input
        document.getElementById('stockSymbol').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                document.getElementById('searchBtn').click();
            }
        });

        // Time range buttons
        document.querySelectorAll('.time-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.time-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                this.currentRange = btn.dataset.range;
                this.loadStockData(this.currentSymbol, this.currentRange);
            });
        });
    }

    async loadStockData(symbol, range) {
        this.showLoading();
        this.hideError();

        try {
            let data;
            if (this.isDemo) {
                data = this.generateDemoData(symbol, range);
            } else {
                data = await this.fetchRealStockData(symbol, range);
            }
            
            this.updateStockInfo(data);
            this.renderChart(data);
        } catch (error) {
            console.error('Error loading stock data:', error);
            this.showError();
        } finally {
            this.hideLoading();
        }
    }

    generateDemoData(symbol, range) {
        const stockNames = {
            'AAPL': 'Apple Inc.',
            'GOOGL': 'Alphabet Inc.',
            'MSFT': 'Microsoft Corporation',
            'AMZN': 'Amazon.com Inc.',
            'TSLA': 'Tesla Inc.',
            'META': 'Meta Platforms Inc.',
            'NVDA': 'NVIDIA Corporation',
            'NFLX': 'Netflix Inc.'
        };

        const basePrice = Math.random() * 200 + 50; // Random base price between 50-250
        const dataPoints = this.getDataPointsForRange(range);
        const labels = [];
        const prices = [];
        
        let currentPrice = basePrice;
        const now = new Date();

        for (let i = dataPoints - 1; i >= 0; i--) {
            let date;
            if (range === '1D') {
                date = new Date(now.getTime() - i * 5 * 60 * 1000); // 5-minute intervals
                labels.push(date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }));
            } else if (range === '1W') {
                date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000); // Daily
                labels.push(date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }));
            } else {
                date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000); // Daily
                labels.push(date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }));
            }

            // Generate realistic price movement
            const change = (Math.random() - 0.5) * (currentPrice * 0.02); // ±2% max change
            currentPrice = Math.max(currentPrice + change, 1); // Ensure price stays positive
            prices.push(parseFloat(currentPrice.toFixed(2)));
        }

        const firstPrice = prices[0];
        const lastPrice = prices[prices.length - 1];
        const priceChange = lastPrice - firstPrice;
        const percentChange = (priceChange / firstPrice) * 100;

        return {
            symbol: symbol,
            name: stockNames[symbol] || `${symbol} Corporation`,
            currentPrice: lastPrice,
            priceChange: priceChange,
            percentChange: percentChange,
            labels: labels,
            prices: prices
        };
    }

    getDataPointsForRange(range) {
        switch (range) {
            case '1D': return 78; // 5-minute intervals for 6.5 hours (market hours)
            case '1W': return 7;
            case '1M': return 30;
            case '3M': return 90;
            case '1Y': return 365;
            default: return 30;
        }
    }

    async fetchRealStockData(symbol, range) {
        // This would be used with a real API key
        const functionMap = {
            '1D': 'TIME_SERIES_INTRADAY',
            '1W': 'TIME_SERIES_DAILY',
            '1M': 'TIME_SERIES_DAILY',
            '3M': 'TIME_SERIES_DAILY',
            '1Y': 'TIME_SERIES_DAILY'
        };

        const interval = range === '1D' ? '5min' : 'daily';
        const func = functionMap[range];
        
        const url = `https://www.alphavantage.co/query?function=${func}&symbol=${symbol}&interval=${interval}&apikey=${this.apiKey}`;
        
        const response = await fetch(url);
        const data = await response.json();
        
        // Process the real API response here
        // This is a simplified version - you'd need to parse the actual Alpha Vantage response
        return this.processAlphaVantageData(data, symbol, range);
    }

    updateStockInfo(data) {
        document.getElementById('stockName').textContent = `${data.name} (${data.symbol})`;
        document.getElementById('currentPrice').textContent = `$${data.currentPrice.toFixed(2)}`;
        
        const changeElement = document.getElementById('priceChange');
        const changeText = `${data.priceChange >= 0 ? '+' : ''}${data.priceChange.toFixed(2)} (${data.percentChange >= 0 ? '+' : ''}${data.percentChange.toFixed(2)}%)`;
        changeElement.textContent = changeText;
        changeElement.className = data.priceChange >= 0 ? 'positive' : 'negative';
    }

    renderChart(data) {
        const ctx = document.getElementById('stockChart').getContext('2d');
        
        if (this.chart) {
            this.chart.destroy();
        }

        const gradient = ctx.createLinearGradient(0, 0, 0, 400);
        gradient.addColorStop(0, data.priceChange >= 0 ? 'rgba(16, 185, 129, 0.2)' : 'rgba(239, 68, 68, 0.2)');
        gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');

        this.chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels,
                datasets: [{
                    label: `${data.symbol} Price`,
                    data: data.prices,
                    borderColor: data.priceChange >= 0 ? '#10b981' : '#ef4444',
                    backgroundColor: gradient,
                    borderWidth: 2,
                    fill: true,
                    tension: 0.1,
                    pointRadius: 0,
                    pointHoverRadius: 5,
                    pointHoverBackgroundColor: data.priceChange >= 0 ? '#10b981' : '#ef4444',
                    pointHoverBorderColor: '#fff',
                    pointHoverBorderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: data.priceChange >= 0 ? '#10b981' : '#ef4444',
                        borderWidth: 1,
                        callbacks: {
                            label: function(context) {
                                return `Price: $${context.parsed.y.toFixed(2)}`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        display: true,
                        grid: {
                            display: false
                        },
                        ticks: {
                            maxTicksLimit: 8
                        }
                    },
                    y: {
                        display: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toFixed(2);
                            }
                        }
                    }
                },
                interaction: {
                    mode: 'nearest',
                    axis: 'x',
                    intersect: false
                }
            }
        });
    }

    showLoading() {
        document.getElementById('loading').style.display = 'flex';
        document.querySelector('.chart-container').style.display = 'none';
    }

    hideLoading() {
        document.getElementById('loading').style.display = 'none';
        document.querySelector('.chart-container').style.display = 'block';
    }

    showError() {
        document.getElementById('error').style.display = 'block';
        document.querySelector('.chart-container').style.display = 'none';
    }

    hideError() {
        document.getElementById('error').style.display = 'none';
    }
}

// Initialize the application when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new StockGraph();
});

openapi: 3.0.3
info:
  title: A/B Testing Experiment Management API
  description: |
    RESTful API for managing A/B testing experiments with multi-tenant support.
    
    ## Features
    - Complete experiment lifecycle management
    - User assignment and targeting
    - Event tracking and analytics
    - Multi-tenant isolation
    - Real-time experiment control
    
    ## Authentication
    All endpoints require Bearer token authentication with appropriate tenant permissions.
    
    ## Rate Limiting
    - 1000 requests per hour for experiment management
    - 10000 requests per hour for user assignment and event tracking
    
  version: 1.0.0
  contact:
    name: A/B Testing Platform Team
    email: <EMAIL>
    url: https://docs.example.com/ab-testing
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://api.example.com/v1
    description: Production server
  - url: https://staging-api.example.com/v1
    description: Staging server
  - url: http://localhost:3000/v1
    description: Development server

security:
  - BearerAuth: []

paths:
  /experiments:
    get:
      summary: List experiments
      description: Retrieve a paginated list of experiments with optional filtering
      operationId: listExperiments
      tags:
        - Experiments
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
        - $ref: '#/components/parameters/SortByParam'
        - $ref: '#/components/parameters/SortOrderParam'
        - name: status
          in: query
          description: Filter by experiment status
          schema:
            type: array
            items:
              $ref: '#/components/schemas/ExperimentStatus'
          style: form
          explode: false
        - name: tags
          in: query
          description: Filter by tags (comma-separated)
          schema:
            type: array
            items:
              type: string
          style: form
          explode: false
        - name: created_by
          in: query
          description: Filter by creator user ID
          schema:
            type: string
            format: uuid
        - name: search
          in: query
          description: Search in name, description, and hypothesis
          schema:
            type: string
            maxLength: 255
        - name: start_date_from
          in: query
          description: Filter experiments starting from this date
          schema:
            type: string
            format: date-time
        - name: start_date_to
          in: query
          description: Filter experiments starting before this date
          schema:
            type: string
            format: date-time
      responses:
        '200':
          description: List of experiments
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExperimentListResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'

    post:
      summary: Create experiment
      description: Create a new experiment with variants and targeting rules
      operationId: createExperiment
      tags:
        - Experiments
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateExperimentRequest'
      responses:
        '201':
          description: Experiment created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExperimentResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '422':
          $ref: '#/components/responses/ValidationError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /experiments/{experimentId}:
    get:
      summary: Get experiment
      description: Retrieve a specific experiment by ID with all related data
      operationId: getExperiment
      tags:
        - Experiments
      parameters:
        - $ref: '#/components/parameters/ExperimentIdParam'
        - name: include
          in: query
          description: Include related data
          schema:
            type: array
            items:
              type: string
              enum: [variants, targeting_rules, statistics, results]
          style: form
          explode: false
      responses:
        '200':
          description: Experiment details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExperimentResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

    put:
      summary: Update experiment
      description: Update an existing experiment (only allowed for DRAFT status)
      operationId: updateExperiment
      tags:
        - Experiments
      parameters:
        - $ref: '#/components/parameters/ExperimentIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateExperimentRequest'
      responses:
        '200':
          description: Experiment updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExperimentResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          $ref: '#/components/responses/Conflict'
        '422':
          $ref: '#/components/responses/ValidationError'
        '500':
          $ref: '#/components/responses/InternalServerError'

    delete:
      summary: Delete experiment
      description: Delete an experiment (only allowed for DRAFT or ARCHIVED status)
      operationId: deleteExperiment
      tags:
        - Experiments
      parameters:
        - $ref: '#/components/parameters/ExperimentIdParam'
      responses:
        '204':
          description: Experiment deleted successfully
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          $ref: '#/components/responses/Conflict'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /experiments/{experimentId}/start:
    post:
      summary: Start experiment
      description: Start an experiment (transition from DRAFT to ACTIVE)
      operationId: startExperiment
      tags:
        - Experiment Control
      parameters:
        - $ref: '#/components/parameters/ExperimentIdParam'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                start_date:
                  type: string
                  format: date-time
                  description: Optional custom start date (defaults to now)
                end_date:
                  type: string
                  format: date-time
                  description: Optional end date for automatic completion
      responses:
        '200':
          description: Experiment started successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExperimentResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          $ref: '#/components/responses/Conflict'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /experiments/{experimentId}/pause:
    post:
      summary: Pause experiment
      description: Pause an active experiment (transition from ACTIVE to PAUSED)
      operationId: pauseExperiment
      tags:
        - Experiment Control
      parameters:
        - $ref: '#/components/parameters/ExperimentIdParam'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                reason:
                  type: string
                  description: Reason for pausing the experiment
                  maxLength: 500
      responses:
        '200':
          description: Experiment paused successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExperimentResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          $ref: '#/components/responses/Conflict'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /experiments/{experimentId}/resume:
    post:
      summary: Resume experiment
      description: Resume a paused experiment (transition from PAUSED to ACTIVE)
      operationId: resumeExperiment
      tags:
        - Experiment Control
      parameters:
        - $ref: '#/components/parameters/ExperimentIdParam'
      responses:
        '200':
          description: Experiment resumed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExperimentResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          $ref: '#/components/responses/Conflict'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /experiments/{experimentId}/complete:
    post:
      summary: Complete experiment
      description: Mark experiment as completed (transition from ACTIVE/PAUSED to COMPLETED)
      operationId: completeExperiment
      tags:
        - Experiment Control
      parameters:
        - $ref: '#/components/parameters/ExperimentIdParam'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                end_date:
                  type: string
                  format: date-time
                  description: Optional custom end date (defaults to now)
                results_summary:
                  type: string
                  description: Summary of experiment results
                  maxLength: 1000
      responses:
        '200':
          description: Experiment completed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExperimentResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          $ref: '#/components/responses/Conflict'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /experiments/{experimentId}/archive:
    post:
      summary: Archive experiment
      description: Archive a completed experiment (transition from COMPLETED to ARCHIVED)
      operationId: archiveExperiment
      tags:
        - Experiment Control
      parameters:
        - $ref: '#/components/parameters/ExperimentIdParam'
      responses:
        '200':
          description: Experiment archived successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExperimentResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          $ref: '#/components/responses/Conflict'
        '500':
          $ref: '#/components/responses/InternalServerError'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token for authentication with tenant context

  parameters:
    ExperimentIdParam:
      name: experimentId
      in: path
      required: true
      description: Unique identifier for the experiment
      schema:
        type: string
        format: uuid
        example: "123e4567-e89b-12d3-a456-************"

    VariantIdParam:
      name: variantId
      in: path
      required: true
      description: Unique identifier for the variant
      schema:
        type: string
        format: uuid

    PageParam:
      name: page
      in: query
      description: Page number for pagination (1-based)
      schema:
        type: integer
        minimum: 1
        default: 1
        example: 1

    LimitParam:
      name: limit
      in: query
      description: Number of items per page
      schema:
        type: integer
        minimum: 1
        maximum: 100
        default: 20
        example: 20

    SortByParam:
      name: sort_by
      in: query
      description: Field to sort by
      schema:
        type: string
        enum: [name, created_at, updated_at, start_date, end_date, status]
        default: created_at

    SortOrderParam:
      name: sort_order
      in: query
      description: Sort order
      schema:
        type: string
        enum: [asc, desc]
        default: desc

  schemas:
    ExperimentStatus:
      type: string
      enum: [DRAFT, ACTIVE, PAUSED, COMPLETED, ARCHIVED]
      description: Current status of the experiment
      example: ACTIVE

    AssignmentMethod:
      type: string
      enum: [RANDOM, STICKY, DETERMINISTIC]
      description: Method for assigning users to variants
      example: STICKY

    TargetingOperator:
      type: string
      enum: [EQUALS, NOT_EQUALS, IN, NOT_IN, GREATER_THAN, LESS_THAN, CONTAINS, REGEX]
      description: Operator for targeting rule evaluation
      example: EQUALS

    Experiment:
      type: object
      required:
        - id
        - name
        - status
        - created_at
        - updated_at
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the experiment
          example: "123e4567-e89b-12d3-a456-************"
        name:
          type: string
          minLength: 1
          maxLength: 255
          description: Human-readable name for the experiment
          example: "Homepage Button Color Test"
        description:
          type: string
          maxLength: 1000
          description: Detailed description of the experiment
          example: "Testing different button colors on the homepage to improve click-through rates"
        hypothesis:
          type: string
          maxLength: 1000
          description: The hypothesis being tested
          example: "A red CTA button will increase click-through rates by 15%"
        status:
          $ref: '#/components/schemas/ExperimentStatus'
        traffic_allocation:
          type: number
          minimum: 0
          maximum: 1
          description: Percentage of traffic to include in experiment (0.0 to 1.0)
          example: 1.0
        assignment_method:
          $ref: '#/components/schemas/AssignmentMethod'
        start_date:
          type: string
          format: date-time
          description: When the experiment started or will start
          example: "2024-01-15T00:00:00Z"
        end_date:
          type: string
          format: date-time
          description: When the experiment ended or will end
          example: "2024-02-15T23:59:59Z"
        sample_size:
          type: integer
          minimum: 1
          description: Target sample size for statistical significance
          example: 5000
        confidence_level:
          type: number
          minimum: 0.8
          maximum: 0.99
          description: Statistical confidence level
          example: 0.95
        minimum_detectable_effect:
          type: number
          minimum: 0.01
          maximum: 1.0
          description: Minimum effect size to detect
          example: 0.15
        primary_metric:
          type: string
          maxLength: 100
          description: Primary metric for measuring success
          example: "conversion_rate"
        secondary_metrics:
          type: array
          items:
            type: string
            maxLength: 100
          description: Secondary metrics to track
          example: ["click_through_rate", "bounce_rate"]
        tags:
          type: array
          items:
            type: string
            maxLength: 50
          description: Tags for categorizing experiments
          example: ["homepage", "cta", "ui"]
        metadata:
          type: object
          additionalProperties: true
          description: Additional metadata for the experiment
          example: {"owner": "growth-team", "priority": "high"}
        created_by:
          type: string
          format: uuid
          description: ID of the user who created the experiment
          example: "456e7890-e89b-12d3-a456-************"
        created_at:
          type: string
          format: date-time
          description: When the experiment was created
          example: "2024-01-10T10:00:00Z"
        updated_at:
          type: string
          format: date-time
          description: When the experiment was last updated
          example: "2024-01-10T15:30:00Z"

    Variant:
      type: object
      required:
        - id
        - name
        - is_control
        - traffic_weight
        - created_at
        - updated_at
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the variant
          example: "789e0123-e89b-12d3-a456-************"
        name:
          type: string
          minLength: 1
          maxLength: 255
          description: Human-readable name for the variant
          example: "Red Button"
        description:
          type: string
          maxLength: 500
          description: Description of the variant
          example: "Red CTA button variant"
        is_control:
          type: boolean
          description: Whether this variant is the control group
          example: false
        traffic_weight:
          type: number
          minimum: 0
          maximum: 1
          description: Percentage of traffic allocated to this variant
          example: 0.5
        configuration:
          type: object
          additionalProperties: true
          description: Variant-specific configuration parameters
          example: {"button_color": "#dc3545", "button_text": "Get Started"}
        created_at:
          type: string
          format: date-time
          example: "2024-01-10T10:00:00Z"
        updated_at:
          type: string
          format: date-time
          example: "2024-01-10T15:30:00Z"

    TargetingRule:
      type: object
      required:
        - id
        - name
        - attribute_name
        - operator
        - is_active
        - priority
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the targeting rule
        name:
          type: string
          minLength: 1
          maxLength: 255
          description: Human-readable name for the rule
          example: "US Users Only"
        attribute_name:
          type: string
          minLength: 1
          maxLength: 100
          description: User attribute to evaluate
          example: "country"
        operator:
          $ref: '#/components/schemas/TargetingOperator'
        value:
          oneOf:
            - type: string
            - type: number
            - type: boolean
            - type: array
              items:
                type: string
          description: Value(s) to compare against the user attribute
          example: "US"
        is_active:
          type: boolean
          description: Whether this rule is currently active
          example: true
        priority:
          type: integer
          minimum: 0
          description: Priority order for rule evaluation (lower = higher priority)
          example: 1

    UserAssignment:
      type: object
      required:
        - id
        - user_id
        - variant_id
        - assignment_timestamp
        - is_excluded
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the assignment
        user_id:
          type: string
          description: User identifier
          example: "user_12345"
        variant_id:
          type: string
          format: uuid
          description: ID of the assigned variant
        session_id:
          type: string
          description: Session identifier
          example: "session_abc123"
        assignment_timestamp:
          type: string
          format: date-time
          description: When the user was assigned
        user_attributes:
          type: object
          additionalProperties: true
          description: User attributes at time of assignment
          example: {"country": "US", "device_type": "desktop"}
        is_excluded:
          type: boolean
          description: Whether the user was excluded from the experiment
          example: false
        exclusion_reason:
          type: string
          description: Reason for exclusion if applicable
          example: "Failed targeting rules"

    Event:
      type: object
      required:
        - id
        - user_id
        - event_name
        - timestamp
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the event
        user_id:
          type: string
          description: User who triggered the event
          example: "user_12345"
        session_id:
          type: string
          description: Session identifier
          example: "session_abc123"
        event_name:
          type: string
          maxLength: 100
          description: Name of the event
          example: "button_click"
        event_value:
          type: number
          description: Numeric value associated with the event
          example: 1
        event_properties:
          type: object
          additionalProperties: true
          description: Additional event properties
          example: {"button_color": "red", "page": "homepage"}
        timestamp:
          type: string
          format: date-time
          description: When the event occurred
          example: "2024-01-15T14:30:00Z"

    # Request Schemas
    CreateExperimentRequest:
      type: object
      required:
        - name
        - variants
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 255
          description: Name of the experiment
          example: "Homepage Button Color Test"
        description:
          type: string
          maxLength: 1000
          description: Description of the experiment
        hypothesis:
          type: string
          maxLength: 1000
          description: Hypothesis being tested
        traffic_allocation:
          type: number
          minimum: 0
          maximum: 1
          default: 1.0
          description: Traffic allocation (0.0 to 1.0)
        assignment_method:
          $ref: '#/components/schemas/AssignmentMethod'
        start_date:
          type: string
          format: date-time
          description: Experiment start date
        end_date:
          type: string
          format: date-time
          description: Experiment end date
        sample_size:
          type: integer
          minimum: 1
          description: Target sample size
        confidence_level:
          type: number
          minimum: 0.8
          maximum: 0.99
          default: 0.95
        minimum_detectable_effect:
          type: number
          minimum: 0.01
          maximum: 1.0
        primary_metric:
          type: string
          maxLength: 100
          description: Primary metric name
        secondary_metrics:
          type: array
          items:
            type: string
            maxLength: 100
        tags:
          type: array
          items:
            type: string
            maxLength: 50
        metadata:
          type: object
          additionalProperties: true
        variants:
          type: array
          minItems: 2
          maxItems: 10
          items:
            $ref: '#/components/schemas/CreateVariantRequest'
        targeting_rules:
          type: array
          items:
            $ref: '#/components/schemas/CreateTargetingRuleRequest'

    CreateVariantRequest:
      type: object
      required:
        - name
        - traffic_weight
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 255
          example: "Red Button"
        description:
          type: string
          maxLength: 500
        is_control:
          type: boolean
          default: false
        traffic_weight:
          type: number
          minimum: 0
          maximum: 1
          example: 0.5
        configuration:
          type: object
          additionalProperties: true

    CreateTargetingRuleRequest:
      type: object
      required:
        - name
        - attribute_name
        - operator
        - value
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 255
          example: "US Users Only"
        attribute_name:
          type: string
          minLength: 1
          maxLength: 100
          example: "country"
        operator:
          $ref: '#/components/schemas/TargetingOperator'
        value:
          oneOf:
            - type: string
            - type: number
            - type: boolean
            - type: array
              items:
                type: string
          example: "US"
        is_active:
          type: boolean
          default: true
        priority:
          type: integer
          minimum: 0
          default: 0

    UpdateExperimentRequest:
      type: object
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 255
        description:
          type: string
          maxLength: 1000
        hypothesis:
          type: string
          maxLength: 1000
        traffic_allocation:
          type: number
          minimum: 0
          maximum: 1
        assignment_method:
          $ref: '#/components/schemas/AssignmentMethod'
        end_date:
          type: string
          format: date-time
        sample_size:
          type: integer
          minimum: 1
        confidence_level:
          type: number
          minimum: 0.8
          maximum: 0.99
        minimum_detectable_effect:
          type: number
          minimum: 0.01
          maximum: 1.0
        primary_metric:
          type: string
          maxLength: 100
        secondary_metrics:
          type: array
          items:
            type: string
            maxLength: 100
        tags:
          type: array
          items:
            type: string
            maxLength: 50
        metadata:
          type: object
          additionalProperties: true

    UpdateVariantRequest:
      type: object
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 255
        description:
          type: string
          maxLength: 500
        traffic_weight:
          type: number
          minimum: 0
          maximum: 1
        configuration:
          type: object
          additionalProperties: true

    UserAssignmentRequest:
      type: object
      required:
        - user_id
      properties:
        user_id:
          type: string
          description: Unique identifier for the user
          example: "user_12345"
        session_id:
          type: string
          description: Session identifier
          example: "session_abc123"
        user_attributes:
          type: object
          additionalProperties: true
          description: User attributes for targeting evaluation
          example: {"country": "US", "device_type": "desktop", "subscription_tier": "premium"}

    TrackEventRequest:
      type: object
      required:
        - user_id
        - event_name
      properties:
        user_id:
          type: string
          description: User who triggered the event
          example: "user_12345"
        session_id:
          type: string
          description: Session identifier
          example: "session_abc123"
        event_name:
          type: string
          maxLength: 100
          description: Name of the event
          example: "button_click"
        event_value:
          type: number
          description: Numeric value associated with the event
          example: 1
        event_properties:
          type: object
          additionalProperties: true
          description: Additional event properties
          example: {"button_color": "red", "page": "homepage"}
        timestamp:
          type: string
          format: date-time
          description: When the event occurred (defaults to now)

    # Response Schemas
    ExperimentResponse:
      type: object
      properties:
        data:
          allOf:
            - $ref: '#/components/schemas/Experiment'
            - type: object
              properties:
                variants:
                  type: array
                  items:
                    $ref: '#/components/schemas/Variant'
                targeting_rules:
                  type: array
                  items:
                    $ref: '#/components/schemas/TargetingRule'
                statistics:
                  $ref: '#/components/schemas/ExperimentStatistics'
        meta:
          $ref: '#/components/schemas/ResponseMeta'

    ExperimentListResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/Experiment'
        meta:
          $ref: '#/components/schemas/ResponseMeta'
        pagination:
          $ref: '#/components/schemas/PaginationMeta'

    VariantResponse:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/Variant'
        meta:
          $ref: '#/components/schemas/ResponseMeta'

    UserAssignmentResponse:
      type: object
      properties:
        data:
          type: object
          properties:
            variant_id:
              type: string
              format: uuid
              nullable: true
              description: ID of assigned variant (null if excluded)
            is_excluded:
              type: boolean
              description: Whether user was excluded from experiment
            exclusion_reason:
              type: string
              nullable: true
              description: Reason for exclusion if applicable
            assignment:
              type: object
              nullable: true
              properties:
                id:
                  type: string
                  format: uuid
                experiment_id:
                  type: string
                  format: uuid
                variant_id:
                  type: string
                  format: uuid
                variant:
                  type: object
                  properties:
                    name:
                      type: string
                    configuration:
                      type: object
                      additionalProperties: true
        meta:
          $ref: '#/components/schemas/ResponseMeta'

    UserAssignmentListResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/UserAssignment'
        meta:
          $ref: '#/components/schemas/ResponseMeta'
        pagination:
          $ref: '#/components/schemas/PaginationMeta'

    EventResponse:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/Event'
        meta:
          $ref: '#/components/schemas/ResponseMeta'

    EventListResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/Event'
        meta:
          $ref: '#/components/schemas/ResponseMeta'
        pagination:
          $ref: '#/components/schemas/PaginationMeta'

    ExperimentAnalyticsResponse:
      type: object
      properties:
        data:
          type: object
          properties:
            experiment_id:
              type: string
              format: uuid
            experiment_name:
              type: string
            status:
              $ref: '#/components/schemas/ExperimentStatus'
            total_users:
              type: integer
              description: Total users assigned to experiment
            start_date:
              type: string
              format: date-time
              nullable: true
            end_date:
              type: string
              format: date-time
              nullable: true
            duration_days:
              type: integer
              nullable: true
              description: Experiment duration in days
            variants:
              type: array
              items:
                type: object
                properties:
                  variant_id:
                    type: string
                    format: uuid
                  variant_name:
                    type: string
                  is_control:
                    type: boolean
                  metrics:
                    $ref: '#/components/schemas/ConversionMetrics'
                  statistical_significance:
                    type: boolean
                    nullable: true
                  p_value:
                    type: number
                    nullable: true
                  confidence_interval:
                    type: object
                    nullable: true
                    properties:
                      lower:
                        type: number
                      upper:
                        type: number
        meta:
          $ref: '#/components/schemas/ResponseMeta'

    ExperimentStatistics:
      type: object
      properties:
        total_assignments:
          type: integer
          description: Total user assignments
        total_events:
          type: integer
          description: Total events tracked
        variants:
          type: array
          items:
            type: object
            properties:
              variant_id:
                type: string
                format: uuid
              variant_name:
                type: string
              is_control:
                type: boolean
              assignments:
                type: integer
              events:
                type: integer
              assignment_percentage:
                type: number
                description: Percentage of total assignments

    ConversionMetrics:
      type: object
      properties:
        total_users:
          type: integer
          description: Total users in this variant
        converted_users:
          type: integer
          description: Users who converted
        conversion_rate:
          type: number
          description: Conversion rate (0.0 to 1.0)
        average_value:
          type: number
          nullable: true
          description: Average conversion value
        total_value:
          type: number
          nullable: true
          description: Total conversion value

    # Common Response Schemas
    ResponseMeta:
      type: object
      properties:
        timestamp:
          type: string
          format: date-time
          description: Response timestamp
          example: "2024-01-15T14:30:00Z"
        request_id:
          type: string
          format: uuid
          description: Unique request identifier
          example: "req_123e4567-e89b-12d3-a456-************"
        tenant_id:
          type: string
          format: uuid
          description: Tenant identifier
          example: "tenant_123e4567-e89b-12d3-a456-************"

    PaginationMeta:
      type: object
      properties:
        page:
          type: integer
          description: Current page number
          example: 1
        limit:
          type: integer
          description: Items per page
          example: 20
        total:
          type: integer
          description: Total number of items
          example: 150
        total_pages:
          type: integer
          description: Total number of pages
          example: 8
        has_next:
          type: boolean
          description: Whether there is a next page
          example: true
        has_prev:
          type: boolean
          description: Whether there is a previous page
          example: false

    Error:
      type: object
      required:
        - code
        - message
      properties:
        code:
          type: string
          description: Error code
          example: "EXPERIMENT_NOT_FOUND"
        message:
          type: string
          description: Human-readable error message
          example: "The requested experiment was not found"
        details:
          type: object
          additionalProperties: true
          description: Additional error details
        field_errors:
          type: array
          items:
            type: object
            properties:
              field:
                type: string
                description: Field name with error
              message:
                type: string
                description: Field-specific error message
          description: Field-level validation errors

  responses:
    BadRequest:
      description: Bad request - invalid parameters or malformed request
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                $ref: '#/components/schemas/Error'
          example:
            error:
              code: "INVALID_REQUEST"
              message: "The request contains invalid parameters"
              details:
                invalid_fields: ["traffic_allocation"]

    Unauthorized:
      description: Unauthorized - missing or invalid authentication
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                $ref: '#/components/schemas/Error'
          example:
            error:
              code: "UNAUTHORIZED"
              message: "Authentication required"

    Forbidden:
      description: Forbidden - insufficient permissions
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                $ref: '#/components/schemas/Error'
          example:
            error:
              code: "FORBIDDEN"
              message: "Insufficient permissions to access this resource"

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                $ref: '#/components/schemas/Error'
          example:
            error:
              code: "EXPERIMENT_NOT_FOUND"
              message: "The requested experiment was not found"

    Conflict:
      description: Conflict - resource state prevents the operation
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                $ref: '#/components/schemas/Error'
          example:
            error:
              code: "INVALID_STATUS_TRANSITION"
              message: "Cannot transition from COMPLETED to ACTIVE status"
              details:
                current_status: "COMPLETED"
                requested_status: "ACTIVE"

    ValidationError:
      description: Validation error - request data failed validation
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                $ref: '#/components/schemas/Error'
          example:
            error:
              code: "VALIDATION_ERROR"
              message: "Request validation failed"
              field_errors:
                - field: "variants"
                  message: "At least 2 variants are required"
                - field: "traffic_allocation"
                  message: "Must be between 0.0 and 1.0"

    RateLimitExceeded:
      description: Rate limit exceeded
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                $ref: '#/components/schemas/Error'
          example:
            error:
              code: "RATE_LIMIT_EXCEEDED"
              message: "Rate limit exceeded. Try again later."
              details:
                retry_after: 3600

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                $ref: '#/components/schemas/Error'
          example:
            error:
              code: "INTERNAL_SERVER_ERROR"
              message: "An unexpected error occurred"

tags:
  - name: Experiments
    description: Experiment management operations
  - name: Experiment Control
    description: Experiment lifecycle control (start, pause, complete, archive)
  - name: Variants
    description: Experiment variant management
  - name: User Assignments
    description: User assignment and bucketing
  - name: Events
    description: Event tracking and analytics
  - name: Analytics
    description: Experiment analytics and reporting

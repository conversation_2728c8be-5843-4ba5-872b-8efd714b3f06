-- Sample Data for A/B Testing Platform
-- This file contains example data to demonstrate the schema usage

-- Insert sample tenants
INSERT INTO tenants (id, name, slug, settings) VALUES
    ('550e8400-e29b-41d4-a716-446655440001', 'Acme Corporation', 'acme-corp', '{"timezone": "UTC", "currency": "USD"}'),
    ('550e8400-e29b-41d4-a716-446655440002', 'TechStart Inc', 'techstart', '{"timezone": "PST", "currency": "USD"}'),
    ('550e8400-e29b-41d4-a716-446655440003', 'Global Retail', 'global-retail', '{"timezone": "EST", "currency": "USD"}');

-- Insert sample experiments
INSERT INTO experiments (id, tenant_id, name, description, hypothesis, status, traffic_allocation, start_date, end_date, primary_metric, secondary_metrics, tags) VALUES
    (
        '660e8400-e29b-41d4-a716-446655440001',
        '550e8400-e29b-41d4-a716-446655440001',
        'Homepage Hero Button Color',
        'Testing different button colors on the homepage hero section',
        'A red CTA button will increase click-through rates compared to the current blue button',
        'active',
        1.0000,
        '2024-01-15 00:00:00+00',
        '2024-02-15 00:00:00+00',
        'cta_click_rate',
        ARRAY['page_views', 'bounce_rate', 'time_on_page'],
        ARRAY['homepage', 'cta', 'ui']
    ),
    (
        '660e8400-e29b-41d4-a716-446655440002',
        '550e8400-e29b-41d4-a716-446655440001',
        'Checkout Flow Simplification',
        'Testing a simplified 2-step vs 3-step checkout process',
        'Reducing checkout steps from 3 to 2 will increase conversion rate',
        'active',
        0.5000,
        '2024-01-20 00:00:00+00',
        '2024-03-20 00:00:00+00',
        'checkout_completion_rate',
        ARRAY['cart_abandonment_rate', 'time_to_complete'],
        ARRAY['checkout', 'conversion', 'ux']
    ),
    (
        '660e8400-e29b-41d4-a716-446655440003',
        '550e8400-e29b-41d4-a716-446655440002',
        'Email Subject Line Test',
        'Testing personalized vs generic email subject lines',
        'Personalized subject lines will increase email open rates',
        'completed',
        1.0000,
        '2024-01-01 00:00:00+00',
        '2024-01-31 00:00:00+00',
        'email_open_rate',
        ARRAY['click_through_rate', 'unsubscribe_rate'],
        ARRAY['email', 'personalization', 'marketing']
    );

-- Insert sample variants
INSERT INTO variants (id, tenant_id, experiment_id, name, description, is_control, traffic_weight, configuration) VALUES
    -- Homepage Hero Button Color variants
    ('770e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440001', 'Control (Blue Button)', 'Original blue CTA button', true, 0.5000, '{"button_color": "#007bff", "button_text": "Get Started"}'),
    ('770e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440001', 'Red Button', 'Red CTA button variant', false, 0.5000, '{"button_color": "#dc3545", "button_text": "Get Started"}'),
    
    -- Checkout Flow variants
    ('770e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440002', 'Control (3-Step)', 'Original 3-step checkout', true, 0.5000, '{"steps": 3, "flow": ["cart", "shipping", "payment"]}'),
    ('770e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440002', '2-Step Checkout', 'Simplified 2-step checkout', false, 0.5000, '{"steps": 2, "flow": ["cart", "shipping_payment"]}'),
    
    -- Email Subject Line variants
    ('770e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440002', '660e8400-e29b-41d4-a716-446655440003', 'Generic Subject', 'Generic email subject line', true, 0.5000, '{"subject_template": "Weekly Newsletter - Latest Updates"}'),
    ('770e8400-e29b-41d4-a716-446655440006', '550e8400-e29b-41d4-a716-446655440002', '660e8400-e29b-41d4-a716-446655440003', 'Personalized Subject', 'Personalized email subject line', false, 0.5000, '{"subject_template": "Hi {{first_name}}, Your Weekly Updates Are Here!"}');

-- Insert sample targeting rules
INSERT INTO targeting_rules (id, tenant_id, experiment_id, name, attribute_name, operator, value_text, value_list, is_active, priority) VALUES
    -- Target users from specific countries for homepage test
    ('880e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440001', 'US and Canada Only', 'country', 'in', NULL, ARRAY['US', 'CA'], true, 1),
    
    -- Target desktop users for checkout test
    ('880e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440002', 'Desktop Users', 'device_type', 'equals', 'desktop', NULL, true, 1),
    
    -- Target premium users for checkout test
    ('880e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440002', 'Premium Users', 'user_tier', 'equals', 'premium', NULL, true, 2),
    
    -- Target users with email preferences for email test
    ('880e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440002', '660e8400-e29b-41d4-a716-446655440003', 'Email Subscribers', 'email_subscribed', 'equals', NULL, NULL, true, 1);

-- Update the targeting rule with boolean value
UPDATE targeting_rules 
SET value_boolean = true 
WHERE id = '880e8400-e29b-41d4-a716-446655440004';

-- Insert sample user assignments
INSERT INTO user_assignments (id, tenant_id, experiment_id, variant_id, user_id, session_id, bucketing_key, user_attributes) VALUES
    ('990e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440001', '770e8400-e29b-41d4-a716-446655440001', 'user_12345', 'session_abc123', 'user_12345', '{"country": "US", "device_type": "desktop", "user_tier": "free"}'),
    ('990e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440001', '770e8400-e29b-41d4-a716-446655440002', 'user_12346', 'session_def456', 'user_12346', '{"country": "US", "device_type": "mobile", "user_tier": "premium"}'),
    ('990e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440002', '770e8400-e29b-41d4-a716-446655440003', 'user_12347', 'session_ghi789', 'user_12347', '{"country": "CA", "device_type": "desktop", "user_tier": "premium"}'),
    ('990e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440002', '770e8400-e29b-41d4-a716-446655440004', 'user_12348', 'session_jkl012', 'user_12348', '{"country": "US", "device_type": "desktop", "user_tier": "premium"}'),
    ('990e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440002', '660e8400-e29b-41d4-a716-446655440003', '770e8400-e29b-41d4-a716-446655440005', 'user_12349', 'session_mno345', 'user_12349', '{"email_subscribed": true, "first_name": "John"}'),
    ('990e8400-e29b-41d4-a716-446655440006', '550e8400-e29b-41d4-a716-446655440002', '660e8400-e29b-41d4-a716-446655440003', '770e8400-e29b-41d4-a716-446655440006', 'user_12350', 'session_pqr678', 'user_12350', '{"email_subscribed": true, "first_name": "Jane"}');

-- Insert sample events
INSERT INTO events (id, tenant_id, experiment_id, variant_id, user_id, session_id, event_name, event_value, event_properties) VALUES
    -- Homepage button clicks
    ('aa0e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440001', '770e8400-e29b-41d4-a716-446655440001', 'user_12345', 'session_abc123', 'cta_click', 1, '{"button_position": "hero", "page": "homepage"}'),
    ('aa0e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440001', '770e8400-e29b-41d4-a716-446655440002', 'user_12346', 'session_def456', 'page_view', 1, '{"page": "homepage", "referrer": "google"}'),
    
    -- Checkout events
    ('aa0e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440002', '770e8400-e29b-41d4-a716-446655440003', 'user_12347', 'session_ghi789', 'checkout_started', 1, '{"cart_value": 99.99, "items_count": 2}'),
    ('aa0e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440002', '770e8400-e29b-41d4-a716-446655440004', 'user_12348', 'session_jkl012', 'checkout_completed', 99.99, '{"cart_value": 99.99, "payment_method": "credit_card"}'),
    
    -- Email events
    ('aa0e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440002', '660e8400-e29b-41d4-a716-446655440003', '770e8400-e29b-41d4-a716-446655440005', 'user_12349', 'session_mno345', 'email_opened', 1, '{"email_type": "newsletter", "subject": "Weekly Newsletter - Latest Updates"}'),
    ('aa0e8400-e29b-41d4-a716-446655440006', '550e8400-e29b-41d4-a716-446655440002', '660e8400-e29b-41d4-a716-446655440003', '770e8400-e29b-41d4-a716-446655440006', 'user_12350', 'session_pqr678', 'email_opened', 1, '{"email_type": "newsletter", "subject": "Hi Jane, Your Weekly Updates Are Here!"}');

-- Insert sample experiment results
INSERT INTO experiment_results (id, tenant_id, experiment_id, variant_id, metric_name, sample_size, conversion_rate, mean_value, standard_deviation, confidence_interval_lower, confidence_interval_upper, p_value, statistical_significance) VALUES
    -- Homepage button test results
    ('bb0e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440001', '770e8400-e29b-41d4-a716-446655440001', 'cta_click_rate', 1000, 0.125000, 0.125000, 0.033071, 0.060181, 0.189819, 0.045000, true),
    ('bb0e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440001', '770e8400-e29b-41d4-a716-446655440002', 'cta_click_rate', 1000, 0.158000, 0.158000, 0.036496, 0.086468, 0.229532, 0.045000, true),
    
    -- Email test results (completed experiment)
    ('bb0e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440002', '660e8400-e29b-41d4-a716-446655440003', '770e8400-e29b-41d4-a716-446655440005', 'email_open_rate', 5000, 0.220000, 0.220000, 0.041473, 0.138713, 0.301287, 0.001200, true),
    ('bb0e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440002', '660e8400-e29b-41d4-a716-446655440003', '770e8400-e29b-41d4-a716-446655440006', 'email_open_rate', 5000, 0.285000, 0.285000, 0.045166, 0.196475, 0.373525, 0.001200, true);

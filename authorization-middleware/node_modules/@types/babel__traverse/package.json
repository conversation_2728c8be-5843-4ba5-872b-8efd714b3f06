{"name": "@types/babel__traverse", "version": "7.20.7", "description": "TypeScript definitions for @babel/traverse", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__traverse", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "yortus", "url": "https://github.com/yortus"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/marvinhagemeister"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/rpetrich"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/mgroenhoff"}, {"name": "<PERSON>", "githubUsername": "dlgrit", "url": "https://github.com/dlgrit"}, {"name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr", "url": "https://github.com/ifiokjr"}, {"name": "ExE Boss", "githubUsername": "ExE-Boss", "url": "https://github.com/ExE-Boss"}, {"name": "<PERSON>", "githubUsername": "danez", "url": "https://github.com/danez"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/babel__traverse"}, "scripts": {}, "dependencies": {"@babel/types": "^7.20.7"}, "peerDependencies": {}, "typesPublisherContentHash": "e58d29a4d5c39ba4fa0291c8c7d5abad18881f7ed9f938feeb97726ad48a0544", "typeScriptVersion": "5.0"}
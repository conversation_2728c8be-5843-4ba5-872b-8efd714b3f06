# commons

[![CircleCI](https://circleci.com/gh/sinonjs/commons.svg?style=svg)](https://circleci.com/gh/sinonjs/commons)
[![codecov](https://codecov.io/gh/sinonjs/commons/branch/master/graph/badge.svg)](https://codecov.io/gh/sinonjs/commons)
<a href="CODE_OF_CONDUCT.md"><img src="https://img.shields.io/badge/Contributor%20Covenant-v2.0%20adopted-ff69b4.svg" alt="Contributor Covenant" /></a>

Simple functions shared among the sinon end user libraries

## Rules

-   Follows the [Sinon.JS compatibility](https://github.com/sinonjs/sinon/blob/master/CONTRIBUTING.md#compatibility)
-   100% test coverage
-   Code formatted using [Prettier](https://prettier.io)
-   No side effects welcome! (only pure functions)
-   No platform specific functions
-   One export per file (any bundler can do tree shaking)

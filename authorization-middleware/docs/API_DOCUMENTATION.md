# Experiment Management API Documentation

## Overview

The Experiment Management API provides comprehensive endpoints for managing A/B testing experiments with role-based access control, ownership verification, and multi-tenant support.

## Base URL

```
http://localhost:3003/api
```

## Authentication

All API endpoints require authentication via Bearer token in the Authorization header:

```http
Authorization: Bearer <token>
```

### Available Test Tokens

| Token | Role | Tenant | Permissions |
|-------|------|--------|-------------|
| `admin_token` | Admin | tenant_123 | Full access to all operations |
| `experimenter_token` | Experimenter | tenant_123 | Experiment management, analytics |
| `viewer_token` | Viewer | tenant_123 | Read-only access |
| `other_tenant_token` | Experimenter | tenant_999 | Cross-tenant isolation testing |

## Common Response Format

### Success Response
```json
{
  "success": true,
  "data": {
    // Response data
  }
}
```

### Error Response
```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE",
  "requestId": "req_**********789_abc123",
  "timestamp": "2023-12-21T10:30:00.000Z",
  "details": {
    // Additional error details
  }
}
```

## Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `AUTHENTICATION_REQUIRED` | 401 | Missing or invalid authentication |
| `INSUFFICIENT_PERMISSIONS` | 403 | User lacks required permissions |
| `NOT_EXPERIMENT_OWNER` | 403 | User doesn't own the experiment |
| `EXPERIMENT_NOT_FOUND` | 404 | Experiment doesn't exist |
| `VALIDATION_ERROR` | 400 | Invalid request data |
| `AUTHORIZATION_ERROR` | 500 | Internal authorization error |

---

## Experiment Endpoints

### List Experiments

Retrieve a paginated list of experiments for the authenticated user's tenant.

**Endpoint:** `GET /experiments`

**Required Permission:** `experiments:read`

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20, max: 100)
- `status` (optional): Filter by status (`draft`, `active`, `paused`, `archived`)
- `search` (optional): Search in experiment name and description

**Request Example:**
```http
GET /api/experiments?page=1&limit=10&status=active
Authorization: Bearer experimenter_token
```

**Response Example:**
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": "exp_001",
        "name": "Button Color Test",
        "description": "Testing different button colors for conversion",
        "type": "ab_test",
        "status": "active",
        "tenantId": "tenant_123",
        "createdBy": "experimenter_456",
        "createdAt": "2023-12-01T00:00:00.000Z",
        "updatedAt": "2023-12-01T00:00:00.000Z"
      }
    ],
    "total": 3,
    "page": 1,
    "limit": 10
  }
}
```

**Error Examples:**
```json
{
  "success": false,
  "error": "Missing permission: experiments:read",
  "code": "INSUFFICIENT_PERMISSIONS",
  "details": {
    "requiredPermission": "experiments:read",
    "denyReasons": ["User does not have permission: experiments:read"]
  }
}
```

---

### Get Experiment

Retrieve a specific experiment by ID. Requires ownership or admin role.

**Endpoint:** `GET /experiments/{id}`

**Required Permission:** `experiments:read` + ownership verification

**Path Parameters:**
- `id`: Experiment ID

**Request Example:**
```http
GET /api/experiments/exp_001
Authorization: Bearer experimenter_token
```

**Response Example:**
```json
{
  "success": true,
  "data": {
    "experiment": {
      "id": "exp_001",
      "name": "Button Color Test",
      "description": "Testing different button colors for conversion",
      "type": "ab_test",
      "status": "active",
      "tenantId": "tenant_123",
      "createdBy": "experimenter_456",
      "createdAt": "2023-12-01T00:00:00.000Z",
      "updatedAt": "2023-12-01T00:00:00.000Z"
    }
  }
}
```

**Error Examples:**
```json
{
  "success": false,
  "error": "User does not own this experiment",
  "code": "NOT_EXPERIMENT_OWNER",
  "details": {
    "experimentId": "exp_001",
    "experimentOwner": "experimenter_456",
    "requestingUser": "viewer_789"
  }
}
```

---

### Create Experiment

Create a new experiment.

**Endpoint:** `POST /experiments`

**Required Permission:** `experiments:create`

**Request Body:**
```json
{
  "name": "string (required, max 255 chars)",
  "description": "string (optional, max 1000 chars)",
  "type": "string (optional, default: 'ab_test')",
  "config": "object (optional)",
  "metadata": "object (optional)"
}
```

**Request Example:**
```http
POST /api/experiments
Authorization: Bearer experimenter_token
Content-Type: application/json

{
  "name": "New Button Test",
  "description": "Testing new button designs",
  "type": "ab_test"
}
```

**Response Example:**
```json
{
  "success": true,
  "data": {
    "experiment": {
      "id": "exp_**********789",
      "name": "New Button Test",
      "description": "Testing new button designs",
      "type": "ab_test",
      "status": "draft",
      "tenantId": "tenant_123",
      "createdBy": "experimenter_456",
      "createdAt": "2023-12-21T10:30:56.789Z",
      "updatedAt": "2023-12-21T10:30:56.789Z"
    }
  }
}
```

**Validation Errors:**
```json
{
  "success": false,
  "error": "Validation failed",
  "code": "VALIDATION_ERROR",
  "details": {
    "errors": [
      {
        "field": "name",
        "message": "Name is required"
      }
    ]
  }
}
```

---

### Update Experiment

Update an existing experiment. Requires ownership or admin role.

**Endpoint:** `PUT /experiments/{id}`

**Required Permission:** `experiments:update` + ownership verification

**Path Parameters:**
- `id`: Experiment ID

**Request Body:**
```json
{
  "name": "string (optional)",
  "description": "string (optional)",
  "status": "string (optional)",
  "config": "object (optional)",
  "metadata": "object (optional)"
}
```

**Request Example:**
```http
PUT /api/experiments/exp_001
Authorization: Bearer experimenter_token
Content-Type: application/json

{
  "name": "Updated Button Test",
  "description": "Updated description",
  "status": "active"
}
```

**Response Example:**
```json
{
  "success": true,
  "data": {
    "experiment": {
      "id": "exp_001",
      "name": "Updated Button Test",
      "description": "Updated description",
      "type": "ab_test",
      "status": "active",
      "tenantId": "tenant_123",
      "createdBy": "experimenter_456",
      "updatedBy": "experimenter_456",
      "createdAt": "2023-12-01T00:00:00.000Z",
      "updatedAt": "2023-12-21T10:35:00.000Z"
    }
  }
}
```

---

### Delete Experiment

Delete an experiment. Requires ownership or admin role.

**Endpoint:** `DELETE /experiments/{id}`

**Required Permission:** `experiments:delete` + ownership verification

**Path Parameters:**
- `id`: Experiment ID

**Request Example:**
```http
DELETE /api/experiments/exp_001
Authorization: Bearer admin_token
```

**Response Example:**
```json
{
  "success": true,
  "message": "Experiment deleted successfully"
}
```

---

### Publish Experiment

Publish an experiment to make it active. Requires multiple permissions.

**Endpoint:** `POST /experiments/{id}/publish`

**Required Permissions:** 
- `experiments:update` AND `experiments:publish` + ownership verification

**Path Parameters:**
- `id`: Experiment ID

**Request Example:**
```http
POST /api/experiments/exp_001/publish
Authorization: Bearer experimenter_token
```

**Response Example:**
```json
{
  "success": true,
  "data": {
    "experiment": {
      "id": "exp_001",
      "name": "Button Color Test",
      "status": "active",
      "publishedAt": "2023-12-21T10:40:00.000Z"
    }
  },
  "message": "Experiment published successfully"
}
```

**Multiple Permission Error:**
```json
{
  "success": false,
  "error": "Access denied (1/2 permissions)",
  "code": "INSUFFICIENT_PERMISSIONS",
  "details": {
    "requiredPermissions": [
      {"resource": "experiments", "action": "update"},
      {"resource": "experiments", "action": "publish"}
    ],
    "grantedCount": 1,
    "requireAll": true,
    "results": [
      {"resource": "experiments", "action": "update", "granted": true},
      {"resource": "experiments", "action": "publish", "granted": false}
    ]
  }
}
```

---

## User Management Endpoints

### List Users

Retrieve users in the tenant. Admin only.

**Endpoint:** `GET /users`

**Required Permission:** `users:read` (Admin role)

**Request Example:**
```http
GET /api/users
Authorization: Bearer admin_token
```

**Response Example:**
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": "admin_123",
        "email": "<EMAIL>",
        "role": "admin"
      },
      {
        "id": "experimenter_456",
        "email": "<EMAIL>",
        "role": "experimenter"
      }
    ]
  }
}
```

---

### Invite User

Invite a new user to the tenant. Admin only.

**Endpoint:** `POST /users/invite`

**Required Permission:** `users:invite` (Admin role)

**Request Body:**
```json
{
  "email": "string (required)",
  "role": "string (required)"
}
```

**Request Example:**
```http
POST /api/users/invite
Authorization: Bearer admin_token
Content-Type: application/json

{
  "email": "<EMAIL>",
  "role": "experimenter"
}
```

**Response Example:**
```json
{
  "success": true,
  "message": "Invitation <NAME_EMAIL> with role experimenter"
}
```

---

## Analytics Endpoints

### View Dashboard

Get analytics dashboard data.

**Endpoint:** `GET /analytics/dashboard`

**Required Permission:** `analytics:view`

**Request Example:**
```http
GET /api/analytics/dashboard
Authorization: Bearer experimenter_token
```

**Response Example:**
```json
{
  "success": true,
  "data": {
    "totalExperiments": 25,
    "activeExperiments": 8,
    "totalConversions": 1250,
    "conversionRate": 0.15
  }
}
```

---

### Export Analytics

Export analytics data.

**Endpoint:** `GET /analytics/export`

**Required Permission:** `analytics:export`

**Query Parameters:**
- `format` (optional): Export format (`csv`, `json`, default: `csv`)
- `startDate` (optional): Start date (ISO 8601)
- `endDate` (optional): End date (ISO 8601)

**Request Example:**
```http
GET /api/analytics/export?format=csv
Authorization: Bearer experimenter_token
```

**Response Example:**
```json
{
  "success": true,
  "data": {
    "exportUrl": "https://example.com/exports/analytics_2023.csv",
    "expiresAt": "2023-12-22T10:30:00.000Z"
  }
}
```

---

## Admin Endpoints

### Get Settings

Retrieve tenant settings. Admin only.

**Endpoint:** `GET /admin/settings`

**Required Permission:** Admin role

**Request Example:**
```http
GET /api/admin/settings
Authorization: Bearer admin_token
```

**Response Example:**
```json
{
  "success": true,
  "data": {
    "tenantName": "Example Corp",
    "maxExperiments": 100,
    "retentionDays": 90,
    "features": ["advanced_analytics", "custom_events", "api_access"]
  }
}
```

---

## Rate Limiting

API endpoints are rate limited to prevent abuse:

- **Standard endpoints**: 100 requests per 15 minutes
- **Create operations**: 20 requests per 15 minutes  
- **Export operations**: 5 requests per 15 minutes
- **Admin operations**: 50 requests per 15 minutes

Rate limit headers are included in responses:
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: **********
```

## Request/Response Headers

### Standard Request Headers
```http
Authorization: Bearer <token>
Content-Type: application/json
X-Request-ID: req_**********789_abc123
```

### Standard Response Headers
```http
Content-Type: application/json
X-Request-ID: req_**********789_abc123
X-Response-Time: 45ms
```

## Testing the API

### Using cURL

```bash
# Health check
curl http://localhost:3003/health

# List experiments (success)
curl -H "Authorization: Bearer experimenter_token" \
     http://localhost:3003/api/experiments

# Access denied example
curl -H "Authorization: Bearer viewer_token" \
     http://localhost:3003/api/users

# Create experiment
curl -X POST \
     -H "Authorization: Bearer experimenter_token" \
     -H "Content-Type: application/json" \
     -d '{"name":"API Test","description":"Testing via API"}' \
     http://localhost:3003/api/experiments
```

### Using JavaScript/Fetch

```javascript
// List experiments
const response = await fetch('http://localhost:3003/api/experiments', {
  headers: {
    'Authorization': 'Bearer experimenter_token',
    'Content-Type': 'application/json'
  }
});

const data = await response.json();
console.log(data);

// Create experiment
const createResponse = await fetch('http://localhost:3003/api/experiments', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer experimenter_token',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    name: 'New Test',
    description: 'Created via JavaScript'
  })
});

const newExperiment = await createResponse.json();
console.log(newExperiment);
```

## SDK Examples

### Node.js SDK Usage

```javascript
const { ExperimentAPI } = require('@yourorg/experiment-sdk');

const client = new ExperimentAPI({
  baseURL: 'http://localhost:3003/api',
  token: 'experimenter_token'
});

// List experiments
const experiments = await client.experiments.list({
  page: 1,
  limit: 10,
  status: 'active'
});

// Create experiment
const newExperiment = await client.experiments.create({
  name: 'SDK Test',
  description: 'Created via SDK'
});

// Get experiment with error handling
try {
  const experiment = await client.experiments.get('exp_001');
  console.log(experiment);
} catch (error) {
  if (error.code === 'NOT_EXPERIMENT_OWNER') {
    console.log('Access denied: You do not own this experiment');
  }
}
```

## Webhooks

The API supports webhooks for real-time notifications:

### Webhook Events
- `experiment.created`
- `experiment.updated`
- `experiment.published`
- `experiment.deleted`
- `user.invited`
- `analytics.exported`

### Webhook Payload Example
```json
{
  "event": "experiment.published",
  "timestamp": "2023-12-21T10:30:00.000Z",
  "data": {
    "experimentId": "exp_001",
    "tenantId": "tenant_123",
    "publishedBy": "experimenter_456"
  }
}
```

## Support

For API support and questions:
- Documentation: [API Docs](http://localhost:3003/docs)
- Health Check: [http://localhost:3003/health](http://localhost:3003/health)
- GitHub Issues: [Report Issues](https://github.com/yourorg/experiment-api/issues)

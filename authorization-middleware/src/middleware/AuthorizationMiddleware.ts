import { Request, Response, NextFunction } from 'express';
import { RBACService } from '../services/RBACService';
import { ExperimentService } from '../services/ExperimentService';
import { AuditLogger } from '../utils/AuditLogger';
import { Logger } from '../utils/Logger';

export interface AuthorizationContext {
  userId: string;
  tenantId: string;
  userRoles: string[];
  experimentId?: string;
  resourceId?: string;
  action: string;
  resource: string;
  ipAddress: string;
  userAgent: string;
  requestId: string;
}

export interface AuthorizationOptions {
  resource: string;
  action: string;
  requireOwnership?: boolean;
  allowSameTenant?: boolean;
  allowedRoles?: string[];
  customCheck?: (context: AuthorizationContext) => Promise<boolean>;
  skipIfMissing?: boolean;
  logLevel?: 'debug' | 'info' | 'warn' | 'error';
}

export interface AuthorizationResult {
  granted: boolean;
  reason: string;
  code: string;
  statusCode: number;
  details?: any;
  context: AuthorizationContext;
}

export class AuthorizationMiddleware {
  private rbacService: RBACService;
  private experimentService: ExperimentService;
  private auditLogger: AuditLogger;
  private logger: Logger;

  constructor(
    rbacService: RBACService,
    experimentService: ExperimentService,
    auditLogger: AuditLogger,
    logger: Logger
  ) {
    this.rbacService = rbacService;
    this.experimentService = experimentService;
    this.auditLogger = auditLogger;
    this.logger = logger;
  }

  /**
   * Main authorization middleware factory
   */
  authorize(options: AuthorizationOptions) {
    return async (req: Request, res: Response, next: NextFunction) => {
      const startTime = Date.now();
      const requestId = req.headers['x-request-id'] as string || this.generateRequestId();

      try {
        // Build authorization context
        const context = await this.buildAuthorizationContext(req, options, requestId);

        // Perform authorization check
        const result = await this.performAuthorizationCheck(context, options);

        // Log the authorization attempt
        await this.logAuthorizationAttempt(result, options, Date.now() - startTime);

        if (!result.granted) {
          return this.sendAuthorizationError(res, result);
        }

        // Add authorization context to request
        req.authorization = {
          context,
          result,
          permissions: await this.getUserPermissions(context.userId, context.tenantId)
        };

        next();

      } catch (error) {
        this.logger.error('Authorization middleware error', {
          error: error instanceof Error ? error.message : 'Unknown error',
          requestId,
          url: req.url,
          method: req.method,
          userId: req.auth?.user?.id,
          duration: Date.now() - startTime
        });

        return res.status(500).json({
          success: false,
          error: 'Authorization check failed',
          code: 'AUTHORIZATION_ERROR',
          requestId
        });
      }
    };
  }

  /**
   * Experiment-specific authorization middleware
   */
  authorizeExperiment(action: string, requireOwnership: boolean = false) {
    return this.authorize({
      resource: 'experiments',
      action,
      requireOwnership,
      logLevel: 'info'
    });
  }

  /**
   * User management authorization middleware
   */
  authorizeUserManagement(action: string) {
    return this.authorize({
      resource: 'users',
      action,
      allowedRoles: ['admin', 'tenant_admin'],
      logLevel: 'warn'
    });
  }

  /**
   * Analytics authorization middleware
   */
  authorizeAnalytics(action: string = 'view') {
    return this.authorize({
      resource: 'analytics',
      action,
      allowSameTenant: true,
      logLevel: 'info'
    });
  }

  /**
   * Admin-only authorization middleware
   */
  authorizeAdmin(resource: string = 'settings', action: string = 'manage') {
    return this.authorize({
      resource,
      action,
      allowedRoles: ['admin', 'tenant_admin', 'super_admin'],
      logLevel: 'warn'
    });
  }

  /**
   * Ownership-based authorization middleware
   */
  authorizeOwnership(resource: string, action: string) {
    return this.authorize({
      resource,
      action,
      requireOwnership: true,
      customCheck: async (context: AuthorizationContext) => {
        return this.checkResourceOwnership(context);
      },
      logLevel: 'info'
    });
  }

  /**
   * Multi-permission authorization middleware
   */
  authorizeMultiple(permissions: Array<{ resource: string; action: string }>, requireAll: boolean = true) {
    return async (req: Request, res: Response, next: NextFunction) => {
      const requestId = req.headers['x-request-id'] as string || this.generateRequestId();
      const startTime = Date.now();

      try {
        const context = await this.buildAuthorizationContext(req, {
          resource: 'multiple',
          action: 'check'
        }, requestId);

        const results: AuthorizationResult[] = [];

        // Check each permission
        for (const permission of permissions) {
          const permissionContext = { ...context, resource: permission.resource, action: permission.action };
          const result = await this.performAuthorizationCheck(permissionContext, {
            resource: permission.resource,
            action: permission.action
          });
          results.push(result);
        }

        // Evaluate results
        const grantedCount = results.filter(r => r.granted).length;
        const hasAccess = requireAll ? grantedCount === permissions.length : grantedCount > 0;

        const finalResult: AuthorizationResult = {
          granted: hasAccess,
          reason: hasAccess 
            ? `Access granted (${grantedCount}/${permissions.length} permissions)` 
            : `Access denied (${grantedCount}/${permissions.length} permissions)`,
          code: hasAccess ? 'ACCESS_GRANTED' : 'INSUFFICIENT_PERMISSIONS',
          statusCode: hasAccess ? 200 : 403,
          context,
          details: {
            requiredPermissions: permissions,
            grantedCount,
            requireAll,
            results: results.map(r => ({
              resource: r.context.resource,
              action: r.context.action,
              granted: r.granted,
              reason: r.reason
            }))
          }
        };

        // Log the authorization attempt
        await this.auditLogger.logAuthorizationAttempt({
          userId: context.userId,
          tenantId: context.tenantId,
          resource: 'multiple',
          action: 'check',
          granted: finalResult.granted,
          reason: finalResult.reason,
          ipAddress: context.ipAddress,
          userAgent: context.userAgent,
          requestId,
          duration: Date.now() - startTime,
          details: finalResult.details
        });

        if (!finalResult.granted) {
          return this.sendAuthorizationError(res, finalResult);
        }

        req.authorization = {
          context,
          result: finalResult,
          permissions: await this.getUserPermissions(context.userId, context.tenantId)
        };

        next();

      } catch (error) {
        this.logger.error('Multiple permissions authorization error', {
          error: error instanceof Error ? error.message : 'Unknown error',
          requestId,
          permissions,
          duration: Date.now() - startTime
        });

        return res.status(500).json({
          success: false,
          error: 'Authorization check failed',
          code: 'AUTHORIZATION_ERROR',
          requestId
        });
      }
    };
  }

  /**
   * Build authorization context from request
   */
  private async buildAuthorizationContext(
    req: Request, 
    options: AuthorizationOptions,
    requestId: string
  ): Promise<AuthorizationContext> {
    const user = req.auth?.user;
    
    if (!user && !options.skipIfMissing) {
      throw new Error('User authentication required');
    }

    const userId = user?.id || 'anonymous';
    const tenantId = user?.tenantId || req.params.tenantId || req.body.tenantId;
    
    if (!tenantId && !options.skipIfMissing) {
      throw new Error('Tenant ID required');
    }

    // Get user roles
    const userRoles = user ? await this.getUserRoles(userId, tenantId) : [];

    // Extract resource identifiers
    const experimentId = req.params.experimentId || req.params.id || req.body.experimentId;
    const resourceId = req.params.id || req.params.resourceId || req.body.resourceId;

    return {
      userId,
      tenantId,
      userRoles,
      experimentId,
      resourceId,
      action: options.action,
      resource: options.resource,
      ipAddress: this.getClientIP(req),
      userAgent: req.headers['user-agent'] || 'unknown',
      requestId
    };
  }

  /**
   * Perform the actual authorization check
   */
  private async performAuthorizationCheck(
    context: AuthorizationContext,
    options: AuthorizationOptions
  ): Promise<AuthorizationResult> {
    const { userId, tenantId, userRoles, resource, action } = context;

    // Check if user is authenticated
    if (userId === 'anonymous' && !options.skipIfMissing) {
      return {
        granted: false,
        reason: 'Authentication required',
        code: 'AUTHENTICATION_REQUIRED',
        statusCode: 401,
        context
      };
    }

    // Check allowed roles if specified
    if (options.allowedRoles && options.allowedRoles.length > 0) {
      const hasAllowedRole = userRoles.some(role => options.allowedRoles!.includes(role));
      if (!hasAllowedRole) {
        return {
          granted: false,
          reason: `Required role not found. Allowed roles: ${options.allowedRoles.join(', ')}`,
          code: 'INSUFFICIENT_ROLE',
          statusCode: 403,
          context,
          details: { requiredRoles: options.allowedRoles, userRoles }
        };
      }
    }

    // Check RBAC permissions
    const permissionResult = await this.rbacService.checkPermission({
      userId,
      tenantId,
      resource,
      action,
      context: {
        experimentId: context.experimentId,
        resourceId: context.resourceId,
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        timestamp: new Date()
      }
    });

    if (!permissionResult.granted) {
      return {
        granted: false,
        reason: permissionResult.reason,
        code: 'INSUFFICIENT_PERMISSIONS',
        statusCode: 403,
        context,
        details: {
          requiredPermission: `${resource}:${action}`,
          denyReasons: permissionResult.denyReasons
        }
      };
    }

    // Check ownership if required
    if (options.requireOwnership && context.experimentId) {
      const ownershipResult = await this.checkExperimentOwnership(context);
      if (!ownershipResult.granted) {
        return ownershipResult;
      }
    }

    // Check same tenant if required
    if (options.allowSameTenant) {
      const tenantResult = await this.checkSameTenant(context);
      if (!tenantResult.granted) {
        return tenantResult;
      }
    }

    // Custom check if provided
    if (options.customCheck) {
      const customResult = await options.customCheck(context);
      if (!customResult) {
        return {
          granted: false,
          reason: 'Custom authorization check failed',
          code: 'CUSTOM_CHECK_FAILED',
          statusCode: 403,
          context
        };
      }
    }

    return {
      granted: true,
      reason: `Access granted for ${action} on ${resource}`,
      code: 'ACCESS_GRANTED',
      statusCode: 200,
      context
    };
  }

  /**
   * Check experiment ownership
   */
  private async checkExperimentOwnership(context: AuthorizationContext): Promise<AuthorizationResult> {
    if (!context.experimentId) {
      return {
        granted: false,
        reason: 'Experiment ID required for ownership check',
        code: 'MISSING_EXPERIMENT_ID',
        statusCode: 400,
        context
      };
    }

    try {
      const experiment = await this.experimentService.getExperiment(context.experimentId, context.tenantId);
      
      if (!experiment) {
        return {
          granted: false,
          reason: 'Experiment not found',
          code: 'EXPERIMENT_NOT_FOUND',
          statusCode: 404,
          context
        };
      }

      // Check if user owns the experiment or has admin role
      const isOwner = experiment.createdBy === context.userId;
      const isAdmin = context.userRoles.some(role => ['admin', 'tenant_admin', 'super_admin'].includes(role));

      if (!isOwner && !isAdmin) {
        return {
          granted: false,
          reason: 'User does not own this experiment',
          code: 'NOT_EXPERIMENT_OWNER',
          statusCode: 403,
          context,
          details: {
            experimentId: context.experimentId,
            experimentOwner: experiment.createdBy,
            requestingUser: context.userId
          }
        };
      }

      return {
        granted: true,
        reason: isOwner ? 'User owns experiment' : 'User has admin privileges',
        code: 'OWNERSHIP_VERIFIED',
        statusCode: 200,
        context
      };

    } catch (error) {
      this.logger.error('Ownership check failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        experimentId: context.experimentId,
        userId: context.userId
      });

      return {
        granted: false,
        reason: 'Ownership verification failed',
        code: 'OWNERSHIP_CHECK_ERROR',
        statusCode: 500,
        context
      };
    }
  }

  /**
   * Check same tenant access
   */
  private async checkSameTenant(context: AuthorizationContext): Promise<AuthorizationResult> {
    // This would check if the requested resource belongs to the same tenant
    // For now, we'll assume it's valid if tenantId matches
    return {
      granted: true,
      reason: 'Same tenant access verified',
      code: 'SAME_TENANT_VERIFIED',
      statusCode: 200,
      context
    };
  }

  /**
   * Check resource ownership (generic)
   */
  private async checkResourceOwnership(context: AuthorizationContext): Promise<boolean> {
    // This would implement generic resource ownership checking
    // For now, return true as a placeholder
    return true;
  }

  /**
   * Get user roles
   */
  private async getUserRoles(userId: string, tenantId: string): Promise<string[]> {
    try {
      const roles = await this.rbacService.getUserRoles(userId, tenantId);
      return roles.map(role => role.name);
    } catch (error) {
      this.logger.warn('Failed to get user roles', { error, userId, tenantId });
      return [];
    }
  }

  /**
   * Get user permissions
   */
  private async getUserPermissions(userId: string, tenantId: string): Promise<string[]> {
    try {
      const effectivePermissions = await this.rbacService.getEffectivePermissions(userId, tenantId);
      return effectivePermissions.permissions.map(p => `${p.resource}:${p.action}`);
    } catch (error) {
      this.logger.warn('Failed to get user permissions', { error, userId, tenantId });
      return [];
    }
  }

  /**
   * Log authorization attempt
   */
  private async logAuthorizationAttempt(
    result: AuthorizationResult,
    options: AuthorizationOptions,
    duration: number
  ): Promise<void> {
    const logLevel = options.logLevel || 'info';
    const logData = {
      userId: result.context.userId,
      tenantId: result.context.tenantId,
      resource: result.context.resource,
      action: result.context.action,
      granted: result.granted,
      reason: result.reason,
      code: result.code,
      ipAddress: result.context.ipAddress,
      userAgent: result.context.userAgent,
      requestId: result.context.requestId,
      duration,
      experimentId: result.context.experimentId,
      resourceId: result.context.resourceId
    };

    // Log to application logger
    this.logger[logLevel]('Authorization attempt', logData);

    // Log to audit logger
    await this.auditLogger.logAuthorizationAttempt({
      ...logData,
      details: result.details
    });
  }

  /**
   * Send authorization error response
   */
  private sendAuthorizationError(res: Response, result: AuthorizationResult): void {
    const response = {
      success: false,
      error: result.reason,
      code: result.code,
      requestId: result.context.requestId,
      timestamp: new Date().toISOString(),
      ...(result.details && { details: result.details })
    };

    res.status(result.statusCode).json(response);
  }

  /**
   * Get client IP address
   */
  private getClientIP(req: Request): string {
    return (
      req.headers['x-forwarded-for'] as string ||
      req.headers['x-real-ip'] as string ||
      req.connection.remoteAddress ||
      req.socket.remoteAddress ||
      'unknown'
    ).split(',')[0].trim();
  }

  /**
   * Generate request ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Extend Express Request interface
declare global {
  namespace Express {
    interface Request {
      authorization?: {
        context: AuthorizationContext;
        result: AuthorizationResult;
        permissions: string[];
      };
    }
  }
}

{"name": "authorization-middleware-demo", "version": "1.0.0", "description": "Authorization middleware demo for A/B testing platform", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "ts-node src/index.ts", "demo": "ts-node examples/authorizationExample.ts", "start": "node dist/index.js", "test": "jest", "setup-db": "ts-node scripts/setupDatabase.ts", "seed-data": "ts-node scripts/seedData.ts"}, "keywords": ["authorization", "middleware", "rbac", "express", "security"], "author": "Your Name", "license": "MIT", "dependencies": {"bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "helmet": "^7.2.0", "ioredis": "^5.6.1", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "pg": "^8.16.0", "uuid": "^9.0.1", "winston": "^3.17.0"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.19", "@types/express": "^4.17.23", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20.10.5", "@types/pg": "^8.15.4", "@types/uuid": "^9.0.8", "jest": "^29.7.0", "nodemon": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}
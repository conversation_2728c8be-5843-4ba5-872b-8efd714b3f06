<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>A/B Testing Platform</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --dark-color: #1e293b;
        }

        body {
            background-color: #f8fafc;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-color), #3b82f6);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }

        .sidebar {
            background: white;
            min-height: calc(100vh - 76px);
            box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
            border-right: 1px solid #e2e8f0;
        }

        .sidebar .nav-link {
            color: var(--secondary-color);
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            margin: 0.25rem 0.75rem;
            transition: all 0.2s;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: var(--primary-color);
            color: white;
        }

        .main-content {
            padding: 2rem;
        }

        .card {
            border: none;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border-radius: 0.75rem;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .stat-card.success {
            background: linear-gradient(135deg, var(--success-color), #059669);
        }

        .stat-card.warning {
            background: linear-gradient(135deg, var(--warning-color), #d97706);
        }

        .stat-card.danger {
            background: linear-gradient(135deg, var(--danger-color), #dc2626);
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: #1d4ed8;
            border-color: #1d4ed8;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-active {
            background-color: #dcfce7;
            color: #166534;
        }

        .status-draft {
            background-color: #fef3c7;
            color: #92400e;
        }

        .status-paused {
            background-color: #fee2e2;
            color: #991b1b;
        }

        .loading {
            display: none;
        }

        .loading.show {
            display: block;
        }

        .user-selector {
            background: white;
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .table th {
            background-color: #f8fafc;
            border-bottom: 2px solid #e2e8f0;
            font-weight: 600;
            color: var(--dark-color);
        }

        .experiment-row {
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .experiment-row:hover {
            background-color: #f8fafc;
        }

        .modal-header {
            background: linear-gradient(135deg, var(--primary-color), #3b82f6);
            color: white;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
        }

        .alert {
            border: none;
            border-radius: 0.75rem;
        }

        .spinner-border-sm {
            width: 1rem;
            height: 1rem;
        }

        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 76px;
                left: -250px;
                width: 250px;
                z-index: 1000;
                transition: left 0.3s;
            }

            .sidebar.show {
                left: 0;
            }

            .main-content {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-flask me-2"></i>
                A/B Testing Platform
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>
                            <span id="currentUserName">Select User</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="switchUser('admin')">
                                <i class="fas fa-crown me-2"></i>Admin User
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="switchUser('experimenter')">
                                <i class="fas fa-flask me-2"></i>Experimenter
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="switchUser('viewer')">
                                <i class="fas fa-eye me-2"></i>Viewer
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="switchUser('other_tenant')">
                                <i class="fas fa-building me-2"></i>Other Tenant
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <nav class="nav flex-column pt-3">
                    <a class="nav-link active" href="#" onclick="showSection('dashboard')">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                    <a class="nav-link" href="#" onclick="showSection('experiments')">
                        <i class="fas fa-flask me-2"></i>Experiments
                    </a>
                    <a class="nav-link" href="#" onclick="showSection('analytics')">
                        <i class="fas fa-chart-bar me-2"></i>Analytics
                    </a>
                    <a class="nav-link" href="#" onclick="showSection('users')" id="usersNavLink">
                        <i class="fas fa-users me-2"></i>Users
                    </a>
                    <a class="nav-link" href="#" onclick="showSection('settings')" id="settingsNavLink">
                        <i class="fas fa-cog me-2"></i>Settings
                    </a>
                </nav>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- User Selector -->
                <div class="user-selector">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h6 class="mb-0">
                                <i class="fas fa-user me-2"></i>
                                Current User: <span id="currentUserDisplay" class="text-primary">Please select a user</span>
                            </h6>
                        </div>
                        <div class="col-md-6 text-end">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="switchUser('admin')">
                                    <i class="fas fa-crown me-1"></i>Admin
                                </button>
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="switchUser('experimenter')">
                                    <i class="fas fa-flask me-1"></i>Experimenter
                                </button>
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="switchUser('viewer')">
                                    <i class="fas fa-eye me-1"></i>Viewer
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Loading Spinner -->
                <div class="loading text-center py-5" id="loadingSpinner">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Loading data...</p>
                </div>

                <!-- Alert Container -->
                <div id="alertContainer"></div>

                <!-- Dashboard Section -->
                <div id="dashboardSection" class="content-section">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="fas fa-tachometer-alt me-2"></i>Dashboard</h2>
                        <button class="btn btn-primary" onclick="refreshDashboard()">
                            <i class="fas fa-sync-alt me-1"></i>Refresh
                        </button>
                    </div>

                    <!-- Stats Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card">
                                <div class="card-body text-center">
                                    <i class="fas fa-flask fa-2x mb-2"></i>
                                    <h3 class="mb-0" id="totalExperiments">-</h3>
                                    <p class="mb-0">Total Experiments</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card success">
                                <div class="card-body text-center">
                                    <i class="fas fa-play fa-2x mb-2"></i>
                                    <h3 class="mb-0" id="activeExperiments">-</h3>
                                    <p class="mb-0">Active Experiments</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card warning">
                                <div class="card-body text-center">
                                    <i class="fas fa-mouse-pointer fa-2x mb-2"></i>
                                    <h3 class="mb-0" id="totalConversions">-</h3>
                                    <p class="mb-0">Total Conversions</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card danger">
                                <div class="card-body text-center">
                                    <i class="fas fa-percentage fa-2x mb-2"></i>
                                    <h3 class="mb-0" id="conversionRate">-</h3>
                                    <p class="mb-0">Conversion Rate</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Experiments -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Recent Experiments</h5>
                        </div>
                        <div class="card-body">
                            <div id="recentExperiments">
                                <p class="text-muted">Loading recent experiments...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Experiments Section -->
                <div id="experimentsSection" class="content-section" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="fas fa-flask me-2"></i>Experiments</h2>
                        <button class="btn btn-primary" onclick="showCreateExperimentModal()" id="createExperimentBtn">
                            <i class="fas fa-plus me-1"></i>Create Experiment
                        </button>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Type</th>
                                            <th>Status</th>
                                            <th>Created By</th>
                                            <th>Created Date</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="experimentsTableBody">
                                        <tr>
                                            <td colspan="6" class="text-center text-muted">Loading experiments...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Analytics Section -->
                <div id="analyticsSection" class="content-section" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="fas fa-chart-bar me-2"></i>Analytics</h2>
                        <button class="btn btn-outline-primary" onclick="exportAnalytics()" id="exportAnalyticsBtn">
                            <i class="fas fa-download me-1"></i>Export Data
                        </button>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Performance Overview</h5>
                                </div>
                                <div class="card-body">
                                    <div id="analyticsChart">
                                        <p class="text-muted">Analytics chart would be displayed here</p>
                                        <div class="bg-light p-4 rounded">
                                            <i class="fas fa-chart-line fa-3x text-muted d-block text-center mb-3"></i>
                                            <p class="text-center text-muted">Chart visualization coming soon</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-trophy me-2"></i>Top Performers</h5>
                                </div>
                                <div class="card-body" id="topPerformers">
                                    <p class="text-muted">Loading top performing experiments...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Users Section -->
                <div id="usersSection" class="content-section" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="fas fa-users me-2"></i>User Management</h2>
                        <button class="btn btn-primary" onclick="showInviteUserModal()" id="inviteUserBtn">
                            <i class="fas fa-user-plus me-1"></i>Invite User
                        </button>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <div id="usersContent">
                                <p class="text-muted">Loading users...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Settings Section -->
                <div id="settingsSection" class="content-section" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="fas fa-cog me-2"></i>Settings</h2>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <div id="settingsContent">
                                <p class="text-muted">Loading settings...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Experiment Modal -->
    <div class="modal fade" id="createExperimentModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-plus me-2"></i>Create New Experiment</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="createExperimentForm">
                        <div class="mb-3">
                            <label for="experimentName" class="form-label">Experiment Name</label>
                            <input type="text" class="form-control" id="experimentName" required>
                        </div>
                        <div class="mb-3">
                            <label for="experimentDescription" class="form-label">Description</label>
                            <textarea class="form-control" id="experimentDescription" rows="3" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="experimentType" class="form-label">Type</label>
                            <select class="form-control" id="experimentType">
                                <option value="ab_test">A/B Test</option>
                                <option value="multivariate">Multivariate</option>
                                <option value="feature_flag">Feature Flag</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="createExperiment()">
                        <i class="fas fa-plus me-1"></i>Create Experiment
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="app.js"></script>
</body>
</html>

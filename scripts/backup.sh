#!/bin/bash

# A/B Testing Platform - Backup Script
set -e

echo "💾 Creating backup of A/B Testing Platform"
echo "=========================================="

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Create backup directory with timestamp
BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

print_status "Backup directory: $BACKUP_DIR"

# Backup PostgreSQL database
print_status "Backing up PostgreSQL database..."
docker-compose exec -T postgres pg_dump -U ab_testing_user ab_testing_platform > "$BACKUP_DIR/database.sql"
print_success "Database backup completed"

# Backup Redis data
print_status "Backing up Redis data..."
docker-compose exec redis redis-cli -a redis_password --rdb /tmp/dump.rdb
docker-compose exec redis cat /tmp/dump.rdb > "$BACKUP_DIR/redis.rdb"
print_success "Redis backup completed"

# Backup application logs
print_status "Backing up application logs..."
docker-compose logs app > "$BACKUP_DIR/app.log" 2>&1
docker-compose logs postgres > "$BACKUP_DIR/postgres.log" 2>&1
docker-compose logs redis > "$BACKUP_DIR/redis.log" 2>&1
print_success "Logs backup completed"

# Backup configuration files
print_status "Backing up configuration files..."
cp .env "$BACKUP_DIR/env.backup" 2>/dev/null || true
cp docker-compose.yml "$BACKUP_DIR/"
cp docker-compose.override.yml "$BACKUP_DIR/" 2>/dev/null || true
print_success "Configuration backup completed"

# Create backup info file
cat > "$BACKUP_DIR/backup_info.txt" << EOF
A/B Testing Platform Backup
===========================
Created: $(date)
Host: $(hostname)
Docker Compose Version: $(docker-compose version --short)
Services Status:
$(docker-compose ps)

Backup Contents:
- database.sql: PostgreSQL database dump
- redis.rdb: Redis data dump
- app.log: Application logs
- postgres.log: Database logs
- redis.log: Cache logs
- docker-compose.yml: Service configuration
- env.backup: Environment variables (sensitive data may be included)

Restore Instructions:
1. Stop current services: docker-compose down -v
2. Restore database: docker-compose exec -T postgres psql -U ab_testing_user -d ab_testing_platform < database.sql
3. Restore Redis: docker-compose exec -i redis redis-cli -a redis_password --pipe < redis.rdb
4. Start services: docker-compose up -d
EOF

# Create compressed archive
print_status "Creating compressed archive..."
tar -czf "$BACKUP_DIR.tar.gz" -C backups "$(basename "$BACKUP_DIR")"
rm -rf "$BACKUP_DIR"

print_success "Backup completed: $BACKUP_DIR.tar.gz"

# Show backup size
BACKUP_SIZE=$(du -h "$BACKUP_DIR.tar.gz" | cut -f1)
print_status "Backup size: $BACKUP_SIZE"

echo ""
print_success "✅ Backup completed successfully!"
echo "📁 Backup file: $BACKUP_DIR.tar.gz"
echo ""
echo "To restore from this backup:"
echo "1. Extract: tar -xzf $BACKUP_DIR.tar.gz"
echo "2. Follow instructions in backup_info.txt"

#!/bin/bash

# A/B Testing Platform - Docker Stop Script
set -e

echo "🛑 Stopping A/B Testing Platform"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Parse command line arguments
REMOVE_VOLUMES=false
REMOVE_IMAGES=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -v|--volumes)
            REMOVE_VOLUMES=true
            shift
            ;;
        -i|--images)
            REMOVE_IMAGES=true
            shift
            ;;
        -a|--all)
            REMOVE_VOLUMES=true
            REMOVE_IMAGES=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  -v, --volumes    Remove volumes (data will be lost)"
            echo "  -i, --images     Remove built images"
            echo "  -a, --all        Remove volumes and images"
            echo "  -h, --help       Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use -h or --help for usage information"
            exit 1
            ;;
    esac
done

# Stop services
print_status "Stopping Docker Compose services..."
docker-compose down

if [ "$REMOVE_VOLUMES" = true ]; then
    print_warning "Removing volumes (all data will be lost)..."
    docker-compose down -v
    
    # Remove named volumes
    print_status "Removing named volumes..."
    docker volume rm $(docker volume ls -q | grep "$(basename $(pwd))") 2>/dev/null || true
fi

if [ "$REMOVE_IMAGES" = true ]; then
    print_status "Removing built images..."
    
    # Remove application image
    docker rmi $(basename $(pwd))_app 2>/dev/null || true
    
    # Remove dangling images
    docker image prune -f
fi

# Clean up stopped containers
print_status "Cleaning up stopped containers..."
docker container prune -f

# Clean up unused networks
print_status "Cleaning up unused networks..."
docker network prune -f

print_success "A/B Testing Platform stopped successfully!"

if [ "$REMOVE_VOLUMES" = true ]; then
    print_warning "All data has been removed. Next startup will create fresh databases."
fi

echo ""
print_status "To start again: ./scripts/start.sh"

#!/bin/bash

# A/B Testing Platform - Docker Startup Script
set -e

echo "🚀 Starting A/B Testing Platform with Docker Compose"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose > /dev/null 2>&1; then
    print_error "Docker Compose is not installed. Please install Docker Compose and try again."
    exit 1
fi

# Set environment
ENVIRONMENT=${1:-development}
print_status "Environment: $ENVIRONMENT"

# Copy environment file
if [ "$ENVIRONMENT" = "production" ]; then
    if [ ! -f .env.docker ]; then
        print_error ".env.docker file not found. Please create it from .env.docker.example"
        exit 1
    fi
    cp .env.docker .env
    COMPOSE_FILE="docker-compose.yml"
else
    cp .env.docker .env
    COMPOSE_FILE="docker-compose.yml:docker-compose.override.yml"
fi

print_status "Using compose files: $COMPOSE_FILE"

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p logs uploads docker/postgres/data docker/redis/data

# Pull latest images
print_status "Pulling latest Docker images..."
COMPOSE_FILE=$COMPOSE_FILE docker-compose pull

# Build application image
print_status "Building application image..."
COMPOSE_FILE=$COMPOSE_FILE docker-compose build app

# Start services
print_status "Starting services..."
COMPOSE_FILE=$COMPOSE_FILE docker-compose up -d

# Wait for services to be healthy
print_status "Waiting for services to be ready..."

# Wait for PostgreSQL
print_status "Waiting for PostgreSQL..."
timeout=60
while ! COMPOSE_FILE=$COMPOSE_FILE docker-compose exec -T postgres pg_isready -U ab_testing_user -d ab_testing_platform > /dev/null 2>&1; do
    sleep 2
    timeout=$((timeout - 2))
    if [ $timeout -le 0 ]; then
        print_error "PostgreSQL failed to start within 60 seconds"
        exit 1
    fi
done
print_success "PostgreSQL is ready"

# Wait for Redis
print_status "Waiting for Redis..."
timeout=30
while ! COMPOSE_FILE=$COMPOSE_FILE docker-compose exec -T redis redis-cli ping > /dev/null 2>&1; do
    sleep 2
    timeout=$((timeout - 2))
    if [ $timeout -le 0 ]; then
        print_error "Redis failed to start within 30 seconds"
        exit 1
    fi
done
print_success "Redis is ready"

# Wait for Application
print_status "Waiting for Application..."
timeout=60
while ! curl -f http://localhost:3003/health > /dev/null 2>&1; do
    sleep 3
    timeout=$((timeout - 3))
    if [ $timeout -le 0 ]; then
        print_error "Application failed to start within 60 seconds"
        print_status "Checking application logs..."
        COMPOSE_FILE=$COMPOSE_FILE docker-compose logs app
        exit 1
    fi
done
print_success "Application is ready"

# Display service status
echo ""
print_success "🎉 A/B Testing Platform is now running!"
echo ""
echo "📊 Service URLs:"
echo "  • Application:     http://localhost:3003"
echo "  • Health Check:    http://localhost:3003/health"
echo "  • API Docs:        http://localhost:3003/api-docs (if enabled)"
echo "  • Frontend:        http://localhost (via Nginx)"
echo ""
echo "🛠️  Management Tools:"
echo "  • pgAdmin:         http://localhost:5050"
echo "  • Redis Commander: http://localhost:8081"
echo "  • Grafana:         http://localhost:3000"
echo "  • Prometheus:      http://localhost:9090"
echo ""
echo "🔑 Default Credentials:"
echo "  • pgAdmin:         <EMAIL> / admin_password"
echo "  • Redis Commander: admin / admin_password"
echo "  • Grafana:         admin / admin_password"
echo ""
echo "🧪 Test Tokens:"
echo "  • Admin:           admin_token"
echo "  • Experimenter:    experimenter_token"
echo "  • Viewer:          viewer_token"
echo "  • Other Tenant:    other_tenant_token"
echo ""
echo "📝 Example API Calls:"
echo "  curl http://localhost:3003/health"
echo "  curl -H \"Authorization: Bearer admin_token\" http://localhost:3003/api/experiments"
echo "  curl -H \"Authorization: Bearer viewer_token\" http://localhost:3003/api/analytics/dashboard"
echo ""

# Show running containers
print_status "Running containers:"
COMPOSE_FILE=$COMPOSE_FILE docker-compose ps

echo ""
print_status "To view logs: docker-compose logs -f [service_name]"
print_status "To stop: docker-compose down"
print_status "To stop and remove volumes: docker-compose down -v"
echo ""
print_success "Setup complete! 🚀"

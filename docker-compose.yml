version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: ab_testing_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ab_testing_platform
      POSTGRES_USER: ab_testing_user
      POSTGRES_PASSWORD: ab_testing_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init:/docker-entrypoint-initdb.d
      - ./docker/postgres/conf:/etc/postgresql/conf.d
    ports:
      - "5432:5432"
    networks:
      - ab_testing_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ab_testing_user -d ab_testing_platform"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: ab_testing_redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redis_password
    volumes:
      - redis_data:/data
      - ./docker/redis/conf:/usr/local/etc/redis
    ports:
      - "6379:6379"
    networks:
      - ab_testing_network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Node.js Application Server
  app:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        NODE_ENV: production
    container_name: ab_testing_app
    restart: unless-stopped
    environment:
      # Application Configuration
      NODE_ENV: production
      PORT: 3003
      
      # Database Configuration
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ab_testing_platform
      DB_USER: ab_testing_user
      DB_PASSWORD: ab_testing_password
      DB_SSL: false
      DB_POOL_MIN: 2
      DB_POOL_MAX: 20
      
      # Redis Configuration
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: redis_password
      REDIS_DB: 0
      
      # Authentication & Security
      JWT_SECRET: your_super_secure_jwt_secret_key_change_in_production
      JWT_EXPIRES_IN: 24h
      BCRYPT_ROUNDS: 12
      SESSION_SECRET: your_super_secure_session_secret_change_in_production
      
      # API Configuration
      API_RATE_LIMIT_WINDOW: 900000
      API_RATE_LIMIT_MAX: 100
      API_CORS_ORIGINS: http://localhost:3000,http://localhost:3001,http://localhost:8080
      
      # Logging Configuration
      LOG_LEVEL: info
      LOG_FORMAT: json
      LOG_FILE_ENABLED: true
      LOG_FILE_PATH: /app/logs/application.log
      
      # Feature Flags
      ENABLE_AUDIT_LOGGING: true
      ENABLE_METRICS: true
      ENABLE_HEALTH_CHECKS: true
      ENABLE_SWAGGER_DOCS: true
      
      # External Services
      MIXPANEL_TOKEN: your_mixpanel_token_here
      SEGMENT_WRITE_KEY: your_segment_write_key_here
      
      # Email Configuration (for user invitations)
      SMTP_HOST: smtp.gmail.com
      SMTP_PORT: 587
      SMTP_USER: <EMAIL>
      SMTP_PASSWORD: your_app_password
      SMTP_FROM: "A/B Testing Platform <<EMAIL>>"
      
      # Monitoring & Observability
      SENTRY_DSN: your_sentry_dsn_here
      NEW_RELIC_LICENSE_KEY: your_new_relic_key_here
      
    volumes:
      - app_logs:/app/logs
      - ./uploads:/app/uploads
    ports:
      - "3003:3003"
    networks:
      - ab_testing_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3003/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: ab_testing_nginx
    restart: unless-stopped
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
      - ./public:/usr/share/nginx/html
      - nginx_logs:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    networks:
      - ab_testing_network
    depends_on:
      - app
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus (Monitoring)
  prometheus:
    image: prom/prometheus:latest
    container_name: ab_testing_prometheus
    restart: unless-stopped
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - ab_testing_network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  # Grafana (Dashboards)
  grafana:
    image: grafana/grafana:latest
    container_name: ab_testing_grafana
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: admin_password
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/provisioning:/etc/grafana/provisioning
      - ./docker/grafana/dashboards:/var/lib/grafana/dashboards
    ports:
      - "3000:3000"
    networks:
      - ab_testing_network
    depends_on:
      - prometheus

  # pgAdmin (Database Management)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: ab_testing_pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin_password
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    volumes:
      - pgadmin_data:/var/lib/pgadmin
      - ./docker/pgadmin/servers.json:/pgadmin4/servers.json
    ports:
      - "5050:80"
    networks:
      - ab_testing_network
    depends_on:
      - postgres

  # Redis Commander (Redis Management)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: ab_testing_redis_commander
    restart: unless-stopped
    environment:
      REDIS_HOSTS: local:redis:6379:0:redis_password
      HTTP_USER: admin
      HTTP_PASSWORD: admin_password
    ports:
      - "8081:8081"
    networks:
      - ab_testing_network
    depends_on:
      - redis

# Networks
networks:
  ab_testing_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Volumes
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  app_logs:
    driver: local
  nginx_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  pgadmin_data:
    driver: local

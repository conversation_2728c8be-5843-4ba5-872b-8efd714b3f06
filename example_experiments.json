{"examples": [{"name": "Homepage Hero Button Color Test", "description": "Testing different button colors on the homepage hero section to optimize click-through rates and user engagement", "hypothesis": "A red CTA button will increase click-through rates by 15% compared to the current blue button due to higher visual contrast and urgency", "startDate": "2024-01-15T00:00:00Z", "endDate": "2024-02-15T23:59:59Z", "status": "running", "variants": [{"name": "Control (<PERSON> Button)", "description": "Original blue CTA button with current styling", "isControl": true, "allocationPercentage": 50, "configuration": {"buttonColor": "#007bff", "buttonText": "Get Started", "buttonSize": "large"}}, {"name": "<PERSON>", "description": "Red CTA button variant with same text and size", "isControl": false, "allocationPercentage": 50, "configuration": {"buttonColor": "#dc3545", "buttonText": "Get Started", "buttonSize": "large"}}], "targetingRules": [{"name": "US and Canada Only", "attribute": "country", "operator": "in", "value": ["US", "CA"], "isActive": true, "priority": 1}, {"name": "Desktop Users", "attribute": "device_type", "operator": "equals", "value": "desktop", "isActive": true, "priority": 2}], "metrics": {"primary": {"name": "CTA Click Rate", "mixpanelEvent": "<PERSON><PERSON> Clicked", "mixpanelProperties": {"button_location": "hero", "page": "homepage"}, "aggregationType": "unique_count"}, "secondary": [{"name": "Page Views", "mixpanelEvent": "Page Viewed", "mixpanelProperties": {"page": "homepage"}, "aggregationType": "count"}, {"name": "Bounce Rate", "mixpanelEvent": "Session End", "mixpanelProperties": {"pages_viewed": 1}, "aggregationType": "count"}, {"name": "Time on Page", "mixpanelEvent": "Page Exit", "aggregationType": "average", "valueProperty": "time_on_page_seconds"}]}, "trafficAllocation": 1.0, "assignmentMethod": "sticky", "sampleSize": 5000, "confidenceLevel": 0.95, "minimumDetectableEffect": 0.15, "tags": ["homepage", "cta", "ui", "conversion"], "mixpanelConfig": {"projectId": "abc123", "experimentProperty": "homepage_button_variant", "cohortTracking": true, "customProperties": {"experiment_id": "homepage_button_color_2024", "experiment_version": "v1", "team": "growth"}}, "metadata": {"owner": "Growth Team", "stakeholders": ["Product Manager", "UX Designer", "Marketing Lead"], "businessImpact": "Expected 15% increase in homepage conversions could lead to $50K additional monthly revenue", "technicalNotes": "Button color changes implemented via CSS variables, no backend changes required"}}, {"name": "Checkout Flow Simplification", "description": "Testing a simplified 2-step checkout process versus the current 3-step process to reduce cart abandonment", "hypothesis": "Reducing checkout steps from 3 to 2 will increase checkout completion rate by 20% by reducing friction and cognitive load", "startDate": "2024-01-20T00:00:00Z", "endDate": "2024-03-20T23:59:59Z", "status": "running", "variants": [{"name": "Control (3-Step Checkout)", "description": "Current 3-step checkout: Cart → Shipping → Payment", "isControl": true, "allocationPercentage": 50, "configuration": {"steps": 3, "flow": ["cart_review", "shipping_info", "payment_info"], "progressIndicator": true}}, {"name": "Simplified (2-Step Checkout)", "description": "Simplified 2-step checkout: Cart → Shipping & Payment", "isControl": false, "allocationPercentage": 50, "configuration": {"steps": 2, "flow": ["cart_review", "shipping_payment_combined"], "progressIndicator": true}}], "targetingRules": [{"name": "Premium Users Only", "attribute": "subscription_tier", "operator": "equals", "value": "premium", "isActive": true, "priority": 1}, {"name": "Cart Value Above $50", "attribute": "cart_value", "operator": "greater_than", "value": 50, "isActive": true, "priority": 2}], "metrics": {"primary": {"name": "Checkout Completion Rate", "mixpanelEvent": "Purchase Completed", "aggregationType": "unique_count"}, "secondary": [{"name": "Cart Abandonment Rate", "mixpanelEvent": "Checkout Abandoned", "aggregationType": "unique_count"}, {"name": "Average Order Value", "mixpanelEvent": "Purchase Completed", "aggregationType": "average", "valueProperty": "order_value"}, {"name": "Time to Complete Checkout", "mixpanelEvent": "Purchase Completed", "aggregationType": "average", "valueProperty": "checkout_duration_seconds"}]}, "trafficAllocation": 0.5, "assignmentMethod": "sticky", "sampleSize": 2000, "confidenceLevel": 0.95, "minimumDetectableEffect": 0.2, "tags": ["checkout", "conversion", "ux", "premium"], "mixpanelConfig": {"projectId": "abc123", "experimentProperty": "checkout_flow_variant", "cohortTracking": true, "customProperties": {"experiment_id": "checkout_simplification_2024", "experiment_version": "v2"}}, "metadata": {"owner": "E-commerce Team", "stakeholders": ["VP Product", "UX Lead", "Engineering Manager"], "businessImpact": "20% improvement in checkout completion could increase monthly revenue by $200K", "technicalNotes": "Requires frontend changes to combine shipping and payment forms, backend API remains unchanged"}}, {"name": "Email Subject Line Personalization", "description": "Testing personalized email subject lines versus generic ones for weekly newsletter campaigns", "hypothesis": "Personalized subject lines with user's first name will increase email open rates by 25% due to increased relevance and attention", "startDate": "2024-02-01T00:00:00Z", "endDate": "2024-02-29T23:59:59Z", "status": "completed", "variants": [{"name": "Generic Subject Line", "description": "Standard newsletter subject line without personalization", "isControl": true, "allocationPercentage": 50, "configuration": {"subjectTemplate": "Weekly Newsletter - Latest Updates and Insights", "personalization": false}}, {"name": "Personalized Subject Line", "description": "Newsletter subject line with user's first name", "isControl": false, "allocationPercentage": 50, "configuration": {"subjectTemplate": "Hi {{first_name}}, Your Weekly Updates Are Here!", "personalization": true, "fallbackSubject": "Your Weekly Updates Are Here!"}}], "targetingRules": [{"name": "Email Subscribers", "attribute": "email_subscribed", "operator": "equals", "value": true, "isActive": true, "priority": 1}, {"name": "Has First Name", "attribute": "first_name", "operator": "not_equals", "value": "", "isActive": true, "priority": 2}], "metrics": {"primary": {"name": "Email Open Rate", "mixpanelEvent": "Email Opened", "mixpanelProperties": {"email_type": "newsletter"}, "aggregationType": "unique_count"}, "secondary": [{"name": "Click-through Rate", "mixpanelEvent": "Email <PERSON> Clicked", "mixpanelProperties": {"email_type": "newsletter"}, "aggregationType": "unique_count"}, {"name": "Unsubscribe Rate", "mixpanelEvent": "<PERSON><PERSON>subscribed", "aggregationType": "unique_count"}]}, "trafficAllocation": 1.0, "assignmentMethod": "deterministic", "sampleSize": 10000, "confidenceLevel": 0.95, "minimumDetectableEffect": 0.25, "tags": ["email", "personalization", "marketing", "newsletter"], "mixpanelConfig": {"projectId": "def456", "experimentProperty": "email_subject_variant", "cohortTracking": false, "customProperties": {"experiment_id": "email_personalization_2024", "campaign_type": "newsletter"}}, "metadata": {"owner": "Marketing Team", "stakeholders": ["CMO", "Email Marketing Manager", "Data Analyst"], "businessImpact": "25% increase in email opens could improve customer engagement and drive 10% more traffic to website", "technicalNotes": "Email template system updated to support dynamic subject line generation with fallback logic"}}]}
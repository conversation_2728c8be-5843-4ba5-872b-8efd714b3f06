export interface TenantTestData {
  tenantId: string;
  tenantSlug: string;
  name: string;
  users: TestUser[];
  experiments: TestExperiment[];
  events: TestEvent[];
  sensitiveData: SensitiveTestData;
}

export interface TestUser {
  id: string;
  tenantId: string;
  email: string;
  name: string;
  role: string;
  metadata: Record<string, any>;
  createdAt: Date;
}

export interface TestExperiment {
  id: string;
  tenantId: string;
  name: string;
  description: string;
  status: 'active' | 'paused' | 'completed';
  variants: TestVariant[];
  targetingRules: TestTargetingRule[];
  createdAt: Date;
}

export interface TestVariant {
  id: string;
  experimentId: string;
  tenantId: string;
  name: string;
  configuration: Record<string, any>;
  trafficWeight: number;
}

export interface TestTargetingRule {
  id: string;
  experimentId: string;
  tenantId: string;
  attribute: string;
  operator: 'equals' | 'contains' | 'greater_than' | 'less_than';
  value: any;
}

export interface TestEvent {
  id: string;
  tenantId: string;
  userId: string;
  experimentId?: string;
  variantId?: string;
  eventName: string;
  properties: Record<string, any>;
  timestamp: Date;
}

export interface SensitiveTestData {
  tenantId: string;
  apiKeys: string[];
  secrets: string[];
  personalData: PersonalData[];
  financialData: FinancialData[];
}

export interface PersonalData {
  userId: string;
  ssn?: string;
  creditCardNumber?: string;
  phoneNumber: string;
  address: string;
}

export interface FinancialData {
  transactionId: string;
  amount: number;
  currency: string;
  accountNumber: string;
  routingNumber?: string;
}

export interface IsolationTestResult {
  testName: string;
  testType: IsolationTestType;
  passed: boolean;
  duration: number;
  tenantsInvolved: string[];
  violations: IsolationViolation[];
  metrics: TestMetrics;
  timestamp: Date;
}

export enum IsolationTestType {
  DATA_ISOLATION = 'data_isolation',
  API_ISOLATION = 'api_isolation',
  CACHE_ISOLATION = 'cache_isolation',
  SESSION_ISOLATION = 'session_isolation',
  RESOURCE_ISOLATION = 'resource_isolation',
  CONCURRENT_ACCESS = 'concurrent_access',
  LOAD_TEST = 'load_test',
  STRESS_TEST = 'stress_test',
  CHAOS_TEST = 'chaos_test'
}

export interface IsolationViolation {
  type: ViolationType;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  affectedTenants: string[];
  leakedData?: any;
  context: Record<string, any>;
  timestamp: Date;
}

export enum ViolationType {
  DATA_LEAK = 'data_leak',
  UNAUTHORIZED_ACCESS = 'unauthorized_access',
  CROSS_TENANT_QUERY = 'cross_tenant_query',
  CACHE_POLLUTION = 'cache_pollution',
  SESSION_BLEED = 'session_bleed',
  RESOURCE_SHARING = 'resource_sharing',
  PERMISSION_BYPASS = 'permission_bypass'
}

export interface TestMetrics {
  requestsPerSecond: number;
  averageResponseTime: number;
  errorRate: number;
  memoryUsage: number;
  cpuUsage: number;
  databaseConnections: number;
  cacheHitRate: number;
  concurrentUsers: number;
}

export interface LoadTestConfig {
  duration: number; // seconds
  rampUpTime: number; // seconds
  maxConcurrentUsers: number;
  requestsPerSecond: number;
  tenantCount: number;
  dataVolumePerTenant: number;
}

export interface TestScenario {
  name: string;
  description: string;
  type: IsolationTestType;
  config: TestScenarioConfig;
  setup: () => Promise<void>;
  execute: () => Promise<IsolationTestResult>;
  cleanup: () => Promise<void>;
}

export interface TestScenarioConfig {
  tenantCount: number;
  usersPerTenant: number;
  experimentsPerTenant: number;
  eventsPerUser: number;
  concurrency?: number;
  duration?: number;
  loadConfig?: LoadTestConfig;
}

export interface TenantContext {
  tenantId: string;
  userId?: string;
  sessionId?: string;
  permissions: string[];
  headers: Record<string, string>;
}

export interface DatabaseTestConfig {
  connectionString: string;
  maxConnections: number;
  enableRowLevelSecurity: boolean;
  isolationLevel: 'read_uncommitted' | 'read_committed' | 'repeatable_read' | 'serializable';
}

export interface CacheTestConfig {
  redisUrl: string;
  keyPrefix: string;
  ttl: number;
  maxMemory: string;
}

export interface APITestConfig {
  baseUrl: string;
  timeout: number;
  retries: number;
  rateLimits: Record<string, number>;
}

export interface TestEnvironment {
  name: string;
  database: DatabaseTestConfig;
  cache: CacheTestConfig;
  api: APITestConfig;
  monitoring: {
    metricsEndpoint: string;
    logsEndpoint: string;
  };
}

export interface IsolationTestSuite {
  name: string;
  description: string;
  environment: TestEnvironment;
  scenarios: TestScenario[];
  globalSetup?: () => Promise<void>;
  globalTeardown?: () => Promise<void>;
}

export interface TestReport {
  suiteId: string;
  suiteName: string;
  startTime: Date;
  endTime: Date;
  duration: number;
  totalTests: number;
  passedTests: number;
  failedTests: number;
  results: IsolationTestResult[];
  summary: TestSummary;
  recommendations: string[];
}

export interface TestSummary {
  overallStatus: 'passed' | 'failed' | 'warning';
  criticalViolations: number;
  highSeverityViolations: number;
  mediumSeverityViolations: number;
  lowSeverityViolations: number;
  averageResponseTime: number;
  maxConcurrentUsers: number;
  dataLeakageDetected: boolean;
  performanceIssues: string[];
}

export interface MonitoringData {
  timestamp: Date;
  metrics: TestMetrics;
  alerts: Alert[];
  logs: LogEntry[];
}

export interface Alert {
  id: string;
  severity: 'info' | 'warning' | 'error' | 'critical';
  message: string;
  tenantId?: string;
  timestamp: Date;
  resolved: boolean;
}

export interface LogEntry {
  timestamp: Date;
  level: 'debug' | 'info' | 'warn' | 'error';
  message: string;
  tenantId?: string;
  userId?: string;
  context: Record<string, any>;
}

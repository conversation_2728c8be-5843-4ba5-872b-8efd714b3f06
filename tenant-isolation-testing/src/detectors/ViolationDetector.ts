import { Pool } from 'pg';
import { Redis } from 'ioredis';
import {
  IsolationViolation,
  ViolationType,
  TenantContext,
  MonitoringData,
  LogEntry
} from '../types/isolation';
import { Logger } from '../utils/Logger';

export interface ViolationDetectorConfig {
  database: {
    connectionString: string;
    monitorQueries: boolean;
    logSlowQueries: boolean;
    slowQueryThreshold: number;
  };
  cache: {
    redisUrl: string;
    monitorKeyAccess: boolean;
    detectCrossTenantAccess: boolean;
  };
  api: {
    monitorRequests: boolean;
    detectUnauthorizedAccess: boolean;
    logHeaders: boolean;
  };
  realTimeDetection: boolean;
  violationThresholds: {
    maxCrossTenantQueries: number;
    maxUnauthorizedAttempts: number;
    maxCacheViolations: number;
  };
}

export class ViolationDetector {
  private config: ViolationDetectorConfig;
  private logger: Logger;
  private database: Pool;
  private cache: Redis;
  private isDetecting: boolean = false;
  private violations: IsolationViolation[] = [];
  private monitoringData: MonitoringData[] = [];
  private activeContexts: Map<string, TenantContext> = new Map();
  private queryLog: Array<{ query: string; params: any[]; context: TenantContext; timestamp: Date }> = [];
  private cacheAccessLog: Array<{ key: string; operation: string; context: TenantContext; timestamp: Date }> = [];

  constructor(
    config: ViolationDetectorConfig,
    database: Pool,
    cache: Redis,
    logger: Logger
  ) {
    this.config = config;
    this.database = database;
    this.cache = cache;
    this.logger = logger;
  }

  /**
   * Start violation detection
   */
  async startDetection(): Promise<void> {
    if (this.isDetecting) {
      return;
    }

    this.isDetecting = true;
    this.violations = [];
    this.monitoringData = [];
    this.queryLog = [];
    this.cacheAccessLog = [];

    this.logger.info('Starting violation detection');

    // Setup database monitoring
    if (this.config.database.monitorQueries) {
      await this.setupDatabaseMonitoring();
    }

    // Setup cache monitoring
    if (this.config.cache.monitorKeyAccess) {
      await this.setupCacheMonitoring();
    }

    // Start real-time detection if enabled
    if (this.config.realTimeDetection) {
      this.startRealTimeDetection();
    }
  }

  /**
   * Stop violation detection and return collected violations
   */
  async stopDetection(): Promise<IsolationViolation[]> {
    if (!this.isDetecting) {
      return this.violations;
    }

    this.isDetecting = false;
    this.logger.info('Stopping violation detection');

    // Analyze collected data for violations
    await this.analyzeCollectedData();

    // Cleanup monitoring
    await this.cleanupMonitoring();

    this.logger.info('Violation detection stopped', {
      violationsFound: this.violations.length
    });

    return [...this.violations];
  }

  /**
   * Register tenant context for current operation
   */
  registerTenantContext(sessionId: string, context: TenantContext): void {
    this.activeContexts.set(sessionId, context);
  }

  /**
   * Unregister tenant context
   */
  unregisterTenantContext(sessionId: string): void {
    this.activeContexts.delete(sessionId);
  }

  /**
   * Log database query for analysis
   */
  logDatabaseQuery(query: string, params: any[], sessionId: string): void {
    if (!this.isDetecting) return;

    const context = this.activeContexts.get(sessionId);
    if (!context) {
      this.addViolation({
        type: ViolationType.UNAUTHORIZED_ACCESS,
        severity: 'high',
        description: 'Database query executed without tenant context',
        affectedTenants: [],
        context: { query, params, sessionId },
        timestamp: new Date()
      });
      return;
    }

    this.queryLog.push({
      query,
      params,
      context,
      timestamp: new Date()
    });

    // Real-time analysis
    if (this.config.realTimeDetection) {
      this.analyzeQuery(query, params, context);
    }
  }

  /**
   * Log cache access for analysis
   */
  logCacheAccess(key: string, operation: string, sessionId: string): void {
    if (!this.isDetecting) return;

    const context = this.activeContexts.get(sessionId);
    if (!context) {
      this.addViolation({
        type: ViolationType.CACHE_POLLUTION,
        severity: 'medium',
        description: 'Cache access without tenant context',
        affectedTenants: [],
        context: { key, operation, sessionId },
        timestamp: new Date()
      });
      return;
    }

    this.cacheAccessLog.push({
      key,
      operation,
      context,
      timestamp: new Date()
    });

    // Real-time analysis
    if (this.config.realTimeDetection) {
      this.analyzeCacheAccess(key, operation, context);
    }
  }

  /**
   * Setup database monitoring
   */
  private async setupDatabaseMonitoring(): Promise<void> {
    // Enable query logging in PostgreSQL
    try {
      await this.database.query('SET log_statement = "all"');
      await this.database.query('SET log_min_duration_statement = 0');
      this.logger.debug('Database monitoring enabled');
    } catch (error) {
      this.logger.warn('Failed to enable database monitoring', { error });
    }
  }

  /**
   * Setup cache monitoring
   */
  private async setupCacheMonitoring(): Promise<void> {
    // Monitor Redis commands if possible
    try {
      // Note: In production, you might use Redis MONITOR command or modules
      this.logger.debug('Cache monitoring enabled');
    } catch (error) {
      this.logger.warn('Failed to enable cache monitoring', { error });
    }
  }

  /**
   * Start real-time detection
   */
  private startRealTimeDetection(): void {
    // This would run periodically to check for violations
    const interval = setInterval(() => {
      if (!this.isDetecting) {
        clearInterval(interval);
        return;
      }
      this.performRealTimeChecks();
    }, 5000); // Check every 5 seconds
  }

  /**
   * Perform real-time violation checks
   */
  private performRealTimeChecks(): void {
    // Check for suspicious patterns in recent activity
    const recentQueries = this.queryLog.filter(
      log => Date.now() - log.timestamp.getTime() < 30000 // Last 30 seconds
    );

    const recentCacheAccess = this.cacheAccessLog.filter(
      log => Date.now() - log.timestamp.getTime() < 30000
    );

    // Detect cross-tenant queries
    this.detectCrossTenantQueries(recentQueries);

    // Detect cache violations
    this.detectCacheViolations(recentCacheAccess);

    // Check violation thresholds
    this.checkViolationThresholds();
  }

  /**
   * Analyze collected data for violations
   */
  private async analyzeCollectedData(): Promise<void> {
    this.logger.info('Analyzing collected data for violations', {
      queries: this.queryLog.length,
      cacheAccesses: this.cacheAccessLog.length
    });

    // Analyze all database queries
    this.detectCrossTenantQueries(this.queryLog);

    // Analyze cache access patterns
    this.detectCacheViolations(this.cacheAccessLog);

    // Detect data leakage patterns
    await this.detectDataLeakage();

    // Analyze session isolation
    this.analyzeSessionIsolation();

    // Check for permission bypasses
    this.detectPermissionBypasses();
  }

  /**
   * Detect cross-tenant database queries
   */
  private detectCrossTenantQueries(queries: typeof this.queryLog): void {
    for (const queryLog of queries) {
      this.analyzeQuery(queryLog.query, queryLog.params, queryLog.context);
    }
  }

  /**
   * Analyze individual query for violations
   */
  private analyzeQuery(query: string, params: any[], context: TenantContext): void {
    const normalizedQuery = query.toLowerCase().trim();

    // Check if query includes tenant_id filter
    if (this.isDataQuery(normalizedQuery) && !this.hasTenantFilter(normalizedQuery, params, context)) {
      this.addViolation({
        type: ViolationType.CROSS_TENANT_QUERY,
        severity: 'critical',
        description: 'Database query missing tenant isolation filter',
        affectedTenants: [context.tenantId],
        context: { query, params, tenantId: context.tenantId },
        timestamp: new Date()
      });
    }

    // Check for suspicious WHERE clauses
    if (this.hasSuspiciousWhereClause(normalizedQuery, context)) {
      this.addViolation({
        type: ViolationType.DATA_LEAK,
        severity: 'high',
        description: 'Query with potentially unsafe WHERE clause',
        affectedTenants: [context.tenantId],
        context: { query, params, tenantId: context.tenantId },
        timestamp: new Date()
      });
    }

    // Check for admin-only operations
    if (this.isAdminOperation(normalizedQuery) && !this.hasAdminPermissions(context)) {
      this.addViolation({
        type: ViolationType.PERMISSION_BYPASS,
        severity: 'critical',
        description: 'Unauthorized admin operation attempted',
        affectedTenants: [context.tenantId],
        context: { query, params, tenantId: context.tenantId, permissions: context.permissions },
        timestamp: new Date()
      });
    }
  }

  /**
   * Detect cache violations
   */
  private detectCacheViolations(cacheAccesses: typeof this.cacheAccessLog): void {
    for (const access of cacheAccesses) {
      this.analyzeCacheAccess(access.key, access.operation, access.context);
    }
  }

  /**
   * Analyze individual cache access
   */
  private analyzeCacheAccess(key: string, operation: string, context: TenantContext): void {
    // Check if cache key includes tenant isolation
    if (!this.isTenantIsolatedCacheKey(key, context.tenantId)) {
      this.addViolation({
        type: ViolationType.CACHE_POLLUTION,
        severity: 'medium',
        description: 'Cache key accessed without tenant isolation',
        affectedTenants: [context.tenantId],
        context: { key, operation, tenantId: context.tenantId },
        timestamp: new Date()
      });
    }

    // Check for cross-tenant cache access
    const extractedTenantId = this.extractTenantIdFromCacheKey(key);
    if (extractedTenantId && extractedTenantId !== context.tenantId) {
      this.addViolation({
        type: ViolationType.DATA_LEAK,
        severity: 'critical',
        description: 'Cross-tenant cache access detected',
        affectedTenants: [context.tenantId, extractedTenantId],
        context: { key, operation, accessingTenant: context.tenantId, keyTenant: extractedTenantId },
        timestamp: new Date()
      });
    }
  }

  /**
   * Detect data leakage patterns
   */
  private async detectDataLeakage(): Promise<void> {
    // Group queries by tenant to detect cross-tenant data access
    const queriesByTenant = new Map<string, typeof this.queryLog>();
    
    for (const queryLog of this.queryLog) {
      const tenantId = queryLog.context.tenantId;
      if (!queriesByTenant.has(tenantId)) {
        queriesByTenant.set(tenantId, []);
      }
      queriesByTenant.get(tenantId)!.push(queryLog);
    }

    // Check for data overlap between tenants
    for (const [tenantId, queries] of queriesByTenant) {
      await this.checkForDataOverlap(tenantId, queries);
    }
  }

  /**
   * Check for data overlap between tenants
   */
  private async checkForDataOverlap(tenantId: string, queries: typeof this.queryLog): Promise<void> {
    // This is a simplified check - in practice, you'd analyze actual data returned
    for (const queryLog of queries) {
      if (this.isSelectQuery(queryLog.query)) {
        // Check if query could potentially return data from other tenants
        if (!this.hasTenantFilter(queryLog.query, queryLog.params, queryLog.context)) {
          this.addViolation({
            type: ViolationType.DATA_LEAK,
            severity: 'critical',
            description: 'SELECT query without tenant filter could leak data',
            affectedTenants: [tenantId],
            context: { query: queryLog.query, params: queryLog.params },
            timestamp: new Date()
          });
        }
      }
    }
  }

  /**
   * Analyze session isolation
   */
  private analyzeSessionIsolation(): void {
    // Check for session context bleeding between tenants
    const sessionsByTenant = new Map<string, Set<string>>();
    
    for (const [sessionId, context] of this.activeContexts) {
      if (!sessionsByTenant.has(context.tenantId)) {
        sessionsByTenant.set(context.tenantId, new Set());
      }
      sessionsByTenant.get(context.tenantId)!.add(sessionId);
    }

    // Check for suspicious session patterns
    for (const [tenantId, sessions] of sessionsByTenant) {
      if (sessions.size > 100) { // Threshold for suspicious activity
        this.addViolation({
          type: ViolationType.SESSION_BLEED,
          severity: 'medium',
          description: 'Unusually high number of sessions for tenant',
          affectedTenants: [tenantId],
          context: { tenantId, sessionCount: sessions.size },
          timestamp: new Date()
        });
      }
    }
  }

  /**
   * Detect permission bypasses
   */
  private detectPermissionBypasses(): void {
    for (const queryLog of this.queryLog) {
      if (this.isPrivilegedOperation(queryLog.query) && 
          !this.hasRequiredPermissions(queryLog.context, this.getRequiredPermissions(queryLog.query))) {
        this.addViolation({
          type: ViolationType.PERMISSION_BYPASS,
          severity: 'high',
          description: 'Operation attempted without required permissions',
          affectedTenants: [queryLog.context.tenantId],
          context: { 
            query: queryLog.query, 
            requiredPermissions: this.getRequiredPermissions(queryLog.query),
            userPermissions: queryLog.context.permissions
          },
          timestamp: new Date()
        });
      }
    }
  }

  /**
   * Helper methods for query analysis
   */
  private isDataQuery(query: string): boolean {
    return query.includes('select') || query.includes('update') || 
           query.includes('delete') || query.includes('insert');
  }

  private isSelectQuery(query: string): boolean {
    return query.toLowerCase().trim().startsWith('select');
  }

  private hasTenantFilter(query: string, params: any[], context: TenantContext): boolean {
    const normalizedQuery = query.toLowerCase();
    return normalizedQuery.includes('tenant_id') || 
           normalizedQuery.includes('where') && params.includes(context.tenantId);
  }

  private hasSuspiciousWhereClause(query: string, context: TenantContext): boolean {
    const normalizedQuery = query.toLowerCase();
    // Check for queries that might bypass tenant isolation
    return normalizedQuery.includes('where 1=1') || 
           normalizedQuery.includes('or 1=1') ||
           normalizedQuery.includes('union select');
  }

  private isAdminOperation(query: string): boolean {
    const normalizedQuery = query.toLowerCase();
    return normalizedQuery.includes('drop ') || 
           normalizedQuery.includes('alter ') ||
           normalizedQuery.includes('create ') ||
           normalizedQuery.includes('grant ') ||
           normalizedQuery.includes('revoke ');
  }

  private isPrivilegedOperation(query: string): boolean {
    const normalizedQuery = query.toLowerCase();
    return normalizedQuery.includes('delete') || 
           normalizedQuery.includes('update') ||
           this.isAdminOperation(query);
  }

  private hasAdminPermissions(context: TenantContext): boolean {
    return context.permissions.includes('admin') || 
           context.permissions.includes('super_admin');
  }

  private hasRequiredPermissions(context: TenantContext, required: string[]): boolean {
    return required.every(permission => context.permissions.includes(permission));
  }

  private getRequiredPermissions(query: string): string[] {
    const normalizedQuery = query.toLowerCase();
    if (normalizedQuery.includes('delete')) return ['delete'];
    if (normalizedQuery.includes('update')) return ['write'];
    if (this.isAdminOperation(query)) return ['admin'];
    return ['read'];
  }

  private isTenantIsolatedCacheKey(key: string, tenantId: string): boolean {
    return key.includes(tenantId) || key.startsWith(`tenant:${tenantId}:`);
  }

  private extractTenantIdFromCacheKey(key: string): string | null {
    const match = key.match(/tenant:([^:]+):/);
    return match ? match[1] : null;
  }

  private checkViolationThresholds(): void {
    const recentViolations = this.violations.filter(
      v => Date.now() - v.timestamp.getTime() < 60000 // Last minute
    );

    const crossTenantQueries = recentViolations.filter(v => v.type === ViolationType.CROSS_TENANT_QUERY);
    if (crossTenantQueries.length > this.config.violationThresholds.maxCrossTenantQueries) {
      this.addViolation({
        type: ViolationType.DATA_LEAK,
        severity: 'critical',
        description: 'Excessive cross-tenant queries detected',
        affectedTenants: [...new Set(crossTenantQueries.flatMap(v => v.affectedTenants))],
        context: { count: crossTenantQueries.length, threshold: this.config.violationThresholds.maxCrossTenantQueries },
        timestamp: new Date()
      });
    }
  }

  private addViolation(violation: IsolationViolation): void {
    this.violations.push(violation);
    this.logger.warn('Isolation violation detected', {
      type: violation.type,
      severity: violation.severity,
      description: violation.description,
      affectedTenants: violation.affectedTenants
    });
  }

  private async cleanupMonitoring(): Promise<void> {
    // Cleanup any monitoring resources
    this.activeContexts.clear();
  }
}

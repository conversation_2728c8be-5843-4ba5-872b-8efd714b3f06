import { EventEmitter } from 'events';
import {
  IsolationTestSuite,
  TestScenario,
  IsolationTestResult,
  TestReport,
  TestSummary,
  IsolationViolation,
  ViolationType,
  MonitoringData,
  TestMetrics
} from '../types/isolation';
import { TestDataGenerator } from '../utils/TestDataGenerator';
import { ViolationDetector } from '../detectors/ViolationDetector';
import { MetricsCollector } from '../monitoring/MetricsCollector';
import { Logger } from '../utils/Logger';

export class IsolationTestRunner extends EventEmitter {
  private testSuite: IsolationTestSuite;
  private dataGenerator: TestDataGenerator;
  private violationDetector: ViolationDetector;
  private metricsCollector: MetricsCollector;
  private logger: Logger;
  private isRunning: boolean = false;
  private currentTest?: TestScenario;
  private results: IsolationTestResult[] = [];

  constructor(
    testSuite: IsolationTestSuite,
    dataGenerator: TestDataGenerator,
    violationDetector: ViolationDetector,
    metricsCollector: MetricsCollector,
    logger: Logger
  ) {
    super();
    this.testSuite = testSuite;
    this.dataGenerator = dataGenerator;
    this.violationDetector = violationDetector;
    this.metricsCollector = metricsCollector;
    this.logger = logger;
  }

  /**
   * Run the complete test suite
   */
  async runSuite(): Promise<TestReport> {
    if (this.isRunning) {
      throw new Error('Test suite is already running');
    }

    this.isRunning = true;
    const startTime = new Date();
    this.results = [];

    this.logger.info('Starting isolation test suite', {
      suiteName: this.testSuite.name,
      scenarioCount: this.testSuite.scenarios.length
    });

    this.emit('suiteStarted', {
      suiteName: this.testSuite.name,
      startTime: startTime
    });

    try {
      // Global setup
      if (this.testSuite.globalSetup) {
        this.logger.info('Running global setup');
        await this.testSuite.globalSetup();
      }

      // Start monitoring
      await this.metricsCollector.startMonitoring();

      // Run each test scenario
      for (const scenario of this.testSuite.scenarios) {
        if (!this.isRunning) {
          this.logger.warn('Test suite execution interrupted');
          break;
        }

        const result = await this.runScenario(scenario);
        this.results.push(result);

        this.emit('scenarioCompleted', result);

        // Brief pause between scenarios
        await this.sleep(1000);
      }

      // Stop monitoring
      await this.metricsCollector.stopMonitoring();

      // Global teardown
      if (this.testSuite.globalTeardown) {
        this.logger.info('Running global teardown');
        await this.testSuite.globalTeardown();
      }

      const endTime = new Date();
      const report = this.generateReport(startTime, endTime);

      this.emit('suiteCompleted', report);
      this.logger.info('Test suite completed', {
        duration: report.duration,
        totalTests: report.totalTests,
        passedTests: report.passedTests,
        failedTests: report.failedTests
      });

      return report;
    } catch (error) {
      this.logger.error('Test suite failed', { error });
      this.emit('suiteError', error);
      throw error;
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Run a single test scenario
   */
  async runScenario(scenario: TestScenario): Promise<IsolationTestResult> {
    this.currentTest = scenario;
    const startTime = Date.now();

    this.logger.info('Starting test scenario', {
      scenarioName: scenario.name,
      type: scenario.type
    });

    this.emit('scenarioStarted', {
      scenarioName: scenario.name,
      type: scenario.type
    });

    try {
      // Setup phase
      this.logger.debug('Setting up test scenario', { scenarioName: scenario.name });
      await scenario.setup();

      // Start violation detection
      this.violationDetector.startDetection();

      // Execute test
      this.logger.debug('Executing test scenario', { scenarioName: scenario.name });
      const result = await scenario.execute();

      // Stop violation detection and collect violations
      const violations = await this.violationDetector.stopDetection();

      // Collect metrics
      const metrics = await this.metricsCollector.getCurrentMetrics();

      // Update result with collected data
      result.violations = violations;
      result.metrics = metrics;
      result.duration = Date.now() - startTime;
      result.passed = violations.length === 0 && result.passed;

      this.logger.info('Test scenario completed', {
        scenarioName: scenario.name,
        passed: result.passed,
        violations: violations.length,
        duration: result.duration
      });

      return result;
    } catch (error) {
      this.logger.error('Test scenario failed', {
        scenarioName: scenario.name,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      return {
        testName: scenario.name,
        testType: scenario.type,
        passed: false,
        duration: Date.now() - startTime,
        tenantsInvolved: [],
        violations: [{
          type: ViolationType.UNAUTHORIZED_ACCESS,
          severity: 'critical',
          description: `Test execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
          affectedTenants: [],
          context: { error: error },
          timestamp: new Date()
        }],
        metrics: await this.metricsCollector.getCurrentMetrics(),
        timestamp: new Date()
      };
    } finally {
      // Cleanup phase
      try {
        this.logger.debug('Cleaning up test scenario', { scenarioName: scenario.name });
        await scenario.cleanup();
      } catch (cleanupError) {
        this.logger.error('Cleanup failed', {
          scenarioName: scenario.name,
          error: cleanupError
        });
      }
      this.currentTest = undefined;
    }
  }

  /**
   * Stop the test suite execution
   */
  async stopSuite(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    this.logger.info('Stopping test suite execution');
    this.isRunning = false;

    // Stop monitoring
    await this.metricsCollector.stopMonitoring();

    // Stop violation detection
    await this.violationDetector.stopDetection();

    this.emit('suiteStopped');
  }

  /**
   * Get current test status
   */
  getStatus(): {
    isRunning: boolean;
    currentTest?: string;
    completedTests: number;
    totalTests: number;
    results: IsolationTestResult[];
  } {
    return {
      isRunning: this.isRunning,
      currentTest: this.currentTest?.name,
      completedTests: this.results.length,
      totalTests: this.testSuite.scenarios.length,
      results: [...this.results]
    };
  }

  /**
   * Generate comprehensive test report
   */
  private generateReport(startTime: Date, endTime: Date): TestReport {
    const duration = endTime.getTime() - startTime.getTime();
    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;

    // Collect all violations
    const allViolations = this.results.flatMap(r => r.violations);
    
    const summary: TestSummary = {
      overallStatus: failedTests > 0 ? 'failed' : 'passed',
      criticalViolations: allViolations.filter(v => v.severity === 'critical').length,
      highSeverityViolations: allViolations.filter(v => v.severity === 'high').length,
      mediumSeverityViolations: allViolations.filter(v => v.severity === 'medium').length,
      lowSeverityViolations: allViolations.filter(v => v.severity === 'low').length,
      averageResponseTime: this.calculateAverageResponseTime(),
      maxConcurrentUsers: this.getMaxConcurrentUsers(),
      dataLeakageDetected: allViolations.some(v => v.type === ViolationType.DATA_LEAK),
      performanceIssues: this.identifyPerformanceIssues()
    };

    // Adjust overall status based on violations
    if (summary.criticalViolations > 0) {
      summary.overallStatus = 'failed';
    } else if (summary.highSeverityViolations > 0) {
      summary.overallStatus = 'warning';
    }

    const recommendations = this.generateRecommendations(summary, allViolations);

    return {
      suiteId: this.generateReportId(),
      suiteName: this.testSuite.name,
      startTime: startTime,
      endTime: endTime,
      duration: duration,
      totalTests: totalTests,
      passedTests: passedTests,
      failedTests: failedTests,
      results: this.results,
      summary: summary,
      recommendations: recommendations
    };
  }

  /**
   * Calculate average response time across all tests
   */
  private calculateAverageResponseTime(): number {
    if (this.results.length === 0) return 0;
    
    const totalResponseTime = this.results.reduce((sum, result) => {
      return sum + (result.metrics.averageResponseTime || 0);
    }, 0);
    
    return totalResponseTime / this.results.length;
  }

  /**
   * Get maximum concurrent users across all tests
   */
  private getMaxConcurrentUsers(): number {
    return Math.max(...this.results.map(r => r.metrics.concurrentUsers || 0), 0);
  }

  /**
   * Identify performance issues from test results
   */
  private identifyPerformanceIssues(): string[] {
    const issues: string[] = [];
    
    const avgResponseTime = this.calculateAverageResponseTime();
    if (avgResponseTime > 1000) {
      issues.push(`High average response time: ${avgResponseTime.toFixed(2)}ms`);
    }

    const highErrorRateTests = this.results.filter(r => (r.metrics.errorRate || 0) > 0.05);
    if (highErrorRateTests.length > 0) {
      issues.push(`${highErrorRateTests.length} tests with error rate > 5%`);
    }

    const highMemoryUsage = this.results.filter(r => (r.metrics.memoryUsage || 0) > 0.8);
    if (highMemoryUsage.length > 0) {
      issues.push(`${highMemoryUsage.length} tests with memory usage > 80%`);
    }

    const highCpuUsage = this.results.filter(r => (r.metrics.cpuUsage || 0) > 0.8);
    if (highCpuUsage.length > 0) {
      issues.push(`${highCpuUsage.length} tests with CPU usage > 80%`);
    }

    return issues;
  }

  /**
   * Generate recommendations based on test results
   */
  private generateRecommendations(summary: TestSummary, violations: IsolationViolation[]): string[] {
    const recommendations: string[] = [];

    if (summary.criticalViolations > 0) {
      recommendations.push('CRITICAL: Address all critical violations immediately before production deployment');
    }

    if (summary.dataLeakageDetected) {
      recommendations.push('Data leakage detected - review tenant isolation mechanisms');
    }

    const dataLeaks = violations.filter(v => v.type === ViolationType.DATA_LEAK);
    if (dataLeaks.length > 0) {
      recommendations.push('Implement stronger data isolation controls');
      recommendations.push('Review database row-level security policies');
    }

    const cacheViolations = violations.filter(v => v.type === ViolationType.CACHE_POLLUTION);
    if (cacheViolations.length > 0) {
      recommendations.push('Implement tenant-specific cache namespacing');
    }

    const sessionViolations = violations.filter(v => v.type === ViolationType.SESSION_BLEED);
    if (sessionViolations.length > 0) {
      recommendations.push('Review session management and tenant context handling');
    }

    if (summary.averageResponseTime > 1000) {
      recommendations.push('Optimize application performance - response times are high');
    }

    if (summary.performanceIssues.length > 0) {
      recommendations.push('Address performance issues identified in the test results');
    }

    if (violations.some(v => v.type === ViolationType.CROSS_TENANT_QUERY)) {
      recommendations.push('Review database queries to ensure proper tenant filtering');
    }

    return recommendations;
  }

  /**
   * Generate unique report ID
   */
  private generateReportId(): string {
    return `report_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Sleep utility
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

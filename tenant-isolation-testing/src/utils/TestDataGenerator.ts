import {
  TenantTestData,
  TestUser,
  TestExperiment,
  TestEvent,
  TestVariant,
  TestTargetingRule
} from '../types/isolation';

export class TestDataGenerator {
  private generatedTenants: TenantTestData[] = [];
  private userCounter = 0;
  private experimentCounter = 0;
  private eventCounter = 0;

  /**
   * Generate test tenants with specified configuration
   */
  async generateTenants(count: number): Promise<TenantTestData[]> {
    this.generatedTenants = [];

    for (let i = 0; i < count; i++) {
      const tenant = this.generateTenant(i);
      this.generatedTenants.push(tenant);
    }

    return this.generatedTenants;
  }

  /**
   * Generate a single tenant with test data
   */
  private generateTenant(index: number): TenantTestData {
    const tenantId = `tenant_${index + 1}_${Date.now()}`;
    const tenantSlug = `test-tenant-${index + 1}`;

    const tenant: TenantTestData = {
      tenantId: tenantId,
      tenantSlug: tenantSlug,
      name: `Test Tenant ${index + 1}`,
      users: [],
      experiments: [],
      events: [],
      sensitiveData: {
        tenantId: tenantId,
        apiKeys: [],
        secrets: [],
        personalData: [],
        financialData: []
      }
    };

    return tenant;
  }

  /**
   * Create comprehensive test data for a tenant
   */
  async createTenantData(tenant: TenantTestData): Promise<void> {
    // Generate users
    const userCount = Math.floor(Math.random() * 50) + 10; // 10-60 users
    for (let i = 0; i < userCount; i++) {
      const user = this.generateUser(tenant.tenantId);
      tenant.users.push(user);
    }

    // Generate experiments
    const experimentCount = Math.floor(Math.random() * 10) + 5; // 5-15 experiments
    for (let i = 0; i < experimentCount; i++) {
      const experiment = this.generateExperiment(tenant.tenantId);
      tenant.experiments.push(experiment);
    }

    // Generate events
    const eventCount = Math.floor(Math.random() * 1000) + 500; // 500-1500 events
    for (let i = 0; i < eventCount; i++) {
      const userId = tenant.users[Math.floor(Math.random() * tenant.users.length)].id;
      const event = this.generateEvent(tenant.tenantId, userId);
      
      // Sometimes associate with experiments
      if (Math.random() < 0.3 && tenant.experiments.length > 0) {
        const experiment = tenant.experiments[Math.floor(Math.random() * tenant.experiments.length)];
        event.experimentId = experiment.id;
        
        if (experiment.variants.length > 0) {
          event.variantId = experiment.variants[Math.floor(Math.random() * experiment.variants.length)].id;
        }
      }
      
      tenant.events.push(event);
    }

    // Generate sensitive data
    this.generateSensitiveData(tenant);
  }

  /**
   * Generate a test user
   */
  generateUser(tenantId: string): TestUser {
    this.userCounter++;
    
    const firstNames = ['John', 'Jane', 'Bob', 'Alice', 'Charlie', 'Diana', 'Eve', 'Frank'];
    const lastNames = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis'];
    const roles = ['admin', 'user', 'viewer', 'editor', 'manager'];
    
    const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
    const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
    
    return {
      id: `user_${this.userCounter}_${Date.now()}`,
      tenantId: tenantId,
      email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}@${tenantId}.test.com`,
      name: `${firstName} ${lastName}`,
      role: roles[Math.floor(Math.random() * roles.length)],
      metadata: {
        department: ['Engineering', 'Marketing', 'Sales', 'Support'][Math.floor(Math.random() * 4)],
        joinDate: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000),
        preferences: {
          theme: ['light', 'dark'][Math.floor(Math.random() * 2)],
          notifications: Math.random() > 0.5
        }
      },
      createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)
    };
  }

  /**
   * Generate a test experiment
   */
  generateExperiment(tenantId: string): TestExperiment {
    this.experimentCounter++;
    
    const experimentTypes = ['A/B Test', 'Multivariate Test', 'Feature Flag', 'Rollout'];
    const statuses = ['active', 'paused', 'completed'] as const;
    
    const experiment: TestExperiment = {
      id: `exp_${this.experimentCounter}_${Date.now()}`,
      tenantId: tenantId,
      name: `Test Experiment ${this.experimentCounter}`,
      description: `This is a test experiment for ${experimentTypes[Math.floor(Math.random() * experimentTypes.length)]}`,
      status: statuses[Math.floor(Math.random() * statuses.length)],
      variants: [],
      targetingRules: [],
      createdAt: new Date(Date.now() - Math.random() * 60 * 24 * 60 * 60 * 1000)
    };

    // Generate variants
    const variantCount = Math.floor(Math.random() * 4) + 2; // 2-5 variants
    for (let i = 0; i < variantCount; i++) {
      const variant = this.generateVariant(experiment.id, tenantId, i);
      experiment.variants.push(variant);
    }

    // Generate targeting rules
    const ruleCount = Math.floor(Math.random() * 3) + 1; // 1-3 rules
    for (let i = 0; i < ruleCount; i++) {
      const rule = this.generateTargetingRule(experiment.id, tenantId);
      experiment.targetingRules.push(rule);
    }

    return experiment;
  }

  /**
   * Generate a test variant
   */
  private generateVariant(experimentId: string, tenantId: string, index: number): TestVariant {
    const variantNames = ['Control', 'Variant A', 'Variant B', 'Variant C', 'Variant D'];
    
    return {
      id: `var_${experimentId}_${index}_${Date.now()}`,
      experimentId: experimentId,
      tenantId: tenantId,
      name: variantNames[index] || `Variant ${index}`,
      configuration: {
        buttonColor: ['blue', 'green', 'red', 'orange'][Math.floor(Math.random() * 4)],
        buttonText: ['Click Me', 'Get Started', 'Learn More', 'Sign Up'][Math.floor(Math.random() * 4)],
        showFeature: Math.random() > 0.5,
        discount: Math.floor(Math.random() * 50)
      },
      trafficWeight: Math.floor(Math.random() * 100) / variantNames.length
    };
  }

  /**
   * Generate a test targeting rule
   */
  private generateTargetingRule(experimentId: string, tenantId: string): TestTargetingRule {
    const attributes = ['country', 'age', 'plan', 'device', 'browser'];
    const operators = ['equals', 'contains', 'greater_than', 'less_than'] as const;
    const values = {
      country: ['US', 'CA', 'UK', 'DE', 'FR'],
      age: [18, 25, 35, 45, 65],
      plan: ['free', 'pro', 'enterprise'],
      device: ['mobile', 'desktop', 'tablet'],
      browser: ['chrome', 'firefox', 'safari', 'edge']
    };

    const attribute = attributes[Math.floor(Math.random() * attributes.length)];
    const operator = operators[Math.floor(Math.random() * operators.length)];
    const valueOptions = values[attribute as keyof typeof values];
    const value = valueOptions[Math.floor(Math.random() * valueOptions.length)];

    return {
      id: `rule_${experimentId}_${Date.now()}_${Math.random()}`,
      experimentId: experimentId,
      tenantId: tenantId,
      attribute: attribute,
      operator: operator,
      value: value
    };
  }

  /**
   * Generate a test event
   */
  generateEvent(tenantId: string, userId: string): TestEvent {
    this.eventCounter++;
    
    const eventNames = [
      'page_view', 'button_click', 'form_submit', 'purchase', 'signup',
      'login', 'logout', 'experiment_view', 'conversion', 'error'
    ];
    
    const eventName = eventNames[Math.floor(Math.random() * eventNames.length)];
    
    return {
      id: `event_${this.eventCounter}_${Date.now()}`,
      tenantId: tenantId,
      userId: userId,
      eventName: eventName,
      properties: this.generateEventProperties(eventName),
      timestamp: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000) // Last 7 days
    };
  }

  /**
   * Generate event properties based on event type
   */
  private generateEventProperties(eventName: string): Record<string, any> {
    const baseProperties = {
      sessionId: `session_${Date.now()}_${Math.random()}`,
      userAgent: 'Mozilla/5.0 (Test Browser)',
      ip: `192.168.1.${Math.floor(Math.random() * 255)}`,
      timestamp: Date.now()
    };

    switch (eventName) {
      case 'page_view':
        return {
          ...baseProperties,
          page: ['/home', '/about', '/pricing', '/contact'][Math.floor(Math.random() * 4)],
          referrer: ['google.com', 'facebook.com', 'direct', 'twitter.com'][Math.floor(Math.random() * 4)],
          loadTime: Math.floor(Math.random() * 3000) + 500
        };
      
      case 'button_click':
        return {
          ...baseProperties,
          buttonId: `btn_${Math.floor(Math.random() * 10)}`,
          buttonText: ['Click Me', 'Get Started', 'Learn More'][Math.floor(Math.random() * 3)],
          position: { x: Math.floor(Math.random() * 1920), y: Math.floor(Math.random() * 1080) }
        };
      
      case 'purchase':
        return {
          ...baseProperties,
          amount: Math.floor(Math.random() * 1000) + 10,
          currency: 'USD',
          productId: `prod_${Math.floor(Math.random() * 100)}`,
          quantity: Math.floor(Math.random() * 5) + 1
        };
      
      case 'signup':
        return {
          ...baseProperties,
          plan: ['free', 'pro', 'enterprise'][Math.floor(Math.random() * 3)],
          source: ['organic', 'paid', 'referral'][Math.floor(Math.random() * 3)]
        };
      
      default:
        return baseProperties;
    }
  }

  /**
   * Generate sensitive test data
   */
  private generateSensitiveData(tenant: TenantTestData): void {
    // Generate API keys
    for (let i = 0; i < 3; i++) {
      tenant.sensitiveData.apiKeys.push(`sk_test_${tenant.tenantId}_${this.generateRandomString(32)}`);
    }

    // Generate secrets
    for (let i = 0; i < 2; i++) {
      tenant.sensitiveData.secrets.push(`secret_${tenant.tenantId}_${this.generateRandomString(64)}`);
    }

    // Generate personal data for users
    tenant.sensitiveData.personalData = tenant.users.map(user => ({
      userId: user.id,
      ssn: `${Math.floor(Math.random() * 900) + 100}-${Math.floor(Math.random() * 90) + 10}-${Math.floor(Math.random() * 9000) + 1000}`,
      creditCardNumber: `4111-1111-1111-${Math.floor(Math.random() * 9000) + 1000}`,
      phoneNumber: `******-${Math.floor(Math.random() * 900) + 100}-${Math.floor(Math.random() * 9000) + 1000}`,
      address: `${Math.floor(Math.random() * 9999) + 1} ${['Main', 'Oak', 'Pine', 'Elm'][Math.floor(Math.random() * 4)]} St, Test City, TC ${Math.floor(Math.random() * 90000) + 10000}`
    }));

    // Generate financial data
    for (let i = 0; i < 5; i++) {
      tenant.sensitiveData.financialData.push({
        transactionId: `txn_${tenant.tenantId}_${Date.now()}_${i}`,
        amount: Math.floor(Math.random() * 10000) + 100,
        currency: 'USD',
        accountNumber: `acc_${tenant.tenantId}_${Math.floor(Math.random() * 100000)}`,
        routingNumber: `${Math.floor(Math.random() * *********) + *********}`
      });
    }
  }

  /**
   * Generate random string
   */
  private generateRandomString(length: number): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * Get generated tenants
   */
  async getGeneratedTenants(): Promise<TenantTestData[]> {
    return [...this.generatedTenants];
  }

  /**
   * Generate specific data patterns for testing
   */
  async generateDataWithPatterns(tenantCount: number, patterns: {
    duplicateEmails?: boolean;
    crossTenantReferences?: boolean;
    maliciousData?: boolean;
    largeDatasets?: boolean;
  }): Promise<TenantTestData[]> {
    const tenants = await this.generateTenants(tenantCount);

    for (const tenant of tenants) {
      await this.createTenantData(tenant);

      if (patterns.duplicateEmails) {
        // Create users with duplicate emails across tenants
        const duplicateUser = this.generateUser(tenant.tenantId);
        duplicateUser.email = '<EMAIL>';
        tenant.users.push(duplicateUser);
      }

      if (patterns.crossTenantReferences) {
        // Create data that references other tenants (should be caught as violation)
        const maliciousEvent = this.generateEvent(tenant.tenantId, tenant.users[0].id);
        maliciousEvent.properties.maliciousTenantRef = tenants[0].tenantId !== tenant.tenantId ? tenants[0].tenantId : tenants[1]?.tenantId;
        tenant.events.push(maliciousEvent);
      }

      if (patterns.maliciousData) {
        // Add potentially malicious data patterns
        const maliciousUser = this.generateUser(tenant.tenantId);
        maliciousUser.name = "'; DROP TABLE users; --";
        maliciousUser.email = "<EMAIL>' OR '1'='1";
        tenant.users.push(maliciousUser);
      }

      if (patterns.largeDatasets) {
        // Generate large amounts of data
        for (let i = 0; i < 10000; i++) {
          const event = this.generateEvent(tenant.tenantId, tenant.users[0].id);
          tenant.events.push(event);
        }
      }
    }

    return tenants;
  }

  /**
   * Cleanup generated data
   */
  async cleanup(): Promise<void> {
    this.generatedTenants = [];
    this.userCounter = 0;
    this.experimentCounter = 0;
    this.eventCounter = 0;
  }

  /**
   * Get statistics about generated data
   */
  getStatistics(): {
    tenantCount: number;
    totalUsers: number;
    totalExperiments: number;
    totalEvents: number;
    averageUsersPerTenant: number;
    averageExperimentsPerTenant: number;
    averageEventsPerTenant: number;
  } {
    const tenantCount = this.generatedTenants.length;
    const totalUsers = this.generatedTenants.reduce((sum, tenant) => sum + tenant.users.length, 0);
    const totalExperiments = this.generatedTenants.reduce((sum, tenant) => sum + tenant.experiments.length, 0);
    const totalEvents = this.generatedTenants.reduce((sum, tenant) => sum + tenant.events.length, 0);

    return {
      tenantCount,
      totalUsers,
      totalExperiments,
      totalEvents,
      averageUsersPerTenant: tenantCount > 0 ? totalUsers / tenantCount : 0,
      averageExperimentsPerTenant: tenantCount > 0 ? totalExperiments / tenantCount : 0,
      averageEventsPerTenant: tenantCount > 0 ? totalEvents / tenantCount : 0
    };
  }
}

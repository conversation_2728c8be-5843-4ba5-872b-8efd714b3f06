import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { Logger } from './Logger';

export interface APIClientConfig {
  baseURL: string;
  timeout: number;
  retries: number;
  retryDelay: number;
  defaultHeaders: Record<string, string>;
}

export interface RequestOptions {
  headers?: Record<string, string>;
  timeout?: number;
  retries?: number;
  validateStatus?: (status: number) => boolean;
}

export class APIClient {
  private client: AxiosInstance;
  private config: APIClientConfig;
  private logger: Logger;

  constructor(config: APIClientConfig, logger: Logger) {
    this.config = config;
    this.logger = logger;

    this.client = axios.create({
      baseURL: config.baseURL,
      timeout: config.timeout,
      headers: config.defaultHeaders
    });

    this.setupInterceptors();
  }

  /**
   * GET request
   */
  async get<T = any>(url: string, options: RequestOptions = {}): Promise<AxiosResponse<T>> {
    return this.request<T>('GET', url, undefined, options);
  }

  /**
   * POST request
   */
  async post<T = any>(url: string, data?: any, options: RequestOptions = {}): Promise<AxiosResponse<T>> {
    return this.request<T>('POST', url, data, options);
  }

  /**
   * PUT request
   */
  async put<T = any>(url: string, data?: any, options: RequestOptions = {}): Promise<AxiosResponse<T>> {
    return this.request<T>('PUT', url, data, options);
  }

  /**
   * DELETE request
   */
  async delete<T = any>(url: string, options: RequestOptions = {}): Promise<AxiosResponse<T>> {
    return this.request<T>('DELETE', url, undefined, options);
  }

  /**
   * PATCH request
   */
  async patch<T = any>(url: string, data?: any, options: RequestOptions = {}): Promise<AxiosResponse<T>> {
    return this.request<T>('PATCH', url, data, options);
  }

  /**
   * Generic request method with retry logic
   */
  private async request<T = any>(
    method: string,
    url: string,
    data?: any,
    options: RequestOptions = {}
  ): Promise<AxiosResponse<T>> {
    const requestConfig: AxiosRequestConfig = {
      method: method as any,
      url,
      data,
      headers: { ...this.config.defaultHeaders, ...options.headers },
      timeout: options.timeout || this.config.timeout,
      validateStatus: options.validateStatus || ((status) => status < 400)
    };

    const maxRetries = options.retries !== undefined ? options.retries : this.config.retries;
    let lastError: Error;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const startTime = Date.now();
        const response = await this.client.request<T>(requestConfig);
        const duration = Date.now() - startTime;

        this.logger.debug('API request successful', {
          method,
          url,
          status: response.status,
          duration,
          attempt: attempt + 1
        });

        return response;
      } catch (error) {
        lastError = error as Error;
        const duration = Date.now() - Date.now();

        this.logger.warn('API request failed', {
          method,
          url,
          error: lastError.message,
          duration,
          attempt: attempt + 1,
          maxRetries
        });

        // Don't retry on the last attempt
        if (attempt === maxRetries) {
          break;
        }

        // Don't retry on certain status codes
        if (axios.isAxiosError(lastError) && lastError.response) {
          const status = lastError.response.status;
          if (status >= 400 && status < 500 && status !== 429) {
            // Don't retry client errors (except rate limiting)
            break;
          }
        }

        // Wait before retrying
        await this.sleep(this.config.retryDelay * Math.pow(2, attempt));
      }
    }

    throw lastError!;
  }

  /**
   * Setup request/response interceptors
   */
  private setupInterceptors(): void {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        this.logger.debug('API request started', {
          method: config.method?.toUpperCase(),
          url: config.url,
          headers: this.sanitizeHeaders(config.headers || {})
        });
        return config;
      },
      (error) => {
        this.logger.error('API request setup failed', { error: error.message });
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => {
        this.logger.debug('API response received', {
          method: response.config.method?.toUpperCase(),
          url: response.config.url,
          status: response.status,
          statusText: response.statusText
        });
        return response;
      },
      (error) => {
        if (axios.isAxiosError(error)) {
          this.logger.error('API response error', {
            method: error.config?.method?.toUpperCase(),
            url: error.config?.url,
            status: error.response?.status,
            statusText: error.response?.statusText,
            message: error.message
          });
        } else {
          this.logger.error('API unknown error', { error: error.message });
        }
        return Promise.reject(error);
      }
    );
  }

  /**
   * Sanitize headers for logging (remove sensitive data)
   */
  private sanitizeHeaders(headers: Record<string, any>): Record<string, any> {
    const sanitized = { ...headers };
    const sensitiveHeaders = ['authorization', 'x-api-key', 'x-auth-token', 'cookie'];

    for (const key of Object.keys(sanitized)) {
      if (sensitiveHeaders.includes(key.toLowerCase())) {
        sanitized[key] = '[REDACTED]';
      }
    }

    return sanitized;
  }

  /**
   * Sleep utility
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Test API connectivity
   */
  async testConnectivity(): Promise<boolean> {
    try {
      await this.get('/health', { timeout: 5000, retries: 0 });
      return true;
    } catch (error) {
      this.logger.warn('API connectivity test failed', { error });
      return false;
    }
  }

  /**
   * Get client configuration
   */
  getConfig(): APIClientConfig {
    return { ...this.config };
  }

  /**
   * Update default headers
   */
  updateDefaultHeaders(headers: Record<string, string>): void {
    this.config.defaultHeaders = { ...this.config.defaultHeaders, ...headers };
    this.client.defaults.headers.common = { ...this.client.defaults.headers.common, ...headers };
  }

  /**
   * Remove default header
   */
  removeDefaultHeader(key: string): void {
    delete this.config.defaultHeaders[key];
    delete this.client.defaults.headers.common[key];
  }
}

export interface Logger {
  debug(message: string, meta?: any): void;
  info(message: string, meta?: any): void;
  warn(message: string, meta?: any): void;
  error(message: string, meta?: any): void;
}

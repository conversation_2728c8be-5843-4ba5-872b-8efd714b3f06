export interface Logger {
  debug(message: string, meta?: any): void;
  info(message: string, meta?: any): void;
  warn(message: string, meta?: any): void;
  error(message: string, meta?: any): void;
}

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3
}

export class ConsoleLogger implements Logger {
  private level: LogLevel;
  private context: string;

  constructor(level: LogLevel = LogLevel.INFO, context: string = 'TenantIsolationTest') {
    this.level = level;
    this.context = context;
  }

  debug(message: string, meta?: any): void {
    if (this.level <= LogLevel.DEBUG) {
      this.log('DEBUG', message, meta);
    }
  }

  info(message: string, meta?: any): void {
    if (this.level <= LogLevel.INFO) {
      this.log('INFO', message, meta);
    }
  }

  warn(message: string, meta?: any): void {
    if (this.level <= LogLevel.WARN) {
      this.log('WARN', message, meta);
    }
  }

  error(message: string, meta?: any): void {
    if (this.level <= LogLevel.ERROR) {
      this.log('ERROR', message, meta);
    }
  }

  private log(level: string, message: string, meta?: any): void {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${level}] [${this.context}] ${message}`;
    
    if (meta) {
      console.log(logMessage, JSON.stringify(meta, null, 2));
    } else {
      console.log(logMessage);
    }
  }

  setLevel(level: LogLevel): void {
    this.level = level;
  }

  setContext(context: string): void {
    this.context = context;
  }
}

export class FileLogger implements Logger {
  private level: LogLevel;
  private context: string;
  private logFile: string;

  constructor(logFile: string, level: LogLevel = LogLevel.INFO, context: string = 'TenantIsolationTest') {
    this.logFile = logFile;
    this.level = level;
    this.context = context;
  }

  debug(message: string, meta?: any): void {
    if (this.level <= LogLevel.DEBUG) {
      this.writeLog('DEBUG', message, meta);
    }
  }

  info(message: string, meta?: any): void {
    if (this.level <= LogLevel.INFO) {
      this.writeLog('INFO', message, meta);
    }
  }

  warn(message: string, meta?: any): void {
    if (this.level <= LogLevel.WARN) {
      this.writeLog('WARN', message, meta);
    }
  }

  error(message: string, meta?: any): void {
    if (this.level <= LogLevel.ERROR) {
      this.writeLog('ERROR', message, meta);
    }
  }

  private writeLog(level: string, message: string, meta?: any): void {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level,
      context: this.context,
      message,
      meta
    };

    // In a real implementation, you would write to a file
    // For now, we'll just use console
    console.log(JSON.stringify(logEntry));
  }
}

export class MultiLogger implements Logger {
  private loggers: Logger[];

  constructor(loggers: Logger[]) {
    this.loggers = loggers;
  }

  debug(message: string, meta?: any): void {
    this.loggers.forEach(logger => logger.debug(message, meta));
  }

  info(message: string, meta?: any): void {
    this.loggers.forEach(logger => logger.info(message, meta));
  }

  warn(message: string, meta?: any): void {
    this.loggers.forEach(logger => logger.warn(message, meta));
  }

  error(message: string, meta?: any): void {
    this.loggers.forEach(logger => logger.error(message, meta));
  }

  addLogger(logger: Logger): void {
    this.loggers.push(logger);
  }

  removeLogger(logger: Logger): void {
    const index = this.loggers.indexOf(logger);
    if (index > -1) {
      this.loggers.splice(index, 1);
    }
  }
}

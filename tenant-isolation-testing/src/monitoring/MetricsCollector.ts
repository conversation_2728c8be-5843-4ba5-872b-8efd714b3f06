import { EventEmitter } from 'events';
import { TestMetrics, MonitoringData, Alert, LogEntry } from '../types/isolation';
import { Logger } from '../utils/Logger';

export interface MetricsCollectorConfig {
  collectInterval: number; // milliseconds
  retentionPeriod: number; // milliseconds
  alertThresholds: {
    responseTime: number;
    errorRate: number;
    memoryUsage: number;
    cpuUsage: number;
    databaseConnections: number;
  };
  enableRealTimeAlerts: boolean;
}

export class MetricsCollector extends EventEmitter {
  private config: MetricsCollectorConfig;
  private logger: Logger;
  private isCollecting: boolean = false;
  private collectionInterval?: NodeJS.Timeout;
  private metrics: TestMetrics[] = [];
  private alerts: Alert[] = [];
  private logs: LogEntry[] = [];
  private startTime?: Date;

  constructor(config: MetricsCollectorConfig, logger: Logger) {
    super();
    this.config = config;
    this.logger = logger;
  }

  /**
   * Start metrics collection
   */
  async startMonitoring(): Promise<void> {
    if (this.isCollecting) {
      return;
    }

    this.isCollecting = true;
    this.startTime = new Date();
    this.metrics = [];
    this.alerts = [];
    this.logs = [];

    this.logger.info('Starting metrics collection', {
      interval: this.config.collectInterval,
      retentionPeriod: this.config.retentionPeriod
    });

    // Start periodic collection
    this.collectionInterval = setInterval(async () => {
      await this.collectMetrics();
    }, this.config.collectInterval);

    // Collect initial metrics
    await this.collectMetrics();
  }

  /**
   * Stop metrics collection
   */
  async stopMonitoring(): Promise<void> {
    if (!this.isCollecting) {
      return;
    }

    this.isCollecting = false;

    if (this.collectionInterval) {
      clearInterval(this.collectionInterval);
      this.collectionInterval = undefined;
    }

    // Collect final metrics
    await this.collectMetrics();

    this.logger.info('Stopped metrics collection', {
      totalMetrics: this.metrics.length,
      totalAlerts: this.alerts.length,
      duration: this.startTime ? Date.now() - this.startTime.getTime() : 0
    });
  }

  /**
   * Get current metrics
   */
  async getCurrentMetrics(): Promise<TestMetrics> {
    return this.collectCurrentMetrics();
  }

  /**
   * Get all collected metrics
   */
  getAllMetrics(): TestMetrics[] {
    return [...this.metrics];
  }

  /**
   * Get monitoring data
   */
  getMonitoringData(): MonitoringData {
    return {
      timestamp: new Date(),
      metrics: this.metrics[this.metrics.length - 1] || this.getEmptyMetrics(),
      alerts: [...this.alerts],
      logs: [...this.logs]
    };
  }

  /**
   * Get alerts
   */
  getAlerts(): Alert[] {
    return [...this.alerts];
  }

  /**
   * Get active alerts
   */
  getActiveAlerts(): Alert[] {
    return this.alerts.filter(alert => !alert.resolved);
  }

  /**
   * Add custom log entry
   */
  addLogEntry(level: 'debug' | 'info' | 'warn' | 'error', message: string, context?: Record<string, any>): void {
    const logEntry: LogEntry = {
      timestamp: new Date(),
      level,
      message,
      context: context || {}
    };

    this.logs.push(logEntry);
    this.cleanupOldLogs();

    // Emit log event
    this.emit('logEntry', logEntry);
  }

  /**
   * Record custom metric
   */
  recordMetric(name: string, value: number, tags?: Record<string, string>): void {
    this.addLogEntry('debug', `Custom metric recorded: ${name}`, {
      metricName: name,
      value,
      tags
    });

    this.emit('customMetric', { name, value, tags, timestamp: new Date() });
  }

  /**
   * Collect current metrics
   */
  private async collectCurrentMetrics(): Promise<TestMetrics> {
    const metrics: TestMetrics = {
      requestsPerSecond: await this.getRequestsPerSecond(),
      averageResponseTime: await this.getAverageResponseTime(),
      errorRate: await this.getErrorRate(),
      memoryUsage: await this.getMemoryUsage(),
      cpuUsage: await this.getCPUUsage(),
      databaseConnections: await this.getDatabaseConnections(),
      cacheHitRate: await this.getCacheHitRate(),
      concurrentUsers: await this.getConcurrentUsers()
    };

    return metrics;
  }

  /**
   * Collect and store metrics
   */
  private async collectMetrics(): Promise<void> {
    try {
      const metrics = await this.collectCurrentMetrics();
      this.metrics.push(metrics);

      // Check for alerts
      if (this.config.enableRealTimeAlerts) {
        await this.checkAlerts(metrics);
      }

      // Cleanup old metrics
      this.cleanupOldMetrics();

      // Emit metrics event
      this.emit('metricsCollected', metrics);

      this.logger.debug('Metrics collected', metrics);
    } catch (error) {
      this.logger.error('Failed to collect metrics', { error });
    }
  }

  /**
   * Check for alert conditions
   */
  private async checkAlerts(metrics: TestMetrics): Promise<void> {
    const thresholds = this.config.alertThresholds;

    // Response time alert
    if (metrics.averageResponseTime > thresholds.responseTime) {
      this.createAlert('warning', `High response time: ${metrics.averageResponseTime}ms`);
    }

    // Error rate alert
    if (metrics.errorRate > thresholds.errorRate) {
      this.createAlert('error', `High error rate: ${(metrics.errorRate * 100).toFixed(2)}%`);
    }

    // Memory usage alert
    if (metrics.memoryUsage > thresholds.memoryUsage) {
      this.createAlert('warning', `High memory usage: ${(metrics.memoryUsage * 100).toFixed(2)}%`);
    }

    // CPU usage alert
    if (metrics.cpuUsage > thresholds.cpuUsage) {
      this.createAlert('warning', `High CPU usage: ${(metrics.cpuUsage * 100).toFixed(2)}%`);
    }

    // Database connections alert
    if (metrics.databaseConnections > thresholds.databaseConnections) {
      this.createAlert('warning', `High database connections: ${metrics.databaseConnections}`);
    }
  }

  /**
   * Create alert
   */
  private createAlert(severity: 'info' | 'warning' | 'error' | 'critical', message: string, tenantId?: string): void {
    const alert: Alert = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      severity,
      message,
      tenantId,
      timestamp: new Date(),
      resolved: false
    };

    this.alerts.push(alert);
    this.emit('alert', alert);

    this.logger.warn('Alert created', {
      alertId: alert.id,
      severity: alert.severity,
      message: alert.message
    });
  }

  /**
   * Resolve alert
   */
  resolveAlert(alertId: string): void {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.resolved = true;
      this.emit('alertResolved', alert);
      this.logger.info('Alert resolved', { alertId });
    }
  }

  /**
   * Cleanup old metrics
   */
  private cleanupOldMetrics(): void {
    const cutoffTime = Date.now() - this.config.retentionPeriod;
    this.metrics = this.metrics.filter(metric => {
      // Assuming metrics have a timestamp property
      return true; // For now, keep all metrics
    });
  }

  /**
   * Cleanup old logs
   */
  private cleanupOldLogs(): void {
    const cutoffTime = Date.now() - this.config.retentionPeriod;
    this.logs = this.logs.filter(log => log.timestamp.getTime() > cutoffTime);
  }

  /**
   * Metric collection methods
   */
  private async getRequestsPerSecond(): Promise<number> {
    // Mock implementation - in real scenario, this would track actual requests
    return Math.floor(Math.random() * 100) + 50;
  }

  private async getAverageResponseTime(): Promise<number> {
    // Mock implementation - in real scenario, this would calculate from actual response times
    return Math.floor(Math.random() * 500) + 100;
  }

  private async getErrorRate(): Promise<number> {
    // Mock implementation - in real scenario, this would calculate from actual error counts
    return Math.random() * 0.1; // 0-10% error rate
  }

  private async getMemoryUsage(): Promise<number> {
    try {
      const memUsage = process.memoryUsage();
      return memUsage.heapUsed / memUsage.heapTotal;
    } catch (error) {
      this.logger.warn('Failed to get memory usage', { error });
      return 0;
    }
  }

  private async getCPUUsage(): Promise<number> {
    try {
      // Simplified CPU usage calculation
      // In a real implementation, you'd use a proper CPU monitoring library
      const startUsage = process.cpuUsage();
      await new Promise(resolve => setTimeout(resolve, 100));
      const endUsage = process.cpuUsage(startUsage);
      
      const totalUsage = endUsage.user + endUsage.system;
      const totalTime = 100 * 1000; // 100ms in microseconds
      
      return Math.min(totalUsage / totalTime, 1);
    } catch (error) {
      this.logger.warn('Failed to get CPU usage', { error });
      return Math.random() * 0.5; // Mock value
    }
  }

  private async getDatabaseConnections(): Promise<number> {
    // Mock implementation - in real scenario, this would query the database
    return Math.floor(Math.random() * 50) + 10;
  }

  private async getCacheHitRate(): Promise<number> {
    // Mock implementation - in real scenario, this would get from cache statistics
    return Math.random() * 0.3 + 0.7; // 70-100% hit rate
  }

  private async getConcurrentUsers(): Promise<number> {
    // Mock implementation - in real scenario, this would track active sessions
    return Math.floor(Math.random() * 200) + 50;
  }

  /**
   * Get empty metrics object
   */
  private getEmptyMetrics(): TestMetrics {
    return {
      requestsPerSecond: 0,
      averageResponseTime: 0,
      errorRate: 0,
      memoryUsage: 0,
      cpuUsage: 0,
      databaseConnections: 0,
      cacheHitRate: 0,
      concurrentUsers: 0
    };
  }

  /**
   * Get metrics summary
   */
  getMetricsSummary(): {
    totalDataPoints: number;
    averageResponseTime: number;
    maxResponseTime: number;
    averageErrorRate: number;
    maxErrorRate: number;
    averageMemoryUsage: number;
    maxMemoryUsage: number;
    averageCPUUsage: number;
    maxCPUUsage: number;
    totalAlerts: number;
    activeAlerts: number;
  } {
    if (this.metrics.length === 0) {
      return {
        totalDataPoints: 0,
        averageResponseTime: 0,
        maxResponseTime: 0,
        averageErrorRate: 0,
        maxErrorRate: 0,
        averageMemoryUsage: 0,
        maxMemoryUsage: 0,
        averageCPUUsage: 0,
        maxCPUUsage: 0,
        totalAlerts: 0,
        activeAlerts: 0
      };
    }

    const responseTimes = this.metrics.map(m => m.averageResponseTime);
    const errorRates = this.metrics.map(m => m.errorRate);
    const memoryUsages = this.metrics.map(m => m.memoryUsage);
    const cpuUsages = this.metrics.map(m => m.cpuUsage);

    return {
      totalDataPoints: this.metrics.length,
      averageResponseTime: responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length,
      maxResponseTime: Math.max(...responseTimes),
      averageErrorRate: errorRates.reduce((a, b) => a + b, 0) / errorRates.length,
      maxErrorRate: Math.max(...errorRates),
      averageMemoryUsage: memoryUsages.reduce((a, b) => a + b, 0) / memoryUsages.length,
      maxMemoryUsage: Math.max(...memoryUsages),
      averageCPUUsage: cpuUsages.reduce((a, b) => a + b, 0) / cpuUsages.length,
      maxCPUUsage: Math.max(...cpuUsages),
      totalAlerts: this.alerts.length,
      activeAlerts: this.getActiveAlerts().length
    };
  }
}

import { Pool } from 'pg';
import { Redis } from 'ioredis';
import {
  IsolationTestSuite,
  TestEnvironment,
  IsolationTestType,
  TestScenarioConfig
} from '../src/types/isolation';
import { IsolationTestRunner } from '../src/core/IsolationTestRunner';
import { ViolationDetector } from '../src/detectors/ViolationDetector';
import { MetricsCollector } from '../src/monitoring/MetricsCollector';
import { TestDataGenerator } from '../src/utils/TestDataGenerator';
import { APIClient } from '../src/utils/APIClient';
import { DataIsolationScenarios } from '../src/scenarios/DataIsolationScenarios';
import { LoadTestScenarios } from '../src/scenarios/LoadTestScenarios';
import { ConsoleLogger, LogLevel } from '../src/utils/Logger';

async function runBasicIsolationTest() {
  console.log('🚀 Starting Basic Tenant Isolation Test Suite\n');

  // Initialize logger
  const logger = new ConsoleLogger(LogLevel.INFO, 'BasicIsolationTest');

  // Database connection
  const database = new Pool({
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    database: process.env.DB_NAME || 'isolation_test',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
    max: 20
  });

  // Redis connection
  const cache = new Redis({
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD,
    db: 1 // Use separate DB for testing
  });

  // API client
  const apiClient = new APIClient({
    baseURL: process.env.API_BASE_URL || 'http://localhost:3000',
    timeout: 30000,
    retries: 3,
    retryDelay: 1000,
    defaultHeaders: {
      'Content-Type': 'application/json',
      'User-Agent': 'TenantIsolationTest/1.0'
    }
  }, logger);

  // Test data generator
  const dataGenerator = new TestDataGenerator();

  // Violation detector
  const violationDetector = new ViolationDetector({
    database: {
      connectionString: process.env.DATABASE_URL || 'postgresql://postgres:password@localhost:5432/isolation_test',
      monitorQueries: true,
      logSlowQueries: true,
      slowQueryThreshold: 1000
    },
    cache: {
      redisUrl: process.env.REDIS_URL || 'redis://localhost:6379/1',
      monitorKeyAccess: true,
      detectCrossTenantAccess: true
    },
    api: {
      monitorRequests: true,
      detectUnauthorizedAccess: true,
      logHeaders: true
    },
    realTimeDetection: true,
    violationThresholds: {
      maxCrossTenantQueries: 5,
      maxUnauthorizedAttempts: 10,
      maxCacheViolations: 3
    }
  }, database, cache, logger);

  // Metrics collector
  const metricsCollector = new MetricsCollector({
    collectInterval: 5000, // 5 seconds
    retentionPeriod: 3600000, // 1 hour
    alertThresholds: {
      responseTime: 2000, // 2 seconds
      errorRate: 0.05, // 5%
      memoryUsage: 0.8, // 80%
      cpuUsage: 0.8, // 80%
      databaseConnections: 50
    },
    enableRealTimeAlerts: true
  }, logger);

  // Test environment
  const testEnvironment: TestEnvironment = {
    name: 'Basic Isolation Test Environment',
    database: {
      connectionString: process.env.DATABASE_URL || 'postgresql://postgres:password@localhost:5432/isolation_test',
      maxConnections: 20,
      enableRowLevelSecurity: true,
      isolationLevel: 'read_committed'
    },
    cache: {
      redisUrl: process.env.REDIS_URL || 'redis://localhost:6379/1',
      keyPrefix: 'test:',
      ttl: 3600,
      maxMemory: '256mb'
    },
    api: {
      baseUrl: process.env.API_BASE_URL || 'http://localhost:3000',
      timeout: 30000,
      retries: 3,
      rateLimits: {
        'default': 100,
        'authenticated': 1000,
        'admin': 10000
      }
    },
    monitoring: {
      metricsEndpoint: '/metrics',
      logsEndpoint: '/logs'
    }
  };

  // Create scenario builders
  const dataScenarios = new DataIsolationScenarios(
    database,
    cache,
    apiClient,
    dataGenerator,
    violationDetector,
    logger
  );

  const loadScenarios = new LoadTestScenarios(
    database,
    cache,
    apiClient,
    dataGenerator,
    violationDetector,
    logger
  );

  // Test configuration
  const basicConfig: TestScenarioConfig = {
    tenantCount: 5,
    usersPerTenant: 20,
    experimentsPerTenant: 10,
    eventsPerUser: 50,
    concurrency: 10
  };

  const loadConfig: TestScenarioConfig = {
    tenantCount: 3,
    usersPerTenant: 100,
    experimentsPerTenant: 20,
    eventsPerUser: 200,
    concurrency: 50,
    duration: 120, // 2 minutes
    loadConfig: {
      duration: 120,
      rampUpTime: 30,
      maxConcurrentUsers: 100,
      requestsPerSecond: 50,
      tenantCount: 3,
      dataVolumePerTenant: 5000
    }
  };

  // Create test suite
  const testSuite: IsolationTestSuite = {
    name: 'Basic Tenant Isolation Test Suite',
    description: 'Comprehensive test suite to verify tenant data isolation',
    environment: testEnvironment,
    scenarios: [
      // Data isolation tests
      dataScenarios.createBasicDataIsolationTest(basicConfig),
      dataScenarios.createSQLInjectionTest(basicConfig),
      dataScenarios.createCacheIsolationTest(basicConfig),
      dataScenarios.createAPIIsolationTest(basicConfig),
      
      // Load tests
      loadScenarios.createHighLoadConcurrentTest(loadConfig),
      loadScenarios.createMemoryPressureTest({
        ...basicConfig,
        loadConfig: {
          ...loadConfig.loadConfig!,
          maxConcurrentUsers: 200
        }
      })
    ],
    globalSetup: async () => {
      logger.info('Running global setup');
      
      // Create test database schema
      await setupTestSchema(database);
      
      // Clear cache
      await cache.flushdb();
      
      // Test API connectivity
      const apiConnected = await apiClient.testConnectivity();
      if (!apiConnected) {
        logger.warn('API connectivity test failed - some tests may fail');
      }
      
      logger.info('Global setup complete');
    },
    globalTeardown: async () => {
      logger.info('Running global teardown');
      
      // Clean up test data
      await cleanupTestData(database);
      
      // Clear cache
      await cache.flushdb();
      
      // Close connections
      await database.end();
      await cache.quit();
      
      logger.info('Global teardown complete');
    }
  };

  // Create test runner
  const testRunner = new IsolationTestRunner(
    testSuite,
    dataGenerator,
    violationDetector,
    metricsCollector,
    logger
  );

  // Setup event listeners
  testRunner.on('suiteStarted', (event) => {
    console.log(`📋 Test suite started: ${event.suiteName}`);
  });

  testRunner.on('scenarioStarted', (event) => {
    console.log(`🧪 Starting scenario: ${event.scenarioName} (${event.type})`);
  });

  testRunner.on('scenarioCompleted', (result) => {
    const status = result.passed ? '✅ PASSED' : '❌ FAILED';
    console.log(`${status} ${result.testName} (${result.duration}ms)`);
    
    if (result.violations.length > 0) {
      console.log(`   ⚠️  ${result.violations.length} violations detected:`);
      result.violations.forEach(violation => {
        console.log(`      - ${violation.type}: ${violation.description} (${violation.severity})`);
      });
    }
  });

  testRunner.on('suiteCompleted', (report) => {
    console.log('\n📊 Test Suite Results:');
    console.log(`   Total Tests: ${report.totalTests}`);
    console.log(`   Passed: ${report.passedTests}`);
    console.log(`   Failed: ${report.failedTests}`);
    console.log(`   Duration: ${report.duration}ms`);
    console.log(`   Overall Status: ${report.summary.overallStatus.toUpperCase()}`);
    
    if (report.summary.criticalViolations > 0) {
      console.log(`   🚨 Critical Violations: ${report.summary.criticalViolations}`);
    }
    
    if (report.summary.dataLeakageDetected) {
      console.log('   🔒 Data leakage detected!');
    }
    
    if (report.recommendations.length > 0) {
      console.log('\n💡 Recommendations:');
      report.recommendations.forEach(rec => {
        console.log(`   - ${rec}`);
      });
    }
  });

  testRunner.on('suiteError', (error) => {
    console.error('❌ Test suite failed:', error);
  });

  try {
    // Run the test suite
    const report = await testRunner.runSuite();
    
    // Display final results
    console.log('\n🎯 Final Test Report:');
    console.log('='.repeat(50));
    console.log(`Suite: ${report.suiteName}`);
    console.log(`Status: ${report.summary.overallStatus.toUpperCase()}`);
    console.log(`Tests: ${report.passedTests}/${report.totalTests} passed`);
    console.log(`Duration: ${(report.duration / 1000).toFixed(2)}s`);
    
    if (report.summary.criticalViolations > 0) {
      console.log(`\n🚨 CRITICAL: ${report.summary.criticalViolations} critical violations found!`);
      console.log('Review violations before deploying to production.');
    }
    
    if (report.summary.dataLeakageDetected) {
      console.log('\n🔒 DATA LEAKAGE DETECTED!');
      console.log('Immediate action required to fix tenant isolation.');
    }
    
    // Save report to file (in a real implementation)
    console.log(`\n📄 Report saved with ID: ${report.suiteId}`);
    
    return report.summary.overallStatus === 'passed';
    
  } catch (error) {
    console.error('💥 Test execution failed:', error);
    return false;
  }
}

/**
 * Setup test database schema
 */
async function setupTestSchema(database: Pool): Promise<void> {
  const client = await database.connect();
  
  try {
    // Create test tables
    await client.query(`
      CREATE TABLE IF NOT EXISTS users (
        id VARCHAR(255) PRIMARY KEY,
        tenant_id VARCHAR(255) NOT NULL,
        email VARCHAR(255) NOT NULL,
        name VARCHAR(255) NOT NULL,
        role VARCHAR(50) NOT NULL,
        metadata JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `);

    await client.query(`
      CREATE TABLE IF NOT EXISTS experiments (
        id VARCHAR(255) PRIMARY KEY,
        tenant_id VARCHAR(255) NOT NULL,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        status VARCHAR(50) NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `);

    await client.query(`
      CREATE TABLE IF NOT EXISTS events (
        id VARCHAR(255) PRIMARY KEY,
        tenant_id VARCHAR(255) NOT NULL,
        user_id VARCHAR(255) NOT NULL,
        experiment_id VARCHAR(255),
        variant_id VARCHAR(255),
        event_name VARCHAR(255) NOT NULL,
        properties JSONB DEFAULT '{}',
        timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create indexes for performance
    await client.query('CREATE INDEX IF NOT EXISTS idx_users_tenant_id ON users(tenant_id)');
    await client.query('CREATE INDEX IF NOT EXISTS idx_experiments_tenant_id ON experiments(tenant_id)');
    await client.query('CREATE INDEX IF NOT EXISTS idx_events_tenant_id ON events(tenant_id)');

    // Enable Row Level Security (RLS)
    await client.query('ALTER TABLE users ENABLE ROW LEVEL SECURITY');
    await client.query('ALTER TABLE experiments ENABLE ROW LEVEL SECURITY');
    await client.query('ALTER TABLE events ENABLE ROW LEVEL SECURITY');

    // Create RLS policies (basic example)
    await client.query(`
      CREATE POLICY IF NOT EXISTS users_tenant_isolation ON users
      FOR ALL TO PUBLIC
      USING (tenant_id = current_setting('app.current_tenant_id', true))
    `);

    await client.query(`
      CREATE POLICY IF NOT EXISTS experiments_tenant_isolation ON experiments
      FOR ALL TO PUBLIC
      USING (tenant_id = current_setting('app.current_tenant_id', true))
    `);

    await client.query(`
      CREATE POLICY IF NOT EXISTS events_tenant_isolation ON events
      FOR ALL TO PUBLIC
      USING (tenant_id = current_setting('app.current_tenant_id', true))
    `);

  } finally {
    client.release();
  }
}

/**
 * Cleanup test data
 */
async function cleanupTestData(database: Pool): Promise<void> {
  const client = await database.connect();
  
  try {
    await client.query('DELETE FROM events WHERE tenant_id LIKE \'tenant_%\'');
    await client.query('DELETE FROM experiments WHERE tenant_id LIKE \'tenant_%\'');
    await client.query('DELETE FROM users WHERE tenant_id LIKE \'tenant_%\'');
  } finally {
    client.release();
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  runBasicIsolationTest()
    .then(success => {
      console.log(success ? '\n🎉 All tests passed!' : '\n💥 Some tests failed!');
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('\n💥 Test execution failed:', error);
      process.exit(1);
    });
}

export default runBasicIsolationTest;

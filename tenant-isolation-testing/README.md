# Tenant Isolation Testing Suite

A comprehensive TypeScript testing framework for verifying tenant data isolation in multi-tenant applications under various scenarios and load conditions.

## Features

- **🔒 Data Isolation Testing**: Verify tenants cannot access each other's data
- **🛡️ SQL Injection Protection**: Test resistance to cross-tenant SQL injection attacks
- **💾 Cache Isolation**: Ensure cached data is properly isolated between tenants
- **🚀 Load Testing**: Test isolation under high concurrent load
- **⚡ Stress Testing**: Verify isolation under extreme conditions
- **🌪️ Chaos Engineering**: Test resilience under chaotic conditions
- **📊 Real-time Monitoring**: Comprehensive metrics and violation detection
- **🔍 Violation Detection**: Automatic detection of isolation breaches
- **📈 Performance Metrics**: Response times, error rates, resource usage
- **📋 Detailed Reporting**: Comprehensive test reports with recommendations

## Installation

```bash
npm install @yourorg/tenant-isolation-testing
```

## Quick Start

### Basic Usage

```typescript
import { runBasicIsolationTest } from '@yourorg/tenant-isolation-testing';

// Run basic isolation tests
const success = await runBasicIsolationTest();
console.log(success ? 'All tests passed!' : 'Some tests failed!');
```

### Custom Test Suite

```typescript
import {
  IsolationTestRunner,
  DataIsolationScenarios,
  LoadTestScenarios,
  ViolationDetector,
  MetricsCollector,
  TestDataGenerator
} from '@yourorg/tenant-isolation-testing';

// Setup components
const database = new Pool(/* database config */);
const cache = new Redis(/* redis config */);
const logger = new ConsoleLogger();
const dataGenerator = new TestDataGenerator();

// Create violation detector
const violationDetector = new ViolationDetector({
  database: { /* config */ },
  cache: { /* config */ },
  api: { /* config */ },
  realTimeDetection: true,
  violationThresholds: {
    maxCrossTenantQueries: 5,
    maxUnauthorizedAttempts: 10,
    maxCacheViolations: 3
  }
}, database, cache, logger);

// Create metrics collector
const metricsCollector = new MetricsCollector({
  collectInterval: 5000,
  retentionPeriod: 3600000,
  alertThresholds: {
    responseTime: 2000,
    errorRate: 0.05,
    memoryUsage: 0.8,
    cpuUsage: 0.8,
    databaseConnections: 50
  },
  enableRealTimeAlerts: true
}, logger);

// Create test scenarios
const dataScenarios = new DataIsolationScenarios(/* ... */);
const loadScenarios = new LoadTestScenarios(/* ... */);

// Build test suite
const testSuite = {
  name: 'Custom Isolation Test Suite',
  description: 'Custom tenant isolation tests',
  environment: testEnvironment,
  scenarios: [
    dataScenarios.createBasicDataIsolationTest(config),
    dataScenarios.createSQLInjectionTest(config),
    loadScenarios.createHighLoadConcurrentTest(loadConfig)
  ]
};

// Run tests
const testRunner = new IsolationTestRunner(/* ... */);
const report = await testRunner.runSuite();
```

## Test Scenarios

### Data Isolation Tests

#### Basic Data Isolation
```typescript
const basicTest = dataScenarios.createBasicDataIsolationTest({
  tenantCount: 5,
  usersPerTenant: 20,
  experimentsPerTenant: 10,
  eventsPerUser: 50
});
```

Verifies that:
- Tenants can only access their own data
- Cross-tenant queries return empty results
- Data counts are correct for each tenant

#### SQL Injection Protection
```typescript
const sqlInjectionTest = dataScenarios.createSQLInjectionTest({
  tenantCount: 3,
  usersPerTenant: 10,
  experimentsPerTenant: 5,
  eventsPerUser: 25
});
```

Tests resistance to:
- `'; DROP TABLE users; --`
- `' OR '1'='1`
- `' UNION SELECT * FROM users WHERE tenant_id != '`
- Cross-tenant data access attempts

#### Cache Isolation
```typescript
const cacheTest = dataScenarios.createCacheIsolationTest({
  tenantCount: 4,
  usersPerTenant: 15,
  experimentsPerTenant: 8,
  eventsPerUser: 30
});
```

Ensures:
- Cache keys are tenant-isolated
- No cross-tenant cache access
- Proper cache key namespacing

### Load Testing

#### High Load Concurrent Access
```typescript
const loadTest = loadScenarios.createHighLoadConcurrentTest({
  tenantCount: 3,
  usersPerTenant: 100,
  experimentsPerTenant: 20,
  eventsPerUser: 200,
  loadConfig: {
    duration: 300, // 5 minutes
    maxConcurrentUsers: 1000,
    requestsPerSecond: 100,
    tenantCount: 3,
    dataVolumePerTenant: 10000
  }
});
```

#### Stress Testing
```typescript
const stressTest = loadScenarios.createStressTest({
  tenantCount: 5,
  usersPerTenant: 200,
  experimentsPerTenant: 50,
  eventsPerUser: 500,
  loadConfig: {
    duration: 600, // 10 minutes
    maxConcurrentUsers: 5000,
    requestsPerSecond: 1000,
    tenantCount: 5,
    dataVolumePerTenant: 50000
  }
});
```

#### Chaos Engineering
```typescript
const chaosTest = loadScenarios.createChaosTest({
  tenantCount: 3,
  usersPerTenant: 50,
  experimentsPerTenant: 10,
  eventsPerUser: 100,
  loadConfig: {
    duration: 180, // 3 minutes
    maxConcurrentUsers: 100,
    requestsPerSecond: 50,
    tenantCount: 3,
    dataVolumePerTenant: 5000
  }
});
```

Introduces chaos:
- Network delays and timeouts
- Database connection issues
- Memory pressure
- CPU intensive operations
- Cache failures

## Violation Detection

The framework automatically detects various types of isolation violations:

### Data Leakage
- Cross-tenant database queries
- Unauthorized data access
- Missing tenant filters in queries

### Cache Violations
- Cross-tenant cache access
- Cache key pollution
- Missing tenant isolation in cache keys

### Session Issues
- Session bleeding between tenants
- Unauthorized session access
- Context switching problems

### Permission Bypasses
- Unauthorized admin operations
- Missing permission checks
- Privilege escalation attempts

## Monitoring and Metrics

### Real-time Metrics
```typescript
const metrics = await metricsCollector.getCurrentMetrics();
console.log({
  requestsPerSecond: metrics.requestsPerSecond,
  averageResponseTime: metrics.averageResponseTime,
  errorRate: metrics.errorRate,
  memoryUsage: metrics.memoryUsage,
  cpuUsage: metrics.cpuUsage,
  databaseConnections: metrics.databaseConnections,
  cacheHitRate: metrics.cacheHitRate,
  concurrentUsers: metrics.concurrentUsers
});
```

### Alerts
```typescript
metricsCollector.on('alert', (alert) => {
  console.log(`Alert: ${alert.severity} - ${alert.message}`);
});
```

### Custom Metrics
```typescript
metricsCollector.recordMetric('custom.tenant.operations', 150, {
  tenantId: 'tenant_123',
  operation: 'data_access'
});
```

## Test Reports

### Report Structure
```typescript
interface TestReport {
  suiteId: string;
  suiteName: string;
  startTime: Date;
  endTime: Date;
  duration: number;
  totalTests: number;
  passedTests: number;
  failedTests: number;
  results: IsolationTestResult[];
  summary: TestSummary;
  recommendations: string[];
}
```

### Sample Report
```json
{
  "suiteId": "report_1703123456789_abc123",
  "suiteName": "Basic Tenant Isolation Test Suite",
  "totalTests": 6,
  "passedTests": 5,
  "failedTests": 1,
  "summary": {
    "overallStatus": "warning",
    "criticalViolations": 0,
    "highSeverityViolations": 2,
    "dataLeakageDetected": false,
    "averageResponseTime": 245.6,
    "maxConcurrentUsers": 1000
  },
  "recommendations": [
    "Review database queries to ensure proper tenant filtering",
    "Implement stronger cache isolation controls",
    "Optimize application performance - response times are high"
  ]
}
```

## Configuration

### Database Configuration
```typescript
const databaseConfig = {
  connectionString: 'postgresql://user:pass@localhost:5432/testdb',
  monitorQueries: true,
  logSlowQueries: true,
  slowQueryThreshold: 1000
};
```

### Cache Configuration
```typescript
const cacheConfig = {
  redisUrl: 'redis://localhost:6379/1',
  monitorKeyAccess: true,
  detectCrossTenantAccess: true
};
```

### API Configuration
```typescript
const apiConfig = {
  monitorRequests: true,
  detectUnauthorizedAccess: true,
  logHeaders: true
};
```

## Environment Setup

### Database Schema
```sql
-- Enable Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE experiments ENABLE ROW LEVEL SECURITY;
ALTER TABLE events ENABLE ROW LEVEL SECURITY;

-- Create tenant isolation policies
CREATE POLICY users_tenant_isolation ON users
FOR ALL TO PUBLIC
USING (tenant_id = current_setting('app.current_tenant_id', true));

CREATE POLICY experiments_tenant_isolation ON experiments
FOR ALL TO PUBLIC
USING (tenant_id = current_setting('app.current_tenant_id', true));

CREATE POLICY events_tenant_isolation ON events
FOR ALL TO PUBLIC
USING (tenant_id = current_setting('app.current_tenant_id', true));
```

### Environment Variables
```bash
# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=isolation_test
DB_USER=postgres
DB_PASSWORD=password
DATABASE_URL=postgresql://postgres:password@localhost:5432/isolation_test

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_URL=redis://localhost:6379/1

# API
API_BASE_URL=http://localhost:3000

# Test Configuration
TEST_TENANT_COUNT=5
TEST_LOAD_DURATION=300
TEST_MAX_CONCURRENT_USERS=1000
```

## Best Practices

### Test Design
1. **Start Small**: Begin with basic isolation tests before load testing
2. **Incremental Load**: Gradually increase load to identify breaking points
3. **Real Data Patterns**: Use realistic data volumes and access patterns
4. **Multiple Scenarios**: Test different tenant sizes and usage patterns

### Monitoring
1. **Real-time Alerts**: Enable real-time violation detection
2. **Comprehensive Metrics**: Monitor all aspects of system performance
3. **Baseline Establishment**: Establish performance baselines before testing
4. **Trend Analysis**: Track metrics over time to identify patterns

### Security
1. **Sensitive Data**: Use realistic but non-production sensitive data
2. **Isolation Verification**: Verify isolation at all layers (DB, cache, API)
3. **Attack Simulation**: Include realistic attack scenarios
4. **Regular Testing**: Run isolation tests regularly, not just before releases

## Examples

See the `examples/` directory for complete working examples:

- `basicIsolationTest.ts` - Complete basic isolation test suite
- `loadTestExample.ts` - High-load concurrent testing
- `chaosTestExample.ts` - Chaos engineering scenarios
- `customScenarios.ts` - Building custom test scenarios

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

MIT

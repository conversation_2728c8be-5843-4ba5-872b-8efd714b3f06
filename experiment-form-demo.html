<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Experiment Form - Interactive Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/sortablejs@latest/Sortable.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .sortable-ghost { opacity: 0.4; }
        .sortable-chosen { transform: scale(1.02); }
        .allocation-error { animation: shake 0.5s ease-in-out; }
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Form Modal -->
    <div class="fixed inset-0 z-50 overflow-y-auto">
        <div class="flex min-h-screen items-center justify-center p-4">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75"></div>
            
            <div class="relative w-full max-w-4xl bg-white rounded-lg shadow-xl max-h-[90vh] overflow-hidden">
                <form id="experimentForm" class="flex flex-col h-full">
                    <!-- Header -->
                    <div class="flex items-center justify-between p-6 border-b border-gray-200">
                        <h2 class="text-xl font-semibold text-gray-900">Create New Experiment</h2>
                        <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeForm()">
                            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <!-- Form Content -->
                    <div class="flex-1 overflow-y-auto p-6 space-y-6">
                        <!-- Basic Information -->
                        <div class="space-y-4">
                            <h3 class="text-lg font-medium text-gray-900">Basic Information</h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        Experiment Name *
                                    </label>
                                    <input
                                        type="text"
                                        id="experimentName"
                                        class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                                        placeholder="Enter experiment name"
                                        required
                                    />
                                    <div id="nameError" class="mt-1 text-sm text-red-600 hidden"></div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                                    <select id="status" class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                                        <option value="DRAFT">Draft</option>
                                        <option value="ACTIVE">Active</option>
                                        <option value="PAUSED">Paused</option>
                                        <option value="COMPLETED">Completed</option>
                                    </select>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                                <textarea
                                    id="description"
                                    rows="3"
                                    class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                                    placeholder="Describe what this experiment tests"
                                ></textarea>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Hypothesis</label>
                                <textarea
                                    id="hypothesis"
                                    rows="3"
                                    class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                                    placeholder="State your hypothesis for this experiment"
                                ></textarea>
                            </div>
                        </div>

                        <!-- Dates and Configuration -->
                        <div class="space-y-4">
                            <h3 class="text-lg font-medium text-gray-900">Schedule & Configuration</h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                                    <input
                                        type="datetime-local"
                                        id="startDate"
                                        class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                                    />
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                                    <input
                                        type="datetime-local"
                                        id="endDate"
                                        class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                                    />
                                    <div id="dateError" class="mt-1 text-sm text-red-600 hidden"></div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Traffic Allocation (%)</label>
                                    <input
                                        type="number"
                                        id="trafficAllocation"
                                        min="1"
                                        max="100"
                                        value="100"
                                        class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                                    />
                                </div>
                            </div>
                        </div>

                        <!-- Variants Section -->
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-medium text-gray-900">Variants *</h3>
                                <div class="flex items-center space-x-2">
                                    <div id="allocationStatus" class="text-sm">
                                        <span id="totalAllocation" class="font-medium">100.0</span>% allocated
                                    </div>
                                    <button
                                        type="button"
                                        onclick="redistributeEqually()"
                                        class="text-sm text-indigo-600 hover:text-indigo-800"
                                    >
                                        Distribute Equally
                                    </button>
                                    <button
                                        type="button"
                                        onclick="addVariant()"
                                        class="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-sm font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                                    >
                                        <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                        Add Variant
                                    </button>
                                </div>
                            </div>

                            <div id="variantsList" class="space-y-3">
                                <!-- Variants will be dynamically added here -->
                            </div>

                            <div id="variantError" class="text-sm text-red-600 hidden"></div>
                        </div>

                        <!-- Advanced Settings -->
                        <div class="space-y-4">
                            <button
                                type="button"
                                onclick="toggleAdvanced()"
                                class="flex items-center text-sm text-indigo-600 hover:text-indigo-800"
                            >
                                <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span id="advancedToggleText">Show Advanced Settings</span>
                            </button>

                            <div id="advancedSettings" class="hidden grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Primary Metric</label>
                                    <input
                                        type="text"
                                        id="primaryMetric"
                                        class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                                        placeholder="e.g., conversion_rate"
                                    />
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Tags</label>
                                    <input
                                        type="text"
                                        id="tags"
                                        class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                                        placeholder="Enter tags separated by commas"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Footer -->
                    <div class="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
                        <div class="flex items-center text-sm text-gray-500">
                            <div id="validationStatus" class="hidden flex items-center text-red-600">
                                <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                                Please fix validation errors before submitting
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-3">
                            <button
                                type="button"
                                onclick="closeForm()"
                                class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                id="submitBtn"
                                class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                Create Experiment
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        let variants = [
            { id: 1, name: 'Control', description: '', isControl: true, allocation: 50 },
            { id: 2, name: 'Treatment', description: '', isControl: false, allocation: 50 }
        ];
        let nextVariantId = 3;
        let showingAdvanced = false;

        // Initialize the form
        document.addEventListener('DOMContentLoaded', function() {
            renderVariants();
            updateAllocationStatus();
            
            // Add event listeners
            document.getElementById('experimentForm').addEventListener('submit', handleSubmit);
            document.getElementById('startDate').addEventListener('change', validateDates);
            document.getElementById('endDate').addEventListener('change', validateDates);
            document.getElementById('experimentName').addEventListener('input', validateName);
        });

        function renderVariants() {
            const container = document.getElementById('variantsList');
            container.innerHTML = '';

            variants.forEach((variant, index) => {
                const variantHtml = `
                    <div class="border border-gray-200 rounded-lg p-4 bg-white variant-item" data-id="${variant.id}">
                        <div class="flex items-start space-x-4">
                            <div class="mt-2 text-gray-400 hover:text-gray-600 cursor-move drag-handle">
                                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M7 2a1 1 0 000 2h6a1 1 0 100-2H7zM7 8a1 1 0 000 2h6a1 1 0 100-2H7zM7 14a1 1 0 000 2h6a1 1 0 100-2H7z" />
                                </svg>
                            </div>

                            <div class="flex-1 space-y-3">
                                <div class="grid grid-cols-1 md:grid-cols-4 gap-3">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Variant Name</label>
                                        <input
                                            type="text"
                                            value="${variant.name}"
                                            onchange="updateVariant(${variant.id}, 'name', this.value)"
                                            class="block w-full text-sm border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                                        />
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Allocation (%)</label>
                                        <input
                                            type="number"
                                            min="0"
                                            max="100"
                                            step="0.1"
                                            value="${variant.allocation}"
                                            onchange="updateVariant(${variant.id}, 'allocation', parseFloat(this.value))"
                                            class="block w-full text-sm border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                                        />
                                    </div>

                                    <div class="flex items-center justify-center">
                                        <label class="flex items-center">
                                            <input
                                                type="checkbox"
                                                ${variant.isControl ? 'checked' : ''}
                                                onchange="updateVariant(${variant.id}, 'isControl', this.checked)"
                                                class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                                            />
                                            <span class="ml-2 text-sm text-gray-700">Control</span>
                                        </label>
                                    </div>

                                    <div class="flex items-center justify-end">
                                        ${variants.length > 2 ? `
                                            <button
                                                type="button"
                                                onclick="removeVariant(${variant.id})"
                                                class="text-red-500 hover:text-red-700"
                                            >
                                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                </svg>
                                            </button>
                                        ` : ''}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                                    <input
                                        type="text"
                                        value="${variant.description}"
                                        onchange="updateVariant(${variant.id}, 'description', this.value)"
                                        class="block w-full text-sm border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                                        placeholder="Optional description"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                container.insertAdjacentHTML('beforeend', variantHtml);
            });

            // Initialize drag and drop
            new Sortable(container, {
                handle: '.drag-handle',
                animation: 150,
                onEnd: function(evt) {
                    const oldIndex = evt.oldIndex;
                    const newIndex = evt.newIndex;
                    const movedVariant = variants.splice(oldIndex, 1)[0];
                    variants.splice(newIndex, 0, movedVariant);
                }
            });
        }

        function updateVariant(id, field, value) {
            const variant = variants.find(v => v.id === id);
            if (variant) {
                if (field === 'isControl' && value) {
                    // Unset other controls
                    variants.forEach(v => {
                        if (v.id !== id) v.isControl = false;
                    });
                    renderVariants();
                } else {
                    variant[field] = value;
                }
                
                if (field === 'allocation') {
                    updateAllocationStatus();
                }
            }
        }

        function addVariant() {
            const currentTotal = variants.reduce((sum, v) => sum + v.allocation, 0);
            const remainingAllocation = Math.max(0, 100 - currentTotal);
            
            variants.push({
                id: nextVariantId++,
                name: `Variant ${variants.length + 1}`,
                description: '',
                isControl: false,
                allocation: remainingAllocation
            });
            
            renderVariants();
            updateAllocationStatus();
        }

        function removeVariant(id) {
            if (variants.length <= 2) return;
            variants = variants.filter(v => v.id !== id);
            renderVariants();
            updateAllocationStatus();
        }

        function redistributeEqually() {
            const equalAllocation = 100 / variants.length;
            variants.forEach(variant => {
                variant.allocation = equalAllocation;
            });
            renderVariants();
            updateAllocationStatus();
        }

        function updateAllocationStatus() {
            const total = variants.reduce((sum, v) => sum + v.allocation, 0);
            const totalElement = document.getElementById('totalAllocation');
            const statusElement = document.getElementById('allocationStatus');
            const errorElement = document.getElementById('variantError');
            
            totalElement.textContent = total.toFixed(1);
            
            if (Math.abs(total - 100) > 0.01) {
                statusElement.className = 'text-sm text-red-600 allocation-error';
                errorElement.textContent = `Total allocation is ${total.toFixed(1)}%. Must equal 100%.`;
                errorElement.classList.remove('hidden');
            } else {
                statusElement.className = 'text-sm text-green-600';
                errorElement.classList.add('hidden');
            }
            
            validateForm();
        }

        function toggleAdvanced() {
            showingAdvanced = !showingAdvanced;
            const settings = document.getElementById('advancedSettings');
            const toggleText = document.getElementById('advancedToggleText');
            
            if (showingAdvanced) {
                settings.classList.remove('hidden');
                toggleText.textContent = 'Hide Advanced Settings';
            } else {
                settings.classList.add('hidden');
                toggleText.textContent = 'Show Advanced Settings';
            }
        }

        function validateName() {
            const name = document.getElementById('experimentName').value;
            const errorElement = document.getElementById('nameError');
            
            if (!name.trim()) {
                errorElement.textContent = 'Experiment name is required';
                errorElement.classList.remove('hidden');
                return false;
            } else if (name.length > 100) {
                errorElement.textContent = 'Name must be less than 100 characters';
                errorElement.classList.remove('hidden');
                return false;
            } else {
                errorElement.classList.add('hidden');
                return true;
            }
        }

        function validateDates() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            const errorElement = document.getElementById('dateError');
            
            if (startDate && endDate && new Date(endDate) <= new Date(startDate)) {
                errorElement.textContent = 'End date must be after start date';
                errorElement.classList.remove('hidden');
                return false;
            } else {
                errorElement.classList.add('hidden');
                return true;
            }
        }

        function validateForm() {
            const isNameValid = validateName();
            const areDatesValid = validateDates();
            const total = variants.reduce((sum, v) => sum + v.allocation, 0);
            const isAllocationValid = Math.abs(total - 100) < 0.01;
            const hasMinVariants = variants.length >= 2;
            
            const isValid = isNameValid && areDatesValid && isAllocationValid && hasMinVariants;
            
            const submitBtn = document.getElementById('submitBtn');
            const validationStatus = document.getElementById('validationStatus');
            
            submitBtn.disabled = !isValid;
            
            if (!isValid) {
                validationStatus.classList.remove('hidden');
            } else {
                validationStatus.classList.add('hidden');
            }
            
            return isValid;
        }

        function handleSubmit(e) {
            e.preventDefault();
            
            if (!validateForm()) {
                return;
            }
            
            const formData = {
                name: document.getElementById('experimentName').value,
                description: document.getElementById('description').value,
                hypothesis: document.getElementById('hypothesis').value,
                startDate: document.getElementById('startDate').value,
                endDate: document.getElementById('endDate').value,
                status: document.getElementById('status').value,
                trafficAllocation: parseInt(document.getElementById('trafficAllocation').value),
                primaryMetric: document.getElementById('primaryMetric').value,
                tags: document.getElementById('tags').value.split(',').map(t => t.trim()).filter(Boolean),
                variants: variants.map(v => ({
                    ...v,
                    trafficWeight: v.allocation / 100
                }))
            };
            
            console.log('Experiment Data:', formData);
            alert('Experiment created successfully! Check console for data.');
        }

        function closeForm() {
            if (confirm('Are you sure you want to close? Any unsaved changes will be lost.')) {
                window.close();
            }
        }

        // Add real-time validation
        document.addEventListener('input', validateForm);
        document.addEventListener('change', validateForm);
    </script>
</body>
</html>

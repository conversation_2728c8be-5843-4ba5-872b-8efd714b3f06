/**
 * Experiment Configuration Validator
 * 
 * This script validates experiment configurations against the JSON schema
 * and provides additional business logic validation.
 */

const Ajv = require('ajv');
const addFormats = require('ajv-formats');

// Load the schema
const experimentSchema = require('./experiment_config_schema.json');

// Initialize AJV with format support
const ajv = new Ajv({ allErrors: true, verbose: true });
addFormats(ajv);

// Compile the schema
const validate = ajv.compile(experimentSchema);

/**
 * Validates an experiment configuration
 * @param {Object} experimentConfig - The experiment configuration to validate
 * @returns {Object} Validation result with isValid flag and errors array
 */
function validateExperiment(experimentConfig) {
    const result = {
        isValid: true,
        errors: [],
        warnings: []
    };

    // JSON Schema validation
    const isSchemaValid = validate(experimentConfig);
    if (!isSchemaValid) {
        result.isValid = false;
        result.errors.push(...validate.errors.map(error => ({
            type: 'schema',
            path: error.instancePath,
            message: error.message,
            value: error.data
        })));
    }

    // Additional business logic validation
    if (experimentConfig.variants) {
        validateVariantAllocations(experimentConfig.variants, result);
        validateControlVariant(experimentConfig.variants, result);
    }

    if (experimentConfig.startDate && experimentConfig.endDate) {
        validateDateRange(experimentConfig.startDate, experimentConfig.endDate, result);
    }

    if (experimentConfig.metrics) {
        validateMetricsConfiguration(experimentConfig.metrics, result);
    }

    if (experimentConfig.targetingRules) {
        validateTargetingRules(experimentConfig.targetingRules, result);
    }

    validateSampleSizeAndEffect(experimentConfig, result);

    return result;
}

/**
 * Validates that variant allocation percentages sum to 100
 */
function validateVariantAllocations(variants, result) {
    const totalAllocation = variants.reduce((sum, variant) => sum + variant.allocationPercentage, 0);
    
    if (Math.abs(totalAllocation - 100) > 0.01) {
        result.errors.push({
            type: 'business_logic',
            path: '/variants',
            message: `Variant allocation percentages must sum to 100, got ${totalAllocation}`,
            value: totalAllocation
        });
        result.isValid = false;
    }

    // Check for duplicate variant names
    const variantNames = variants.map(v => v.name);
    const duplicateNames = variantNames.filter((name, index) => variantNames.indexOf(name) !== index);
    if (duplicateNames.length > 0) {
        result.errors.push({
            type: 'business_logic',
            path: '/variants',
            message: `Duplicate variant names found: ${duplicateNames.join(', ')}`,
            value: duplicateNames
        });
        result.isValid = false;
    }
}

/**
 * Validates that exactly one variant is marked as control
 */
function validateControlVariant(variants, result) {
    const controlVariants = variants.filter(v => v.isControl);
    
    if (controlVariants.length === 0) {
        result.warnings.push({
            type: 'business_logic',
            path: '/variants',
            message: 'No control variant specified. Consider marking one variant as control.',
            value: null
        });
    } else if (controlVariants.length > 1) {
        result.errors.push({
            type: 'business_logic',
            path: '/variants',
            message: `Multiple control variants found. Only one variant should be marked as control.`,
            value: controlVariants.map(v => v.name)
        });
        result.isValid = false;
    }
}

/**
 * Validates date range logic
 */
function validateDateRange(startDate, endDate, result) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const now = new Date();

    if (start >= end) {
        result.errors.push({
            type: 'business_logic',
            path: '/endDate',
            message: 'End date must be after start date',
            value: { startDate, endDate }
        });
        result.isValid = false;
    }

    // Warning for experiments starting in the past
    if (start < now) {
        result.warnings.push({
            type: 'business_logic',
            path: '/startDate',
            message: 'Start date is in the past',
            value: startDate
        });
    }

    // Warning for very short experiments
    const durationDays = (end - start) / (1000 * 60 * 60 * 24);
    if (durationDays < 7) {
        result.warnings.push({
            type: 'business_logic',
            path: '/endDate',
            message: 'Experiment duration is less than 7 days. Consider running longer for statistical significance.',
            value: durationDays
        });
    }
}

/**
 * Validates metrics configuration
 */
function validateMetricsConfiguration(metrics, result) {
    // Check if aggregation type matches value property requirement
    const checkMetric = (metric, path) => {
        if (['sum', 'average'].includes(metric.aggregationType) && !metric.valueProperty) {
            result.errors.push({
                type: 'business_logic',
                path: path,
                message: `Aggregation type '${metric.aggregationType}' requires a valueProperty`,
                value: metric.aggregationType
            });
            result.isValid = false;
        }
    };

    checkMetric(metrics.primary, '/metrics/primary');
    
    if (metrics.secondary) {
        metrics.secondary.forEach((metric, index) => {
            checkMetric(metric, `/metrics/secondary/${index}`);
        });
    }
}

/**
 * Validates targeting rules
 */
function validateTargetingRules(targetingRules, result) {
    targetingRules.forEach((rule, index) => {
        const path = `/targetingRules/${index}`;
        
        // Validate operator-value combinations
        if (['in', 'not_in'].includes(rule.operator) && !Array.isArray(rule.value)) {
            result.errors.push({
                type: 'business_logic',
                path: `${path}/value`,
                message: `Operator '${rule.operator}' requires an array value`,
                value: rule.value
            });
            result.isValid = false;
        }

        if (['greater_than', 'less_than'].includes(rule.operator) && typeof rule.value !== 'number') {
            result.errors.push({
                type: 'business_logic',
                path: `${path}/value`,
                message: `Operator '${rule.operator}' requires a numeric value`,
                value: rule.value
            });
            result.isValid = false;
        }
    });
}

/**
 * Validates sample size and minimum detectable effect relationship
 */
function validateSampleSizeAndEffect(config, result) {
    if (config.sampleSize && config.minimumDetectableEffect) {
        // Simple heuristic: smaller effects need larger sample sizes
        const minSampleForEffect = Math.ceil(1000 / config.minimumDetectableEffect);
        
        if (config.sampleSize < minSampleForEffect) {
            result.warnings.push({
                type: 'statistical',
                path: '/sampleSize',
                message: `Sample size may be too small to detect effect of ${config.minimumDetectableEffect}. Consider increasing to at least ${minSampleForEffect}.`,
                value: config.sampleSize
            });
        }
    }
}

/**
 * Validates multiple experiments for conflicts
 */
function validateExperimentConflicts(experiments) {
    const conflicts = [];
    
    for (let i = 0; i < experiments.length; i++) {
        for (let j = i + 1; j < experiments.length; j++) {
            const exp1 = experiments[i];
            const exp2 = experiments[j];
            
            // Check for overlapping date ranges
            if (datesOverlap(exp1.startDate, exp1.endDate, exp2.startDate, exp2.endDate)) {
                // Check for overlapping targeting
                if (hasOverlappingTargeting(exp1.targetingRules, exp2.targetingRules)) {
                    conflicts.push({
                        experiments: [exp1.name, exp2.name],
                        type: 'targeting_overlap',
                        message: 'Experiments have overlapping date ranges and targeting rules'
                    });
                }
            }
        }
    }
    
    return conflicts;
}

function datesOverlap(start1, end1, start2, end2) {
    if (!start1 || !end1 || !start2 || !end2) return false;
    
    const s1 = new Date(start1);
    const e1 = new Date(end1);
    const s2 = new Date(start2);
    const e2 = new Date(end2);
    
    return s1 <= e2 && s2 <= e1;
}

function hasOverlappingTargeting(rules1, rules2) {
    // Simplified overlap detection - in practice, this would be more sophisticated
    if (!rules1 || !rules2) return true; // No targeting rules means all users
    
    // Check if any attributes are targeted by both experiments
    const attrs1 = new Set(rules1.map(r => r.attribute));
    const attrs2 = new Set(rules2.map(r => r.attribute));
    
    return [...attrs1].some(attr => attrs2.has(attr));
}

// Export functions for use in other modules
module.exports = {
    validateExperiment,
    validateExperimentConflicts,
    validate: validate
};

// CLI usage
if (require.main === module) {
    const fs = require('fs');
    const path = process.argv[2];
    
    if (!path) {
        console.error('Usage: node validate_experiment.js <experiment_config.json>');
        process.exit(1);
    }
    
    try {
        const config = JSON.parse(fs.readFileSync(path, 'utf8'));
        const result = validateExperiment(config);
        
        console.log('Validation Result:');
        console.log('=================');
        console.log(`Valid: ${result.isValid}`);
        
        if (result.errors.length > 0) {
            console.log('\nErrors:');
            result.errors.forEach(error => {
                console.log(`  - ${error.path}: ${error.message}`);
            });
        }
        
        if (result.warnings.length > 0) {
            console.log('\nWarnings:');
            result.warnings.forEach(warning => {
                console.log(`  - ${warning.path}: ${warning.message}`);
            });
        }
        
        process.exit(result.isValid ? 0 : 1);
    } catch (error) {
        console.error('Error reading or parsing file:', error.message);
        process.exit(1);
    }
}

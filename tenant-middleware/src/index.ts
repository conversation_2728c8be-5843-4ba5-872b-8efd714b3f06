// Main exports
export { TenantMiddleware } from './middleware/tenantMiddleware';
export { TenantResolver } from './resolvers/TenantResolver';

// Store implementations
export { 
  MemoryTenantStore, 
  DatabaseTenantStore, 
  RedisTenantStore 
} from './stores/TenantStore';

// Types
export {
  TenantInfo,
  TenantContext,
  TenantResolutionConfig,
  TenantStore,
  TenantRequest,
  TenantResponse,
  TenantResolutionError,
  TenantMetrics,
  TenantMiddlewareOptions
} from './types/tenant';

// Utility functions
export {
  getCurrentTenant,
  getCurrentTenantId,
  getCurrentTenantSlug,
  tenantHasFeature,
  tenantHasTier,
  runWithTenantContext
} from './middleware/tenantMiddleware';

// Factory function for easy setup
import { TenantMiddleware } from './middleware/tenantMiddleware';
import { MemoryTenantStore } from './stores/TenantStore';
import { TenantResolutionConfig, TenantStore, TenantInfo } from './types/tenant';

/**
 * Create tenant middleware with default configuration
 */
export function createTenantMiddleware(options: {
  store?: TenantStore;
  tenants?: TenantInfo[];
  config?: Partial<TenantResolutionConfig>;
  logger?: any;
  metrics?: any;
}) {
  // Default configuration
  const defaultConfig: TenantResolutionConfig = {
    methods: ['subdomain', 'header', 'jwt'],
    subdomain: {
      enabled: true,
      baseDomain: process.env.BASE_DOMAIN || 'localhost',
      excludeSubdomains: ['www', 'api', 'admin']
    },
    header: {
      enabled: true,
      name: 'X-Tenant-ID'
    },
    jwt: {
      enabled: true,
      secret: process.env.JWT_SECRET || 'your-secret-key',
      tenantClaim: 'tenant_id',
      userClaim: 'user_id'
    },
    cache: {
      enabled: true,
      ttl: 300, // 5 minutes
      maxSize: 1000
    },
    required: false
  };

  // Merge with provided config
  const config = { ...defaultConfig, ...options.config };

  // Use provided store or create default memory store
  const store = options.store || new MemoryTenantStore(options.tenants || []);

  // Create middleware instance
  return new TenantMiddleware({
    config,
    store,
    logger: options.logger,
    metrics: options.metrics
  });
}

/**
 * Create sample tenants for testing
 */
export function createSampleTenants(): TenantInfo[] {
  return [
    {
      id: '550e8400-e29b-41d4-a716-446655440001',
      slug: 'acme',
      name: 'Acme Corporation',
      domain: 'acme.example.com',
      status: 'active',
      tier: 'enterprise',
      features: ['analytics', 'api_access', 'custom_domain', 'sso'],
      settings: {
        timezone: 'UTC',
        currency: 'USD',
        theme: 'dark'
      },
      createdAt: new Date('2023-01-01'),
      updatedAt: new Date('2023-12-01')
    },
    {
      id: '550e8400-e29b-41d4-a716-446655440002',
      slug: 'startup',
      name: 'Startup Inc',
      status: 'active',
      tier: 'pro',
      features: ['analytics', 'api_access'],
      settings: {
        timezone: 'PST',
        currency: 'USD'
      },
      createdAt: new Date('2023-06-01'),
      updatedAt: new Date('2023-12-01')
    },
    {
      id: '550e8400-e29b-41d4-a716-446655440003',
      slug: 'demo',
      name: 'Demo Company',
      status: 'active',
      tier: 'free',
      features: ['basic_analytics'],
      settings: {
        timezone: 'UTC',
        currency: 'USD'
      },
      createdAt: new Date('2023-11-01'),
      updatedAt: new Date('2023-12-01')
    }
  ];
}

/**
 * Express.js integration helper
 */
export function setupTenantMiddleware(app: any, options: Parameters<typeof createTenantMiddleware>[0] = {}) {
  const tenantMiddleware = createTenantMiddleware(options);

  // Add tenant resolution middleware
  app.use(tenantMiddleware.middleware());

  // Add utility middlewares to app for easy access
  app.requireTenant = () => tenantMiddleware.requireTenant();
  app.requireActiveTenant = () => tenantMiddleware.requireActiveTenant();
  app.requireTenantTier = (tiers: string[]) => tenantMiddleware.requireTenantTier(tiers);
  app.requireTenantFeature = (features: string[]) => tenantMiddleware.requireTenantFeature(features);
  app.tenantRateLimit = (getRateLimit: any) => tenantMiddleware.tenantRateLimit(getRateLimit);

  // Add management endpoints
  app.getTenantCacheStats = () => tenantMiddleware.getCacheStats();
  app.invalidateTenantCache = (identifier: string) => tenantMiddleware.invalidateCache(identifier);

  return tenantMiddleware;
}

// Default export
export default {
  TenantMiddleware,
  TenantResolver,
  MemoryTenantStore,
  DatabaseTenantStore,
  RedisTenantStore,
  createTenantMiddleware,
  createSampleTenants,
  setupTenantMiddleware
};

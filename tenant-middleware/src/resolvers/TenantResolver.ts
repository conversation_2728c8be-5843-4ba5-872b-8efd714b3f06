import { Request } from 'express';
import jwt from 'jsonwebtoken';
import { LRUCache } from 'lru-cache';
import {
  TenantInfo,
  TenantContext,
  TenantResolutionConfig,
  TenantStore,
  TenantResolutionError,
  TenantRequest
} from '../types/tenant';

export class TenantResolver {
  private config: TenantResolutionConfig;
  private store: TenantStore;
  private cache?: LRUCache<string, TenantInfo>;
  private logger?: any;
  private metrics?: any;

  constructor(
    config: TenantResolutionConfig,
    store: TenantStore,
    logger?: any,
    metrics?: any
  ) {
    this.config = config;
    this.store = store;
    this.logger = logger;
    this.metrics = metrics;

    // Initialize cache if enabled
    if (config.cache?.enabled) {
      this.cache = new LRUCache<string, TenantInfo>({
        max: config.cache.maxSize || 1000,
        ttl: (config.cache.ttl || 300) * 1000, // Convert to milliseconds
      });
    }
  }

  /**
   * Resolve tenant from request using configured methods
   */
  async resolveTenant(req: TenantRequest): Promise<TenantContext | null> {
    const startTime = Date.now();
    const requestId = this.generateRequestId();

    try {
      this.metrics?.increment('tenant.resolution.attempts');

      // Try each resolution method in order
      for (const method of this.config.methods) {
        try {
          const identifier = await this.extractIdentifier(req, method);
          if (!identifier) continue;

          this.logger?.debug('Attempting tenant resolution', {
            method,
            identifier,
            requestId
          });

          const tenant = await this.getTenant(identifier, method);
          if (tenant) {
            // Validate tenant if validator is provided
            if (this.config.validateTenant && !this.config.validateTenant(tenant, req)) {
              this.logger?.warn('Tenant validation failed', {
                tenantId: tenant.id,
                method,
                requestId
              });
              continue;
            }

            const context = await this.createTenantContext(tenant, req, method, requestId);
            
            this.metrics?.increment('tenant.resolution.successes', {
              method,
              tenantId: tenant.id
            });
            
            this.metrics?.timing(
              'tenant.resolution.time',
              Date.now() - startTime,
              { method }
            );

            this.logger?.info('Tenant resolved successfully', {
              tenantId: tenant.id,
              tenantSlug: tenant.slug,
              method,
              requestId
            });

            return context;
          }
        } catch (error) {
          this.logger?.warn('Tenant resolution method failed', {
            method,
            error: error instanceof Error ? error.message : 'Unknown error',
            requestId
          });
          
          this.metrics?.increment('tenant.resolution.method_errors', {
            method,
            error: error instanceof Error ? error.constructor.name : 'UnknownError'
          });
        }
      }

      // Try default tenant if configured
      if (this.config.defaultTenant) {
        const defaultTenant = await this.getTenant(this.config.defaultTenant, 'default');
        if (defaultTenant) {
          const context = await this.createTenantContext(
            defaultTenant,
            req,
            'default',
            requestId
          );
          
          this.logger?.info('Using default tenant', {
            tenantId: defaultTenant.id,
            requestId
          });
          
          return context;
        }
      }

      // Handle tenant not found
      this.metrics?.increment('tenant.resolution.failures');
      
      if (this.config.required) {
        throw new TenantResolutionError(
          'Tenant resolution required but no tenant found',
          'TENANT_REQUIRED'
        );
      }

      this.logger?.debug('No tenant resolved', { requestId });
      return null;

    } catch (error) {
      this.metrics?.increment('tenant.resolution.errors', {
        error: error instanceof Error ? error.constructor.name : 'UnknownError'
      });

      this.logger?.error('Tenant resolution failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        requestId
      });

      if (this.config.onError) {
        this.config.onError(error as Error, req);
      }

      throw error;
    }
  }

  /**
   * Extract tenant identifier from request based on method
   */
  private async extractIdentifier(req: Request, method: string): Promise<string | null> {
    switch (method) {
      case 'subdomain':
        return this.extractFromSubdomain(req);
      
      case 'header':
        return this.extractFromHeader(req);
      
      case 'jwt':
        return this.extractFromJWT(req);
      
      case 'path':
        return this.extractFromPath(req);
      
      default:
        return null;
    }
  }

  /**
   * Extract tenant from subdomain
   */
  private extractFromSubdomain(req: Request): string | null {
    if (!this.config.subdomain?.enabled) return null;

    const host = req.get('host') || req.hostname;
    if (!host) return null;

    const baseDomain = this.config.subdomain.baseDomain;
    const excludeSubdomains = this.config.subdomain.excludeSubdomains || [];

    // Extract subdomain
    const hostParts = host.split('.');
    if (hostParts.length < 3) return null; // No subdomain

    const subdomain = hostParts[0];
    
    // Check if subdomain is excluded
    if (excludeSubdomains.includes(subdomain)) return null;

    // Verify the rest matches base domain
    const remainingDomain = hostParts.slice(1).join('.');
    if (remainingDomain !== baseDomain) return null;

    return subdomain;
  }

  /**
   * Extract tenant from header
   */
  private extractFromHeader(req: Request): string | null {
    if (!this.config.header?.enabled) return null;

    const headerName = this.config.header.name;
    const headerValue = req.get(headerName);

    if (!headerValue) return null;

    // Handle multiple values if allowed
    if (this.config.header.allowMultiple && headerValue.includes(',')) {
      return headerValue.split(',')[0].trim();
    }

    return headerValue.trim();
  }

  /**
   * Extract tenant from JWT token
   */
  private extractFromJWT(req: Request): string | null {
    if (!this.config.jwt?.enabled) return null;

    const authHeader = req.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) return null;

    try {
      const token = authHeader.substring(7);
      const decoded = jwt.verify(token, this.config.jwt.secret, {
        algorithms: [this.config.jwt.algorithm || 'HS256']
      }) as any;

      const tenantClaim = this.config.jwt.tenantClaim;
      return decoded[tenantClaim] || null;
    } catch (error) {
      this.logger?.debug('JWT verification failed', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return null;
    }
  }

  /**
   * Extract tenant from URL path
   */
  private extractFromPath(req: Request): string | null {
    if (!this.config.path?.enabled) return null;

    const paramName = this.config.path.paramName;
    return (req.params as any)[paramName] || null;
  }

  /**
   * Get tenant from store with caching
   */
  private async getTenant(identifier: string, method: string): Promise<TenantInfo | null> {
    // Check cache first
    if (this.cache) {
      const cached = this.cache.get(identifier);
      if (cached) {
        this.metrics?.increment('tenant.cache.hits', { method });
        return cached;
      }
      this.metrics?.increment('tenant.cache.misses', { method });
    }

    // Try different store methods based on identifier type
    let tenant: TenantInfo | null = null;

    // Try by ID first (UUID format)
    if (this.isUUID(identifier)) {
      tenant = await this.store.getTenantById(identifier);
    }

    // Try by slug if not found by ID
    if (!tenant) {
      tenant = await this.store.getTenantBySlug(identifier);
    }

    // Try by domain if not found by slug
    if (!tenant && method === 'subdomain') {
      tenant = await this.store.getTenantByDomain(identifier);
    }

    // Try custom resolver if configured
    if (!tenant && this.config.onTenantNotFound) {
      tenant = this.config.onTenantNotFound(identifier, method);
    }

    // Cache the result if found
    if (tenant && this.cache) {
      this.cache.set(identifier, tenant);
      if (this.store.cacheTenant) {
        await this.store.cacheTenant(tenant);
      }
    }

    return tenant;
  }

  /**
   * Create tenant context with user information
   */
  private async createTenantContext(
    tenant: TenantInfo,
    req: Request,
    source: string,
    requestId: string
  ): Promise<TenantContext> {
    const context: TenantContext = {
      tenant,
      requestId,
      timestamp: new Date(),
      source: source as any
    };

    // Extract user information from JWT if available
    if (this.config.jwt?.enabled) {
      const authHeader = req.get('Authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        try {
          const token = authHeader.substring(7);
          const decoded = jwt.verify(token, this.config.jwt.secret) as any;
          
          const userClaim = this.config.jwt.userClaim;
          if (userClaim && decoded[userClaim]) {
            context.user = {
              id: decoded[userClaim],
              email: decoded.email || '',
              roles: decoded.roles || [],
              permissions: decoded.permissions || []
            };
          }
        } catch (error) {
          // JWT verification failed, but we already have tenant context
          this.logger?.debug('Failed to extract user from JWT', {
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }
    }

    return context;
  }

  /**
   * Check if string is a valid UUID
   */
  private isUUID(str: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(str);
  }

  /**
   * Generate unique request ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Invalidate cache for tenant
   */
  async invalidateCache(identifier: string): Promise<void> {
    if (this.cache) {
      this.cache.delete(identifier);
    }
    
    if (this.store.invalidateCache) {
      await this.store.invalidateCache(identifier);
    }
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): any {
    if (!this.cache) return null;

    return {
      size: this.cache.size,
      max: this.cache.max,
      calculatedSize: this.cache.calculatedSize,
      hits: this.cache.hits,
      misses: this.cache.misses
    };
  }
}

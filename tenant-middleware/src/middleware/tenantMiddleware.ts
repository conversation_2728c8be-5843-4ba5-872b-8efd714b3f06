import { Request, Response, NextFunction } from 'express';
import { AsyncLocalStorage } from 'async_hooks';
import {
  TenantContext,
  TenantRequest,
  TenantResponse,
  TenantMiddlewareOptions,
  TenantResolutionError
} from '../types/tenant';
import { TenantResolver } from '../resolvers/TenantResolver';

// Global async local storage for tenant context
const tenantStorage = new AsyncLocalStorage<TenantContext>();

export class TenantMiddleware {
  private resolver: TenantResolver;
  private logger?: any;
  private metrics?: any;

  constructor(options: TenantMiddlewareOptions) {
    this.resolver = new TenantResolver(
      options.config,
      options.store,
      options.logger,
      options.metrics
    );
    this.logger = options.logger;
    this.metrics = options.metrics;
  }

  /**
   * Main tenant resolution middleware
   */
  middleware() {
    return async (req: TenantRequest, res: TenantResponse, next: NextFunction): Promise<void> => {
      try {
        const startTime = Date.now();

        // Resolve tenant context
        const tenantContext = await this.resolver.resolveTenant(req);

        if (tenantContext) {
          // Attach tenant context to request and response
          req.tenant = tenantContext;
          res.tenant = tenantContext;
          
          // Set convenience properties
          req.tenantId = tenantContext.tenant.id;
          req.tenantSlug = tenantContext.tenant.slug;

          // Add tenant headers to response
          res.set({
            'X-Tenant-ID': tenantContext.tenant.id,
            'X-Tenant-Slug': tenantContext.tenant.slug,
            'X-Request-ID': tenantContext.requestId
          });

          // Run the rest of the request in tenant context
          tenantStorage.run(tenantContext, () => {
            this.metrics?.timing(
              'tenant.middleware.duration',
              Date.now() - startTime,
              { tenantId: tenantContext.tenant.id }
            );

            next();
          });
        } else {
          // No tenant context - continue without it
          this.metrics?.timing('tenant.middleware.duration', Date.now() - startTime);
          next();
        }
      } catch (error) {
        this.handleError(error as Error, req, res, next);
      }
    };
  }

  /**
   * Middleware that requires tenant context
   */
  requireTenant() {
    return (req: TenantRequest, res: Response, next: NextFunction): void => {
      if (!req.tenant) {
        const error = new TenantResolutionError(
          'Tenant context is required for this endpoint',
          'TENANT_REQUIRED'
        );
        this.handleError(error, req, res, next);
        return;
      }
      next();
    };
  }

  /**
   * Middleware that validates tenant status
   */
  requireActiveTenant() {
    return (req: TenantRequest, res: Response, next: NextFunction): void => {
      if (!req.tenant) {
        const error = new TenantResolutionError(
          'Tenant context is required',
          'TENANT_REQUIRED'
        );
        this.handleError(error, req, res, next);
        return;
      }

      if (req.tenant.tenant.status !== 'active') {
        const error = new TenantResolutionError(
          `Tenant is ${req.tenant.tenant.status}`,
          'TENANT_INACTIVE'
        );
        this.handleError(error, req, res, next);
        return;
      }

      next();
    };
  }

  /**
   * Middleware that validates tenant tier
   */
  requireTenantTier(allowedTiers: string[]) {
    return (req: TenantRequest, res: Response, next: NextFunction): void => {
      if (!req.tenant) {
        const error = new TenantResolutionError(
          'Tenant context is required',
          'TENANT_REQUIRED'
        );
        this.handleError(error, req, res, next);
        return;
      }

      if (!allowedTiers.includes(req.tenant.tenant.tier)) {
        const error = new TenantResolutionError(
          `Tenant tier '${req.tenant.tenant.tier}' not allowed. Required: ${allowedTiers.join(', ')}`,
          'TENANT_TIER_NOT_ALLOWED'
        );
        this.handleError(error, req, res, next);
        return;
      }

      next();
    };
  }

  /**
   * Middleware that validates tenant features
   */
  requireTenantFeature(requiredFeatures: string[]) {
    return (req: TenantRequest, res: Response, next: NextFunction): void => {
      if (!req.tenant) {
        const error = new TenantResolutionError(
          'Tenant context is required',
          'TENANT_REQUIRED'
        );
        this.handleError(error, req, res, next);
        return;
      }

      const tenantFeatures = req.tenant.tenant.features || [];
      const hasAllFeatures = requiredFeatures.every(feature => 
        tenantFeatures.includes(feature)
      );

      if (!hasAllFeatures) {
        const missingFeatures = requiredFeatures.filter(feature => 
          !tenantFeatures.includes(feature)
        );
        
        const error = new TenantResolutionError(
          `Tenant missing required features: ${missingFeatures.join(', ')}`,
          'TENANT_FEATURE_NOT_AVAILABLE'
        );
        this.handleError(error, req, res, next);
        return;
      }

      next();
    };
  }

  /**
   * Middleware for tenant-specific rate limiting
   */
  tenantRateLimit(getRateLimit: (tenant: TenantContext) => { requests: number; window: number }) {
    const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

    return (req: TenantRequest, res: Response, next: NextFunction): void => {
      if (!req.tenant) {
        next();
        return;
      }

      const tenantId = req.tenant.tenant.id;
      const limits = getRateLimit(req.tenant);
      const now = Date.now();
      const windowMs = limits.window * 1000;

      // Get or create rate limit entry
      let entry = rateLimitStore.get(tenantId);
      if (!entry || now > entry.resetTime) {
        entry = { count: 0, resetTime: now + windowMs };
        rateLimitStore.set(tenantId, entry);
      }

      // Check rate limit
      if (entry.count >= limits.requests) {
        res.status(429).json({
          error: 'Rate limit exceeded',
          code: 'RATE_LIMIT_EXCEEDED',
          details: {
            limit: limits.requests,
            window: limits.window,
            resetTime: entry.resetTime
          }
        });
        return;
      }

      // Increment counter
      entry.count++;

      // Add rate limit headers
      res.set({
        'X-RateLimit-Limit': limits.requests.toString(),
        'X-RateLimit-Remaining': (limits.requests - entry.count).toString(),
        'X-RateLimit-Reset': Math.ceil(entry.resetTime / 1000).toString()
      });

      next();
    };
  }

  /**
   * Error handling for tenant-related errors
   */
  private handleError(
    error: Error,
    req: TenantRequest,
    res: Response,
    next: NextFunction
  ): void {
    this.logger?.error('Tenant middleware error', {
      error: error.message,
      code: error instanceof TenantResolutionError ? error.code : 'UNKNOWN_ERROR',
      requestId: req.tenant?.requestId,
      url: req.url,
      method: req.method
    });

    this.metrics?.increment('tenant.middleware.errors', {
      code: error instanceof TenantResolutionError ? error.code : 'UNKNOWN_ERROR'
    });

    if (error instanceof TenantResolutionError) {
      const statusCode = this.getStatusCodeForError(error.code);
      res.status(statusCode).json({
        error: error.message,
        code: error.code,
        source: error.source,
        identifier: error.identifier,
        timestamp: new Date().toISOString()
      });
      return;
    }

    // For non-tenant errors, pass to next error handler
    next(error);
  }

  /**
   * Get appropriate HTTP status code for error
   */
  private getStatusCodeForError(code: string): number {
    switch (code) {
      case 'TENANT_REQUIRED':
      case 'TENANT_NOT_FOUND':
        return 400;
      case 'TENANT_INACTIVE':
      case 'TENANT_SUSPENDED':
        return 403;
      case 'TENANT_TIER_NOT_ALLOWED':
      case 'TENANT_FEATURE_NOT_AVAILABLE':
        return 403;
      case 'RATE_LIMIT_EXCEEDED':
        return 429;
      default:
        return 500;
    }
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): any {
    return this.resolver.getCacheStats();
  }

  /**
   * Invalidate cache for tenant
   */
  async invalidateCache(identifier: string): Promise<void> {
    await this.resolver.invalidateCache(identifier);
  }
}

/**
 * Get current tenant context from async local storage
 */
export function getCurrentTenant(): TenantContext | undefined {
  return tenantStorage.getStore();
}

/**
 * Get current tenant ID
 */
export function getCurrentTenantId(): string | undefined {
  const context = tenantStorage.getStore();
  return context?.tenant.id;
}

/**
 * Get current tenant slug
 */
export function getCurrentTenantSlug(): string | undefined {
  const context = tenantStorage.getStore();
  return context?.tenant.slug;
}

/**
 * Check if current tenant has feature
 */
export function tenantHasFeature(feature: string): boolean {
  const context = tenantStorage.getStore();
  return context?.tenant.features.includes(feature) || false;
}

/**
 * Check if current tenant has tier
 */
export function tenantHasTier(tier: string): boolean {
  const context = tenantStorage.getStore();
  return context?.tenant.tier === tier;
}

/**
 * Run function with specific tenant context
 */
export function runWithTenantContext<T>(
  context: TenantContext,
  fn: () => T
): T {
  return tenantStorage.run(context, fn);
}

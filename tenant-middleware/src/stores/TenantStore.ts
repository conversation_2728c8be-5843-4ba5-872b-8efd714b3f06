import { TenantInfo, TenantStore } from '../types/tenant';

/**
 * In-memory tenant store implementation
 */
export class MemoryTenantStore implements TenantStore {
  private tenants: Map<string, TenantInfo> = new Map();
  private slugIndex: Map<string, string> = new Map();
  private domainIndex: Map<string, string> = new Map();

  constructor(initialTenants: TenantInfo[] = []) {
    initialTenants.forEach(tenant => this.addTenant(tenant));
  }

  async getTenantById(id: string): Promise<TenantInfo | null> {
    return this.tenants.get(id) || null;
  }

  async getTenantBySlug(slug: string): Promise<TenantInfo | null> {
    const id = this.slugIndex.get(slug);
    return id ? this.tenants.get(id) || null : null;
  }

  async getTenantByDomain(domain: string): Promise<TenantInfo | null> {
    const id = this.domainIndex.get(domain);
    return id ? this.tenants.get(id) || null : null;
  }

  addTenant(tenant: TenantInfo): void {
    this.tenants.set(tenant.id, tenant);
    this.slugIndex.set(tenant.slug, tenant.id);
    if (tenant.domain) {
      this.domainIndex.set(tenant.domain, tenant.id);
    }
  }

  removeTenant(id: string): void {
    const tenant = this.tenants.get(id);
    if (tenant) {
      this.tenants.delete(id);
      this.slugIndex.delete(tenant.slug);
      if (tenant.domain) {
        this.domainIndex.delete(tenant.domain);
      }
    }
  }

  getAllTenants(): TenantInfo[] {
    return Array.from(this.tenants.values());
  }
}

/**
 * Database tenant store implementation
 */
export class DatabaseTenantStore implements TenantStore {
  private db: any; // Database connection
  private cache?: Map<string, TenantInfo>;
  private cacheTtl: number;

  constructor(db: any, options: { enableCache?: boolean; cacheTtl?: number } = {}) {
    this.db = db;
    this.cacheTtl = options.cacheTtl || 300000; // 5 minutes default
    
    if (options.enableCache) {
      this.cache = new Map();
    }
  }

  async getTenantById(id: string): Promise<TenantInfo | null> {
    // Check cache first
    if (this.cache?.has(id)) {
      return this.cache.get(id) || null;
    }

    try {
      const result = await this.db.query(
        'SELECT * FROM tenants WHERE id = $1 AND status != $2',
        [id, 'deleted']
      );

      if (result.rows.length === 0) {
        return null;
      }

      const tenant = this.mapRowToTenant(result.rows[0]);
      
      // Cache the result
      if (this.cache) {
        this.cache.set(id, tenant);
        setTimeout(() => this.cache?.delete(id), this.cacheTtl);
      }

      return tenant;
    } catch (error) {
      console.error('Error fetching tenant by ID:', error);
      return null;
    }
  }

  async getTenantBySlug(slug: string): Promise<TenantInfo | null> {
    const cacheKey = `slug:${slug}`;
    
    // Check cache first
    if (this.cache?.has(cacheKey)) {
      return this.cache.get(cacheKey) || null;
    }

    try {
      const result = await this.db.query(
        'SELECT * FROM tenants WHERE slug = $1 AND status != $2',
        [slug, 'deleted']
      );

      if (result.rows.length === 0) {
        return null;
      }

      const tenant = this.mapRowToTenant(result.rows[0]);
      
      // Cache the result
      if (this.cache) {
        this.cache.set(cacheKey, tenant);
        this.cache.set(tenant.id, tenant);
        setTimeout(() => {
          this.cache?.delete(cacheKey);
          this.cache?.delete(tenant.id);
        }, this.cacheTtl);
      }

      return tenant;
    } catch (error) {
      console.error('Error fetching tenant by slug:', error);
      return null;
    }
  }

  async getTenantByDomain(domain: string): Promise<TenantInfo | null> {
    const cacheKey = `domain:${domain}`;
    
    // Check cache first
    if (this.cache?.has(cacheKey)) {
      return this.cache.get(cacheKey) || null;
    }

    try {
      const result = await this.db.query(
        'SELECT * FROM tenants WHERE domain = $1 AND status != $2',
        [domain, 'deleted']
      );

      if (result.rows.length === 0) {
        return null;
      }

      const tenant = this.mapRowToTenant(result.rows[0]);
      
      // Cache the result
      if (this.cache) {
        this.cache.set(cacheKey, tenant);
        this.cache.set(tenant.id, tenant);
        setTimeout(() => {
          this.cache?.delete(cacheKey);
          this.cache?.delete(tenant.id);
        }, this.cacheTtl);
      }

      return tenant;
    } catch (error) {
      console.error('Error fetching tenant by domain:', error);
      return null;
    }
  }

  async cacheTenant(tenant: TenantInfo): Promise<void> {
    if (this.cache) {
      this.cache.set(tenant.id, tenant);
      this.cache.set(`slug:${tenant.slug}`, tenant);
      if (tenant.domain) {
        this.cache.set(`domain:${tenant.domain}`, tenant);
      }
    }
  }

  async invalidateCache(identifier: string): Promise<void> {
    if (this.cache) {
      // Try to get tenant first to invalidate all cache keys
      const tenant = this.cache.get(identifier) || 
                   this.cache.get(`slug:${identifier}`) || 
                   this.cache.get(`domain:${identifier}`);

      if (tenant) {
        this.cache.delete(tenant.id);
        this.cache.delete(`slug:${tenant.slug}`);
        if (tenant.domain) {
          this.cache.delete(`domain:${tenant.domain}`);
        }
      }

      // Also try direct deletion
      this.cache.delete(identifier);
      this.cache.delete(`slug:${identifier}`);
      this.cache.delete(`domain:${identifier}`);
    }
  }

  private mapRowToTenant(row: any): TenantInfo {
    return {
      id: row.id,
      slug: row.slug,
      name: row.name,
      domain: row.domain,
      status: row.status,
      tier: row.tier,
      features: row.features || [],
      settings: row.settings || {},
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    };
  }
}

/**
 * Redis tenant store implementation
 */
export class RedisTenantStore implements TenantStore {
  private redis: any; // Redis client
  private fallbackStore?: TenantStore;
  private keyPrefix: string;
  private ttl: number;

  constructor(
    redis: any,
    options: {
      fallbackStore?: TenantStore;
      keyPrefix?: string;
      ttl?: number;
    } = {}
  ) {
    this.redis = redis;
    this.fallbackStore = options.fallbackStore;
    this.keyPrefix = options.keyPrefix || 'tenant:';
    this.ttl = options.ttl || 3600; // 1 hour default
  }

  async getTenantById(id: string): Promise<TenantInfo | null> {
    try {
      const key = `${this.keyPrefix}id:${id}`;
      const cached = await this.redis.get(key);
      
      if (cached) {
        return JSON.parse(cached);
      }

      // Try fallback store
      if (this.fallbackStore) {
        const tenant = await this.fallbackStore.getTenantById(id);
        if (tenant) {
          await this.cacheTenant(tenant);
          return tenant;
        }
      }

      return null;
    } catch (error) {
      console.error('Error fetching tenant from Redis:', error);
      
      // Try fallback store on Redis error
      if (this.fallbackStore) {
        return this.fallbackStore.getTenantById(id);
      }
      
      return null;
    }
  }

  async getTenantBySlug(slug: string): Promise<TenantInfo | null> {
    try {
      const key = `${this.keyPrefix}slug:${slug}`;
      const cached = await this.redis.get(key);
      
      if (cached) {
        return JSON.parse(cached);
      }

      // Try fallback store
      if (this.fallbackStore) {
        const tenant = await this.fallbackStore.getTenantBySlug(slug);
        if (tenant) {
          await this.cacheTenant(tenant);
          return tenant;
        }
      }

      return null;
    } catch (error) {
      console.error('Error fetching tenant from Redis:', error);
      
      // Try fallback store on Redis error
      if (this.fallbackStore) {
        return this.fallbackStore.getTenantBySlug(slug);
      }
      
      return null;
    }
  }

  async getTenantByDomain(domain: string): Promise<TenantInfo | null> {
    try {
      const key = `${this.keyPrefix}domain:${domain}`;
      const cached = await this.redis.get(key);
      
      if (cached) {
        return JSON.parse(cached);
      }

      // Try fallback store
      if (this.fallbackStore) {
        const tenant = await this.fallbackStore.getTenantByDomain(domain);
        if (tenant) {
          await this.cacheTenant(tenant);
          return tenant;
        }
      }

      return null;
    } catch (error) {
      console.error('Error fetching tenant from Redis:', error);
      
      // Try fallback store on Redis error
      if (this.fallbackStore) {
        return this.fallbackStore.getTenantByDomain(domain);
      }
      
      return null;
    }
  }

  async cacheTenant(tenant: TenantInfo): Promise<void> {
    try {
      const serialized = JSON.stringify(tenant);
      const pipeline = this.redis.pipeline();

      pipeline.setex(`${this.keyPrefix}id:${tenant.id}`, this.ttl, serialized);
      pipeline.setex(`${this.keyPrefix}slug:${tenant.slug}`, this.ttl, serialized);
      
      if (tenant.domain) {
        pipeline.setex(`${this.keyPrefix}domain:${tenant.domain}`, this.ttl, serialized);
      }

      await pipeline.exec();
    } catch (error) {
      console.error('Error caching tenant in Redis:', error);
    }
  }

  async invalidateCache(identifier: string): Promise<void> {
    try {
      const keys = [
        `${this.keyPrefix}id:${identifier}`,
        `${this.keyPrefix}slug:${identifier}`,
        `${this.keyPrefix}domain:${identifier}`
      ];

      await this.redis.del(...keys);
    } catch (error) {
      console.error('Error invalidating tenant cache in Redis:', error);
    }
  }
}

export interface TenantInfo {
  id: string;
  slug: string;
  name: string;
  domain?: string;
  status: 'active' | 'inactive' | 'suspended';
  tier: 'free' | 'pro' | 'enterprise';
  features: string[];
  settings: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface TenantContext {
  tenant: TenantInfo;
  user?: {
    id: string;
    email: string;
    roles: string[];
    permissions: string[];
  };
  requestId: string;
  timestamp: Date;
  source: 'subdomain' | 'header' | 'jwt' | 'default';
}

export interface TenantResolutionConfig {
  // Resolution methods in order of priority
  methods: ('subdomain' | 'header' | 'jwt' | 'path')[];
  
  // Subdomain configuration
  subdomain?: {
    enabled: boolean;
    baseDomain: string; // e.g., 'myapp.com'
    excludeSubdomains?: string[]; // e.g., ['www', 'api', 'admin']
  };
  
  // Header configuration
  header?: {
    enabled: boolean;
    name: string; // e.g., 'X-Tenant-ID'
    allowMultiple?: boolean;
  };
  
  // JWT configuration
  jwt?: {
    enabled: boolean;
    secret: string;
    algorithm?: string;
    tenantClaim: string; // e.g., 'tenant_id' or 'tenant_slug'
    userClaim?: string; // e.g., 'user_id'
  };
  
  // Path configuration
  path?: {
    enabled: boolean;
    pattern: string; // e.g., '/api/v1/:tenant/*'
    paramName: string; // e.g., 'tenant'
  };
  
  // Default tenant (fallback)
  defaultTenant?: string;
  
  // Caching
  cache?: {
    enabled: boolean;
    ttl: number; // seconds
    maxSize: number;
  };
  
  // Error handling
  onTenantNotFound?: (identifier: string, source: string) => TenantInfo | null;
  onError?: (error: Error, req: any) => void;
  
  // Validation
  validateTenant?: (tenant: TenantInfo, req: any) => boolean;
  
  // Required tenant (throw error if not found)
  required?: boolean;
}

export interface TenantStore {
  getTenantById(id: string): Promise<TenantInfo | null>;
  getTenantBySlug(slug: string): Promise<TenantInfo | null>;
  getTenantByDomain(domain: string): Promise<TenantInfo | null>;
  cacheTenant?(tenant: TenantInfo): Promise<void>;
  invalidateCache?(identifier: string): Promise<void>;
}

export interface TenantRequest extends Request {
  tenant?: TenantContext;
  tenantId?: string;
  tenantSlug?: string;
}

export interface TenantResponse extends Response {
  tenant?: TenantContext;
}

export class TenantResolutionError extends Error {
  public code: string;
  public source?: string;
  public identifier?: string;
  
  constructor(
    message: string,
    code: string = 'TENANT_RESOLUTION_ERROR',
    source?: string,
    identifier?: string
  ) {
    super(message);
    this.name = 'TenantResolutionError';
    this.code = code;
    this.source = source;
    this.identifier = identifier;
  }
}

export interface TenantMetrics {
  resolutionAttempts: number;
  resolutionSuccesses: number;
  resolutionFailures: number;
  cacheHits: number;
  cacheMisses: number;
  resolutionTimeMs: number;
  errorsByCode: Record<string, number>;
  resolutionBySource: Record<string, number>;
}

export interface TenantMiddlewareOptions {
  config: TenantResolutionConfig;
  store: TenantStore;
  logger?: {
    info: (message: string, meta?: any) => void;
    warn: (message: string, meta?: any) => void;
    error: (message: string, meta?: any) => void;
    debug: (message: string, meta?: any) => void;
  };
  metrics?: {
    increment: (metric: string, tags?: Record<string, string>) => void;
    timing: (metric: string, value: number, tags?: Record<string, string>) => void;
  };
}

import express from 'express';
import { Pool } from 'pg';
import Redis from 'ioredis';
import winston from 'winston';
import {
  TenantMiddleware,
  DatabaseTenantStore,
  RedisTenantStore,
  TenantResolutionConfig,
  TenantRequest,
  getCurrentTenant,
  runWithTenantContext
} from '../src/index';

// Initialize logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'tenant.log' })
  ]
});

// Initialize database connection
const db = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432'),
  database: process.env.DB_NAME || 'myapp',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'password'
});

// Initialize Redis
const redis = new Redis({
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
  retryDelayOnFailover: 100
});

// Metrics collection (mock implementation)
const metrics = {
  increment: (metric: string, tags?: Record<string, string>) => {
    logger.info('Metric increment', { metric, tags });
  },
  timing: (metric: string, value: number, tags?: Record<string, string>) => {
    logger.info('Metric timing', { metric, value, tags });
  }
};

// Advanced tenant resolution configuration
const tenantConfig: TenantResolutionConfig = {
  methods: ['jwt', 'header', 'subdomain', 'path'],
  
  subdomain: {
    enabled: true,
    baseDomain: process.env.BASE_DOMAIN || 'myapp.com',
    excludeSubdomains: ['www', 'api', 'admin', 'docs']
  },
  
  header: {
    enabled: true,
    name: 'X-Tenant-ID',
    allowMultiple: false
  },
  
  jwt: {
    enabled: true,
    secret: process.env.JWT_SECRET || 'your-jwt-secret',
    algorithm: 'HS256',
    tenantClaim: 'tenant_id',
    userClaim: 'user_id'
  },
  
  path: {
    enabled: true,
    pattern: '/api/v1/:tenant/*',
    paramName: 'tenant'
  },
  
  cache: {
    enabled: true,
    ttl: 300, // 5 minutes
    maxSize: 5000
  },
  
  required: false,
  
  // Custom tenant not found handler
  onTenantNotFound: (identifier: string, source: string) => {
    logger.warn('Tenant not found', { identifier, source });
    return null;
  },
  
  // Custom error handler
  onError: (error: Error, req: any) => {
    logger.error('Tenant resolution error', {
      error: error.message,
      url: req.url,
      method: req.method,
      headers: req.headers
    });
  },
  
  // Custom tenant validator
  validateTenant: (tenant, req) => {
    // Check if tenant is active and not suspended
    if (tenant.status !== 'active') {
      logger.warn('Inactive tenant access attempt', {
        tenantId: tenant.id,
        status: tenant.status,
        ip: req.ip
      });
      return false;
    }
    
    // Additional custom validation logic
    return true;
  }
};

// Create tenant stores with fallback chain
const databaseStore = new DatabaseTenantStore(db, {
  enableCache: true,
  cacheTtl: 300000 // 5 minutes
});

const tenantStore = new RedisTenantStore(redis, {
  fallbackStore: databaseStore,
  keyPrefix: 'tenant:',
  ttl: 3600 // 1 hour
});

// Create tenant middleware
const tenantMiddleware = new TenantMiddleware({
  config: tenantConfig,
  store: tenantStore,
  logger,
  metrics
});

const app = express();

// Basic middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Request ID middleware
app.use((req: any, res, next) => {
  req.requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  res.set('X-Request-ID', req.requestId);
  next();
});

// Apply tenant middleware
app.use(tenantMiddleware.middleware());

// Tenant context logging middleware
app.use((req: TenantRequest, res, next) => {
  if (req.tenant) {
    logger.info('Request with tenant context', {
      requestId: req.tenant.requestId,
      tenantId: req.tenant.tenant.id,
      tenantSlug: req.tenant.tenant.slug,
      method: req.method,
      url: req.url,
      userAgent: req.get('User-Agent'),
      ip: req.ip
    });
  }
  next();
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    tenant: req.tenant ? {
      id: req.tenant.tenant.id,
      name: req.tenant.tenant.name
    } : null
  });
});

// Multi-tenant API routes
const apiRouter = express.Router();

// Middleware that requires tenant for all API routes
apiRouter.use(tenantMiddleware.requireActiveTenant());

// Get tenant information
apiRouter.get('/tenant', (req: TenantRequest, res) => {
  const tenant = getCurrentTenant();
  res.json({
    tenant: {
      id: tenant!.tenant.id,
      slug: tenant!.tenant.slug,
      name: tenant!.tenant.name,
      tier: tenant!.tenant.tier,
      features: tenant!.tenant.features,
      settings: tenant!.tenant.settings
    },
    user: tenant!.user,
    context: {
      requestId: tenant!.requestId,
      source: tenant!.source,
      timestamp: tenant!.timestamp
    }
  });
});

// Tenant-specific data endpoint
apiRouter.get('/data', async (req: TenantRequest, res) => {
  try {
    const tenantId = getCurrentTenant()!.tenant.id;
    
    // Example: Fetch tenant-specific data from database
    const result = await db.query(
      'SELECT * FROM user_data WHERE tenant_id = $1 LIMIT 10',
      [tenantId]
    );
    
    res.json({
      data: result.rows,
      count: result.rowCount,
      tenant: tenantId
    });
  } catch (error) {
    logger.error('Error fetching tenant data', { error });
    res.status(500).json({
      error: 'Failed to fetch data'
    });
  }
});

// Feature-gated endpoint
apiRouter.get('/premium-analytics',
  tenantMiddleware.requireTenantFeature(['premium_analytics']),
  (req: TenantRequest, res) => {
    res.json({
      message: 'Premium analytics data',
      data: {
        advanced_metrics: true,
        custom_reports: true,
        real_time_data: true
      }
    });
  }
);

// Tier-specific endpoint
apiRouter.get('/enterprise-features',
  tenantMiddleware.requireTenantTier(['enterprise']),
  (req: TenantRequest, res) => {
    res.json({
      message: 'Enterprise features',
      features: [
        'white_labeling',
        'sso_integration',
        'advanced_security',
        'dedicated_support'
      ]
    });
  }
);

// Rate-limited endpoint with tier-based limits
apiRouter.get('/api-calls',
  tenantMiddleware.tenantRateLimit((tenant) => {
    const tierLimits = {
      free: { requests: 100, window: 3600 },
      pro: { requests: 1000, window: 3600 },
      enterprise: { requests: 10000, window: 3600 }
    };
    return tierLimits[tenant.tenant.tier] || tierLimits.free;
  }),
  (req: TenantRequest, res) => {
    res.json({
      message: 'API call successful',
      tier: req.tenant!.tenant.tier,
      timestamp: new Date().toISOString()
    });
  }
);

// Background job endpoint (demonstrates context propagation)
apiRouter.post('/background-job', async (req: TenantRequest, res) => {
  const tenant = getCurrentTenant()!;
  
  // Simulate background job that needs tenant context
  setTimeout(() => {
    runWithTenantContext(tenant, () => {
      const currentTenant = getCurrentTenant();
      logger.info('Background job executed', {
        tenantId: currentTenant!.tenant.id,
        jobId: Math.random().toString(36).substr(2, 9)
      });
    });
  }, 1000);
  
  res.json({
    message: 'Background job queued',
    tenantId: tenant.tenant.id
  });
});

// Mount API router
app.use('/api/v1', apiRouter);

// Path-based tenant routing (alternative approach)
app.use('/tenant/:tenant/*', (req: TenantRequest, res, next) => {
  // Tenant is already resolved from path parameter
  if (!req.tenant) {
    return res.status(400).json({
      error: 'Invalid tenant in path'
    });
  }
  next();
});

app.get('/tenant/:tenant/dashboard', (req: TenantRequest, res) => {
  res.json({
    message: `Dashboard for ${req.tenant!.tenant.name}`,
    tenant: req.tenant!.tenant
  });
});

// Admin endpoints
app.get('/admin/tenants/cache-stats', (req, res) => {
  const stats = tenantMiddleware.getCacheStats();
  res.json(stats);
});

app.post('/admin/tenants/:identifier/invalidate-cache', async (req, res) => {
  const { identifier } = req.params;
  await tenantMiddleware.invalidateCache(identifier);
  res.json({
    message: 'Cache invalidated',
    identifier
  });
});

// Error handling
app.use((error: any, req: TenantRequest, res: any, next: any) => {
  logger.error('Unhandled error', {
    error: error.message,
    stack: error.stack,
    tenantId: req.tenant?.tenant.id,
    requestId: req.tenant?.requestId,
    url: req.url,
    method: req.method
  });

  res.status(500).json({
    error: 'Internal server error',
    requestId: req.tenant?.requestId
  });
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  logger.info('Shutting down gracefully');
  await db.end();
  await redis.quit();
  process.exit(0);
});

const PORT = process.env.PORT || 3000;

app.listen(PORT, () => {
  logger.info(`Advanced multi-tenant server started on port ${PORT}`);
});

export default app;

import express from 'express';
import jwt from 'jsonwebtoken';
import {
  createTenantMiddleware,
  createSampleTenants,
  getCurrentTenant,
  getCurrentTenantId,
  tenantHasFeature,
  TenantRequest
} from '../src/index';

const app = express();

// Basic middleware
app.use(express.json());

// Create tenant middleware with sample data
const tenantMiddleware = createTenantMiddleware({
  tenants: createSampleTenants(),
  config: {
    methods: ['subdomain', 'header', 'jwt'],
    subdomain: {
      enabled: true,
      baseDomain: 'localhost:3000',
      excludeSubdomains: ['www', 'api']
    },
    header: {
      enabled: true,
      name: 'X-Tenant-ID'
    },
    jwt: {
      enabled: true,
      secret: 'your-secret-key',
      tenantClaim: 'tenant_id',
      userClaim: 'user_id'
    },
    required: false
  },
  logger: console
});

// Apply tenant middleware
app.use(tenantMiddleware.middleware());

// Public endpoint (no tenant required)
app.get('/', (req: TenantRequest, res) => {
  res.json({
    message: 'Welcome to the multi-tenant API',
    tenant: req.tenant ? {
      id: req.tenant.tenant.id,
      name: req.tenant.tenant.name,
      tier: req.tenant.tenant.tier
    } : null
  });
});

// Tenant-aware endpoint
app.get('/dashboard', (req: TenantRequest, res) => {
  const tenant = getCurrentTenant();
  
  if (!tenant) {
    return res.status(400).json({
      error: 'Tenant context required'
    });
  }

  res.json({
    message: `Welcome to ${tenant.tenant.name} dashboard`,
    tenant: {
      id: tenant.tenant.id,
      name: tenant.tenant.name,
      tier: tenant.tenant.tier,
      features: tenant.tenant.features
    },
    user: tenant.user
  });
});

// Endpoint that requires active tenant
app.get('/api/data', 
  tenantMiddleware.requireActiveTenant(),
  (req: TenantRequest, res) => {
    res.json({
      message: 'Tenant-specific data',
      tenantId: getCurrentTenantId(),
      data: [
        { id: 1, name: 'Item 1' },
        { id: 2, name: 'Item 2' }
      ]
    });
  }
);

// Endpoint that requires specific tenant tier
app.get('/api/premium-features',
  tenantMiddleware.requireTenantTier(['pro', 'enterprise']),
  (req: TenantRequest, res) => {
    res.json({
      message: 'Premium features available',
      features: ['advanced_analytics', 'custom_reports']
    });
  }
);

// Endpoint that requires specific feature
app.get('/api/analytics',
  tenantMiddleware.requireTenantFeature(['analytics']),
  (req: TenantRequest, res) => {
    res.json({
      message: 'Analytics data',
      hasAdvancedAnalytics: tenantHasFeature('advanced_analytics'),
      data: {
        views: 1234,
        users: 567,
        revenue: 8900
      }
    });
  }
);

// Endpoint with tenant-specific rate limiting
app.get('/api/limited',
  tenantMiddleware.tenantRateLimit((tenant) => {
    // Different limits based on tier
    const limits = {
      free: { requests: 10, window: 60 },
      pro: { requests: 100, window: 60 },
      enterprise: { requests: 1000, window: 60 }
    };
    return limits[tenant.tenant.tier] || limits.free;
  }),
  (req: TenantRequest, res) => {
    res.json({
      message: 'Rate limited endpoint',
      tenantTier: req.tenant?.tenant.tier
    });
  }
);

// Authentication endpoint (generates JWT with tenant info)
app.post('/auth/login', (req, res) => {
  const { email, password, tenantSlug } = req.body;
  
  // Mock authentication
  if (email === '<EMAIL>' && password === 'password') {
    // Find tenant by slug
    const tenants = createSampleTenants();
    const tenant = tenants.find(t => t.slug === tenantSlug);
    
    if (!tenant) {
      return res.status(400).json({
        error: 'Invalid tenant'
      });
    }

    const token = jwt.sign({
      user_id: '123',
      email: email,
      tenant_id: tenant.id,
      roles: ['user'],
      permissions: ['read', 'write']
    }, 'your-secret-key', { expiresIn: '1h' });

    res.json({
      token,
      user: {
        id: '123',
        email: email
      },
      tenant: {
        id: tenant.id,
        slug: tenant.slug,
        name: tenant.name
      }
    });
  } else {
    res.status(401).json({
      error: 'Invalid credentials'
    });
  }
});

// Admin endpoints
app.get('/admin/cache-stats', (req, res) => {
  const stats = tenantMiddleware.getCacheStats();
  res.json(stats);
});

app.post('/admin/invalidate-cache/:identifier', async (req, res) => {
  const { identifier } = req.params;
  await tenantMiddleware.invalidateCache(identifier);
  res.json({ message: 'Cache invalidated' });
});

// Error handling middleware
app.use((error: any, req: any, res: any, next: any) => {
  console.error('Error:', error);
  res.status(500).json({
    error: 'Internal server error',
    message: error.message
  });
});

const PORT = process.env.PORT || 3000;

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log('\nTesting URLs:');
  console.log('- http://localhost:3000/ (no tenant)');
  console.log('- http://acme.localhost:3000/ (subdomain tenant)');
  console.log('- http://localhost:3000/dashboard (with X-Tenant-ID header)');
  console.log('- http://localhost:3000/api/data (requires active tenant)');
  console.log('\nSample tenants:');
  createSampleTenants().forEach(tenant => {
    console.log(`- ${tenant.slug}: ${tenant.name} (${tenant.tier})`);
  });
  console.log('\nTo test with JWT:');
  console.log('1. POST /auth/login with { "email": "<EMAIL>", "password": "password", "tenantSlug": "acme" }');
  console.log('2. Use returned token in Authorization: Bearer <token> header');
});

export default app;

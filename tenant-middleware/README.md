# Multi-Tenant Express Middleware

A comprehensive Node.js Express middleware for handling tenant resolution from subdomain, header, JWT token, or URL path with tenant context propagation throughout the request lifecycle.

## Features

- **Multiple Resolution Methods**: Subdomain, header, JWT token, and URL path
- **Flexible Configuration**: Configurable resolution priority and fallback options
- **Context Propagation**: Async local storage for tenant context throughout request lifecycle
- **Caching**: Built-in LRU cache with TTL support
- **Multiple Store Backends**: Memory, Database (PostgreSQL), and Redis implementations
- **Rate Limiting**: Tenant-specific rate limiting with tier-based limits
- **Feature Gates**: Tenant feature and tier-based access control
- **Comprehensive Logging**: Structured logging with tenant context
- **Metrics Support**: Built-in metrics collection hooks
- **TypeScript Support**: Full TypeScript definitions

## Installation

```bash
npm install @yourorg/tenant-middleware
```

## Quick Start

### Basic Usage

```typescript
import express from 'express';
import { createTenantMiddleware, createSampleTenants } from '@yourorg/tenant-middleware';

const app = express();

// Create tenant middleware with sample data
const tenantMiddleware = createTenantMiddleware({
  tenants: createSampleTenants(),
  config: {
    methods: ['subdomain', 'header', 'jwt'],
    subdomain: {
      enabled: true,
      baseDomain: 'myapp.com'
    },
    header: {
      enabled: true,
      name: 'X-Tenant-ID'
    },
    jwt: {
      enabled: true,
      secret: 'your-secret-key',
      tenantClaim: 'tenant_id'
    }
  }
});

// Apply middleware
app.use(tenantMiddleware.middleware());

// Tenant-aware endpoint
app.get('/dashboard', (req, res) => {
  if (!req.tenant) {
    return res.status(400).json({ error: 'Tenant required' });
  }
  
  res.json({
    message: `Welcome to ${req.tenant.tenant.name}`,
    tenant: req.tenant.tenant
  });
});

app.listen(3000);
```

### Advanced Configuration

```typescript
import { TenantMiddleware, DatabaseTenantStore, RedisTenantStore } from '@yourorg/tenant-middleware';

// Database store with Redis cache
const databaseStore = new DatabaseTenantStore(dbPool);
const tenantStore = new RedisTenantStore(redis, {
  fallbackStore: databaseStore,
  ttl: 3600
});

const tenantMiddleware = new TenantMiddleware({
  config: {
    methods: ['jwt', 'header', 'subdomain'],
    cache: {
      enabled: true,
      ttl: 300,
      maxSize: 1000
    },
    required: true,
    validateTenant: (tenant, req) => {
      return tenant.status === 'active';
    }
  },
  store: tenantStore,
  logger: winston,
  metrics: metricsClient
});
```

## Resolution Methods

### 1. Subdomain Resolution

Extracts tenant from subdomain:

```typescript
// acme.myapp.com -> tenant: "acme"
config: {
  subdomain: {
    enabled: true,
    baseDomain: 'myapp.com',
    excludeSubdomains: ['www', 'api', 'admin']
  }
}
```

### 2. Header Resolution

Extracts tenant from HTTP header:

```typescript
// X-Tenant-ID: acme -> tenant: "acme"
config: {
  header: {
    enabled: true,
    name: 'X-Tenant-ID',
    allowMultiple: false
  }
}
```

### 3. JWT Token Resolution

Extracts tenant from JWT token:

```typescript
// Authorization: Bearer <token> -> tenant from token claim
config: {
  jwt: {
    enabled: true,
    secret: 'your-secret-key',
    tenantClaim: 'tenant_id',
    userClaim: 'user_id'
  }
}
```

### 4. URL Path Resolution

Extracts tenant from URL path parameter:

```typescript
// /api/v1/acme/users -> tenant: "acme"
config: {
  path: {
    enabled: true,
    pattern: '/api/v1/:tenant/*',
    paramName: 'tenant'
  }
}
```

## Middleware Functions

### Basic Middleware

```typescript
// Apply tenant resolution
app.use(tenantMiddleware.middleware());

// Require tenant context
app.use('/api', tenantMiddleware.requireTenant());

// Require active tenant
app.use('/api', tenantMiddleware.requireActiveTenant());
```

### Feature and Tier Gates

```typescript
// Require specific tenant tier
app.get('/premium', 
  tenantMiddleware.requireTenantTier(['pro', 'enterprise']),
  handler
);

// Require specific features
app.get('/analytics',
  tenantMiddleware.requireTenantFeature(['analytics']),
  handler
);
```

### Rate Limiting

```typescript
// Tenant-specific rate limiting
app.use('/api',
  tenantMiddleware.tenantRateLimit((tenant) => ({
    requests: tenant.tenant.tier === 'enterprise' ? 1000 : 100,
    window: 3600
  }))
);
```

## Context Access

### In Request Handlers

```typescript
app.get('/data', (req, res) => {
  // Access via request object
  const tenantId = req.tenantId;
  const tenant = req.tenant;
  
  res.json({ tenantId, tenant });
});
```

### Using Utility Functions

```typescript
import { getCurrentTenant, getCurrentTenantId, tenantHasFeature } from '@yourorg/tenant-middleware';

app.get('/features', (req, res) => {
  const tenant = getCurrentTenant();
  const hasAnalytics = tenantHasFeature('analytics');
  
  res.json({ tenant, hasAnalytics });
});
```

### Context Propagation

```typescript
import { runWithTenantContext } from '@yourorg/tenant-middleware';

// Run async operations with tenant context
setTimeout(() => {
  runWithTenantContext(tenantContext, () => {
    const currentTenant = getCurrentTenant();
    // Process with tenant context
  });
}, 1000);
```

## Store Implementations

### Memory Store

```typescript
import { MemoryTenantStore } from '@yourorg/tenant-middleware';

const store = new MemoryTenantStore([
  {
    id: '1',
    slug: 'acme',
    name: 'Acme Corp',
    status: 'active',
    tier: 'enterprise',
    features: ['analytics', 'api_access']
  }
]);
```

### Database Store

```typescript
import { DatabaseTenantStore } from '@yourorg/tenant-middleware';

const store = new DatabaseTenantStore(pgPool, {
  enableCache: true,
  cacheTtl: 300000
});
```

### Redis Store

```typescript
import { RedisTenantStore } from '@yourorg/tenant-middleware';

const store = new RedisTenantStore(redisClient, {
  fallbackStore: databaseStore,
  keyPrefix: 'tenant:',
  ttl: 3600
});
```

## Error Handling

```typescript
import { TenantResolutionError } from '@yourorg/tenant-middleware';

app.use((error, req, res, next) => {
  if (error instanceof TenantResolutionError) {
    res.status(400).json({
      error: error.message,
      code: error.code,
      source: error.source
    });
    return;
  }
  next(error);
});
```

## Configuration Options

```typescript
interface TenantResolutionConfig {
  methods: ('subdomain' | 'header' | 'jwt' | 'path')[];
  subdomain?: {
    enabled: boolean;
    baseDomain: string;
    excludeSubdomains?: string[];
  };
  header?: {
    enabled: boolean;
    name: string;
    allowMultiple?: boolean;
  };
  jwt?: {
    enabled: boolean;
    secret: string;
    algorithm?: string;
    tenantClaim: string;
    userClaim?: string;
  };
  path?: {
    enabled: boolean;
    pattern: string;
    paramName: string;
  };
  defaultTenant?: string;
  cache?: {
    enabled: boolean;
    ttl: number;
    maxSize: number;
  };
  onTenantNotFound?: (identifier: string, source: string) => TenantInfo | null;
  onError?: (error: Error, req: any) => void;
  validateTenant?: (tenant: TenantInfo, req: any) => boolean;
  required?: boolean;
}
```

## Examples

See the `examples/` directory for complete working examples:

- `basicUsage.ts` - Simple setup with memory store
- `advancedUsage.ts` - Production setup with database and Redis

## License

MIT

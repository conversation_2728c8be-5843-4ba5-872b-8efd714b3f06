{"name": "@yourorg/tenant-middleware", "version": "1.0.0", "description": "Comprehensive Node.js Express middleware for multi-tenant applications with tenant resolution from subdomain, header, JWT token, or URL path", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "ts-node examples/basicUsage.ts", "dev:advanced": "ts-node examples/advancedUsage.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "clean": "<PERSON><PERSON><PERSON> dist", "prepublishOnly": "npm run clean && npm run build"}, "keywords": ["express", "middleware", "multi-tenant", "tenant", "subdomain", "jwt", "header", "context", "nodejs", "typescript"], "author": "Your Organization", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yourorg/tenant-middleware.git"}, "bugs": {"url": "https://github.com/yourorg/tenant-middleware/issues"}, "homepage": "https://github.com/yourorg/tenant-middleware#readme", "dependencies": {"jsonwebtoken": "^9.0.2", "lru-cache": "^10.0.1"}, "peerDependencies": {"express": "^4.18.0", "ioredis": "^5.3.0", "pg": "^8.11.0", "winston": "^3.10.0"}, "peerDependenciesMeta": {"ioredis": {"optional": true}, "pg": {"optional": true}, "winston": {"optional": true}}, "devDependencies": {"@types/express": "^4.17.17", "@types/jest": "^29.5.5", "@types/jsonwebtoken": "^9.0.3", "@types/node": "^20.6.0", "@types/pg": "^8.10.2", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "eslint": "^8.49.0", "express": "^4.18.2", "ioredis": "^5.3.2", "jest": "^29.7.0", "pg": "^8.11.3", "rimraf": "^5.0.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.2.2", "winston": "^3.10.0"}, "files": ["dist", "README.md", "LICENSE"], "engines": {"node": ">=16.0.0"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/examples/**"], "testMatch": ["**/__tests__/**/*.test.ts"]}, "eslintConfig": {"parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "extends": ["eslint:recommended", "@typescript-eslint/recommended"], "rules": {"@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-unused-vars": "error"}}}
-- Helper Functions and Views for A/B Testing Platform
-- This file contains useful functions and views for common operations

-- Function to assign users to experiments based on targeting rules
CREATE OR REPLACE FUNCTION assign_user_to_experiment(
    p_tenant_id UUID,
    p_experiment_id UUID,
    p_user_id VARCHAR(255),
    p_user_attributes JSONB DEFAULT '{}'
) RETURNS UUID AS $$
DECLARE
    v_variant_id UUID;
    v_experiment_record RECORD;
    v_targeting_passed BOOLEAN := true;
    v_rule RECORD;
    v_hash_value INTEGER;
    v_bucket DECIMAL;
    v_cumulative_weight DECIMAL := 0;
    v_variant RECORD;
BEGIN
    -- Check if experiment exists and is active
    SELECT * INTO v_experiment_record 
    FROM experiments 
    WHERE id = p_experiment_id 
      AND tenant_id = p_tenant_id 
      AND status = 'active'
      AND (start_date IS NULL OR start_date <= CURRENT_TIMESTAMP)
      AND (end_date IS NULL OR end_date > CURRENT_TIMESTAMP);
    
    IF NOT FOUND THEN
        RETURN NULL;
    END IF;
    
    -- Check if user already assigned
    SELECT variant_id INTO v_variant_id
    FROM user_assignments
    WHERE tenant_id = p_tenant_id 
      AND experiment_id = p_experiment_id 
      AND user_id = p_user_id;
    
    IF FOUND THEN
        RETURN v_variant_id;
    END IF;
    
    -- Check targeting rules
    FOR v_rule IN 
        SELECT * FROM targeting_rules 
        WHERE tenant_id = p_tenant_id 
          AND experiment_id = p_experiment_id 
          AND is_active = true
        ORDER BY priority ASC
    LOOP
        v_targeting_passed := false;
        
        CASE v_rule.operator
            WHEN 'equals' THEN
                IF v_rule.value_text IS NOT NULL THEN
                    v_targeting_passed := (p_user_attributes->>v_rule.attribute_name = v_rule.value_text);
                ELSIF v_rule.value_number IS NOT NULL THEN
                    v_targeting_passed := ((p_user_attributes->>v_rule.attribute_name)::DECIMAL = v_rule.value_number);
                ELSIF v_rule.value_boolean IS NOT NULL THEN
                    v_targeting_passed := ((p_user_attributes->>v_rule.attribute_name)::BOOLEAN = v_rule.value_boolean);
                END IF;
            WHEN 'not_equals' THEN
                IF v_rule.value_text IS NOT NULL THEN
                    v_targeting_passed := (p_user_attributes->>v_rule.attribute_name != v_rule.value_text);
                END IF;
            WHEN 'in' THEN
                IF v_rule.value_list IS NOT NULL THEN
                    v_targeting_passed := (p_user_attributes->>v_rule.attribute_name = ANY(v_rule.value_list));
                END IF;
            WHEN 'not_in' THEN
                IF v_rule.value_list IS NOT NULL THEN
                    v_targeting_passed := NOT (p_user_attributes->>v_rule.attribute_name = ANY(v_rule.value_list));
                END IF;
            WHEN 'greater_than' THEN
                IF v_rule.value_number IS NOT NULL THEN
                    v_targeting_passed := ((p_user_attributes->>v_rule.attribute_name)::DECIMAL > v_rule.value_number);
                END IF;
            WHEN 'less_than' THEN
                IF v_rule.value_number IS NOT NULL THEN
                    v_targeting_passed := ((p_user_attributes->>v_rule.attribute_name)::DECIMAL < v_rule.value_number);
                END IF;
            WHEN 'contains' THEN
                IF v_rule.value_text IS NOT NULL THEN
                    v_targeting_passed := (p_user_attributes->>v_rule.attribute_name ILIKE '%' || v_rule.value_text || '%');
                END IF;
        END CASE;
        
        IF NOT v_targeting_passed THEN
            -- User doesn't match targeting rules, exclude from experiment
            INSERT INTO user_assignments (tenant_id, experiment_id, variant_id, user_id, user_attributes, is_excluded, exclusion_reason)
            VALUES (p_tenant_id, p_experiment_id, NULL, p_user_id, p_user_attributes, true, 'Failed targeting rule: ' || v_rule.name);
            RETURN NULL;
        END IF;
    END LOOP;
    
    -- Generate hash for consistent bucketing
    v_hash_value := abs(hashtext(p_user_id || p_experiment_id::text));
    v_bucket := (v_hash_value % 10000) / 10000.0;
    
    -- Check if user falls within traffic allocation
    IF v_bucket >= v_experiment_record.traffic_allocation THEN
        INSERT INTO user_assignments (tenant_id, experiment_id, variant_id, user_id, user_attributes, is_excluded, exclusion_reason)
        VALUES (p_tenant_id, p_experiment_id, NULL, p_user_id, p_user_attributes, true, 'Outside traffic allocation');
        RETURN NULL;
    END IF;
    
    -- Assign to variant based on traffic weights
    FOR v_variant IN 
        SELECT * FROM variants 
        WHERE tenant_id = p_tenant_id 
          AND experiment_id = p_experiment_id
        ORDER BY is_control DESC, name ASC
    LOOP
        v_cumulative_weight := v_cumulative_weight + v_variant.traffic_weight;
        IF v_bucket <= v_cumulative_weight THEN
            v_variant_id := v_variant.id;
            EXIT;
        END IF;
    END LOOP;
    
    -- Insert assignment
    INSERT INTO user_assignments (tenant_id, experiment_id, variant_id, user_id, user_attributes, bucketing_key)
    VALUES (p_tenant_id, p_experiment_id, v_variant_id, p_user_id, p_user_attributes, p_user_id);
    
    RETURN v_variant_id;
END;
$$ LANGUAGE plpgsql;

-- Function to track events
CREATE OR REPLACE FUNCTION track_event(
    p_tenant_id UUID,
    p_user_id VARCHAR(255),
    p_event_name VARCHAR(100),
    p_event_value DECIMAL DEFAULT NULL,
    p_event_properties JSONB DEFAULT '{}',
    p_session_id VARCHAR(255) DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    v_event_id UUID;
    v_assignment RECORD;
BEGIN
    -- Generate event ID
    v_event_id := uuid_generate_v4();
    
    -- Get user's current experiment assignments
    FOR v_assignment IN
        SELECT ua.experiment_id, ua.variant_id
        FROM user_assignments ua
        JOIN experiments e ON ua.experiment_id = e.id
        WHERE ua.tenant_id = p_tenant_id 
          AND ua.user_id = p_user_id
          AND ua.is_excluded = false
          AND e.status = 'active'
    LOOP
        -- Insert event with experiment context
        INSERT INTO events (id, tenant_id, experiment_id, variant_id, user_id, session_id, event_name, event_value, event_properties)
        VALUES (v_event_id, p_tenant_id, v_assignment.experiment_id, v_assignment.variant_id, p_user_id, p_session_id, p_event_name, p_event_value, p_event_properties);
    END LOOP;
    
    -- Also insert event without experiment context for general tracking
    INSERT INTO events (id, tenant_id, user_id, session_id, event_name, event_value, event_properties)
    VALUES (uuid_generate_v4(), p_tenant_id, p_user_id, p_session_id, p_event_name, p_event_value, p_event_properties);
    
    RETURN v_event_id;
END;
$$ LANGUAGE plpgsql;

-- View for experiment performance summary
CREATE OR REPLACE VIEW experiment_performance AS
SELECT 
    e.tenant_id,
    e.id as experiment_id,
    e.name as experiment_name,
    e.status,
    v.id as variant_id,
    v.name as variant_name,
    v.is_control,
    COUNT(DISTINCT ua.user_id) as total_users,
    COUNT(DISTINCT CASE WHEN ua.is_excluded = false THEN ua.user_id END) as assigned_users,
    COUNT(DISTINCT CASE WHEN ua.is_excluded = true THEN ua.user_id END) as excluded_users,
    COUNT(DISTINCT ev.user_id) as users_with_events,
    COUNT(ev.id) as total_events,
    COALESCE(AVG(ev.event_value), 0) as avg_event_value
FROM experiments e
LEFT JOIN variants v ON e.id = v.experiment_id
LEFT JOIN user_assignments ua ON v.id = ua.variant_id
LEFT JOIN events ev ON ua.experiment_id = ev.experiment_id AND ua.variant_id = ev.variant_id
GROUP BY e.tenant_id, e.id, e.name, e.status, v.id, v.name, v.is_control;

-- View for conversion rates by experiment and variant
CREATE OR REPLACE VIEW conversion_rates AS
SELECT 
    e.tenant_id,
    e.id as experiment_id,
    e.name as experiment_name,
    e.primary_metric,
    v.id as variant_id,
    v.name as variant_name,
    v.is_control,
    COUNT(DISTINCT ua.user_id) as total_users,
    COUNT(DISTINCT CASE WHEN ev.event_name = e.primary_metric THEN ev.user_id END) as converted_users,
    CASE 
        WHEN COUNT(DISTINCT ua.user_id) > 0 
        THEN COUNT(DISTINCT CASE WHEN ev.event_name = e.primary_metric THEN ev.user_id END)::DECIMAL / COUNT(DISTINCT ua.user_id)
        ELSE 0 
    END as conversion_rate,
    COALESCE(AVG(CASE WHEN ev.event_name = e.primary_metric THEN ev.event_value END), 0) as avg_conversion_value
FROM experiments e
JOIN variants v ON e.id = v.experiment_id
LEFT JOIN user_assignments ua ON v.id = ua.variant_id AND ua.is_excluded = false
LEFT JOIN events ev ON ua.experiment_id = ev.experiment_id AND ua.variant_id = ev.variant_id
WHERE e.primary_metric IS NOT NULL
GROUP BY e.tenant_id, e.id, e.name, e.primary_metric, v.id, v.name, v.is_control;

-- View for active experiments summary
CREATE OR REPLACE VIEW active_experiments_summary AS
SELECT 
    t.name as tenant_name,
    e.id as experiment_id,
    e.name as experiment_name,
    e.status,
    e.start_date,
    e.end_date,
    e.traffic_allocation,
    COUNT(DISTINCT v.id) as variant_count,
    COUNT(DISTINCT ua.user_id) as total_assigned_users,
    COUNT(DISTINCT ev.user_id) as users_with_events,
    COUNT(ev.id) as total_events,
    MAX(ev.timestamp) as last_event_time
FROM tenants t
JOIN experiments e ON t.id = e.tenant_id
LEFT JOIN variants v ON e.id = v.experiment_id
LEFT JOIN user_assignments ua ON v.id = ua.variant_id AND ua.is_excluded = false
LEFT JOIN events ev ON ua.experiment_id = ev.experiment_id
WHERE e.status IN ('active', 'paused')
GROUP BY t.name, e.id, e.name, e.status, e.start_date, e.end_date, e.traffic_allocation
ORDER BY e.start_date DESC;

-- Function to calculate statistical significance
CREATE OR REPLACE FUNCTION calculate_statistical_significance(
    control_conversions INTEGER,
    control_sample_size INTEGER,
    variant_conversions INTEGER,
    variant_sample_size INTEGER,
    confidence_level DECIMAL DEFAULT 0.95
) RETURNS TABLE(
    p_value DECIMAL,
    is_significant BOOLEAN,
    confidence_interval_lower DECIMAL,
    confidence_interval_upper DECIMAL,
    lift_percentage DECIMAL
) AS $$
DECLARE
    control_rate DECIMAL;
    variant_rate DECIMAL;
    pooled_rate DECIMAL;
    standard_error DECIMAL;
    z_score DECIMAL;
    critical_value DECIMAL;
    margin_of_error DECIMAL;
BEGIN
    -- Calculate conversion rates
    control_rate := control_conversions::DECIMAL / control_sample_size;
    variant_rate := variant_conversions::DECIMAL / variant_sample_size;
    
    -- Calculate pooled rate for standard error
    pooled_rate := (control_conversions + variant_conversions)::DECIMAL / (control_sample_size + variant_sample_size);
    
    -- Calculate standard error
    standard_error := sqrt(pooled_rate * (1 - pooled_rate) * (1.0/control_sample_size + 1.0/variant_sample_size));
    
    -- Calculate z-score
    IF standard_error > 0 THEN
        z_score := abs(variant_rate - control_rate) / standard_error;
    ELSE
        z_score := 0;
    END IF;
    
    -- Approximate p-value calculation (two-tailed test)
    -- This is a simplified calculation; for production use, consider a more precise implementation
    p_value := 2 * (1 - (0.5 * (1 + erf(z_score / sqrt(2)))));
    
    -- Determine significance
    critical_value := CASE 
        WHEN confidence_level = 0.95 THEN 1.96
        WHEN confidence_level = 0.99 THEN 2.576
        ELSE 1.96
    END;
    
    is_significant := z_score > critical_value;
    
    -- Calculate confidence interval for lift
    margin_of_error := critical_value * sqrt(variant_rate * (1 - variant_rate) / variant_sample_size + control_rate * (1 - control_rate) / control_sample_size);
    confidence_interval_lower := (variant_rate - control_rate) - margin_of_error;
    confidence_interval_upper := (variant_rate - control_rate) + margin_of_error;
    
    -- Calculate lift percentage
    IF control_rate > 0 THEN
        lift_percentage := ((variant_rate - control_rate) / control_rate) * 100;
    ELSE
        lift_percentage := 0;
    END IF;
    
    RETURN QUERY SELECT p_value, is_significant, confidence_interval_lower, confidence_interval_upper, lift_percentage;
END;
$$ LANGUAGE plpgsql;

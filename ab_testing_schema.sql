-- PostgreSQL Schema for A/B Testing Platform with Multi-Tenant Support
-- Created: $(date)

-- Enable UUID extension for generating unique identifiers
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom types for better data integrity
CREATE TYPE experiment_status AS ENUM ('draft', 'active', 'paused', 'completed', 'archived');
CREATE TYPE targeting_operator AS ENUM ('equals', 'not_equals', 'in', 'not_in', 'greater_than', 'less_than', 'contains', 'regex');
CREATE TYPE assignment_method AS ENUM ('random', 'sticky', 'deterministic');

-- Tenants table for multi-tenant isolation
CREATE TABLE tenants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(100) NOT NULL UNIQUE,
    settings JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Experiments table - core A/B test definitions
CREATE TABLE experiments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    hypothesis TEXT,
    status experiment_status DEFAULT 'draft',
    traffic_allocation DECIMAL(5,4) DEFAULT 1.0000 CHECK (traffic_allocation >= 0 AND traffic_allocation <= 1),
    assignment_method assignment_method DEFAULT 'random',
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    sample_size INTEGER,
    confidence_level DECIMAL(3,2) DEFAULT 0.95,
    minimum_detectable_effect DECIMAL(5,4),
    primary_metric VARCHAR(100),
    secondary_metrics TEXT[],
    tags TEXT[],
    metadata JSONB DEFAULT '{}',
    created_by UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT valid_date_range CHECK (end_date IS NULL OR start_date < end_date),
    CONSTRAINT valid_confidence_level CHECK (confidence_level > 0 AND confidence_level < 1)
);

-- Variants table - different versions being tested
CREATE TABLE variants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    experiment_id UUID NOT NULL REFERENCES experiments(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    is_control BOOLEAN DEFAULT false,
    traffic_weight DECIMAL(5,4) DEFAULT 0.5000 CHECK (traffic_weight >= 0 AND traffic_weight <= 1),
    configuration JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(experiment_id, name)
);

-- Targeting rules table - defines who gets included in experiments
CREATE TABLE targeting_rules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    experiment_id UUID NOT NULL REFERENCES experiments(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    attribute_name VARCHAR(100) NOT NULL,
    operator targeting_operator NOT NULL,
    value_text TEXT,
    value_number DECIMAL,
    value_boolean BOOLEAN,
    value_list TEXT[],
    is_active BOOLEAN DEFAULT true,
    priority INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- User assignments table - tracks which users are assigned to which variants
CREATE TABLE user_assignments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    experiment_id UUID NOT NULL REFERENCES experiments(id) ON DELETE CASCADE,
    variant_id UUID NOT NULL REFERENCES variants(id) ON DELETE CASCADE,
    user_id VARCHAR(255) NOT NULL,
    session_id VARCHAR(255),
    assignment_timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    bucketing_key VARCHAR(255),
    user_attributes JSONB DEFAULT '{}',
    is_excluded BOOLEAN DEFAULT false,
    exclusion_reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(tenant_id, experiment_id, user_id)
);

-- Events table - tracks user interactions and conversions
CREATE TABLE events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    experiment_id UUID REFERENCES experiments(id) ON DELETE CASCADE,
    variant_id UUID REFERENCES variants(id) ON DELETE CASCADE,
    user_id VARCHAR(255) NOT NULL,
    session_id VARCHAR(255),
    event_name VARCHAR(100) NOT NULL,
    event_value DECIMAL(15,4),
    event_properties JSONB DEFAULT '{}',
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Experiment results table - stores statistical analysis results
CREATE TABLE experiment_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    experiment_id UUID NOT NULL REFERENCES experiments(id) ON DELETE CASCADE,
    variant_id UUID NOT NULL REFERENCES variants(id) ON DELETE CASCADE,
    metric_name VARCHAR(100) NOT NULL,
    sample_size INTEGER NOT NULL,
    conversion_rate DECIMAL(8,6),
    mean_value DECIMAL(15,4),
    standard_deviation DECIMAL(15,4),
    confidence_interval_lower DECIMAL(15,4),
    confidence_interval_upper DECIMAL(15,4),
    p_value DECIMAL(10,8),
    statistical_significance BOOLEAN,
    calculated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(experiment_id, variant_id, metric_name, calculated_at)
);

-- Create indexes for performance optimization

-- Tenants indexes
CREATE INDEX idx_tenants_slug ON tenants(slug);
CREATE INDEX idx_tenants_active ON tenants(is_active);

-- Experiments indexes
CREATE INDEX idx_experiments_tenant_id ON experiments(tenant_id);
CREATE INDEX idx_experiments_status ON experiments(status);
CREATE INDEX idx_experiments_dates ON experiments(start_date, end_date);
CREATE INDEX idx_experiments_tenant_status ON experiments(tenant_id, status);
CREATE INDEX idx_experiments_created_at ON experiments(created_at);

-- Variants indexes
CREATE INDEX idx_variants_tenant_id ON variants(tenant_id);
CREATE INDEX idx_variants_experiment_id ON variants(experiment_id);
CREATE INDEX idx_variants_tenant_experiment ON variants(tenant_id, experiment_id);

-- Targeting rules indexes
CREATE INDEX idx_targeting_rules_tenant_id ON targeting_rules(tenant_id);
CREATE INDEX idx_targeting_rules_experiment_id ON targeting_rules(experiment_id);
CREATE INDEX idx_targeting_rules_active ON targeting_rules(is_active);
CREATE INDEX idx_targeting_rules_attribute ON targeting_rules(attribute_name);

-- User assignments indexes
CREATE INDEX idx_user_assignments_tenant_id ON user_assignments(tenant_id);
CREATE INDEX idx_user_assignments_experiment_id ON user_assignments(experiment_id);
CREATE INDEX idx_user_assignments_user_id ON user_assignments(user_id);
CREATE INDEX idx_user_assignments_tenant_user ON user_assignments(tenant_id, user_id);
CREATE INDEX idx_user_assignments_timestamp ON user_assignments(assignment_timestamp);

-- Events indexes
CREATE INDEX idx_events_tenant_id ON events(tenant_id);
CREATE INDEX idx_events_experiment_id ON events(experiment_id);
CREATE INDEX idx_events_user_id ON events(user_id);
CREATE INDEX idx_events_event_name ON events(event_name);
CREATE INDEX idx_events_timestamp ON events(timestamp);
CREATE INDEX idx_events_tenant_experiment ON events(tenant_id, experiment_id);

-- Experiment results indexes
CREATE INDEX idx_experiment_results_tenant_id ON experiment_results(tenant_id);
CREATE INDEX idx_experiment_results_experiment_id ON experiment_results(experiment_id);
CREATE INDEX idx_experiment_results_variant_id ON experiment_results(variant_id);
CREATE INDEX idx_experiment_results_metric ON experiment_results(metric_name);
CREATE INDEX idx_experiment_results_calculated_at ON experiment_results(calculated_at);

-- Create triggers for automatic updated_at timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to all tables with updated_at columns
CREATE TRIGGER update_tenants_updated_at BEFORE UPDATE ON tenants FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_experiments_updated_at BEFORE UPDATE ON experiments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_variants_updated_at BEFORE UPDATE ON variants FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_targeting_rules_updated_at BEFORE UPDATE ON targeting_rules FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_assignments_updated_at BEFORE UPDATE ON user_assignments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_experiment_results_updated_at BEFORE UPDATE ON experiment_results FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

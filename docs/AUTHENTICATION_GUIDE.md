# Authentication & Authorization Guide

## Overview

The Experiment Management API uses a comprehensive authentication and authorization system with role-based access control (RBAC), ownership verification, and multi-tenant isolation.

## Authentication Methods

### Bearer Token Authentication

All API requests require a Bearer token in the Authorization header:

```http
Authorization: Bearer <token>
```

### Token Types

#### Demo Tokens (Development)

For testing and development, use these predefined tokens:

| Token | User | Role | Tenant | Description |
|-------|------|------|--------|-------------|
| `admin_token` | admin_123 | Admin | tenant_123 | Full administrative access |
| `experimenter_token` | experimenter_456 | Experimenter | tenant_123 | Experiment management |
| `viewer_token` | viewer_789 | Viewer | tenant_123 | Read-only access |
| `other_tenant_token` | user_999 | Experimenter | tenant_999 | Cross-tenant testing |

#### Production Tokens (JWT)

In production, use JWT tokens with the following structure:

```json
{
  "sub": "user_id",
  "email": "<EMAIL>",
  "tenantId": "tenant_123",
  "roles": ["experimenter"],
  "permissions": ["experiments:read", "experiments:create"],
  "iat": 1703123456,
  "exp": 1703209856
}
```

## Authorization Model

### Role Hierarchy

```
Super Admin (System-wide access)
    ↓
Tenant Admin (Full tenant access + billing)
    ↓
Admin (Tenant management)
    ↓
Experimenter (Experiment management)
    ↓
Viewer (Read-only access)
    ↓
Guest (Limited read access)
```

### Role Definitions

#### Viewer Role
- **Permissions**: `experiments:read`, `analytics:view`
- **Description**: Read-only access to experiments and basic analytics
- **Use Case**: Stakeholders who need to view experiment results

```json
{
  "role": "viewer",
  "permissions": [
    "experiments:read",
    "analytics:view"
  ]
}
```

#### Experimenter Role
- **Permissions**: All Viewer permissions plus:
  - `experiments:create`, `experiments:update`, `experiments:publish`
  - `analytics:export`
- **Description**: Can create and manage experiments
- **Use Case**: Product managers, data scientists

```json
{
  "role": "experimenter",
  "permissions": [
    "experiments:read",
    "experiments:create", 
    "experiments:update",
    "experiments:publish",
    "analytics:view",
    "analytics:export"
  ]
}
```

#### Admin Role
- **Permissions**: All Experimenter permissions plus:
  - `experiments:delete`, `experiments:archive`
  - `users:read`, `users:create`, `users:update`, `users:invite`
  - `settings:read`, `settings:update`
  - `roles:assign`, `roles:unassign`
- **Description**: Full tenant administration
- **Use Case**: Team leads, administrators

```json
{
  "role": "admin",
  "permissions": [
    "experiments:*",
    "analytics:*",
    "users:*",
    "settings:*",
    "roles:*"
  ]
}
```

## Permission System

### Permission Format

Permissions follow the format: `resource:action`

### Core Resources

- **experiments**: A/B tests and experiments
- **analytics**: Analytics data and reports
- **users**: User management
- **settings**: System configuration
- **roles**: Role and permission management

### Actions

- **read**: View/list resources
- **create**: Create new resources
- **update**: Modify existing resources
- **delete**: Remove resources
- **publish**: Make experiments live
- **archive**: Archive resources
- **export**: Export data
- **invite**: Invite new users
- **assign**: Assign roles/permissions

### Permission Examples

```typescript
// Basic permissions
'experiments:read'     // View experiments
'experiments:create'   // Create experiments
'experiments:update'   // Update experiments
'experiments:delete'   // Delete experiments

// Advanced permissions
'analytics:export'     // Export analytics data
'users:invite'         // Invite new users
'settings:update'      // Modify system settings
'roles:assign'         // Assign roles to users
```

## Ownership Verification

### Experiment Ownership

Users can only access experiments they own unless they have admin privileges:

```typescript
// Ownership check logic
const isOwner = experiment.createdBy === user.id;
const isAdmin = user.roles.includes('admin');
const hasAccess = isOwner || isAdmin;
```

### Ownership Rules

1. **Creator Ownership**: Users automatically own experiments they create
2. **Admin Override**: Admins can access any experiment in their tenant
3. **Collaborator Access**: Shared experiments allow multiple owners
4. **Tenant Isolation**: Users cannot access experiments from other tenants

### API Endpoints with Ownership

| Endpoint | Ownership Required | Admin Override |
|----------|-------------------|----------------|
| `GET /experiments` | No | N/A |
| `GET /experiments/:id` | Yes | Yes |
| `PUT /experiments/:id` | Yes | Yes |
| `DELETE /experiments/:id` | Yes | Yes |
| `POST /experiments/:id/publish` | Yes | Yes |

## Multi-Tenant Isolation

### Tenant Separation

Each tenant's data is completely isolated:

```sql
-- All queries include tenant_id filter
SELECT * FROM experiments 
WHERE tenant_id = $1 AND id = $2;
```

### Tenant Context

The tenant context is extracted from the authenticated user:

```typescript
interface UserContext {
  userId: string;
  email: string;
  tenantId: string;    // Tenant isolation
  roles: string[];
  permissions: string[];
}
```

### Cross-Tenant Protection

The system prevents cross-tenant access:

```typescript
// Automatic tenant filtering
app.get('/api/experiments/:id', 
  authMiddleware.requireSameTenant(),
  authMiddleware.authorizeExperiment('read', true),
  controller.getExperiment
);
```

## Authorization Middleware

### Middleware Chain

```typescript
app.get('/api/experiments/:id',
  // 1. Authentication - verify token
  authenticationMiddleware,
  
  // 2. Authorization - check permissions
  authorizationMiddleware.authorizeExperiment('read', true),
  
  // 3. Controller - business logic
  experimentController.getExperiment
);
```

### Authorization Options

```typescript
interface AuthorizationOptions {
  resource: string;              // Resource being accessed
  action: string;                // Action being performed
  requireOwnership?: boolean;    // Require resource ownership
  allowSameTenant?: boolean;     // Allow same-tenant access
  allowedRoles?: string[];       // Specific roles allowed
  customCheck?: Function;        // Custom authorization logic
}
```

### Usage Examples

```typescript
// Basic permission check
authMiddleware.authorize({
  resource: 'experiments',
  action: 'read'
})

// Ownership required
authMiddleware.authorize({
  resource: 'experiments',
  action: 'update',
  requireOwnership: true
})

// Role-based access
authMiddleware.authorize({
  resource: 'users',
  action: 'invite',
  allowedRoles: ['admin', 'tenant_admin']
})

// Multiple permissions
authMiddleware.authorizeMultiple([
  { resource: 'experiments', action: 'update' },
  { resource: 'experiments', action: 'publish' }
], true) // require ALL permissions
```

## Error Handling

### Authentication Errors

#### Missing Token
```json
{
  "success": false,
  "error": "Authorization header required",
  "code": "MISSING_AUTH_HEADER",
  "requestId": "req_1703123456789_abc123"
}
```

#### Invalid Token
```json
{
  "success": false,
  "error": "Invalid token",
  "code": "INVALID_TOKEN",
  "requestId": "req_1703123456789_abc123"
}
```

### Authorization Errors

#### Insufficient Permissions
```json
{
  "success": false,
  "error": "Missing permission: experiments:delete",
  "code": "INSUFFICIENT_PERMISSIONS",
  "details": {
    "requiredPermission": "experiments:delete",
    "userPermissions": ["experiments:read", "experiments:create"],
    "denyReasons": ["User does not have permission: experiments:delete"]
  }
}
```

#### Ownership Violation
```json
{
  "success": false,
  "error": "User does not own this experiment",
  "code": "NOT_EXPERIMENT_OWNER",
  "details": {
    "experimentId": "exp_001",
    "experimentOwner": "experimenter_456",
    "requestingUser": "viewer_789"
  }
}
```

#### Cross-Tenant Access
```json
{
  "success": false,
  "error": "Cross-tenant access denied",
  "code": "CROSS_TENANT_ACCESS_DENIED",
  "details": {
    "userTenant": "tenant_123",
    "resourceTenant": "tenant_999"
  }
}
```

## Security Features

### Audit Logging

All authorization attempts are logged:

```typescript
interface AuthorizationAuditLog {
  userId: string;
  tenantId: string;
  resource: string;
  action: string;
  granted: boolean;
  reason: string;
  ipAddress: string;
  userAgent: string;
  timestamp: Date;
  experimentId?: string;
  details?: any;
}
```

### Security Monitoring

The system automatically detects suspicious activity:

- **Multiple Failed Attempts**: 5+ failed authorization attempts
- **Privilege Escalation**: Attempts to access admin resources
- **Cross-Tenant Access**: Attempts to access other tenants' data
- **Ownership Violations**: Attempts to access unowned resources

### Rate Limiting

API endpoints are rate limited by user and IP:

```typescript
// Rate limits by endpoint type
const rateLimits = {
  standard: { requests: 100, window: '15m' },
  create: { requests: 20, window: '15m' },
  export: { requests: 5, window: '15m' },
  admin: { requests: 50, window: '15m' }
};
```

## Implementation Examples

### Frontend Integration

```javascript
// React hook for authentication
function useAuth() {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(localStorage.getItem('token'));

  const login = async (credentials) => {
    const response = await fetch('/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(credentials)
    });
    
    const data = await response.json();
    setToken(data.token);
    setUser(data.user);
    localStorage.setItem('token', data.token);
  };

  const hasPermission = (permission) => {
    return user?.permissions?.includes(permission) || false;
  };

  const canAccessExperiment = (experiment) => {
    const isOwner = experiment.createdBy === user?.id;
    const isAdmin = user?.roles?.includes('admin');
    return isOwner || isAdmin;
  };

  return { user, token, login, hasPermission, canAccessExperiment };
}
```

### API Client

```javascript
// API client with automatic token handling
class ExperimentAPI {
  constructor(baseURL, token) {
    this.baseURL = baseURL;
    this.token = token;
  }

  async request(endpoint, options = {}) {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      ...options,
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json',
        ...options.headers
      }
    });

    if (response.status === 401) {
      throw new AuthenticationError('Token expired or invalid');
    }

    if (response.status === 403) {
      const error = await response.json();
      throw new AuthorizationError(error.error, error.code, error.details);
    }

    return response.json();
  }

  async getExperiments() {
    return this.request('/experiments');
  }

  async createExperiment(data) {
    return this.request('/experiments', {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }
}
```

### Custom Authorization Hook

```javascript
// React hook for authorization checks
function useAuthorization() {
  const { user } = useAuth();

  const checkPermission = useCallback((resource, action) => {
    const permission = `${resource}:${action}`;
    return user?.permissions?.includes(permission) || false;
  }, [user]);

  const checkMultiplePermissions = useCallback((permissions, requireAll = true) => {
    const results = permissions.map(({ resource, action }) => 
      checkPermission(resource, action)
    );
    
    return requireAll 
      ? results.every(Boolean)
      : results.some(Boolean);
  }, [checkPermission]);

  const checkExperimentAccess = useCallback((experiment, action) => {
    // Check basic permission
    if (!checkPermission('experiments', action)) {
      return false;
    }

    // Check ownership for sensitive operations
    const sensitiveActions = ['update', 'delete', 'publish'];
    if (sensitiveActions.includes(action)) {
      const isOwner = experiment.createdBy === user?.id;
      const isAdmin = user?.roles?.includes('admin');
      return isOwner || isAdmin;
    }

    return true;
  }, [user, checkPermission]);

  return {
    checkPermission,
    checkMultiplePermissions,
    checkExperimentAccess
  };
}
```

## Best Practices

### Security Best Practices

1. **Principle of Least Privilege**: Grant minimum necessary permissions
2. **Regular Token Rotation**: Rotate tokens regularly
3. **Secure Token Storage**: Store tokens securely (httpOnly cookies)
4. **Input Validation**: Validate all inputs
5. **Audit Everything**: Log all authorization attempts
6. **Monitor Anomalies**: Watch for suspicious patterns

### Development Best Practices

1. **Test Authorization**: Write tests for all authorization scenarios
2. **Mock Tokens**: Use consistent test tokens
3. **Error Handling**: Handle authorization errors gracefully
4. **User Feedback**: Provide clear error messages
5. **Documentation**: Document permission requirements

### Performance Best Practices

1. **Cache Permissions**: Cache user permissions
2. **Batch Checks**: Use bulk permission checks
3. **Efficient Queries**: Optimize database queries
4. **Connection Pooling**: Use database connection pools
5. **Rate Limiting**: Implement appropriate rate limits

This authentication and authorization guide provides comprehensive coverage of the security model, helping developers understand and implement secure access patterns in the experiment management system.

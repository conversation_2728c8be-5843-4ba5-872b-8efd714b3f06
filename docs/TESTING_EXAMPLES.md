# Testing Examples & Scenarios

## Overview

This document provides comprehensive testing examples for the Experiment Management API, covering various authorization scenarios, edge cases, and integration patterns.

## Quick Test Commands

### Health Check
```bash
curl http://localhost:3003/health
```

### Basic Authentication Test
```bash
# Valid token
curl -H "Authorization: Bearer admin_token" \
     http://localhost:3003/api/experiments

# Invalid token
curl -H "Authorization: Bearer invalid_token" \
     http://localhost:3003/api/experiments
```

## Authorization Test Scenarios

### 1. Role-Based Access Tests

#### Admin Access (Should Succeed)
```bash
# List all experiments
curl -H "Authorization: Bearer admin_token" \
     http://localhost:3003/api/experiments

# Access user management
curl -H "Authorization: Bearer admin_token" \
     http://localhost:3003/api/users

# Access admin settings
curl -H "Authorization: Bearer admin_token" \
     http://localhost:3003/api/admin/settings

# Create experiment
curl -X POST \
     -H "Authorization: Bearer admin_token" \
     -H "Content-Type: application/json" \
     -d '{"name":"Admin Test","description":"Created by admin"}' \
     http://localhost:3003/api/experiments
```

#### Experimenter Access (Mixed Results)
```bash
# List experiments (✅ Should succeed)
curl -H "Authorization: Bearer experimenter_token" \
     http://localhost:3003/api/experiments

# Create experiment (✅ Should succeed)
curl -X POST \
     -H "Authorization: Bearer experimenter_token" \
     -H "Content-Type: application/json" \
     -d '{"name":"Experimenter Test","description":"Created by experimenter"}' \
     http://localhost:3003/api/experiments

# Access user management (❌ Should fail)
curl -H "Authorization: Bearer experimenter_token" \
     http://localhost:3003/api/users

# Export analytics (✅ Should succeed)
curl -H "Authorization: Bearer experimenter_token" \
     http://localhost:3003/api/analytics/export
```

#### Viewer Access (Limited)
```bash
# List experiments (✅ Should succeed)
curl -H "Authorization: Bearer viewer_token" \
     http://localhost:3003/api/experiments

# View analytics dashboard (✅ Should succeed)
curl -H "Authorization: Bearer viewer_token" \
     http://localhost:3003/api/analytics/dashboard

# Create experiment (❌ Should fail)
curl -X POST \
     -H "Authorization: Bearer viewer_token" \
     -H "Content-Type: application/json" \
     -d '{"name":"Viewer Test","description":"Should fail"}' \
     http://localhost:3003/api/experiments

# Export analytics (❌ Should fail)
curl -H "Authorization: Bearer viewer_token" \
     http://localhost:3003/api/analytics/export

# Access user management (❌ Should fail)
curl -H "Authorization: Bearer viewer_token" \
     http://localhost:3003/api/users
```

### 2. Ownership-Based Access Tests

#### Access Own Experiment (Should Succeed)
```bash
# Experimenter accessing their own experiment
curl -H "Authorization: Bearer experimenter_token" \
     http://localhost:3003/api/experiments/exp_001

# Update own experiment
curl -X PUT \
     -H "Authorization: Bearer experimenter_token" \
     -H "Content-Type: application/json" \
     -d '{"name":"Updated by Owner","description":"Owner can update"}' \
     http://localhost:3003/api/experiments/exp_001
```

#### Access Other's Experiment (Should Fail)
```bash
# Viewer trying to access experimenter's experiment
curl -H "Authorization: Bearer viewer_token" \
     http://localhost:3003/api/experiments/exp_001

# Expected response:
# {
#   "success": false,
#   "error": "User does not own this experiment",
#   "code": "NOT_EXPERIMENT_OWNER",
#   "details": {
#     "experimentId": "exp_001",
#     "experimentOwner": "experimenter_456",
#     "requestingUser": "viewer_789"
#   }
# }
```

#### Admin Override (Should Succeed)
```bash
# Admin can access any experiment regardless of ownership
curl -H "Authorization: Bearer admin_token" \
     http://localhost:3003/api/experiments/exp_001

curl -H "Authorization: Bearer admin_token" \
     http://localhost:3003/api/experiments/exp_003
```

### 3. Cross-Tenant Isolation Tests

#### Same Tenant Access (Should Succeed)
```bash
# Users in tenant_123 can see each other's experiments (with proper permissions)
curl -H "Authorization: Bearer admin_token" \
     http://localhost:3003/api/experiments

curl -H "Authorization: Bearer experimenter_token" \
     http://localhost:3003/api/experiments
```

#### Cross-Tenant Access (Should Fail)
```bash
# User from tenant_999 trying to access tenant_123 experiments
curl -H "Authorization: Bearer other_tenant_token" \
     http://localhost:3003/api/experiments

# Expected: Empty list or filtered results for tenant_999 only
```

### 4. Multiple Permission Tests

#### Publish Experiment (Requires Multiple Permissions)
```bash
# Experimenter has both update and publish permissions (✅ Should succeed)
curl -X POST \
     -H "Authorization: Bearer experimenter_token" \
     http://localhost:3003/api/experiments/exp_001/publish

# Viewer lacks publish permission (❌ Should fail)
curl -X POST \
     -H "Authorization: Bearer viewer_token" \
     http://localhost:3003/api/experiments/exp_001/publish

# Expected response for viewer:
# {
#   "success": false,
#   "error": "Access denied (1/2 permissions)",
#   "code": "INSUFFICIENT_PERMISSIONS",
#   "details": {
#     "requiredPermissions": [
#       {"resource": "experiments", "action": "update"},
#       {"resource": "experiments", "action": "publish"}
#     ],
#     "grantedCount": 0,
#     "requireAll": true
#   }
# }
```

## Advanced Testing Scenarios

### 5. Error Handling Tests

#### Missing Authorization Header
```bash
curl http://localhost:3003/api/experiments

# Expected response:
# {
#   "success": false,
#   "error": "Authorization header required",
#   "code": "MISSING_AUTH_HEADER"
# }
```

#### Malformed Authorization Header
```bash
curl -H "Authorization: InvalidFormat" \
     http://localhost:3003/api/experiments

# Expected response:
# {
#   "success": false,
#   "error": "Invalid token",
#   "code": "INVALID_TOKEN"
# }
```

#### Non-existent Resource
```bash
curl -H "Authorization: Bearer admin_token" \
     http://localhost:3003/api/experiments/non_existent_id

# Expected response:
# {
#   "success": false,
#   "error": "Experiment not found",
#   "code": "EXPERIMENT_NOT_FOUND"
# }
```

### 6. Data Validation Tests

#### Invalid Experiment Data
```bash
# Missing required name field
curl -X POST \
     -H "Authorization: Bearer experimenter_token" \
     -H "Content-Type: application/json" \
     -d '{"description":"Missing name"}' \
     http://localhost:3003/api/experiments

# Empty name field
curl -X POST \
     -H "Authorization: Bearer experimenter_token" \
     -H "Content-Type: application/json" \
     -d '{"name":"","description":"Empty name"}' \
     http://localhost:3003/api/experiments

# Name too long
curl -X POST \
     -H "Authorization: Bearer experimenter_token" \
     -H "Content-Type: application/json" \
     -d '{"name":"'$(printf 'A%.0s' {1..300})'","description":"Name too long"}' \
     http://localhost:3003/api/experiments
```

### 7. Pagination and Filtering Tests

#### Pagination
```bash
# First page
curl -H "Authorization: Bearer experimenter_token" \
     "http://localhost:3003/api/experiments?page=1&limit=2"

# Second page
curl -H "Authorization: Bearer experimenter_token" \
     "http://localhost:3003/api/experiments?page=2&limit=2"

# Large limit (should be capped)
curl -H "Authorization: Bearer experimenter_token" \
     "http://localhost:3003/api/experiments?limit=1000"
```

#### Filtering
```bash
# Filter by status
curl -H "Authorization: Bearer experimenter_token" \
     "http://localhost:3003/api/experiments?status=active"

# Search by name
curl -H "Authorization: Bearer experimenter_token" \
     "http://localhost:3003/api/experiments?search=Button"

# Combined filters
curl -H "Authorization: Bearer experimenter_token" \
     "http://localhost:3003/api/experiments?status=active&search=Test&page=1&limit=5"
```

## Automated Testing Scripts

### Bash Test Suite

```bash
#!/bin/bash
# test_authorization.sh

BASE_URL="http://localhost:3003/api"
ADMIN_TOKEN="admin_token"
EXPERIMENTER_TOKEN="experimenter_token"
VIEWER_TOKEN="viewer_token"
OTHER_TENANT_TOKEN="other_tenant_token"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test counter
TESTS_RUN=0
TESTS_PASSED=0

# Test function
test_endpoint() {
    local description="$1"
    local expected_status="$2"
    local curl_command="$3"
    
    TESTS_RUN=$((TESTS_RUN + 1))
    
    echo -e "\n${YELLOW}Test $TESTS_RUN: $description${NC}"
    echo "Command: $curl_command"
    
    response=$(eval "$curl_command" 2>/dev/null)
    status_code=$(eval "$curl_command -w '%{http_code}' -o /dev/null -s" 2>/dev/null)
    
    if [ "$status_code" = "$expected_status" ]; then
        echo -e "${GREEN}✅ PASS${NC} (Status: $status_code)"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        echo -e "${RED}❌ FAIL${NC} (Expected: $expected_status, Got: $status_code)"
        echo "Response: $response"
    fi
}

echo "🧪 Starting Authorization Test Suite"
echo "======================================"

# Test 1: Health check
test_endpoint "Health check" "200" \
    "curl -s '$BASE_URL/../health'"

# Test 2: Admin can list experiments
test_endpoint "Admin can list experiments" "200" \
    "curl -s -H 'Authorization: Bearer $ADMIN_TOKEN' '$BASE_URL/experiments'"

# Test 3: Experimenter can list experiments
test_endpoint "Experimenter can list experiments" "200" \
    "curl -s -H 'Authorization: Bearer $EXPERIMENTER_TOKEN' '$BASE_URL/experiments'"

# Test 4: Viewer can list experiments
test_endpoint "Viewer can list experiments" "200" \
    "curl -s -H 'Authorization: Bearer $VIEWER_TOKEN' '$BASE_URL/experiments'"

# Test 5: Viewer cannot access user management
test_endpoint "Viewer cannot access user management" "403" \
    "curl -s -H 'Authorization: Bearer $VIEWER_TOKEN' '$BASE_URL/users'"

# Test 6: Experimenter cannot access user management
test_endpoint "Experimenter cannot access user management" "403" \
    "curl -s -H 'Authorization: Bearer $EXPERIMENTER_TOKEN' '$BASE_URL/users'"

# Test 7: Admin can access user management
test_endpoint "Admin can access user management" "200" \
    "curl -s -H 'Authorization: Bearer $ADMIN_TOKEN' '$BASE_URL/users'"

# Test 8: Invalid token is rejected
test_endpoint "Invalid token is rejected" "401" \
    "curl -s -H 'Authorization: Bearer invalid_token' '$BASE_URL/experiments'"

# Test 9: Missing token is rejected
test_endpoint "Missing token is rejected" "401" \
    "curl -s '$BASE_URL/experiments'"

# Test 10: Experimenter can create experiment
test_endpoint "Experimenter can create experiment" "201" \
    "curl -s -X POST -H 'Authorization: Bearer $EXPERIMENTER_TOKEN' -H 'Content-Type: application/json' -d '{\"name\":\"Test Experiment\",\"description\":\"Automated test\"}' '$BASE_URL/experiments'"

# Test 11: Viewer cannot create experiment
test_endpoint "Viewer cannot create experiment" "403" \
    "curl -s -X POST -H 'Authorization: Bearer $VIEWER_TOKEN' -H 'Content-Type: application/json' -d '{\"name\":\"Test Experiment\",\"description\":\"Should fail\"}' '$BASE_URL/experiments'"

# Test 12: Cross-tenant isolation
test_endpoint "Cross-tenant user sees only their experiments" "200" \
    "curl -s -H 'Authorization: Bearer $OTHER_TENANT_TOKEN' '$BASE_URL/experiments'"

# Test 13: Viewer cannot export analytics
test_endpoint "Viewer cannot export analytics" "403" \
    "curl -s -H 'Authorization: Bearer $VIEWER_TOKEN' '$BASE_URL/analytics/export'"

# Test 14: Experimenter can export analytics
test_endpoint "Experimenter can export analytics" "200" \
    "curl -s -H 'Authorization: Bearer $EXPERIMENTER_TOKEN' '$BASE_URL/analytics/export'"

# Test 15: Ownership check - viewer cannot access specific experiment
test_endpoint "Viewer cannot access specific experiment (ownership)" "403" \
    "curl -s -H 'Authorization: Bearer $VIEWER_TOKEN' '$BASE_URL/experiments/exp_001'"

echo -e "\n======================================"
echo -e "📊 Test Results: ${GREEN}$TESTS_PASSED${NC}/${TESTS_RUN} tests passed"

if [ $TESTS_PASSED -eq $TESTS_RUN ]; then
    echo -e "${GREEN}🎉 All tests passed!${NC}"
    exit 0
else
    echo -e "${RED}❌ Some tests failed${NC}"
    exit 1
fi
```

### JavaScript Test Suite

```javascript
// test_authorization.js
const axios = require('axios');

const BASE_URL = 'http://localhost:3003/api';
const TOKENS = {
  admin: 'admin_token',
  experimenter: 'experimenter_token',
  viewer: 'viewer_token',
  otherTenant: 'other_tenant_token'
};

class AuthorizationTester {
  constructor() {
    this.testsRun = 0;
    this.testsPassed = 0;
  }

  async test(description, expectedStatus, requestConfig) {
    this.testsRun++;
    
    console.log(`\n🧪 Test ${this.testsRun}: ${description}`);
    
    try {
      const response = await axios(requestConfig);
      const actualStatus = response.status;
      
      if (actualStatus === expectedStatus) {
        console.log(`✅ PASS (Status: ${actualStatus})`);
        this.testsPassed++;
      } else {
        console.log(`❌ FAIL (Expected: ${expectedStatus}, Got: ${actualStatus})`);
        console.log('Response:', response.data);
      }
    } catch (error) {
      const actualStatus = error.response?.status || 'ERROR';
      
      if (actualStatus === expectedStatus) {
        console.log(`✅ PASS (Status: ${actualStatus})`);
        this.testsPassed++;
      } else {
        console.log(`❌ FAIL (Expected: ${expectedStatus}, Got: ${actualStatus})`);
        console.log('Error:', error.response?.data || error.message);
      }
    }
  }

  createAuthHeader(token) {
    return { Authorization: `Bearer ${token}` };
  }

  async runAllTests() {
    console.log('🚀 Starting Authorization Test Suite');
    console.log('=====================================');

    // Test 1: Health check
    await this.test('Health check', 200, {
      method: 'GET',
      url: 'http://localhost:3003/health'
    });

    // Test 2: Admin access
    await this.test('Admin can list experiments', 200, {
      method: 'GET',
      url: `${BASE_URL}/experiments`,
      headers: this.createAuthHeader(TOKENS.admin)
    });

    // Test 3: Experimenter access
    await this.test('Experimenter can list experiments', 200, {
      method: 'GET',
      url: `${BASE_URL}/experiments`,
      headers: this.createAuthHeader(TOKENS.experimenter)
    });

    // Test 4: Viewer access
    await this.test('Viewer can list experiments', 200, {
      method: 'GET',
      url: `${BASE_URL}/experiments`,
      headers: this.createAuthHeader(TOKENS.viewer)
    });

    // Test 5: Permission denied
    await this.test('Viewer cannot access user management', 403, {
      method: 'GET',
      url: `${BASE_URL}/users`,
      headers: this.createAuthHeader(TOKENS.viewer)
    });

    // Test 6: Admin user management
    await this.test('Admin can access user management', 200, {
      method: 'GET',
      url: `${BASE_URL}/users`,
      headers: this.createAuthHeader(TOKENS.admin)
    });

    // Test 7: Invalid token
    await this.test('Invalid token is rejected', 401, {
      method: 'GET',
      url: `${BASE_URL}/experiments`,
      headers: this.createAuthHeader('invalid_token')
    });

    // Test 8: Create experiment
    await this.test('Experimenter can create experiment', 201, {
      method: 'POST',
      url: `${BASE_URL}/experiments`,
      headers: {
        ...this.createAuthHeader(TOKENS.experimenter),
        'Content-Type': 'application/json'
      },
      data: {
        name: 'JS Test Experiment',
        description: 'Created by JavaScript test'
      }
    });

    // Test 9: Viewer cannot create
    await this.test('Viewer cannot create experiment', 403, {
      method: 'POST',
      url: `${BASE_URL}/experiments`,
      headers: {
        ...this.createAuthHeader(TOKENS.viewer),
        'Content-Type': 'application/json'
      },
      data: {
        name: 'Should Fail',
        description: 'Viewer should not be able to create'
      }
    });

    // Test 10: Analytics export
    await this.test('Experimenter can export analytics', 200, {
      method: 'GET',
      url: `${BASE_URL}/analytics/export`,
      headers: this.createAuthHeader(TOKENS.experimenter)
    });

    // Test 11: Viewer cannot export
    await this.test('Viewer cannot export analytics', 403, {
      method: 'GET',
      url: `${BASE_URL}/analytics/export`,
      headers: this.createAuthHeader(TOKENS.viewer)
    });

    // Test 12: Ownership check
    await this.test('Viewer cannot access specific experiment', 403, {
      method: 'GET',
      url: `${BASE_URL}/experiments/exp_001`,
      headers: this.createAuthHeader(TOKENS.viewer)
    });

    // Results
    console.log('\n=====================================');
    console.log(`📊 Test Results: ${this.testsPassed}/${this.testsRun} tests passed`);
    
    if (this.testsPassed === this.testsRun) {
      console.log('🎉 All tests passed!');
      process.exit(0);
    } else {
      console.log('❌ Some tests failed');
      process.exit(1);
    }
  }
}

// Run tests
const tester = new AuthorizationTester();
tester.runAllTests().catch(console.error);
```

### Python Test Suite

```python
#!/usr/bin/env python3
# test_authorization.py

import requests
import json
import sys

BASE_URL = "http://localhost:3003/api"
TOKENS = {
    'admin': 'admin_token',
    'experimenter': 'experimenter_token',
    'viewer': 'viewer_token',
    'other_tenant': 'other_tenant_token'
}

class AuthorizationTester:
    def __init__(self):
        self.tests_run = 0
        self.tests_passed = 0

    def test(self, description, expected_status, method, endpoint, token=None, data=None):
        self.tests_run += 1
        
        print(f"\n🧪 Test {self.tests_run}: {description}")
        
        headers = {}
        if token:
            headers['Authorization'] = f'Bearer {token}'
        if data:
            headers['Content-Type'] = 'application/json'
        
        try:
            if method == 'GET':
                response = requests.get(f"{BASE_URL}{endpoint}", headers=headers)
            elif method == 'POST':
                response = requests.post(f"{BASE_URL}{endpoint}", headers=headers, 
                                       json=data if data else None)
            elif method == 'PUT':
                response = requests.put(f"{BASE_URL}{endpoint}", headers=headers, 
                                      json=data if data else None)
            elif method == 'DELETE':
                response = requests.delete(f"{BASE_URL}{endpoint}", headers=headers)
            
            actual_status = response.status_code
            
            if actual_status == expected_status:
                print(f"✅ PASS (Status: {actual_status})")
                self.tests_passed += 1
            else:
                print(f"❌ FAIL (Expected: {expected_status}, Got: {actual_status})")
                print(f"Response: {response.text}")
                
        except Exception as e:
            print(f"❌ ERROR: {str(e)}")

    def run_all_tests(self):
        print("🚀 Starting Authorization Test Suite")
        print("=====================================")

        # Health check
        response = requests.get("http://localhost:3003/health")
        if response.status_code == 200:
            print("✅ Server is running")
        else:
            print("❌ Server is not responding")
            return

        # Test cases
        self.test("Admin can list experiments", 200, 'GET', '/experiments', TOKENS['admin'])
        self.test("Experimenter can list experiments", 200, 'GET', '/experiments', TOKENS['experimenter'])
        self.test("Viewer can list experiments", 200, 'GET', '/experiments', TOKENS['viewer'])
        self.test("Viewer cannot access user management", 403, 'GET', '/users', TOKENS['viewer'])
        self.test("Admin can access user management", 200, 'GET', '/users', TOKENS['admin'])
        self.test("Invalid token is rejected", 401, 'GET', '/experiments', 'invalid_token')
        self.test("Experimenter can create experiment", 201, 'POST', '/experiments', 
                 TOKENS['experimenter'], {'name': 'Python Test', 'description': 'Created by Python'})
        self.test("Viewer cannot create experiment", 403, 'POST', '/experiments', 
                 TOKENS['viewer'], {'name': 'Should Fail', 'description': 'Should not work'})
        self.test("Experimenter can export analytics", 200, 'GET', '/analytics/export', TOKENS['experimenter'])
        self.test("Viewer cannot export analytics", 403, 'GET', '/analytics/export', TOKENS['viewer'])
        self.test("Viewer cannot access specific experiment", 403, 'GET', '/experiments/exp_001', TOKENS['viewer'])

        # Results
        print("\n=====================================")
        print(f"📊 Test Results: {self.tests_passed}/{self.tests_run} tests passed")
        
        if self.tests_passed == self.tests_run:
            print("🎉 All tests passed!")
            sys.exit(0)
        else:
            print("❌ Some tests failed")
            sys.exit(1)

if __name__ == "__main__":
    tester = AuthorizationTester()
    tester.run_all_tests()
```

## Running the Tests

### Prerequisites
Make sure the authorization middleware demo is running:
```bash
cd authorization-middleware
node demo.js
```

### Run Bash Tests
```bash
chmod +x test_authorization.sh
./test_authorization.sh
```

### Run JavaScript Tests
```bash
npm install axios
node test_authorization.js
```

### Run Python Tests
```bash
pip install requests
python test_authorization.py
```

## Expected Test Results

All test suites should show similar results:
- ✅ 12-15 tests passing
- ❌ 0 tests failing
- 🎉 All tests passed!

This comprehensive testing suite validates that the authorization middleware correctly enforces permissions, ownership rules, and tenant isolation across all API endpoints.

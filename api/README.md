# A/B Testing API - Node.js Express Implementation

A production-ready Node.js Express API implementation for A/B testing experiment management based on the OpenAPI 3.0 specification.

## 🚀 Features

- **Complete OpenAPI Implementation** - Full implementation of the OpenAPI 3.0 specification
- **Authentication & Authorization** - JWT-based authentication with role-based permissions
- **Tenant Isolation** - Multi-tenant architecture with automatic data segregation
- **Request Validation** - Comprehensive request validation using Joi schemas
- **Rate Limiting** - Redis-backed distributed rate limiting with different tiers
- **Error Handling** - Structured error handling with detailed error responses
- **Logging** - Comprehensive logging with Winston and structured log formats
- **Security** - Helmet.js security headers and CORS configuration
- **Documentation** - Auto-generated Swagger UI documentation
- **Health Checks** - Health and metrics endpoints for monitoring
- **Graceful Shutdown** - Proper cleanup and graceful shutdown handling

## 📋 Prerequisites

- Node.js 18+ 
- PostgreSQL 12+
- Redis 6+ (for rate limiting)
- npm or yarn

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd api
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Set up the database**
   ```bash
   # Run Prisma migrations
   npm run db:migrate
   
   # Generate Prisma client
   npm run db:generate
   
   # Seed the database (optional)
   npm run db:seed
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `PORT` | Server port | `3000` |
| `NODE_ENV` | Environment | `development` |
| `DATABASE_URL` | PostgreSQL connection string | Required |
| `JWT_SECRET` | JWT signing secret | Required |
| `REDIS_URL` | Redis connection string | `redis://localhost:6379` |
| `RATE_LIMIT_EXPERIMENTS` | Experiment management rate limit | `1000` |
| `RATE_LIMIT_ASSIGNMENTS` | User assignment rate limit | `10000` |
| `RATE_LIMIT_EVENTS` | Event tracking rate limit | `10000` |

See `.env.example` for all available configuration options.

### Rate Limiting Tiers

- **Experiment Management**: 1,000 requests/hour
- **User Assignment**: 10,000 requests/hour  
- **Event Tracking**: 10,000 requests/hour
- **General API**: 1,000 requests/15 minutes

## 🏗️ Architecture

### Middleware Stack

1. **Security** - Helmet.js security headers
2. **CORS** - Cross-origin resource sharing
3. **Compression** - Response compression
4. **Request Parsing** - JSON and URL-encoded body parsing
5. **Request ID** - Unique request identifier generation
6. **Logging** - Request/response logging
7. **Rate Limiting** - Distributed rate limiting
8. **Authentication** - JWT token validation
9. **Authorization** - Permission-based access control
10. **Validation** - Request schema validation
11. **Error Handling** - Global error handling

### Project Structure

```
api/
├── src/
│   ├── config/           # Configuration management
│   ├── middleware/       # Express middleware
│   │   ├── auth.ts       # Authentication & authorization
│   │   ├── validation.ts # Request validation
│   │   ├── rateLimiting.ts # Rate limiting
│   │   └── errorHandler.ts # Error handling
│   ├── controllers/      # Route controllers
│   │   ├── experiments.controller.ts
│   │   ├── assignments.controller.ts
│   │   ├── events.controller.ts
│   │   └── analytics.controller.ts
│   ├── routes/          # Route definitions
│   ├── utils/           # Utility functions
│   │   ├── errors.ts    # Custom error classes
│   │   ├── logger.ts    # Logging utilities
│   │   └── response.ts  # Response helpers
│   ├── app.ts           # Express app setup
│   └── server.ts        # Server entry point
├── dist/                # Compiled JavaScript
├── .env.example         # Environment variables template
├── package.json         # Dependencies and scripts
└── tsconfig.json        # TypeScript configuration
```

## 🔐 Authentication

The API uses JWT (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```bash
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### JWT Payload Structure

```json
{
  "sub": "user-id",
  "email": "<EMAIL>",
  "tenant_id": "tenant-id",
  "roles": ["admin", "experimenter"],
  "permissions": ["experiments:read", "experiments:write"],
  "iat": 1640995200,
  "exp": 1641081600,
  "iss": "ab-testing-api",
  "aud": "ab-testing-clients"
}
```

### Permissions

| Permission | Description |
|------------|-------------|
| `experiments:read` | View experiments |
| `experiments:write` | Create/update experiments |
| `experiments:delete` | Delete experiments |
| `experiments:control` | Start/pause/complete experiments |
| `assignments:read` | View user assignments |
| `assignments:write` | Create user assignments |
| `events:read` | View events |
| `events:write` | Track events |
| `analytics:read` | View analytics |

## 📡 API Endpoints

### Core Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/v1/experiments` | List experiments |
| `POST` | `/v1/experiments` | Create experiment |
| `GET` | `/v1/experiments/{id}` | Get experiment |
| `PUT` | `/v1/experiments/{id}` | Update experiment |
| `DELETE` | `/v1/experiments/{id}` | Delete experiment |

### Experiment Control

| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/v1/experiments/{id}/start` | Start experiment |
| `POST` | `/v1/experiments/{id}/pause` | Pause experiment |
| `POST` | `/v1/experiments/{id}/resume` | Resume experiment |
| `POST` | `/v1/experiments/{id}/complete` | Complete experiment |
| `POST` | `/v1/experiments/{id}/archive` | Archive experiment |

### User Assignment & Events

| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/v1/experiments/{id}/assignments` | Assign user |
| `GET` | `/v1/experiments/{id}/assignments` | List assignments |
| `POST` | `/v1/experiments/{id}/events` | Track event |
| `GET` | `/v1/experiments/{id}/events` | List events |
| `GET` | `/v1/experiments/{id}/analytics` | Get analytics |

### Utility Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/health` | Health check |
| `GET` | `/v1/metrics` | API metrics |
| `GET` | `/docs` | Swagger UI documentation |

## 🧪 Usage Examples

### Create an Experiment

```bash
curl -X POST "http://localhost:3000/v1/experiments" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Button Color Test",
    "description": "Testing button colors for conversion",
    "variants": [
      {
        "name": "Control (Blue)",
        "is_control": true,
        "traffic_weight": 0.5,
        "configuration": {"button_color": "#007bff"}
      },
      {
        "name": "Red Button",
        "traffic_weight": 0.5,
        "configuration": {"button_color": "#dc3545"}
      }
    ]
  }'
```

### Assign User to Experiment

```bash
curl -X POST "http://localhost:3000/v1/experiments/{experiment_id}/assignments" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user_12345",
    "user_attributes": {
      "country": "US",
      "device_type": "desktop"
    }
  }'
```

### Track an Event

```bash
curl -X POST "http://localhost:3000/v1/experiments/{experiment_id}/events" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user_12345",
    "event_name": "button_click",
    "event_value": 1
  }'
```

## 🔍 Monitoring

### Health Check

```bash
curl http://localhost:3000/health
```

Response:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:00:00Z",
  "uptime": 3600,
  "environment": "development",
  "version": "1.0.0",
  "services": {
    "database": "healthy",
    "redis": "healthy"
  }
}
```

### Metrics

```bash
curl http://localhost:3000/v1/metrics
```

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch
```

## 🚀 Deployment

### Production Build

```bash
# Build the application
npm run build

# Start production server
npm start
```

### Docker Deployment

```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY dist ./dist
COPY prisma ./prisma

EXPOSE 3000

CMD ["npm", "start"]
```

### Environment Variables for Production

```bash
NODE_ENV=production
DATABASE_URL="********************************/db?sslmode=require"
REDIS_URL="redis://redis-host:6379"
JWT_SECRET="your-production-secret"
LOG_LEVEL="warn"
ENABLE_SWAGGER_UI=false
```

## 🔧 Development

### Available Scripts

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run test` - Run tests
- `npm run lint` - Run ESLint
- `npm run format` - Format code with Prettier
- `npm run db:migrate` - Run database migrations
- `npm run db:generate` - Generate Prisma client

### Code Style

The project uses ESLint and Prettier for code formatting:

```bash
# Lint code
npm run lint

# Fix linting issues
npm run lint:fix

# Format code
npm run format
```

## 📝 API Documentation

Interactive API documentation is available at `/docs` when the server is running with Swagger UI enabled.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/ab_testing_db?schema=public"
DB_MAX_CONNECTIONS=10
DB_CONNECTION_TIMEOUT=5000
DB_QUERY_TIMEOUT=10000

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-change-in-production"
JWT_EXPIRES_IN="24h"
JWT_ISSUER="ab-testing-api"
JWT_AUDIENCE="ab-testing-clients"

# Redis Configuration (for rate limiting and caching)
REDIS_URL="redis://localhost:6379"
REDIS_KEY_PREFIX="ab-testing:"

# Rate Limiting Configuration
RATE_LIMIT_EXPERIMENTS=1000
RATE_LIMIT_ASSIGNMENTS=10000
RATE_LIMIT_EVENTS=10000

# CORS Configuration
CORS_ORIGIN="http://localhost:3000,http://localhost:3001"

# Logging Configuration
LOG_LEVEL="info"
LOG_FORMAT="combined"
LOG_FILENAME=""

# Security Configuration
BCRYPT_ROUNDS=12

# Pagination Configuration
DEFAULT_PAGE_LIMIT=20
MAX_PAGE_LIMIT=100

# Feature Flags
ENABLE_METRICS=true
ENABLE_SWAGGER_UI=true
ENABLE_REQUEST_LOGGING=true

# Build Information (set by CI/CD)
BUILD_DATE=""
GIT_COMMIT=""

# Production-specific settings (uncomment for production)
# NODE_ENV=production
# DATABASE_URL="***************************************************/ab_testing_prod?schema=public&sslmode=require"
# REDIS_URL="redis://production-redis:6379"
# JWT_SECRET="your-production-jwt-secret-with-high-entropy"
# LOG_LEVEL="warn"
# ENABLE_SWAGGER_UI=false

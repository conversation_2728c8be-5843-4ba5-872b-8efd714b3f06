// Response utility functions
export interface ResponseMeta {
  timestamp: string;
  request_id: string;
  tenant_id?: string;
}

export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  total_pages: number;
  has_next: boolean;
  has_prev: boolean;
}

export interface SuccessResponse<T> {
  data: T;
  meta: ResponseMeta;
}

export interface PaginatedResponse<T> {
  data: T[];
  meta: ResponseMeta;
  pagination: PaginationMeta;
}

/**
 * Build a success response with metadata
 */
export function buildSuccessResponse<T>(data: T, meta: ResponseMeta): SuccessResponse<T> {
  return {
    data,
    meta,
  };
}

/**
 * Build a paginated response with metadata
 */
export function buildPaginationResponse<T>(
  data: T[],
  pagination: {
    page: number;
    limit: number;
    total: number;
  },
  meta: ResponseMeta
): PaginatedResponse<T> {
  const totalPages = Math.ceil(pagination.total / pagination.limit);
  
  return {
    data,
    meta,
    pagination: {
      page: pagination.page,
      limit: pagination.limit,
      total: pagination.total,
      total_pages: totalPages,
      has_next: pagination.page < totalPages,
      has_prev: pagination.page > 1,
    },
  };
}

/**
 * Build an error response
 */
export function buildErrorResponse(
  code: string,
  message: string,
  details?: any,
  fieldErrors?: Array<{ field: string; message: string }>,
  meta?: ResponseMeta
) {
  return {
    error: {
      code,
      message,
      details,
      field_errors: fieldErrors,
    },
    meta: meta || {
      timestamp: new Date().toISOString(),
      request_id: 'unknown',
    },
  };
}

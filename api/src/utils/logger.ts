// Logging utility using <PERSON>
import winston from 'winston';
import config from '../config';

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Define colors for each level
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

// Tell winston that you want to link the colors
winston.addColors(colors);

// Define format for logs
const format = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.errors({ stack: true }),
  winston.format.colorize({ all: true }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}${
      info.stack ? `\n${info.stack}` : ''
    }${
      info.metadata && Object.keys(info.metadata).length > 0 
        ? `\n${JSON.stringify(info.metadata, null, 2)}` 
        : ''
    }`
  )
);

// Define which transports the logger must use
const transports = [
  // Console transport
  new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      format
    ),
  }),
];

// Add file transport in production
if (config.nodeEnv === 'production' && config.logging.filename) {
  transports.push(
    new winston.transports.File({
      filename: config.logging.filename,
      format: winston.format.combine(
        winston.format.uncolorize(),
        winston.format.json()
      ),
    })
  );
}

// Create the logger
export const logger = winston.createLogger({
  level: config.logging.level,
  levels,
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.metadata({ fillExcept: ['message', 'level', 'timestamp'] })
  ),
  transports,
  exitOnError: false,
});

// Create a stream object for Morgan HTTP logging
export const loggerStream = {
  write: (message: string) => {
    logger.http(message.trim());
  },
};

// Utility functions for structured logging

/**
 * Log request information
 */
export const logRequest = (req: any, res: any, responseTime?: number) => {
  const logData = {
    method: req.method,
    url: req.url,
    status: res.statusCode,
    responseTime: responseTime ? `${responseTime}ms` : undefined,
    userAgent: req.get('User-Agent'),
    ip: req.ip,
    userId: req.user?.id,
    tenantId: req.tenantId,
    requestId: req.headers['x-request-id'],
  };

  if (res.statusCode >= 400) {
    logger.warn('HTTP Request', logData);
  } else {
    logger.info('HTTP Request', logData);
  }
};

/**
 * Log database operations
 */
export const logDatabase = (operation: string, table: string, duration?: number, error?: Error) => {
  const logData = {
    operation,
    table,
    duration: duration ? `${duration}ms` : undefined,
    error: error ? {
      name: error.name,
      message: error.message,
    } : undefined,
  };

  if (error) {
    logger.error('Database Operation Failed', logData);
  } else if (duration && duration > 1000) {
    logger.warn('Slow Database Operation', logData);
  } else {
    logger.debug('Database Operation', logData);
  }
};

/**
 * Log authentication events
 */
export const logAuth = (event: string, userId?: string, tenantId?: string, details?: any) => {
  const logData = {
    event,
    userId,
    tenantId,
    ...details,
  };

  if (event.includes('failed') || event.includes('error')) {
    logger.warn('Authentication Event', logData);
  } else {
    logger.info('Authentication Event', logData);
  }
};

/**
 * Log business logic events
 */
export const logBusiness = (event: string, details?: any) => {
  logger.info('Business Event', { event, ...details });
};

/**
 * Log security events
 */
export const logSecurity = (event: string, severity: 'low' | 'medium' | 'high', details?: any) => {
  const logData = {
    event,
    severity,
    ...details,
  };

  if (severity === 'high') {
    logger.error('Security Event', logData);
  } else if (severity === 'medium') {
    logger.warn('Security Event', logData);
  } else {
    logger.info('Security Event', logData);
  }
};

/**
 * Log performance metrics
 */
export const logPerformance = (metric: string, value: number, unit: string, details?: any) => {
  const logData = {
    metric,
    value,
    unit,
    ...details,
  };

  if (metric.includes('response_time') && value > 1000) {
    logger.warn('Performance Metric', logData);
  } else {
    logger.debug('Performance Metric', logData);
  }
};

/**
 * Log external service calls
 */
export const logExternalService = (
  service: string,
  operation: string,
  duration?: number,
  error?: Error,
  details?: any
) => {
  const logData = {
    service,
    operation,
    duration: duration ? `${duration}ms` : undefined,
    error: error ? {
      name: error.name,
      message: error.message,
    } : undefined,
    ...details,
  };

  if (error) {
    logger.error('External Service Call Failed', logData);
  } else if (duration && duration > 5000) {
    logger.warn('Slow External Service Call', logData);
  } else {
    logger.info('External Service Call', logData);
  }
};

/**
 * Log experiment events
 */
export const logExperiment = (
  event: string,
  experimentId: string,
  tenantId: string,
  details?: any
) => {
  const logData = {
    event,
    experimentId,
    tenantId,
    ...details,
  };

  logger.info('Experiment Event', logData);
};

/**
 * Log user assignment events
 */
export const logUserAssignment = (
  experimentId: string,
  userId: string,
  variantId: string | null,
  isExcluded: boolean,
  tenantId: string,
  details?: any
) => {
  const logData = {
    event: 'user_assignment',
    experimentId,
    userId,
    variantId,
    isExcluded,
    tenantId,
    ...details,
  };

  logger.info('User Assignment', logData);
};

/**
 * Log event tracking
 */
export const logEventTracking = (
  experimentId: string,
  userId: string,
  eventName: string,
  eventValue?: number,
  tenantId?: string,
  details?: any
) => {
  const logData = {
    event: 'event_tracked',
    experimentId,
    userId,
    eventName,
    eventValue,
    tenantId,
    ...details,
  };

  logger.info('Event Tracking', logData);
};

// Error logging helpers

/**
 * Log validation errors
 */
export const logValidationError = (errors: any[], requestId?: string) => {
  logger.warn('Validation Error', {
    errors,
    requestId,
  });
};

/**
 * Log rate limit violations
 */
export const logRateLimit = (
  key: string,
  limit: number,
  windowMs: number,
  requestId?: string
) => {
  logger.warn('Rate Limit Exceeded', {
    key,
    limit,
    windowMs,
    requestId,
  });
};

/**
 * Log tenant isolation violations
 */
export const logTenantViolation = (
  userTenant: string,
  requestedTenant: string,
  userId?: string,
  requestId?: string
) => {
  logger.error('Tenant Isolation Violation', {
    userTenant,
    requestedTenant,
    userId,
    requestId,
  });
};

// Development helpers

/**
 * Log in development mode only
 */
export const logDev = (message: string, data?: any) => {
  if (config.nodeEnv === 'development') {
    logger.debug(`[DEV] ${message}`, data);
  }
};

/**
 * Log timing information
 */
export const logTiming = (operation: string, startTime: number, details?: any) => {
  const duration = Date.now() - startTime;
  logger.debug('Operation Timing', {
    operation,
    duration: `${duration}ms`,
    ...details,
  });
};

// Export default logger
export default logger;

// Custom error classes and utilities
import { StatusCodes } from 'http-status-codes';

export interface FieldError {
  field: string;
  message: string;
}

/**
 * Custom API Error class for consistent error handling
 */
export class ApiError extends Error {
  public readonly statusCode: number;
  public readonly code: string;
  public readonly details?: any;
  public readonly fieldErrors?: FieldError[];
  public readonly isOperational: boolean;

  constructor(
    message: string,
    code: string,
    statusCode: number = StatusCodes.INTERNAL_SERVER_ERROR,
    details?: any,
    fieldErrors?: FieldError[]
  ) {
    super(message);
    
    this.name = 'ApiError';
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
    this.fieldErrors = fieldErrors;
    this.isOperational = true;

    // Maintains proper stack trace for where our error was thrown
    Error.captureStackTrace(this, ApiError);
  }

  /**
   * Convert error to JSON format for API responses
   */
  toJSON() {
    return {
      code: this.code,
      message: this.message,
      details: this.details,
      field_errors: this.fieldErrors,
    };
  }

  /**
   * Check if error is operational (expected) vs programming error
   */
  static isOperational(error: Error): boolean {
    if (error instanceof ApiError) {
      return error.isOperational;
    }
    return false;
  }
}

/**
 * Validation Error - for request validation failures
 */
export class ValidationError extends ApiError {
  constructor(message: string, fieldErrors?: FieldError[], details?: any) {
    super(
      message,
      'VALIDATION_ERROR',
      StatusCodes.UNPROCESSABLE_ENTITY,
      details,
      fieldErrors
    );
    this.name = 'ValidationError';
  }
}

/**
 * Authentication Error - for auth failures
 */
export class AuthenticationError extends ApiError {
  constructor(message: string = 'Authentication required', details?: any) {
    super(message, 'UNAUTHORIZED', StatusCodes.UNAUTHORIZED, details);
    this.name = 'AuthenticationError';
  }
}

/**
 * Authorization Error - for permission failures
 */
export class AuthorizationError extends ApiError {
  constructor(message: string = 'Insufficient permissions', details?: any) {
    super(message, 'FORBIDDEN', StatusCodes.FORBIDDEN, details);
    this.name = 'AuthorizationError';
  }
}

/**
 * Not Found Error - for missing resources
 */
export class NotFoundError extends ApiError {
  constructor(resource: string, identifier?: string, details?: any) {
    const message = identifier 
      ? `${resource} with identifier '${identifier}' not found`
      : `${resource} not found`;
    
    super(message, `${resource.toUpperCase()}_NOT_FOUND`, StatusCodes.NOT_FOUND, details);
    this.name = 'NotFoundError';
  }
}

/**
 * Conflict Error - for resource conflicts
 */
export class ConflictError extends ApiError {
  constructor(message: string, details?: any) {
    super(message, 'CONFLICT', StatusCodes.CONFLICT, details);
    this.name = 'ConflictError';
  }
}

/**
 * Business Logic Error - for domain-specific errors
 */
export class BusinessLogicError extends ApiError {
  constructor(message: string, code: string, details?: any) {
    super(message, code, StatusCodes.BAD_REQUEST, details);
    this.name = 'BusinessLogicError';
  }
}

/**
 * Rate Limit Error - for rate limiting
 */
export class RateLimitError extends ApiError {
  constructor(message: string = 'Rate limit exceeded', retryAfter?: number) {
    super(
      message,
      'RATE_LIMIT_EXCEEDED',
      StatusCodes.TOO_MANY_REQUESTS,
      { retry_after: retryAfter }
    );
    this.name = 'RateLimitError';
  }
}

/**
 * Service Unavailable Error - for service outages
 */
export class ServiceUnavailableError extends ApiError {
  constructor(message: string = 'Service temporarily unavailable', details?: any) {
    super(message, 'SERVICE_UNAVAILABLE', StatusCodes.SERVICE_UNAVAILABLE, details);
    this.name = 'ServiceUnavailableError';
  }
}

/**
 * Database Error - for database-related errors
 */
export class DatabaseError extends ApiError {
  constructor(message: string, details?: any) {
    super(message, 'DATABASE_ERROR', StatusCodes.INTERNAL_SERVER_ERROR, details);
    this.name = 'DatabaseError';
  }
}

/**
 * External Service Error - for third-party service errors
 */
export class ExternalServiceError extends ApiError {
  constructor(service: string, message: string, details?: any) {
    super(
      `External service error: ${service} - ${message}`,
      'EXTERNAL_SERVICE_ERROR',
      StatusCodes.BAD_GATEWAY,
      { service, ...details }
    );
    this.name = 'ExternalServiceError';
  }
}

// Experiment-specific errors

/**
 * Experiment Status Error - for invalid status transitions
 */
export class ExperimentStatusError extends BusinessLogicError {
  constructor(currentStatus: string, requestedStatus: string, validTransitions?: string[]) {
    super(
      `Invalid status transition from ${currentStatus} to ${requestedStatus}`,
      'INVALID_STATUS_TRANSITION',
      {
        current_status: currentStatus,
        requested_status: requestedStatus,
        valid_transitions: validTransitions,
      }
    );
    this.name = 'ExperimentStatusError';
  }
}

/**
 * Variant Configuration Error - for variant-related errors
 */
export class VariantConfigurationError extends BusinessLogicError {
  constructor(message: string, details?: any) {
    super(message, 'VARIANT_CONFIGURATION_ERROR', details);
    this.name = 'VariantConfigurationError';
  }
}

/**
 * User Assignment Error - for assignment-related errors
 */
export class UserAssignmentError extends BusinessLogicError {
  constructor(message: string, details?: any) {
    super(message, 'USER_ASSIGNMENT_ERROR', details);
    this.name = 'UserAssignmentError';
  }
}

/**
 * Targeting Rule Error - for targeting-related errors
 */
export class TargetingRuleError extends BusinessLogicError {
  constructor(message: string, details?: any) {
    super(message, 'TARGETING_RULE_ERROR', details);
    this.name = 'TargetingRuleError';
  }
}

/**
 * Tenant Isolation Error - for multi-tenancy violations
 */
export class TenantIsolationError extends AuthorizationError {
  constructor(message: string = 'Tenant access violation', details?: any) {
    super(message, { code: 'TENANT_ISOLATION_VIOLATION', ...details });
    this.name = 'TenantIsolationError';
  }
}

// Error factory functions

/**
 * Create a validation error with field-level errors
 */
export function createValidationError(
  message: string,
  fieldErrors: FieldError[],
  details?: any
): ValidationError {
  return new ValidationError(message, fieldErrors, details);
}

/**
 * Create a not found error for a specific resource
 */
export function createNotFoundError(
  resource: string,
  identifier?: string,
  details?: any
): NotFoundError {
  return new NotFoundError(resource, identifier, details);
}

/**
 * Create a conflict error
 */
export function createConflictError(message: string, details?: any): ConflictError {
  return new ConflictError(message, details);
}

/**
 * Create an experiment status error
 */
export function createExperimentStatusError(
  currentStatus: string,
  requestedStatus: string,
  validTransitions?: string[]
): ExperimentStatusError {
  return new ExperimentStatusError(currentStatus, requestedStatus, validTransitions);
}

/**
 * Create a tenant isolation error
 */
export function createTenantIsolationError(
  userTenant: string,
  requestedTenant: string
): TenantIsolationError {
  return new TenantIsolationError(
    'Access denied: tenant mismatch',
    {
      user_tenant: userTenant,
      requested_tenant: requestedTenant,
    }
  );
}

// Error type guards

/**
 * Check if error is an API error
 */
export function isApiError(error: any): error is ApiError {
  return error instanceof ApiError;
}

/**
 * Check if error is a validation error
 */
export function isValidationError(error: any): error is ValidationError {
  return error instanceof ValidationError;
}

/**
 * Check if error is an authentication error
 */
export function isAuthenticationError(error: any): error is AuthenticationError {
  return error instanceof AuthenticationError;
}

/**
 * Check if error is an authorization error
 */
export function isAuthorizationError(error: any): error is AuthorizationError {
  return error instanceof AuthorizationError;
}

/**
 * Check if error is a not found error
 */
export function isNotFoundError(error: any): error is NotFoundError {
  return error instanceof NotFoundError;
}

/**
 * Check if error is a conflict error
 */
export function isConflictError(error: any): error is ConflictError {
  return error instanceof ConflictError;
}

/**
 * Check if error is a rate limit error
 */
export function isRateLimitError(error: any): error is RateLimitError {
  return error instanceof RateLimitError;
}

// Express application setup
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import swaggerUi from 'swagger-ui-express';
import YAML from 'yamljs';
import path from 'path';
import 'express-async-errors';

import config from './config';
import { logger, loggerStream } from './utils/logger';
import { errorHandler, notFoundHandler } from './middleware/errorHandler';
import { applyRateLimiting, addRateLimitHeaders } from './middleware/rateLimiting';
import routes from './routes';

// Load OpenAPI specification
let swaggerDocument: any = null;
if (config.features.enableSwaggerUI) {
  try {
    const swaggerPath = path.join(__dirname, '../../openapi.yaml');
    swaggerDocument = YAML.load(swaggerPath);
  } catch (error) {
    logger.warn('Could not load OpenAPI specification', { error });
  }
}

/**
 * Create Express application with all middleware and routes
 */
export function createApp(): express.Application {
  const app = express();

  // Trust proxy (important for rate limiting and IP detection)
  app.set('trust proxy', 1);

  // Security middleware
  app.use(helmet(config.security.helmet));

  // CORS middleware
  app.use(cors(config.cors));

  // Compression middleware
  app.use(compression());

  // Request parsing middleware
  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true, limit: '10mb' }));

  // Request ID middleware
  app.use((req, res, next) => {
    req.headers['x-request-id'] = req.headers['x-request-id'] || 
      `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    res.setHeader('X-Request-ID', req.headers['x-request-id'] as string);
    next();
  });

  // Request logging middleware
  if (config.features.enableRequestLogging) {
    app.use((req, res, next) => {
      const startTime = Date.now();
      
      res.on('finish', () => {
        const duration = Date.now() - startTime;
        logger.info('HTTP Request', {
          method: req.method,
          url: req.url,
          status: res.statusCode,
          duration: `${duration}ms`,
          userAgent: req.get('User-Agent'),
          ip: req.ip,
          requestId: req.headers['x-request-id'],
        });
      });
      
      next();
    });
  }

  // Rate limiting middleware
  app.use(addRateLimitHeaders);
  app.use(applyRateLimiting);

  // API documentation
  if (config.features.enableSwaggerUI && swaggerDocument) {
    app.use(
      config.api.docsPath,
      swaggerUi.serve,
      swaggerUi.setup(swaggerDocument, {
        customCss: '.swagger-ui .topbar { display: none }',
        customSiteTitle: 'A/B Testing API Documentation',
        customfavIcon: '/favicon.ico',
        swaggerOptions: {
          persistAuthorization: true,
          displayRequestDuration: true,
          filter: true,
          showExtensions: true,
          showCommonExtensions: true,
        },
      })
    );
  }

  // API routes
  app.use(config.api.basePath, routes);

  // Health check at root level (outside versioned API)
  app.get('/health', (req, res) => {
    res.status(200).json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    });
  });

  // Redirect root to API info
  app.get('/', (req, res) => {
    res.redirect(config.api.basePath);
  });

  // 404 handler for unknown routes
  app.use('*', notFoundHandler);

  // Global error handler (must be last)
  app.use(errorHandler);

  return app;
}

/**
 * Start the Express server
 */
export function startServer(app: express.Application): Promise<void> {
  return new Promise((resolve, reject) => {
    const server = app.listen(config.port, () => {
      logger.info('Server started successfully', {
        port: config.port,
        environment: config.nodeEnv,
        apiBasePath: config.api.basePath,
        docsPath: config.features.enableSwaggerUI ? config.api.docsPath : 'disabled',
        features: {
          authentication: true,
          rateLimiting: true,
          tenantIsolation: true,
          requestValidation: true,
          errorHandling: true,
          logging: config.features.enableRequestLogging,
          metrics: config.features.enableMetrics,
          swaggerUI: config.features.enableSwaggerUI,
        },
      });
      resolve();
    });

    server.on('error', (error: Error) => {
      logger.error('Server startup failed', { error });
      reject(error);
    });

    // Graceful shutdown handling
    const gracefulShutdown = (signal: string) => {
      logger.info(`Received ${signal}, starting graceful shutdown`);
      
      server.close((err) => {
        if (err) {
          logger.error('Error during server shutdown', { error: err });
          process.exit(1);
        }
        
        logger.info('Server closed successfully');
        process.exit(0);
      });

      // Force shutdown after 30 seconds
      setTimeout(() => {
        logger.error('Forced shutdown after timeout');
        process.exit(1);
      }, 30000);
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
  });
}

// Export the app for testing
export default createApp;

// Server entry point
import dotenv from 'dotenv';

// Load environment variables first
dotenv.config();

import { createApp, startServer } from './app';
import { logger } from './utils/logger';
import { 
  unhandledRejectionHandler, 
  uncaughtExceptionHandler 
} from './middleware/errorHandler';
import { closeRateLimitStore } from './middleware/rateLimiting';
import config from './config';

// Handle unhandled promise rejections
process.on('unhandledRejection', unhandledRejectionHandler);

// Handle uncaught exceptions
process.on('uncaughtException', uncaughtExceptionHandler);

/**
 * Bootstrap the application
 */
async function bootstrap(): Promise<void> {
  try {
    logger.info('Starting A/B Testing API server', {
      environment: config.nodeEnv,
      port: config.port,
      nodeVersion: process.version,
    });

    // Create Express application
    const app = createApp();

    // Start the server
    await startServer(app);

    logger.info('Application bootstrap completed successfully');
  } catch (error) {
    logger.error('Application bootstrap failed', { error });
    process.exit(1);
  }
}

/**
 * Graceful shutdown handler
 */
async function gracefulShutdown(signal: string): Promise<void> {
  logger.info(`Received ${signal}, initiating graceful shutdown`);

  try {
    // Close rate limiting store (Redis connection)
    await closeRateLimitStore();
    logger.info('Rate limiting store closed');

    // Close database connections (handled by Prisma)
    // In a real application, you might want to explicitly close connections
    
    logger.info('Graceful shutdown completed');
    process.exit(0);
  } catch (error) {
    logger.error('Error during graceful shutdown', { error });
    process.exit(1);
  }
}

// Register shutdown handlers
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Start the application
if (require.main === module) {
  bootstrap().catch((error) => {
    logger.error('Failed to start application', { error });
    process.exit(1);
  });
}

export { bootstrap };

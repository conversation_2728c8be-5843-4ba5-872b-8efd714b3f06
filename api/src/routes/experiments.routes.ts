// Experiments routes
import { Router } from 'express';
import { ExperimentsController } from '../controllers/experiments.controller';
import { AssignmentsController } from '../controllers/assignments.controller';
import { EventsController } from '../controllers/events.controller';
import { AnalyticsController } from '../controllers/analytics.controller';
import { authenticate, authorize, PERMISSIONS } from '../middleware/auth';
import { validate } from '../middleware/validation';
import { 
  experimentSchemas, 
  assignmentSchemas, 
  eventSchemas, 
  analyticsSchemas 
} from '../middleware/validation';
import { 
  experimentManagementLimiter, 
  userAssignmentLimiter, 
  eventTrackingLimiter 
} from '../middleware/rateLimiting';
import { asyncHandler } from '../middleware/errorHandler';

const router = Router();

// Initialize controllers
const experimentsController = new ExperimentsController();
const assignmentsController = new AssignmentsController();
const eventsController = new EventsController();
const analyticsController = new AnalyticsController();

// Apply authentication to all routes
router.use(authenticate);

// Experiment CRUD routes
router.get(
  '/',
  experimentManagementLimiter,
  authorize([PERMISSIONS.EXPERIMENTS_READ]),
  validate(experimentSchemas.listExperiments),
  asyncHandler(experimentsController.listExperiments)
);

router.post(
  '/',
  experimentManagementLimiter,
  authorize([PERMISSIONS.EXPERIMENTS_WRITE]),
  validate(experimentSchemas.createExperiment),
  asyncHandler(experimentsController.createExperiment)
);

router.get(
  '/:experimentId',
  experimentManagementLimiter,
  authorize([PERMISSIONS.EXPERIMENTS_READ]),
  validate(experimentSchemas.getExperiment),
  asyncHandler(experimentsController.getExperiment)
);

router.put(
  '/:experimentId',
  experimentManagementLimiter,
  authorize([PERMISSIONS.EXPERIMENTS_WRITE]),
  validate(experimentSchemas.updateExperiment),
  asyncHandler(experimentsController.updateExperiment)
);

router.delete(
  '/:experimentId',
  experimentManagementLimiter,
  authorize([PERMISSIONS.EXPERIMENTS_DELETE]),
  validate({ params: experimentSchemas.updateExperiment.params }),
  asyncHandler(experimentsController.deleteExperiment)
);

// Experiment control routes
router.post(
  '/:experimentId/start',
  experimentManagementLimiter,
  authorize([PERMISSIONS.EXPERIMENTS_CONTROL]),
  validate(experimentSchemas.startExperiment),
  asyncHandler(experimentsController.startExperiment)
);

router.post(
  '/:experimentId/pause',
  experimentManagementLimiter,
  authorize([PERMISSIONS.EXPERIMENTS_CONTROL]),
  validate(experimentSchemas.pauseExperiment),
  asyncHandler(experimentsController.pauseExperiment)
);

router.post(
  '/:experimentId/resume',
  experimentManagementLimiter,
  authorize([PERMISSIONS.EXPERIMENTS_CONTROL]),
  validate({ params: experimentSchemas.pauseExperiment.params }),
  asyncHandler(experimentsController.resumeExperiment)
);

router.post(
  '/:experimentId/complete',
  experimentManagementLimiter,
  authorize([PERMISSIONS.EXPERIMENTS_CONTROL]),
  validate(experimentSchemas.completeExperiment),
  asyncHandler(experimentsController.completeExperiment)
);

router.post(
  '/:experimentId/archive',
  experimentManagementLimiter,
  authorize([PERMISSIONS.EXPERIMENTS_CONTROL]),
  validate({ params: experimentSchemas.pauseExperiment.params }),
  asyncHandler(experimentsController.archiveExperiment)
);

// User assignment routes
router.post(
  '/:experimentId/assignments',
  userAssignmentLimiter,
  authorize([PERMISSIONS.ASSIGNMENTS_WRITE]),
  validate(assignmentSchemas.assignUser),
  asyncHandler(assignmentsController.assignUser)
);

router.get(
  '/:experimentId/assignments',
  experimentManagementLimiter,
  authorize([PERMISSIONS.ASSIGNMENTS_READ]),
  validate(assignmentSchemas.listAssignments),
  asyncHandler(assignmentsController.listAssignments)
);

router.get(
  '/:experimentId/assignments/stats',
  experimentManagementLimiter,
  authorize([PERMISSIONS.ASSIGNMENTS_READ]),
  validate({ params: assignmentSchemas.assignUser.params }),
  asyncHandler(assignmentsController.getAssignmentStats)
);

router.post(
  '/:experimentId/assignments/bulk',
  userAssignmentLimiter,
  authorize([PERMISSIONS.ASSIGNMENTS_WRITE]),
  validate({ params: assignmentSchemas.assignUser.params }),
  asyncHandler(assignmentsController.bulkAssignUsers)
);

// Event tracking routes
router.post(
  '/:experimentId/events',
  eventTrackingLimiter,
  authorize([PERMISSIONS.EVENTS_WRITE]),
  validate(eventSchemas.trackEvent),
  asyncHandler(eventsController.trackEvent)
);

router.get(
  '/:experimentId/events',
  experimentManagementLimiter,
  authorize([PERMISSIONS.EVENTS_READ]),
  validate(eventSchemas.listEvents),
  asyncHandler(eventsController.listEvents)
);

router.get(
  '/:experimentId/events/stats',
  experimentManagementLimiter,
  authorize([PERMISSIONS.EVENTS_READ]),
  validate({ params: eventSchemas.trackEvent.params }),
  asyncHandler(eventsController.getEventStats)
);

router.get(
  '/:experimentId/events/funnel',
  experimentManagementLimiter,
  authorize([PERMISSIONS.EVENTS_READ]),
  validate({ params: eventSchemas.trackEvent.params }),
  asyncHandler(eventsController.getConversionFunnel)
);

router.get(
  '/:experimentId/events/names',
  experimentManagementLimiter,
  authorize([PERMISSIONS.EVENTS_READ]),
  validate({ params: eventSchemas.trackEvent.params }),
  asyncHandler(eventsController.getEventNames)
);

router.post(
  '/events/bulk',
  eventTrackingLimiter,
  authorize([PERMISSIONS.EVENTS_WRITE]),
  asyncHandler(eventsController.bulkTrackEvents)
);

// Analytics routes
router.get(
  '/:experimentId/analytics',
  experimentManagementLimiter,
  authorize([PERMISSIONS.ANALYTICS_READ]),
  validate(analyticsSchemas.getAnalytics),
  asyncHandler(analyticsController.getExperimentAnalytics)
);

router.get(
  '/:experimentId/analytics/stats',
  experimentManagementLimiter,
  authorize([PERMISSIONS.ANALYTICS_READ]),
  validate({ params: analyticsSchemas.getAnalytics.params }),
  asyncHandler(analyticsController.getExperimentStats)
);

router.get(
  '/:experimentId/analytics/realtime',
  experimentManagementLimiter,
  authorize([PERMISSIONS.ANALYTICS_READ]),
  validate({ params: analyticsSchemas.getAnalytics.params }),
  asyncHandler(analyticsController.getRealTimeMetrics)
);

router.get(
  '/:experimentId/analytics/summary',
  experimentManagementLimiter,
  authorize([PERMISSIONS.ANALYTICS_READ]),
  validate({ params: analyticsSchemas.getAnalytics.params }),
  asyncHandler(analyticsController.getPerformanceSummary)
);

router.get(
  '/:experimentId/analytics/export',
  experimentManagementLimiter,
  authorize([PERMISSIONS.ANALYTICS_READ]),
  validate({ params: analyticsSchemas.getAnalytics.params }),
  asyncHandler(analyticsController.exportExperimentData)
);

// Additional utility routes
router.get(
  '/assignments/history',
  experimentManagementLimiter,
  authorize([PERMISSIONS.ASSIGNMENTS_READ]),
  asyncHandler(assignmentsController.getUserAssignmentHistory)
);

router.get(
  '/analytics/dashboard',
  experimentManagementLimiter,
  authorize([PERMISSIONS.ANALYTICS_READ]),
  asyncHandler(analyticsController.getTenantDashboard)
);

export default router;

// Main routes index
import { Router } from 'express';
import { Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import experimentsRoutes from './experiments.routes';
import { buildSuccessResponse } from '../utils/response';
import { logger } from '../utils/logger';
import config from '../config';

const router = Router();

// Health check endpoint
router.get('/health', (req: Request, res: Response) => {
  const healthCheck = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: config.nodeEnv,
    version: process.env.npm_package_version || '1.0.0',
    node_version: process.version,
    memory: process.memoryUsage(),
    // Add more health indicators as needed
    services: {
      database: 'healthy', // In real implementation, check database connection
      redis: 'healthy',    // In real implementation, check Redis connection
    },
  };

  res.status(StatusCodes.OK).json(healthCheck);
});

// Metrics endpoint (basic implementation)
router.get('/metrics', (req: Request, res: Response) => {
  const metrics = {
    timestamp: new Date().toISOString(),
    uptime_seconds: process.uptime(),
    memory_usage: process.memoryUsage(),
    cpu_usage: process.cpuUsage(),
    // In a real implementation, you'd include:
    // - Request counts by endpoint
    // - Response times
    // - Error rates
    // - Database query metrics
    // - Rate limiting metrics
    // - Business metrics (experiments, assignments, events)
  };

  res.status(StatusCodes.OK).json(metrics);
});

// API info endpoint
router.get('/', (req: Request, res: Response) => {
  const apiInfo = {
    name: 'A/B Testing Experiment Management API',
    version: '1.0.0',
    description: 'RESTful API for managing A/B testing experiments with multi-tenant support',
    documentation: {
      openapi: `${req.protocol}://${req.get('host')}/docs`,
      postman: 'https://documenter.getpostman.com/view/your-collection',
    },
    endpoints: {
      experiments: `${req.protocol}://${req.get('host')}/v1/experiments`,
      health: `${req.protocol}://${req.get('host')}/v1/health`,
      metrics: `${req.protocol}://${req.get('host')}/v1/metrics`,
    },
    features: [
      'Complete experiment lifecycle management',
      'Real-time user assignment with targeting',
      'Event tracking and analytics',
      'Multi-tenant isolation',
      'Rate limiting and authentication',
      'Comprehensive error handling',
    ],
    rate_limits: {
      experiment_management: `${config.rateLimiting.experimentManagement.max} requests per hour`,
      user_assignment: `${config.rateLimiting.userAssignment.max} requests per hour`,
      event_tracking: `${config.rateLimiting.eventTracking.max} requests per hour`,
    },
  };

  const response = buildSuccessResponse(apiInfo, {
    timestamp: new Date().toISOString(),
    request_id: req.headers['x-request-id'] as string || 'unknown',
  });

  res.status(StatusCodes.OK).json(response);
});

// Mount experiment routes
router.use('/experiments', experimentsRoutes);

// Catch-all for API versioning info
router.get('/version', (req: Request, res: Response) => {
  const versionInfo = {
    api_version: 'v1',
    build_date: process.env.BUILD_DATE || new Date().toISOString(),
    git_commit: process.env.GIT_COMMIT || 'unknown',
    environment: config.nodeEnv,
    features: {
      authentication: true,
      rate_limiting: true,
      tenant_isolation: true,
      request_validation: true,
      error_handling: true,
      logging: true,
      metrics: config.features.enableMetrics,
      swagger_ui: config.features.enableSwaggerUI,
    },
  };

  const response = buildSuccessResponse(versionInfo, {
    timestamp: new Date().toISOString(),
    request_id: req.headers['x-request-id'] as string || 'unknown',
  });

  res.status(StatusCodes.OK).json(response);
});

export default router;

// Error handling middleware
import { Request, Response, NextFunction } from 'express';
import { StatusCodes } from 'http-status-codes';
import { Prisma } from '@prisma/client';
import { JsonWebTokenError, TokenExpiredError } from 'jsonwebtoken';
import { ValidationError } from 'joi';
import config from '../config';
import { logger } from '../utils/logger';
import { ApiError } from '../utils/errors';

export interface ErrorResponse {
  error: {
    code: string;
    message: string;
    details?: any;
    field_errors?: Array<{ field: string; message: string }>;
    stack?: string;
  };
  meta: {
    timestamp: string;
    request_id: string;
    tenant_id?: string;
  };
}

/**
 * Global error handling middleware
 */
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Skip if response already sent
  if (res.headersSent) {
    return next(error);
  }

  const requestId = req.headers['x-request-id'] as string || 'unknown';
  const tenantId = (req as any).tenantId;

  // Log error details
  logger.error('Request error', {
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
    },
    request: {
      method: req.method,
      url: req.url,
      headers: req.headers,
      body: req.body,
      params: req.params,
      query: req.query,
    },
    requestId,
    tenantId,
  });

  let apiError: ApiError;

  // Convert different error types to ApiError
  if (error instanceof ApiError) {
    apiError = error;
  } else if (error instanceof ValidationError) {
    apiError = handleJoiValidationError(error);
  } else if (error instanceof JsonWebTokenError) {
    apiError = handleJWTError(error);
  } else if (error instanceof Prisma.PrismaClientKnownRequestError) {
    apiError = handlePrismaError(error);
  } else if (error instanceof Prisma.PrismaClientValidationError) {
    apiError = new ApiError(
      'Database validation error',
      'DATABASE_VALIDATION_ERROR',
      StatusCodes.BAD_REQUEST,
      { original_error: error.message }
    );
  } else if (error instanceof Prisma.PrismaClientInitializationError) {
    apiError = new ApiError(
      'Database connection error',
      'DATABASE_CONNECTION_ERROR',
      StatusCodes.SERVICE_UNAVAILABLE,
      { original_error: error.message }
    );
  } else if (error.name === 'SyntaxError' && 'body' in error) {
    apiError = new ApiError(
      'Invalid JSON in request body',
      'INVALID_JSON',
      StatusCodes.BAD_REQUEST
    );
  } else {
    // Unknown error - don't expose internal details in production
    apiError = new ApiError(
      config.nodeEnv === 'production' 
        ? 'An unexpected error occurred' 
        : error.message,
      'INTERNAL_SERVER_ERROR',
      StatusCodes.INTERNAL_SERVER_ERROR,
      config.nodeEnv === 'production' ? undefined : { original_error: error.message }
    );
  }

  // Build error response
  const errorResponse: ErrorResponse = {
    error: {
      code: apiError.code,
      message: apiError.message,
      details: apiError.details,
      field_errors: apiError.fieldErrors,
    },
    meta: {
      timestamp: new Date().toISOString(),
      request_id: requestId,
      tenant_id: tenantId,
    },
  };

  // Include stack trace in development
  if (config.nodeEnv === 'development') {
    errorResponse.error.stack = error.stack;
  }

  // Send error response
  res.status(apiError.statusCode).json(errorResponse);
};

/**
 * Handle 404 errors for unknown routes
 */
export const notFoundHandler = (req: Request, res: Response): void => {
  const error = new ApiError(
    `Route ${req.method} ${req.path} not found`,
    'ROUTE_NOT_FOUND',
    StatusCodes.NOT_FOUND,
    {
      method: req.method,
      path: req.path,
      available_routes: [
        'GET /v1/experiments',
        'POST /v1/experiments',
        'GET /v1/experiments/:id',
        'PUT /v1/experiments/:id',
        'DELETE /v1/experiments/:id',
        'POST /v1/experiments/:id/start',
        'POST /v1/experiments/:id/pause',
        'POST /v1/experiments/:id/resume',
        'POST /v1/experiments/:id/complete',
        'POST /v1/experiments/:id/archive',
        'GET /v1/experiments/:id/variants',
        'POST /v1/experiments/:id/variants',
        'POST /v1/experiments/:id/assignments',
        'POST /v1/experiments/:id/events',
        'GET /v1/experiments/:id/analytics',
      ],
    }
  );

  const errorResponse: ErrorResponse = {
    error: {
      code: error.code,
      message: error.message,
      details: error.details,
    },
    meta: {
      timestamp: new Date().toISOString(),
      request_id: req.headers['x-request-id'] as string || 'unknown',
      tenant_id: (req as any).tenantId,
    },
  };

  res.status(error.statusCode).json(errorResponse);
};

/**
 * Async error wrapper for route handlers
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// Helper functions for specific error types

function handleJoiValidationError(error: ValidationError): ApiError {
  const fieldErrors = error.details.map(detail => ({
    field: detail.path.join('.'),
    message: detail.message,
  }));

  return new ApiError(
    'Request validation failed',
    'VALIDATION_ERROR',
    StatusCodes.UNPROCESSABLE_ENTITY,
    { field_errors: fieldErrors }
  );
}

function handleJWTError(error: JsonWebTokenError): ApiError {
  if (error instanceof TokenExpiredError) {
    return new ApiError(
      'Token has expired',
      'TOKEN_EXPIRED',
      StatusCodes.UNAUTHORIZED,
      { expired_at: error.expiredAt }
    );
  }

  return new ApiError(
    'Invalid token',
    'INVALID_TOKEN',
    StatusCodes.UNAUTHORIZED,
    { jwt_error: error.message }
  );
}

function handlePrismaError(error: Prisma.PrismaClientKnownRequestError): ApiError {
  switch (error.code) {
    case 'P2002':
      // Unique constraint violation
      const target = error.meta?.target as string[] || [];
      return new ApiError(
        `Duplicate value for ${target.join(', ')}`,
        'DUPLICATE_VALUE',
        StatusCodes.CONFLICT,
        { 
          fields: target,
          constraint: error.meta?.constraint_name 
        }
      );

    case 'P2025':
      // Record not found
      return new ApiError(
        'Record not found',
        'RECORD_NOT_FOUND',
        StatusCodes.NOT_FOUND,
        { 
          cause: error.meta?.cause,
          model: error.meta?.modelName 
        }
      );

    case 'P2003':
      // Foreign key constraint violation
      return new ApiError(
        'Referenced record does not exist',
        'FOREIGN_KEY_VIOLATION',
        StatusCodes.BAD_REQUEST,
        { 
          field: error.meta?.field_name,
          constraint: error.meta?.constraint_name 
        }
      );

    case 'P2014':
      // Required relation violation
      return new ApiError(
        'Required relation is missing',
        'REQUIRED_RELATION_VIOLATION',
        StatusCodes.BAD_REQUEST,
        { 
          relation: error.meta?.relation_name,
          model: error.meta?.model_a_name 
        }
      );

    case 'P2021':
      // Table does not exist
      return new ApiError(
        'Database table does not exist',
        'TABLE_NOT_FOUND',
        StatusCodes.INTERNAL_SERVER_ERROR,
        { table: error.meta?.table }
      );

    case 'P2022':
      // Column does not exist
      return new ApiError(
        'Database column does not exist',
        'COLUMN_NOT_FOUND',
        StatusCodes.INTERNAL_SERVER_ERROR,
        { column: error.meta?.column }
      );

    default:
      return new ApiError(
        'Database operation failed',
        'DATABASE_ERROR',
        StatusCodes.INTERNAL_SERVER_ERROR,
        { 
          prisma_code: error.code,
          prisma_meta: error.meta 
        }
      );
  }
}

/**
 * Middleware to catch unhandled promise rejections
 */
export const unhandledRejectionHandler = (reason: any, promise: Promise<any>) => {
  logger.error('Unhandled Promise Rejection', {
    reason: reason instanceof Error ? {
      name: reason.name,
      message: reason.message,
      stack: reason.stack,
    } : reason,
    promise: promise.toString(),
  });

  // In production, you might want to gracefully shutdown the server
  if (config.nodeEnv === 'production') {
    logger.error('Shutting down due to unhandled promise rejection');
    process.exit(1);
  }
};

/**
 * Middleware to catch uncaught exceptions
 */
export const uncaughtExceptionHandler = (error: Error) => {
  logger.error('Uncaught Exception', {
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
    },
  });

  // Always exit on uncaught exceptions
  logger.error('Shutting down due to uncaught exception');
  process.exit(1);
};

/**
 * Health check error handler
 */
export const healthCheckError = (error: Error): { status: string; error: string } => {
  logger.error('Health check failed', { error: error.message });
  
  return {
    status: 'unhealthy',
    error: error.message,
  };
};

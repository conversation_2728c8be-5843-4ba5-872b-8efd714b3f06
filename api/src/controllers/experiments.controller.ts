// Experiments controller
import { Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import { AuthenticatedRequest } from '../middleware/auth';
import { ExperimentService } from '../../../src/services/experiment.service';
import { ExperimentRepository } from '../../../src/repositories/experiment.repository';
import { VariantRepository } from '../../../src/repositories/variant.repository';
import { TargetingRuleRepository } from '../../../src/repositories/targeting-rule.repository';
import { UserAssignmentRepository } from '../../../src/repositories/user-assignment.repository';
import { EventRepository } from '../../../src/repositories/event.repository';
import { 
  ApiError, 
  NotFoundError, 
  ConflictError, 
  ExperimentStatusError,
  BusinessLogicError 
} from '../utils/errors';
import { logger, logExperiment } from '../utils/logger';
import { buildPaginationResponse, buildSuccessResponse } from '../utils/response';

export class ExperimentsController {
  private experimentService: ExperimentService;
  private experimentRepo: ExperimentRepository;
  private variantRepo: VariantRepository;
  private targetingRuleRepo: TargetingRuleRepository;
  private userAssignmentRepo: UserAssignmentRepository;
  private eventRepo: EventRepository;

  constructor() {
    this.experimentService = new ExperimentService();
    this.experimentRepo = new ExperimentRepository();
    this.variantRepo = new VariantRepository();
    this.targetingRuleRepo = new TargetingRuleRepository();
    this.userAssignmentRepo = new UserAssignmentRepository();
    this.eventRepo = new EventRepository();
  }

  /**
   * List experiments with filtering and pagination
   */
  public listExperiments = async (req: Request, res: Response): Promise<void> => {
    const authReq = req as AuthenticatedRequest;
    const { 
      page = 1, 
      limit = 20, 
      sort_by = 'created_at', 
      sort_order = 'desc',
      status,
      tags,
      created_by,
      search,
      start_date_from,
      start_date_to,
    } = req.query;

    try {
      const filters = {
        status: status ? (Array.isArray(status) ? status : [status]) : undefined,
        tags: tags ? (Array.isArray(tags) ? tags : [tags]) : undefined,
        createdBy: created_by as string,
        search: search as string,
        startDateFrom: start_date_from ? new Date(start_date_from as string) : undefined,
        startDateTo: start_date_to ? new Date(start_date_to as string) : undefined,
      };

      const options = {
        orderBy: { [sort_by as string]: sort_order },
        pagination: { 
          page: parseInt(page as string, 10), 
          limit: parseInt(limit as string, 10) 
        },
      };

      const result = await this.experimentRepo.findWithFilters(
        authReq.tenantId,
        filters,
        options
      );

      const response = buildPaginationResponse(result.data, result.pagination, {
        timestamp: new Date().toISOString(),
        request_id: req.headers['x-request-id'] as string,
        tenant_id: authReq.tenantId,
      });

      res.status(StatusCodes.OK).json(response);
    } catch (error) {
      logger.error('Error listing experiments', { 
        error, 
        tenantId: authReq.tenantId,
        filters,
        requestId: req.headers['x-request-id']
      });
      throw error;
    }
  };

  /**
   * Get a specific experiment by ID
   */
  public getExperiment = async (req: Request, res: Response): Promise<void> => {
    const authReq = req as AuthenticatedRequest;
    const { experimentId } = req.params;
    const { include } = req.query;

    try {
      let experiment;

      if (include && (include as string[]).length > 0) {
        experiment = await this.experimentRepo.findByIdWithRelations(
          experimentId,
          authReq.tenantId
        );
      } else {
        experiment = await this.experimentRepo.findById(experimentId, authReq.tenantId);
      }

      if (!experiment) {
        throw new NotFoundError('Experiment', experimentId);
      }

      const response = buildSuccessResponse(experiment, {
        timestamp: new Date().toISOString(),
        request_id: req.headers['x-request-id'] as string,
        tenant_id: authReq.tenantId,
      });

      res.status(StatusCodes.OK).json(response);
    } catch (error) {
      logger.error('Error getting experiment', { 
        error, 
        experimentId,
        tenantId: authReq.tenantId,
        requestId: req.headers['x-request-id']
      });
      throw error;
    }
  };

  /**
   * Create a new experiment
   */
  public createExperiment = async (req: Request, res: Response): Promise<void> => {
    const authReq = req as AuthenticatedRequest;
    const experimentData = req.body;

    try {
      // Extract variants and targeting rules from request
      const { variants, targeting_rules, ...experimentConfig } = experimentData;

      // Add created_by field
      experimentConfig.createdBy = authReq.user.id;

      // Create experiment with variants and targeting rules
      const result = await this.experimentService.createExperiment(
        experimentConfig,
        variants || [],
        targeting_rules || [],
        { tenantId: authReq.tenantId }
      );

      logExperiment('experiment_created', result.experiment.id, authReq.tenantId, {
        experimentName: result.experiment.name,
        variantCount: result.variants.length,
        targetingRuleCount: result.targetingRules.length,
        createdBy: authReq.user.id,
      });

      const response = buildSuccessResponse(result, {
        timestamp: new Date().toISOString(),
        request_id: req.headers['x-request-id'] as string,
        tenant_id: authReq.tenantId,
      });

      res.status(StatusCodes.CREATED).json(response);
    } catch (error) {
      logger.error('Error creating experiment', { 
        error, 
        experimentData,
        tenantId: authReq.tenantId,
        userId: authReq.user.id,
        requestId: req.headers['x-request-id']
      });
      throw error;
    }
  };

  /**
   * Update an existing experiment
   */
  public updateExperiment = async (req: Request, res: Response): Promise<void> => {
    const authReq = req as AuthenticatedRequest;
    const { experimentId } = req.params;
    const updateData = req.body;

    try {
      // Check if experiment exists and is in DRAFT status
      const existingExperiment = await this.experimentRepo.findById(
        experimentId,
        authReq.tenantId
      );

      if (!existingExperiment) {
        throw new NotFoundError('Experiment', experimentId);
      }

      if (existingExperiment.status !== 'DRAFT') {
        throw new ConflictError(
          'Only experiments in DRAFT status can be updated',
          { current_status: existingExperiment.status }
        );
      }

      const updatedExperiment = await this.experimentRepo.update(
        experimentId,
        updateData,
        authReq.tenantId
      );

      logExperiment('experiment_updated', experimentId, authReq.tenantId, {
        experimentName: updatedExperiment.name,
        updatedBy: authReq.user.id,
        changes: Object.keys(updateData),
      });

      const response = buildSuccessResponse(updatedExperiment, {
        timestamp: new Date().toISOString(),
        request_id: req.headers['x-request-id'] as string,
        tenant_id: authReq.tenantId,
      });

      res.status(StatusCodes.OK).json(response);
    } catch (error) {
      logger.error('Error updating experiment', { 
        error, 
        experimentId,
        updateData,
        tenantId: authReq.tenantId,
        userId: authReq.user.id,
        requestId: req.headers['x-request-id']
      });
      throw error;
    }
  };

  /**
   * Delete an experiment
   */
  public deleteExperiment = async (req: Request, res: Response): Promise<void> => {
    const authReq = req as AuthenticatedRequest;
    const { experimentId } = req.params;

    try {
      // Check if experiment exists
      const experiment = await this.experimentRepo.findById(experimentId, authReq.tenantId);

      if (!experiment) {
        throw new NotFoundError('Experiment', experimentId);
      }

      // Only allow deletion of DRAFT or ARCHIVED experiments
      if (!['DRAFT', 'ARCHIVED'].includes(experiment.status)) {
        throw new ConflictError(
          'Only experiments in DRAFT or ARCHIVED status can be deleted',
          { current_status: experiment.status }
        );
      }

      // Check if experiment has user assignments
      const assignmentCount = await this.userAssignmentRepo.count(authReq.tenantId, {
        experimentId,
      });

      if (assignmentCount > 0) {
        throw new ConflictError(
          'Cannot delete experiment with existing user assignments',
          { assignment_count: assignmentCount }
        );
      }

      await this.experimentRepo.delete(experimentId, authReq.tenantId);

      logExperiment('experiment_deleted', experimentId, authReq.tenantId, {
        experimentName: experiment.name,
        deletedBy: authReq.user.id,
        status: experiment.status,
      });

      res.status(StatusCodes.NO_CONTENT).send();
    } catch (error) {
      logger.error('Error deleting experiment', { 
        error, 
        experimentId,
        tenantId: authReq.tenantId,
        userId: authReq.user.id,
        requestId: req.headers['x-request-id']
      });
      throw error;
    }
  };

  /**
   * Start an experiment
   */
  public startExperiment = async (req: Request, res: Response): Promise<void> => {
    const authReq = req as AuthenticatedRequest;
    const { experimentId } = req.params;
    const { start_date, end_date } = req.body;

    try {
      const experiment = await this.experimentRepo.findById(experimentId, authReq.tenantId);

      if (!experiment) {
        throw new NotFoundError('Experiment', experimentId);
      }

      if (experiment.status !== 'DRAFT') {
        throw new ExperimentStatusError(experiment.status, 'ACTIVE', ['DRAFT']);
      }

      // Validate experiment has at least 2 variants
      const variants = await this.variantRepo.findByExperiment(experimentId, authReq.tenantId);
      if (variants.length < 2) {
        throw new BusinessLogicError(
          'Experiment must have at least 2 variants to start',
          'INSUFFICIENT_VARIANTS',
          { variant_count: variants.length }
        );
      }

      // Update experiment status and dates
      const updateData: any = { status: 'ACTIVE' };
      if (start_date) updateData.startDate = new Date(start_date);
      if (end_date) updateData.endDate = new Date(end_date);

      const updatedExperiment = await this.experimentRepo.updateStatus(
        experimentId,
        'ACTIVE',
        authReq.tenantId
      );

      logExperiment('experiment_started', experimentId, authReq.tenantId, {
        experimentName: updatedExperiment.name,
        startedBy: authReq.user.id,
        startDate: updatedExperiment.startDate,
        endDate: updatedExperiment.endDate,
      });

      const response = buildSuccessResponse(updatedExperiment, {
        timestamp: new Date().toISOString(),
        request_id: req.headers['x-request-id'] as string,
        tenant_id: authReq.tenantId,
      });

      res.status(StatusCodes.OK).json(response);
    } catch (error) {
      logger.error('Error starting experiment', { 
        error, 
        experimentId,
        tenantId: authReq.tenantId,
        userId: authReq.user.id,
        requestId: req.headers['x-request-id']
      });
      throw error;
    }
  };

  /**
   * Pause an experiment
   */
  public pauseExperiment = async (req: Request, res: Response): Promise<void> => {
    const authReq = req as AuthenticatedRequest;
    const { experimentId } = req.params;
    const { reason } = req.body;

    try {
      const experiment = await this.experimentRepo.findById(experimentId, authReq.tenantId);

      if (!experiment) {
        throw new NotFoundError('Experiment', experimentId);
      }

      if (experiment.status !== 'ACTIVE') {
        throw new ExperimentStatusError(experiment.status, 'PAUSED', ['ACTIVE']);
      }

      const updatedExperiment = await this.experimentRepo.updateStatus(
        experimentId,
        'PAUSED',
        authReq.tenantId
      );

      logExperiment('experiment_paused', experimentId, authReq.tenantId, {
        experimentName: updatedExperiment.name,
        pausedBy: authReq.user.id,
        reason,
      });

      const response = buildSuccessResponse(updatedExperiment, {
        timestamp: new Date().toISOString(),
        request_id: req.headers['x-request-id'] as string,
        tenant_id: authReq.tenantId,
      });

      res.status(StatusCodes.OK).json(response);
    } catch (error) {
      logger.error('Error pausing experiment', { 
        error, 
        experimentId,
        tenantId: authReq.tenantId,
        userId: authReq.user.id,
        requestId: req.headers['x-request-id']
      });
      throw error;
    }
  };

  /**
   * Resume an experiment
   */
  public resumeExperiment = async (req: Request, res: Response): Promise<void> => {
    const authReq = req as AuthenticatedRequest;
    const { experimentId } = req.params;

    try {
      const experiment = await this.experimentRepo.findById(experimentId, authReq.tenantId);

      if (!experiment) {
        throw new NotFoundError('Experiment', experimentId);
      }

      if (experiment.status !== 'PAUSED') {
        throw new ExperimentStatusError(experiment.status, 'ACTIVE', ['PAUSED']);
      }

      const updatedExperiment = await this.experimentRepo.updateStatus(
        experimentId,
        'ACTIVE',
        authReq.tenantId
      );

      logExperiment('experiment_resumed', experimentId, authReq.tenantId, {
        experimentName: updatedExperiment.name,
        resumedBy: authReq.user.id,
      });

      const response = buildSuccessResponse(updatedExperiment, {
        timestamp: new Date().toISOString(),
        request_id: req.headers['x-request-id'] as string,
        tenant_id: authReq.tenantId,
      });

      res.status(StatusCodes.OK).json(response);
    } catch (error) {
      logger.error('Error resuming experiment', { 
        error, 
        experimentId,
        tenantId: authReq.tenantId,
        userId: authReq.user.id,
        requestId: req.headers['x-request-id']
      });
      throw error;
    }
  };

  /**
   * Complete an experiment
   */
  public completeExperiment = async (req: Request, res: Response): Promise<void> => {
    const authReq = req as AuthenticatedRequest;
    const { experimentId } = req.params;
    const { end_date, results_summary } = req.body;

    try {
      const experiment = await this.experimentRepo.findById(experimentId, authReq.tenantId);

      if (!experiment) {
        throw new NotFoundError('Experiment', experimentId);
      }

      if (!['ACTIVE', 'PAUSED'].includes(experiment.status)) {
        throw new ExperimentStatusError(experiment.status, 'COMPLETED', ['ACTIVE', 'PAUSED']);
      }

      const updatedExperiment = await this.experimentRepo.updateStatus(
        experimentId,
        'COMPLETED',
        authReq.tenantId
      );

      logExperiment('experiment_completed', experimentId, authReq.tenantId, {
        experimentName: updatedExperiment.name,
        completedBy: authReq.user.id,
        endDate: updatedExperiment.endDate,
        resultsSummary: results_summary,
      });

      const response = buildSuccessResponse(updatedExperiment, {
        timestamp: new Date().toISOString(),
        request_id: req.headers['x-request-id'] as string,
        tenant_id: authReq.tenantId,
      });

      res.status(StatusCodes.OK).json(response);
    } catch (error) {
      logger.error('Error completing experiment', { 
        error, 
        experimentId,
        tenantId: authReq.tenantId,
        userId: authReq.user.id,
        requestId: req.headers['x-request-id']
      });
      throw error;
    }
  };

  /**
   * Archive an experiment
   */
  public archiveExperiment = async (req: Request, res: Response): Promise<void> => {
    const authReq = req as AuthenticatedRequest;
    const { experimentId } = req.params;

    try {
      const experiment = await this.experimentRepo.findById(experimentId, authReq.tenantId);

      if (!experiment) {
        throw new NotFoundError('Experiment', experimentId);
      }

      if (!['COMPLETED', 'DRAFT'].includes(experiment.status)) {
        throw new ExperimentStatusError(experiment.status, 'ARCHIVED', ['COMPLETED', 'DRAFT']);
      }

      const updatedExperiment = await this.experimentRepo.updateStatus(
        experimentId,
        'ARCHIVED',
        authReq.tenantId
      );

      logExperiment('experiment_archived', experimentId, authReq.tenantId, {
        experimentName: updatedExperiment.name,
        archivedBy: authReq.user.id,
        previousStatus: experiment.status,
      });

      const response = buildSuccessResponse(updatedExperiment, {
        timestamp: new Date().toISOString(),
        request_id: req.headers['x-request-id'] as string,
        tenant_id: authReq.tenantId,
      });

      res.status(StatusCodes.OK).json(response);
    } catch (error) {
      logger.error('Error archiving experiment', { 
        error, 
        experimentId,
        tenantId: authReq.tenantId,
        userId: authReq.user.id,
        requestId: req.headers['x-request-id']
      });
      throw error;
    }
  };
}

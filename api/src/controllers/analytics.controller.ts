// Analytics controller
import { Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import { AuthenticatedRequest } from '../middleware/auth';
import { ExperimentService } from '../../../src/services/experiment.service';
import { ExperimentRepository } from '../../../src/repositories/experiment.repository';
import { logger } from '../utils/logger';
import { buildSuccessResponse } from '../utils/response';

export class AnalyticsController {
  private experimentService: ExperimentService;
  private experimentRepo: ExperimentRepository;

  constructor() {
    this.experimentService = new ExperimentService();
    this.experimentRepo = new ExperimentRepository();
  }

  /**
   * Get comprehensive analytics for an experiment
   */
  public getExperimentAnalytics = async (req: Request, res: Response): Promise<void> => {
    const authReq = req as AuthenticatedRequest;
    const { experimentId } = req.params;
    const { start_date, end_date, metrics } = req.query;

    try {
      const analytics = await this.experimentService.getExperimentAnalytics(
        experimentId,
        { tenantId: authReq.tenantId }
      );

      // Apply date filtering if provided
      let filteredAnalytics = analytics;
      if (start_date || end_date) {
        // Note: In a real implementation, you'd pass these dates to the service
        // to filter the underlying data queries
        filteredAnalytics = {
          ...analytics,
          // Add date range info to response
          date_range: {
            start: start_date ? new Date(start_date as string).toISOString() : null,
            end: end_date ? new Date(end_date as string).toISOString() : null,
          },
        };
      }

      // Filter metrics if specified
      if (metrics && Array.isArray(metrics)) {
        // Note: In a real implementation, you'd filter the metrics at the service level
        filteredAnalytics = {
          ...filteredAnalytics,
          requested_metrics: metrics,
        };
      }

      const response = buildSuccessResponse(filteredAnalytics, {
        timestamp: new Date().toISOString(),
        request_id: req.headers['x-request-id'] as string,
        tenant_id: authReq.tenantId,
      });

      res.status(StatusCodes.OK).json(response);
    } catch (error) {
      logger.error('Error getting experiment analytics', {
        error,
        experimentId,
        tenantId: authReq.tenantId,
        requestId: req.headers['x-request-id'],
      });
      throw error;
    }
  };

  /**
   * Get experiment statistics (basic metrics)
   */
  public getExperimentStats = async (req: Request, res: Response): Promise<void> => {
    const authReq = req as AuthenticatedRequest;
    const { experimentId } = req.params;

    try {
      const stats = await this.experimentRepo.getStatistics(
        experimentId,
        authReq.tenantId
      );

      const response = buildSuccessResponse(stats, {
        timestamp: new Date().toISOString(),
        request_id: req.headers['x-request-id'] as string,
        tenant_id: authReq.tenantId,
      });

      res.status(StatusCodes.OK).json(response);
    } catch (error) {
      logger.error('Error getting experiment statistics', {
        error,
        experimentId,
        tenantId: authReq.tenantId,
        requestId: req.headers['x-request-id'],
      });
      throw error;
    }
  };

  /**
   * Get real-time experiment metrics
   */
  public getRealTimeMetrics = async (req: Request, res: Response): Promise<void> => {
    const authReq = req as AuthenticatedRequest;
    const { experimentId } = req.params;

    try {
      // Get basic stats
      const stats = await this.experimentRepo.getStatistics(
        experimentId,
        authReq.tenantId
      );

      // Add real-time indicators
      const realTimeMetrics = {
        ...stats,
        real_time: true,
        last_updated: new Date().toISOString(),
        // In a real implementation, you might add:
        // - Active users count
        // - Events in last hour/minute
        // - Conversion rate trends
        // - Statistical significance updates
      };

      const response = buildSuccessResponse(realTimeMetrics, {
        timestamp: new Date().toISOString(),
        request_id: req.headers['x-request-id'] as string,
        tenant_id: authReq.tenantId,
      });

      res.status(StatusCodes.OK).json(response);
    } catch (error) {
      logger.error('Error getting real-time metrics', {
        error,
        experimentId,
        tenantId: authReq.tenantId,
        requestId: req.headers['x-request-id'],
      });
      throw error;
    }
  };

  /**
   * Get experiment performance summary
   */
  public getPerformanceSummary = async (req: Request, res: Response): Promise<void> => {
    const authReq = req as AuthenticatedRequest;
    const { experimentId } = req.params;

    try {
      const analytics = await this.experimentService.getExperimentAnalytics(
        experimentId,
        { tenantId: authReq.tenantId }
      );

      // Create performance summary
      const summary = {
        experiment_id: analytics.experimentId,
        experiment_name: analytics.experimentName,
        status: analytics.status,
        duration_days: analytics.startDate && analytics.endDate
          ? Math.ceil((new Date(analytics.endDate).getTime() - new Date(analytics.startDate).getTime()) / (1000 * 60 * 60 * 24))
          : null,
        total_users: analytics.totalUsers,
        variants_count: analytics.variants.length,
        // Find best performing variant
        best_variant: analytics.variants.reduce((best, current) => {
          if (!best) return current;
          return current.metrics.conversionRate > best.metrics.conversionRate ? current : best;
        }, null as any),
        // Calculate overall conversion rate
        overall_conversion_rate: analytics.variants.reduce((sum, variant) => {
          return sum + (variant.metrics.conversionRate * variant.metrics.totalUsers);
        }, 0) / analytics.totalUsers,
        // Check if any variant has statistical significance
        has_significant_results: analytics.variants.some(v => v.statistical_significance),
      };

      const response = buildSuccessResponse(summary, {
        timestamp: new Date().toISOString(),
        request_id: req.headers['x-request-id'] as string,
        tenant_id: authReq.tenantId,
      });

      res.status(StatusCodes.OK).json(response);
    } catch (error) {
      logger.error('Error getting performance summary', {
        error,
        experimentId,
        tenantId: authReq.tenantId,
        requestId: req.headers['x-request-id'],
      });
      throw error;
    }
  };

  /**
   * Export experiment data
   */
  public exportExperimentData = async (req: Request, res: Response): Promise<void> => {
    const authReq = req as AuthenticatedRequest;
    const { experimentId } = req.params;
    const { format = 'json', include_raw_data = false } = req.query;

    try {
      const analytics = await this.experimentService.getExperimentAnalytics(
        experimentId,
        { tenantId: authReq.tenantId }
      );

      let exportData: any = {
        experiment: analytics,
        exported_at: new Date().toISOString(),
        exported_by: authReq.user.id,
        format,
      };

      // Include raw data if requested (in a real implementation, this would include
      // all user assignments, events, etc.)
      if (include_raw_data === 'true') {
        exportData.raw_data = {
          note: 'Raw data export would include all assignments and events',
          // In real implementation:
          // assignments: await this.userAssignmentRepo.findByExperiment(...),
          // events: await this.eventRepo.findByExperiment(...),
        };
      }

      // Handle different export formats
      if (format === 'csv') {
        // In a real implementation, you'd convert to CSV format
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename="experiment-${experimentId}.csv"`);
        res.status(StatusCodes.OK).send('CSV export not implemented in this example');
        return;
      }

      const response = buildSuccessResponse(exportData, {
        timestamp: new Date().toISOString(),
        request_id: req.headers['x-request-id'] as string,
        tenant_id: authReq.tenantId,
      });

      res.status(StatusCodes.OK).json(response);
    } catch (error) {
      logger.error('Error exporting experiment data', {
        error,
        experimentId,
        format,
        tenantId: authReq.tenantId,
        requestId: req.headers['x-request-id'],
      });
      throw error;
    }
  };

  /**
   * Get tenant-wide analytics dashboard
   */
  public getTenantDashboard = async (req: Request, res: Response): Promise<void> => {
    const authReq = req as AuthenticatedRequest;
    const { 
      start_date, 
      end_date, 
      status = ['ACTIVE', 'COMPLETED'] 
    } = req.query;

    try {
      // Get experiments for the tenant
      const experiments = await this.experimentRepo.findByStatus(
        authReq.tenantId,
        Array.isArray(status) ? status as any[] : [status]
      );

      // Calculate dashboard metrics
      const dashboard = {
        total_experiments: experiments.length,
        active_experiments: experiments.filter(e => e.status === 'ACTIVE').length,
        completed_experiments: experiments.filter(e => e.status === 'COMPLETED').length,
        total_variants: experiments.reduce((sum, exp) => sum + (exp._count?.variants || 0), 0),
        total_assignments: experiments.reduce((sum, exp) => sum + (exp._count?.userAssignments || 0), 0),
        experiments_summary: experiments.map(exp => ({
          id: exp.id,
          name: exp.name,
          status: exp.status,
          created_at: exp.createdAt,
          start_date: exp.startDate,
          end_date: exp.endDate,
          variants_count: exp._count?.variants || 0,
          assignments_count: exp._count?.userAssignments || 0,
        })),
        date_range: {
          start: start_date ? new Date(start_date as string).toISOString() : null,
          end: end_date ? new Date(end_date as string).toISOString() : null,
        },
      };

      const response = buildSuccessResponse(dashboard, {
        timestamp: new Date().toISOString(),
        request_id: req.headers['x-request-id'] as string,
        tenant_id: authReq.tenantId,
      });

      res.status(StatusCodes.OK).json(response);
    } catch (error) {
      logger.error('Error getting tenant dashboard', {
        error,
        tenantId: authReq.tenantId,
        requestId: req.headers['x-request-id'],
      });
      throw error;
    }
  };
}

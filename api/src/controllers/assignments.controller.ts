// User assignments controller
import { Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import { AuthenticatedRequest } from '../middleware/auth';
import { ExperimentService } from '../../../src/services/experiment.service';
import { UserAssignmentRepository } from '../../../src/repositories/user-assignment.repository';
import { NotFoundError } from '../utils/errors';
import { logger, logUserAssignment } from '../utils/logger';
import { buildPaginationResponse, buildSuccessResponse } from '../utils/response';

export class AssignmentsController {
  private experimentService: ExperimentService;
  private userAssignmentRepo: UserAssignmentRepository;

  constructor() {
    this.experimentService = new ExperimentService();
    this.userAssignmentRepo = new UserAssignmentRepository();
  }

  /**
   * Assign user to experiment
   */
  public assignUser = async (req: Request, res: Response): Promise<void> => {
    const authReq = req as AuthenticatedRequest;
    const { experimentId } = req.params;
    const { user_id, session_id, user_attributes } = req.body;

    try {
      const assignmentRequest = {
        userId: user_id,
        sessionId: session_id,
        userAttributes: user_attributes || {},
      };

      const result = await this.experimentService.assignUserToExperiment(
        experimentId,
        assignmentRequest,
        { tenantId: authReq.tenantId }
      );

      logUserAssignment(
        experimentId,
        user_id,
        result.variantId,
        result.isExcluded,
        authReq.tenantId,
        {
          sessionId: session_id,
          exclusionReason: result.exclusionReason,
          userAttributes: user_attributes,
        }
      );

      const response = buildSuccessResponse(result, {
        timestamp: new Date().toISOString(),
        request_id: req.headers['x-request-id'] as string,
        tenant_id: authReq.tenantId,
      });

      res.status(StatusCodes.OK).json(response);
    } catch (error) {
      logger.error('Error assigning user to experiment', {
        error,
        experimentId,
        userId: user_id,
        tenantId: authReq.tenantId,
        requestId: req.headers['x-request-id'],
      });
      throw error;
    }
  };

  /**
   * List user assignments for an experiment
   */
  public listAssignments = async (req: Request, res: Response): Promise<void> => {
    const authReq = req as AuthenticatedRequest;
    const { experimentId } = req.params;
    const {
      page = 1,
      limit = 20,
      variant_id,
      include_excluded = false,
      user_id,
    } = req.query;

    try {
      const options = {
        variantId: variant_id as string,
        includeExcluded: include_excluded === 'true',
        pagination: {
          page: parseInt(page as string, 10),
          limit: parseInt(limit as string, 10),
        },
      };

      // If user_id is provided, get specific user assignment
      if (user_id) {
        const assignment = await this.userAssignmentRepo.findByUserAndExperiment(
          user_id as string,
          experimentId,
          authReq.tenantId
        );

        if (!assignment) {
          throw new NotFoundError('User assignment', `${user_id}:${experimentId}`);
        }

        const response = buildSuccessResponse(assignment, {
          timestamp: new Date().toISOString(),
          request_id: req.headers['x-request-id'] as string,
          tenant_id: authReq.tenantId,
        });

        res.status(StatusCodes.OK).json(response);
        return;
      }

      const result = await this.userAssignmentRepo.findByExperiment(
        experimentId,
        authReq.tenantId,
        options
      );

      const response = buildPaginationResponse(result.data, result.pagination, {
        timestamp: new Date().toISOString(),
        request_id: req.headers['x-request-id'] as string,
        tenant_id: authReq.tenantId,
      });

      res.status(StatusCodes.OK).json(response);
    } catch (error) {
      logger.error('Error listing user assignments', {
        error,
        experimentId,
        tenantId: authReq.tenantId,
        requestId: req.headers['x-request-id'],
      });
      throw error;
    }
  };

  /**
   * Get assignment statistics for an experiment
   */
  public getAssignmentStats = async (req: Request, res: Response): Promise<void> => {
    const authReq = req as AuthenticatedRequest;
    const { experimentId } = req.params;

    try {
      const stats = await this.userAssignmentRepo.getExperimentAssignmentStats(
        experimentId,
        authReq.tenantId
      );

      const response = buildSuccessResponse(stats, {
        timestamp: new Date().toISOString(),
        request_id: req.headers['x-request-id'] as string,
        tenant_id: authReq.tenantId,
      });

      res.status(StatusCodes.OK).json(response);
    } catch (error) {
      logger.error('Error getting assignment statistics', {
        error,
        experimentId,
        tenantId: authReq.tenantId,
        requestId: req.headers['x-request-id'],
      });
      throw error;
    }
  };

  /**
   * Get user assignment history
   */
  public getUserAssignmentHistory = async (req: Request, res: Response): Promise<void> => {
    const authReq = req as AuthenticatedRequest;
    const { user_id, limit = 50, include_excluded = false } = req.query;

    if (!user_id) {
      throw new NotFoundError('User ID is required');
    }

    try {
      const options = {
        limit: parseInt(limit as string, 10),
        includeExcluded: include_excluded === 'true',
      };

      const assignments = await this.userAssignmentRepo.getUserAssignmentHistory(
        user_id as string,
        authReq.tenantId,
        options
      );

      const response = buildSuccessResponse(assignments, {
        timestamp: new Date().toISOString(),
        request_id: req.headers['x-request-id'] as string,
        tenant_id: authReq.tenantId,
      });

      res.status(StatusCodes.OK).json(response);
    } catch (error) {
      logger.error('Error getting user assignment history', {
        error,
        userId: user_id,
        tenantId: authReq.tenantId,
        requestId: req.headers['x-request-id'],
      });
      throw error;
    }
  };

  /**
   * Bulk assign users to experiment
   */
  public bulkAssignUsers = async (req: Request, res: Response): Promise<void> => {
    const authReq = req as AuthenticatedRequest;
    const { experimentId } = req.params;
    const { assignments } = req.body;

    if (!Array.isArray(assignments) || assignments.length === 0) {
      throw new Error('Assignments array is required and must not be empty');
    }

    try {
      // Add experiment ID to each assignment
      const assignmentsWithExperiment = assignments.map((assignment: any) => ({
        ...assignment,
        experimentId,
      }));

      const result = await this.userAssignmentRepo.bulkAssign(
        assignmentsWithExperiment,
        authReq.tenantId
      );

      logger.info('Bulk user assignment completed', {
        experimentId,
        assignmentCount: result.count,
        tenantId: authReq.tenantId,
        requestId: req.headers['x-request-id'],
      });

      const response = buildSuccessResponse(
        { assigned_count: result.count },
        {
          timestamp: new Date().toISOString(),
          request_id: req.headers['x-request-id'] as string,
          tenant_id: authReq.tenantId,
        }
      );

      res.status(StatusCodes.CREATED).json(response);
    } catch (error) {
      logger.error('Error bulk assigning users', {
        error,
        experimentId,
        assignmentCount: assignments.length,
        tenantId: authReq.tenantId,
        requestId: req.headers['x-request-id'],
      });
      throw error;
    }
  };
}

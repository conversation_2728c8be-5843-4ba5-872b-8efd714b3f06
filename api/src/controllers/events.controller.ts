// Events controller
import { Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import { AuthenticatedRequest } from '../middleware/auth';
import { ExperimentService } from '../../../src/services/experiment.service';
import { EventRepository } from '../../../src/repositories/event.repository';
import { logger, logEventTracking } from '../utils/logger';
import { buildPaginationResponse, buildSuccessResponse } from '../utils/response';

export class EventsController {
  private experimentService: ExperimentService;
  private eventRepo: EventRepository;

  constructor() {
    this.experimentService = new ExperimentService();
    this.eventRepo = new EventRepository();
  }

  /**
   * Track an event for an experiment
   */
  public trackEvent = async (req: Request, res: Response): Promise<void> => {
    const authReq = req as AuthenticatedRequest;
    const { experimentId } = req.params;
    const {
      user_id,
      session_id,
      event_name,
      event_value,
      event_properties,
      timestamp,
    } = req.body;

    try {
      const eventRequest = {
        userId: user_id,
        sessionId: session_id,
        eventName: event_name,
        eventValue: event_value,
        eventProperties: event_properties || {},
        timestamp: timestamp ? new Date(timestamp) : new Date(),
      };

      await this.experimentService.trackEvent(
        eventRequest,
        { tenantId: authReq.tenantId }
      );

      logEventTracking(
        experimentId,
        user_id,
        event_name,
        event_value,
        authReq.tenantId,
        {
          sessionId: session_id,
          eventProperties: event_properties,
        }
      );

      const response = buildSuccessResponse(
        {
          event_name,
          user_id,
          experiment_id: experimentId,
          tracked_at: eventRequest.timestamp.toISOString(),
        },
        {
          timestamp: new Date().toISOString(),
          request_id: req.headers['x-request-id'] as string,
          tenant_id: authReq.tenantId,
        }
      );

      res.status(StatusCodes.CREATED).json(response);
    } catch (error) {
      logger.error('Error tracking event', {
        error,
        experimentId,
        userId: user_id,
        eventName: event_name,
        tenantId: authReq.tenantId,
        requestId: req.headers['x-request-id'],
      });
      throw error;
    }
  };

  /**
   * List events for an experiment
   */
  public listEvents = async (req: Request, res: Response): Promise<void> => {
    const authReq = req as AuthenticatedRequest;
    const { experimentId } = req.params;
    const {
      page = 1,
      limit = 20,
      event_name,
      variant_id,
      user_id,
      start_date,
      end_date,
    } = req.query;

    try {
      const options = {
        eventName: event_name as string,
        variantId: variant_id as string,
        startDate: start_date ? new Date(start_date as string) : undefined,
        endDate: end_date ? new Date(end_date as string) : undefined,
        pagination: {
          page: parseInt(page as string, 10),
          limit: parseInt(limit as string, 10),
        },
      };

      // If user_id is provided, get events for specific user
      if (user_id) {
        const userEvents = await this.eventRepo.findByUser(
          user_id as string,
          authReq.tenantId,
          {
            experimentId,
            eventName: event_name as string,
            startDate: options.startDate,
            endDate: options.endDate,
            limit: options.pagination.limit,
          }
        );

        const response = buildSuccessResponse(userEvents, {
          timestamp: new Date().toISOString(),
          request_id: req.headers['x-request-id'] as string,
          tenant_id: authReq.tenantId,
        });

        res.status(StatusCodes.OK).json(response);
        return;
      }

      const result = await this.eventRepo.findByExperiment(
        experimentId,
        authReq.tenantId,
        options
      );

      const response = buildPaginationResponse(result.data, result.pagination, {
        timestamp: new Date().toISOString(),
        request_id: req.headers['x-request-id'] as string,
        tenant_id: authReq.tenantId,
      });

      res.status(StatusCodes.OK).json(response);
    } catch (error) {
      logger.error('Error listing events', {
        error,
        experimentId,
        tenantId: authReq.tenantId,
        requestId: req.headers['x-request-id'],
      });
      throw error;
    }
  };

  /**
   * Get event statistics for an experiment
   */
  public getEventStats = async (req: Request, res: Response): Promise<void> => {
    const authReq = req as AuthenticatedRequest;
    const { experimentId } = req.params;
    const { event_name, start_date, end_date } = req.query;

    try {
      const options = {
        eventName: event_name as string,
        startDate: start_date ? new Date(start_date as string) : undefined,
        endDate: end_date ? new Date(end_date as string) : undefined,
      };

      const stats = await this.eventRepo.getExperimentEventStats(
        experimentId,
        authReq.tenantId,
        options
      );

      const response = buildSuccessResponse(stats, {
        timestamp: new Date().toISOString(),
        request_id: req.headers['x-request-id'] as string,
        tenant_id: authReq.tenantId,
      });

      res.status(StatusCodes.OK).json(response);
    } catch (error) {
      logger.error('Error getting event statistics', {
        error,
        experimentId,
        tenantId: authReq.tenantId,
        requestId: req.headers['x-request-id'],
      });
      throw error;
    }
  };

  /**
   * Get conversion funnel data
   */
  public getConversionFunnel = async (req: Request, res: Response): Promise<void> => {
    const authReq = req as AuthenticatedRequest;
    const { experimentId } = req.params;
    const { event_names, start_date, end_date } = req.query;

    if (!event_names) {
      throw new Error('event_names parameter is required');
    }

    try {
      const eventNamesList = Array.isArray(event_names) 
        ? event_names as string[]
        : (event_names as string).split(',');

      const options = {
        startDate: start_date ? new Date(start_date as string) : undefined,
        endDate: end_date ? new Date(end_date as string) : undefined,
      };

      const funnelData = await this.eventRepo.getConversionFunnel(
        experimentId,
        eventNamesList,
        authReq.tenantId,
        options
      );

      const response = buildSuccessResponse(funnelData, {
        timestamp: new Date().toISOString(),
        request_id: req.headers['x-request-id'] as string,
        tenant_id: authReq.tenantId,
      });

      res.status(StatusCodes.OK).json(response);
    } catch (error) {
      logger.error('Error getting conversion funnel', {
        error,
        experimentId,
        tenantId: authReq.tenantId,
        requestId: req.headers['x-request-id'],
      });
      throw error;
    }
  };

  /**
   * Get unique event names for tenant
   */
  public getEventNames = async (req: Request, res: Response): Promise<void> => {
    const authReq = req as AuthenticatedRequest;
    const { experimentId } = req.params;
    const { limit = 100 } = req.query;

    try {
      const eventNames = await this.eventRepo.getUniqueEventNames(
        authReq.tenantId,
        {
          experimentId,
          limit: parseInt(limit as string, 10),
        }
      );

      const response = buildSuccessResponse(
        { event_names: eventNames },
        {
          timestamp: new Date().toISOString(),
          request_id: req.headers['x-request-id'] as string,
          tenant_id: authReq.tenantId,
        }
      );

      res.status(StatusCodes.OK).json(response);
    } catch (error) {
      logger.error('Error getting event names', {
        error,
        experimentId,
        tenantId: authReq.tenantId,
        requestId: req.headers['x-request-id'],
      });
      throw error;
    }
  };

  /**
   * Bulk track events
   */
  public bulkTrackEvents = async (req: Request, res: Response): Promise<void> => {
    const authReq = req as AuthenticatedRequest;
    const { events } = req.body;

    if (!Array.isArray(events) || events.length === 0) {
      throw new Error('Events array is required and must not be empty');
    }

    try {
      // Process events with timestamp defaults
      const eventsWithDefaults = events.map((event: any) => ({
        ...event,
        timestamp: event.timestamp ? new Date(event.timestamp) : new Date(),
      }));

      const result = await this.eventRepo.bulkCreate(
        eventsWithDefaults,
        authReq.tenantId
      );

      logger.info('Bulk event tracking completed', {
        eventCount: result.count,
        tenantId: authReq.tenantId,
        requestId: req.headers['x-request-id'],
      });

      const response = buildSuccessResponse(
        { tracked_count: result.count },
        {
          timestamp: new Date().toISOString(),
          request_id: req.headers['x-request-id'] as string,
          tenant_id: authReq.tenantId,
        }
      );

      res.status(StatusCodes.CREATED).json(response);
    } catch (error) {
      logger.error('Error bulk tracking events', {
        error,
        eventCount: events.length,
        tenantId: authReq.tenantId,
        requestId: req.headers['x-request-id'],
      });
      throw error;
    }
  };
}

// Mock data for testing
import { ExperimentStatus } from '@prisma/client';

// Mock tenants
export const mockTenants = {
  tenant1: {
    id: 'tenant-1',
    name: 'Test Tenant 1',
    slug: 'test-tenant-1',
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-01T00:00:00Z'),
  },
  tenant2: {
    id: 'tenant-2',
    name: 'Test Tenant 2',
    slug: 'test-tenant-2',
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-01T00:00:00Z'),
  },
};

// Mock users
export const mockUsers = {
  admin: {
    id: 'user-admin',
    email: '<EMAIL>',
    tenantId: 'tenant-1',
    roles: ['admin'],
    permissions: [
      'experiments:read',
      'experiments:write',
      'experiments:delete',
      'experiments:control',
      'assignments:read',
      'assignments:write',
      'events:read',
      'events:write',
      'analytics:read',
    ],
  },
  experimenter: {
    id: 'user-experimenter',
    email: '<EMAIL>',
    tenantId: 'tenant-1',
    roles: ['experimenter'],
    permissions: [
      'experiments:read',
      'experiments:write',
      'experiments:control',
      'assignments:read',
      'assignments:write',
      'events:read',
      'events:write',
      'analytics:read',
    ],
  },
  viewer: {
    id: 'user-viewer',
    email: '<EMAIL>',
    tenantId: 'tenant-1',
    roles: ['viewer'],
    permissions: [
      'experiments:read',
      'assignments:read',
      'events:read',
      'analytics:read',
    ],
  },
  otherTenant: {
    id: 'user-other',
    email: '<EMAIL>',
    tenantId: 'tenant-2',
    roles: ['admin'],
    permissions: [
      'experiments:read',
      'experiments:write',
      'experiments:delete',
      'experiments:control',
      'assignments:read',
      'assignments:write',
      'events:read',
      'events:write',
      'analytics:read',
    ],
  },
};

// Mock experiments
export const mockExperiments = {
  draft: {
    id: 'exp-draft',
    tenantId: 'tenant-1',
    name: 'Draft Experiment',
    description: 'A draft experiment for testing',
    hypothesis: 'This is a test hypothesis',
    status: 'DRAFT' as ExperimentStatus,
    trafficAllocation: 1.0,
    assignmentMethod: 'STICKY',
    sampleSize: 1000,
    confidenceLevel: 0.95,
    minimumDetectableEffect: 0.1,
    primaryMetric: 'conversion_rate',
    secondaryMetrics: ['click_through_rate'],
    tags: ['test', 'draft'],
    metadata: { test: true },
    createdBy: 'user-admin',
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-01T00:00:00Z'),
    startDate: null,
    endDate: null,
    _count: {
      variants: 2,
      userAssignments: 0,
      events: 0,
    },
  },
  active: {
    id: 'exp-active',
    tenantId: 'tenant-1',
    name: 'Active Experiment',
    description: 'An active experiment for testing',
    hypothesis: 'This is an active test hypothesis',
    status: 'ACTIVE' as ExperimentStatus,
    trafficAllocation: 1.0,
    assignmentMethod: 'STICKY',
    sampleSize: 5000,
    confidenceLevel: 0.95,
    minimumDetectableEffect: 0.15,
    primaryMetric: 'button_clicks',
    secondaryMetrics: ['page_views'],
    tags: ['test', 'active'],
    metadata: { test: true },
    createdBy: 'user-admin',
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-15T00:00:00Z'),
    startDate: new Date('2024-01-15T00:00:00Z'),
    endDate: new Date('2024-02-15T00:00:00Z'),
    _count: {
      variants: 2,
      userAssignments: 100,
      events: 250,
    },
  },
  completed: {
    id: 'exp-completed',
    tenantId: 'tenant-1',
    name: 'Completed Experiment',
    description: 'A completed experiment for testing',
    hypothesis: 'This is a completed test hypothesis',
    status: 'COMPLETED' as ExperimentStatus,
    trafficAllocation: 1.0,
    assignmentMethod: 'STICKY',
    sampleSize: 2000,
    confidenceLevel: 0.95,
    minimumDetectableEffect: 0.2,
    primaryMetric: 'purchases',
    secondaryMetrics: ['revenue'],
    tags: ['test', 'completed'],
    metadata: { test: true },
    createdBy: 'user-admin',
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-30T00:00:00Z'),
    startDate: new Date('2024-01-01T00:00:00Z'),
    endDate: new Date('2024-01-30T00:00:00Z'),
    _count: {
      variants: 2,
      userAssignments: 2000,
      events: 500,
    },
  },
  otherTenant: {
    id: 'exp-other-tenant',
    tenantId: 'tenant-2',
    name: 'Other Tenant Experiment',
    description: 'An experiment from another tenant',
    hypothesis: 'This should not be accessible',
    status: 'ACTIVE' as ExperimentStatus,
    trafficAllocation: 1.0,
    assignmentMethod: 'STICKY',
    sampleSize: 1000,
    confidenceLevel: 0.95,
    minimumDetectableEffect: 0.1,
    primaryMetric: 'conversion_rate',
    secondaryMetrics: [],
    tags: ['other'],
    metadata: {},
    createdBy: 'user-other',
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-01T00:00:00Z'),
    startDate: new Date('2024-01-01T00:00:00Z'),
    endDate: null,
    _count: {
      variants: 2,
      userAssignments: 50,
      events: 100,
    },
  },
};

// Mock variants
export const mockVariants = {
  control: {
    id: 'variant-control',
    experimentId: 'exp-active',
    tenantId: 'tenant-1',
    name: 'Control',
    description: 'Control variant',
    isControl: true,
    trafficWeight: 0.5,
    configuration: { button_color: '#007bff' },
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-01T00:00:00Z'),
  },
  treatment: {
    id: 'variant-treatment',
    experimentId: 'exp-active',
    tenantId: 'tenant-1',
    name: 'Treatment',
    description: 'Treatment variant',
    isControl: false,
    trafficWeight: 0.5,
    configuration: { button_color: '#dc3545' },
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-01T00:00:00Z'),
  },
};

// Mock targeting rules
export const mockTargetingRules = {
  countryRule: {
    id: 'rule-country',
    experimentId: 'exp-active',
    tenantId: 'tenant-1',
    name: 'US Users Only',
    attributeName: 'country',
    operator: 'EQUALS',
    value: 'US',
    isActive: true,
    priority: 1,
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-01T00:00:00Z'),
  },
  deviceRule: {
    id: 'rule-device',
    experimentId: 'exp-active',
    tenantId: 'tenant-1',
    name: 'Desktop Users',
    attributeName: 'device_type',
    operator: 'EQUALS',
    value: 'desktop',
    isActive: true,
    priority: 2,
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-01T00:00:00Z'),
  },
};

// Mock user assignments
export const mockUserAssignments = {
  user1Control: {
    id: 'assignment-1',
    experimentId: 'exp-active',
    variantId: 'variant-control',
    tenantId: 'tenant-1',
    userId: 'user-1',
    sessionId: 'session-1',
    assignmentTimestamp: new Date('2024-01-15T10:00:00Z'),
    userAttributes: { country: 'US', device_type: 'desktop' },
    isExcluded: false,
    exclusionReason: null,
  },
  user2Treatment: {
    id: 'assignment-2',
    experimentId: 'exp-active',
    variantId: 'variant-treatment',
    tenantId: 'tenant-1',
    userId: 'user-2',
    sessionId: 'session-2',
    assignmentTimestamp: new Date('2024-01-15T11:00:00Z'),
    userAttributes: { country: 'US', device_type: 'desktop' },
    isExcluded: false,
    exclusionReason: null,
  },
  user3Excluded: {
    id: 'assignment-3',
    experimentId: 'exp-active',
    variantId: null,
    tenantId: 'tenant-1',
    userId: 'user-3',
    sessionId: 'session-3',
    assignmentTimestamp: new Date('2024-01-15T12:00:00Z'),
    userAttributes: { country: 'CA', device_type: 'mobile' },
    isExcluded: true,
    exclusionReason: 'Failed targeting rules',
  },
};

// Mock events
export const mockEvents = {
  buttonClick1: {
    id: 'event-1',
    experimentId: 'exp-active',
    variantId: 'variant-control',
    tenantId: 'tenant-1',
    userId: 'user-1',
    sessionId: 'session-1',
    eventName: 'button_click',
    eventValue: 1,
    eventProperties: { button_color: 'blue', page: 'homepage' },
    timestamp: new Date('2024-01-15T10:05:00Z'),
  },
  buttonClick2: {
    id: 'event-2',
    experimentId: 'exp-active',
    variantId: 'variant-treatment',
    tenantId: 'tenant-1',
    userId: 'user-2',
    sessionId: 'session-2',
    eventName: 'button_click',
    eventValue: 1,
    eventProperties: { button_color: 'red', page: 'homepage' },
    timestamp: new Date('2024-01-15T11:05:00Z'),
  },
  purchase1: {
    id: 'event-3',
    experimentId: 'exp-active',
    variantId: 'variant-treatment',
    tenantId: 'tenant-1',
    userId: 'user-2',
    sessionId: 'session-2',
    eventName: 'purchase',
    eventValue: 99.99,
    eventProperties: { product_id: 'prod-123', category: 'electronics' },
    timestamp: new Date('2024-01-15T11:30:00Z'),
  },
};

// Mock request payloads
export const mockRequestPayloads = {
  createExperiment: {
    valid: {
      name: 'New Test Experiment',
      description: 'A new experiment for testing',
      hypothesis: 'This will improve conversion rates',
      traffic_allocation: 1.0,
      assignment_method: 'STICKY',
      sample_size: 1000,
      confidence_level: 0.95,
      minimum_detectable_effect: 0.1,
      primary_metric: 'conversion_rate',
      secondary_metrics: ['click_through_rate'],
      tags: ['test', 'new'],
      variants: [
        {
          name: 'Control',
          description: 'Control variant',
          is_control: true,
          traffic_weight: 0.5,
          configuration: { button_color: '#007bff' },
        },
        {
          name: 'Treatment',
          description: 'Treatment variant',
          is_control: false,
          traffic_weight: 0.5,
          configuration: { button_color: '#dc3545' },
        },
      ],
      targeting_rules: [
        {
          name: 'US Users',
          attribute_name: 'country',
          operator: 'EQUALS',
          value: 'US',
          is_active: true,
          priority: 1,
        },
      ],
    },
    invalidName: {
      name: '', // Invalid: empty name
      variants: [
        {
          name: 'Control',
          traffic_weight: 0.5,
        },
      ],
    },
    invalidVariants: {
      name: 'Test Experiment',
      variants: [
        {
          name: 'Only Variant',
          traffic_weight: 1.0,
        },
      ], // Invalid: only one variant
    },
    invalidTrafficWeights: {
      name: 'Test Experiment',
      variants: [
        {
          name: 'Variant 1',
          traffic_weight: 0.6,
        },
        {
          name: 'Variant 2',
          traffic_weight: 0.6, // Invalid: total > 1.0
        },
      ],
    },
  },
  updateExperiment: {
    valid: {
      name: 'Updated Experiment Name',
      description: 'Updated description',
      tags: ['updated', 'test'],
    },
    invalidStatus: {
      status: 'INVALID_STATUS',
    },
  },
  assignUser: {
    valid: {
      user_id: 'user-123',
      session_id: 'session-123',
      user_attributes: {
        country: 'US',
        device_type: 'desktop',
        subscription_tier: 'premium',
      },
    },
    missingUserId: {
      session_id: 'session-123',
      user_attributes: {},
    },
  },
  trackEvent: {
    valid: {
      user_id: 'user-123',
      session_id: 'session-123',
      event_name: 'button_click',
      event_value: 1,
      event_properties: {
        button_color: 'red',
        page: 'homepage',
      },
    },
    missingEventName: {
      user_id: 'user-123',
      event_value: 1,
    },
  },
};

// Helper functions for creating mock data
export const createMockExperiment = (overrides: Partial<typeof mockExperiments.draft> = {}) => ({
  ...mockExperiments.draft,
  ...overrides,
});

export const createMockVariant = (overrides: Partial<typeof mockVariants.control> = {}) => ({
  ...mockVariants.control,
  ...overrides,
});

export const createMockUserAssignment = (overrides: Partial<typeof mockUserAssignments.user1Control> = {}) => ({
  ...mockUserAssignments.user1Control,
  ...overrides,
});

export const createMockEvent = (overrides: Partial<typeof mockEvents.buttonClick1> = {}) => ({
  ...mockEvents.buttonClick1,
  ...overrides,
});

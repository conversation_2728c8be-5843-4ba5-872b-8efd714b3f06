// Tenant isolation integration tests
import request from 'supertest';
import { createApp } from '../../app';
import { mockPrismaClient } from '../__mocks__/prisma';
import { 
  mockExperiments, 
  mockUsers, 
  mockUserAssignments,
  mockEvents 
} from '../fixtures/mockData';
import { 
  generateTestTokens,
  createRequestHeaders,
  assertErrorResponse,
  setupPrismaMocks,
  resetPrismaMocks 
} from '../helpers/testHelpers';

describe('Tenant Isolation Integration Tests', () => {
  let app: any;
  let tokens: any;

  beforeAll(() => {
    app = createApp();
    tokens = generateTestTokens();
  });

  beforeEach(() => {
    setupPrismaMocks(mockPrismaClient);
  });

  afterEach(() => {
    resetPrismaMocks(mockPrismaClient);
    jest.clearAllMocks();
  });

  describe('Cross-Tenant Access Prevention', () => {
    describe('Experiments', () => {
      it('should not allow access to experiments from other tenants', async () => {
        // User from tenant-1 trying to access experiment from tenant-2
        mockPrismaClient.experiment.findUnique.mockResolvedValue(null);

        const response = await request(app)
          .get(`/v1/experiments/${mockExperiments.otherTenant.id}`)
          .set(createRequestHeaders(tokens.admin)) // tenant-1 user
          .expect(404);

        assertErrorResponse(response, 'EXPERIMENT_NOT_FOUND', 404);

        // Verify the query was made with the correct tenant filter
        expect(mockPrismaClient.experiment.findUnique).toHaveBeenCalledWith(
          expect.objectContaining({
            where: expect.objectContaining({
              id: mockExperiments.otherTenant.id,
              tenantId: mockUsers.admin.tenantId, // Should filter by user's tenant
            }),
          })
        );
      });

      it('should not allow updating experiments from other tenants', async () => {
        mockPrismaClient.experiment.findUnique.mockResolvedValue(null);

        const response = await request(app)
          .put(`/v1/experiments/${mockExperiments.otherTenant.id}`)
          .set(createRequestHeaders(tokens.admin))
          .send({ name: 'Updated Name' })
          .expect(404);

        assertErrorResponse(response, 'EXPERIMENT_NOT_FOUND', 404);
      });

      it('should not allow deleting experiments from other tenants', async () => {
        mockPrismaClient.experiment.findUnique.mockResolvedValue(null);

        const response = await request(app)
          .delete(`/v1/experiments/${mockExperiments.otherTenant.id}`)
          .set(createRequestHeaders(tokens.admin))
          .expect(404);

        assertErrorResponse(response, 'EXPERIMENT_NOT_FOUND', 404);
      });

      it('should not allow controlling experiments from other tenants', async () => {
        mockPrismaClient.experiment.findUnique.mockResolvedValue(null);

        const response = await request(app)
          .post(`/v1/experiments/${mockExperiments.otherTenant.id}/start`)
          .set(createRequestHeaders(tokens.admin))
          .expect(404);

        assertErrorResponse(response, 'EXPERIMENT_NOT_FOUND', 404);
      });

      it('should only return experiments from user tenant in list', async () => {
        // Mock experiments from both tenants
        const allExperiments = [
          mockExperiments.draft,    // tenant-1
          mockExperiments.active,   // tenant-1
          mockExperiments.otherTenant, // tenant-2 - should be filtered out
        ];

        // Repository should only return experiments from user's tenant
        mockPrismaClient.experiment.findMany.mockResolvedValue([
          mockExperiments.draft,
          mockExperiments.active,
        ]);
        mockPrismaClient.experiment.count.mockResolvedValue(2);

        const response = await request(app)
          .get('/v1/experiments')
          .set(createRequestHeaders(tokens.admin))
          .expect(200);

        // Should only return experiments from tenant-1
        expect(response.body.data).toHaveLength(2);
        expect(response.body.data.every((exp: any) => exp.tenantId === mockUsers.admin.tenantId)).toBe(true);

        // Verify the query was made with tenant filter
        expect(mockPrismaClient.experiment.findMany).toHaveBeenCalledWith(
          expect.objectContaining({
            where: expect.objectContaining({
              tenantId: mockUsers.admin.tenantId,
            }),
          })
        );
      });
    });

    describe('User Assignments', () => {
      it('should not allow assigning users to experiments from other tenants', async () => {
        const response = await request(app)
          .post(`/v1/experiments/${mockExperiments.otherTenant.id}/assignments`)
          .set(createRequestHeaders(tokens.admin))
          .send({
            user_id: 'user-123',
            user_attributes: { country: 'US' },
          })
          .expect(404);

        assertErrorResponse(response, 'EXPERIMENT_NOT_FOUND', 404);
      });

      it('should not allow viewing assignments from other tenants', async () => {
        mockPrismaClient.userAssignment.findMany.mockResolvedValue([]);
        mockPrismaClient.userAssignment.count.mockResolvedValue(0);

        const response = await request(app)
          .get(`/v1/experiments/${mockExperiments.otherTenant.id}/assignments`)
          .set(createRequestHeaders(tokens.admin))
          .expect(404);

        assertErrorResponse(response, 'EXPERIMENT_NOT_FOUND', 404);
      });

      it('should only return assignments from user tenant', async () => {
        mockPrismaClient.userAssignment.findMany.mockResolvedValue([
          mockUserAssignments.user1Control, // tenant-1
          mockUserAssignments.user2Treatment, // tenant-1
        ]);
        mockPrismaClient.userAssignment.count.mockResolvedValue(2);

        const response = await request(app)
          .get(`/v1/experiments/${mockExperiments.active.id}/assignments`)
          .set(createRequestHeaders(tokens.admin))
          .expect(200);

        expect(response.body.data).toHaveLength(2);
        expect(response.body.data.every((a: any) => a.tenantId === mockUsers.admin.tenantId)).toBe(true);

        // Verify tenant filtering in query
        expect(mockPrismaClient.userAssignment.findMany).toHaveBeenCalledWith(
          expect.objectContaining({
            where: expect.objectContaining({
              tenantId: mockUsers.admin.tenantId,
              experimentId: mockExperiments.active.id,
            }),
          })
        );
      });

      it('should filter user assignment history by tenant', async () => {
        mockPrismaClient.userAssignment.findMany.mockResolvedValue([
          mockUserAssignments.user1Control, // tenant-1
        ]);

        const response = await request(app)
          .get('/v1/experiments/assignments/history')
          .query({ user_id: 'user-1' })
          .set(createRequestHeaders(tokens.admin))
          .expect(200);

        expect(response.body.data.every((a: any) => a.tenantId === mockUsers.admin.tenantId)).toBe(true);

        // Verify tenant filtering
        expect(mockPrismaClient.userAssignment.findMany).toHaveBeenCalledWith(
          expect.objectContaining({
            where: expect.objectContaining({
              tenantId: mockUsers.admin.tenantId,
              userId: 'user-1',
            }),
          })
        );
      });
    });

    describe('Events', () => {
      it('should not allow tracking events for experiments from other tenants', async () => {
        const response = await request(app)
          .post(`/v1/experiments/${mockExperiments.otherTenant.id}/events`)
          .set(createRequestHeaders(tokens.admin))
          .send({
            user_id: 'user-123',
            event_name: 'button_click',
            event_value: 1,
          })
          .expect(404);

        assertErrorResponse(response, 'EXPERIMENT_NOT_FOUND', 404);
      });

      it('should not allow viewing events from other tenants', async () => {
        const response = await request(app)
          .get(`/v1/experiments/${mockExperiments.otherTenant.id}/events`)
          .set(createRequestHeaders(tokens.admin))
          .expect(404);

        assertErrorResponse(response, 'EXPERIMENT_NOT_FOUND', 404);
      });

      it('should only return events from user tenant', async () => {
        mockPrismaClient.event.findMany.mockResolvedValue([
          mockEvents.buttonClick1, // tenant-1
          mockEvents.buttonClick2, // tenant-1
        ]);
        mockPrismaClient.event.count.mockResolvedValue(2);

        const response = await request(app)
          .get(`/v1/experiments/${mockExperiments.active.id}/events`)
          .set(createRequestHeaders(tokens.admin))
          .expect(200);

        expect(response.body.data).toHaveLength(2);
        expect(response.body.data.every((e: any) => e.tenantId === mockUsers.admin.tenantId)).toBe(true);

        // Verify tenant filtering
        expect(mockPrismaClient.event.findMany).toHaveBeenCalledWith(
          expect.objectContaining({
            where: expect.objectContaining({
              tenantId: mockUsers.admin.tenantId,
              experimentId: mockExperiments.active.id,
            }),
          })
        );
      });
    });

    describe('Analytics', () => {
      it('should not allow viewing analytics from other tenants', async () => {
        const response = await request(app)
          .get(`/v1/experiments/${mockExperiments.otherTenant.id}/analytics`)
          .set(createRequestHeaders(tokens.admin))
          .expect(404);

        assertErrorResponse(response, 'EXPERIMENT_NOT_FOUND', 404);
      });

      it('should only include tenant data in dashboard', async () => {
        mockPrismaClient.experiment.findMany.mockResolvedValue([
          mockExperiments.draft,
          mockExperiments.active,
          // Should not include mockExperiments.otherTenant
        ]);

        const response = await request(app)
          .get('/v1/experiments/analytics/dashboard')
          .set(createRequestHeaders(tokens.admin))
          .expect(200);

        expect(response.body.data.experiments_summary.every(
          (exp: any) => exp.tenantId === mockUsers.admin.tenantId || !exp.tenantId
        )).toBe(true);

        // Verify tenant filtering
        expect(mockPrismaClient.experiment.findMany).toHaveBeenCalledWith(
          expect.objectContaining({
            where: expect.objectContaining({
              tenantId: mockUsers.admin.tenantId,
            }),
          })
        );
      });
    });
  });

  describe('JWT Token Tenant Validation', () => {
    it('should extract tenant ID from JWT token', async () => {
      mockPrismaClient.experiment.findMany.mockResolvedValue([]);
      mockPrismaClient.experiment.count.mockResolvedValue(0);

      const response = await request(app)
        .get('/v1/experiments')
        .set(createRequestHeaders(tokens.admin))
        .expect(200);

      // Verify tenant ID is included in response metadata
      expect(response.body.meta.tenant_id).toBe(mockUsers.admin.tenantId);
    });

    it('should reject requests with missing tenant in JWT', async () => {
      // Create token without tenant_id
      const invalidToken = tokens.admin.replace(
        /tenant_id[^,}]*/,
        'tenant_id":null'
      );

      const response = await request(app)
        .get('/v1/experiments')
        .set(createRequestHeaders(invalidToken))
        .expect(400);

      assertErrorResponse(response, 'TENANT_REQUIRED', 400);
    });

    it('should use different tenant data for different users', async () => {
      // Test with user from tenant-1
      mockPrismaClient.experiment.findMany.mockResolvedValue([mockExperiments.draft]);
      mockPrismaClient.experiment.count.mockResolvedValue(1);

      const tenant1Response = await request(app)
        .get('/v1/experiments')
        .set(createRequestHeaders(tokens.admin)) // tenant-1
        .expect(200);

      expect(tenant1Response.body.meta.tenant_id).toBe(mockUsers.admin.tenantId);

      // Reset mocks
      mockPrismaClient.experiment.findMany.mockReset();
      mockPrismaClient.experiment.count.mockReset();

      // Test with user from tenant-2
      mockPrismaClient.experiment.findMany.mockResolvedValue([mockExperiments.otherTenant]);
      mockPrismaClient.experiment.count.mockResolvedValue(1);

      const tenant2Response = await request(app)
        .get('/v1/experiments')
        .set(createRequestHeaders(tokens.otherTenant)) // tenant-2
        .expect(200);

      expect(tenant2Response.body.meta.tenant_id).toBe(mockUsers.otherTenant.tenantId);

      // Verify different tenant IDs were used in queries
      expect(mockPrismaClient.experiment.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            tenantId: mockUsers.otherTenant.tenantId,
          }),
        })
      );
    });
  });

  describe('Data Leakage Prevention', () => {
    it('should not leak tenant data in error messages', async () => {
      // Try to access experiment from other tenant
      mockPrismaClient.experiment.findUnique.mockResolvedValue(null);

      const response = await request(app)
        .get(`/v1/experiments/${mockExperiments.otherTenant.id}`)
        .set(createRequestHeaders(tokens.admin))
        .expect(404);

      // Error message should not reveal that experiment exists in another tenant
      expect(response.body.error.message).toBe('Experiment not found');
      expect(response.body.error.message).not.toContain('tenant');
      expect(response.body.error.message).not.toContain(mockExperiments.otherTenant.tenantId);
    });

    it('should not include cross-tenant data in aggregations', async () => {
      // Mock repository to return only tenant-specific data
      mockPrismaClient.experiment.count.mockResolvedValue(2); // Only tenant-1 experiments

      const response = await request(app)
        .get('/v1/experiments/analytics/dashboard')
        .set(createRequestHeaders(tokens.admin))
        .expect(200);

      // Dashboard should only show data from user's tenant
      expect(response.body.data.total_experiments).toBe(2);
      expect(response.body.meta.tenant_id).toBe(mockUsers.admin.tenantId);

      // Verify query was filtered by tenant
      expect(mockPrismaClient.experiment.count).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            tenantId: mockUsers.admin.tenantId,
          }),
        })
      );
    });

    it('should not allow bulk operations across tenants', async () => {
      const bulkEvents = [
        {
          experiment_id: mockExperiments.active.id, // tenant-1
          user_id: 'user-1',
          event_name: 'click',
        },
        {
          experiment_id: mockExperiments.otherTenant.id, // tenant-2 - should be rejected
          user_id: 'user-2',
          event_name: 'click',
        },
      ];

      const response = await request(app)
        .post('/v1/experiments/events/bulk')
        .set(createRequestHeaders(tokens.admin))
        .send({ events: bulkEvents })
        .expect(400);

      // Should reject the entire batch if any event is for wrong tenant
      assertErrorResponse(response, 'TENANT_ISOLATION_VIOLATION', 400);
    });
  });

  describe('Tenant Context Consistency', () => {
    it('should maintain tenant context throughout request lifecycle', async () => {
      // Mock a complex operation that involves multiple repository calls
      mockPrismaClient.experiment.findUnique.mockResolvedValue(mockExperiments.active);
      mockPrismaClient.variant.findMany.mockResolvedValue([]);
      mockPrismaClient.userAssignment.count.mockResolvedValue(0);
      mockPrismaClient.experiment.delete.mockResolvedValue({ id: mockExperiments.active.id });

      const response = await request(app)
        .delete(`/v1/experiments/${mockExperiments.active.id}`)
        .set(createRequestHeaders(tokens.admin))
        .expect(204);

      // Verify all repository calls used the same tenant ID
      expect(mockPrismaClient.experiment.findUnique).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            tenantId: mockUsers.admin.tenantId,
          }),
        })
      );

      expect(mockPrismaClient.userAssignment.count).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            tenantId: mockUsers.admin.tenantId,
          }),
        })
      );

      expect(mockPrismaClient.experiment.delete).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            tenantId: mockUsers.admin.tenantId,
          }),
        })
      );
    });

    it('should include tenant context in all response metadata', async () => {
      mockPrismaClient.experiment.findMany.mockResolvedValue([]);
      mockPrismaClient.experiment.count.mockResolvedValue(0);

      const response = await request(app)
        .get('/v1/experiments')
        .set(createRequestHeaders(tokens.admin))
        .expect(200);

      expect(response.body.meta).toHaveProperty('tenant_id', mockUsers.admin.tenantId);
      expect(response.body.meta).toHaveProperty('timestamp');
      expect(response.body.meta).toHaveProperty('request_id');
    });
  });
});

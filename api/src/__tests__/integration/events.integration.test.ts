// Integration tests for events API
import request from 'supertest';
import { createApp } from '../../app';
import { mockPrismaClient } from '../__mocks__/prisma';
import { 
  mockExperiments, 
  mockUsers, 
  mockRequestPayloads,
  mockEvents 
} from '../fixtures/mockData';
import { 
  generateTestTokens,
  createRequestHeaders,
  assertResponseStructure,
  assertErrorResponse,
  assertValidationError,
  assertTenantIsolation,
  setupPrismaMocks,
  resetPrismaMocks 
} from '../helpers/testHelpers';

describe('Events API Integration Tests', () => {
  let app: any;
  let tokens: any;

  beforeAll(() => {
    app = createApp();
    tokens = generateTestTokens();
  });

  beforeEach(() => {
    setupPrismaMocks(mockPrismaClient);
  });

  afterEach(() => {
    resetPrismaMocks(mockPrismaClient);
    jest.clearAllMocks();
  });

  describe('POST /v1/experiments/:experimentId/events', () => {
    it('should track event for experiment', async () => {
      mockPrismaClient.event.create.mockResolvedValue({
        ...mockEvents.buttonClick1,
        id: 'new-event-id',
      });

      const response = await request(app)
        .post(`/v1/experiments/${mockExperiments.active.id}/events`)
        .set(createRequestHeaders(tokens.admin))
        .send(mockRequestPayloads.trackEvent.valid)
        .expect(201);

      assertResponseStructure(response, true, false);
      expect(response.body.data.event_name).toBe(mockRequestPayloads.trackEvent.valid.event_name);
      expect(response.body.data.user_id).toBe(mockRequestPayloads.trackEvent.valid.user_id);
      expect(response.body.data.experiment_id).toBe(mockExperiments.active.id);
      expect(response.body.data).toHaveProperty('tracked_at');
    });

    it('should track event with custom timestamp', async () => {
      const customTimestamp = '2024-01-15T10:30:00Z';
      
      mockPrismaClient.event.create.mockResolvedValue({
        ...mockEvents.buttonClick1,
        timestamp: new Date(customTimestamp),
      });

      const response = await request(app)
        .post(`/v1/experiments/${mockExperiments.active.id}/events`)
        .set(createRequestHeaders(tokens.admin))
        .send({
          ...mockRequestPayloads.trackEvent.valid,
          timestamp: customTimestamp,
        })
        .expect(201);

      assertResponseStructure(response, true, false);
      expect(response.body.data.tracked_at).toBe(customTimestamp);
    });

    it('should track event with properties and value', async () => {
      const eventWithValue = {
        ...mockRequestPayloads.trackEvent.valid,
        event_value: 99.99,
        event_properties: {
          product_id: 'prod-123',
          category: 'electronics',
          discount_applied: true,
        },
      };

      mockPrismaClient.event.create.mockResolvedValue({
        ...mockEvents.purchase1,
        eventValue: 99.99,
        eventProperties: eventWithValue.event_properties,
      });

      const response = await request(app)
        .post(`/v1/experiments/${mockExperiments.active.id}/events`)
        .set(createRequestHeaders(tokens.admin))
        .send(eventWithValue)
        .expect(201);

      assertResponseStructure(response, true, false);
      expect(response.body.data.event_name).toBe(eventWithValue.event_name);
    });

    it('should validate required fields', async () => {
      const response = await request(app)
        .post(`/v1/experiments/${mockExperiments.active.id}/events`)
        .set(createRequestHeaders(tokens.admin))
        .send(mockRequestPayloads.trackEvent.missingEventName)
        .expect(422);

      assertValidationError(response, ['event_name']);
    });

    it('should validate experiment ID format', async () => {
      const response = await request(app)
        .post('/v1/experiments/invalid-uuid/events')
        .set(createRequestHeaders(tokens.admin))
        .send(mockRequestPayloads.trackEvent.valid)
        .expect(422);

      assertValidationError(response, ['experimentId']);
    });

    it('should reject requests without event write permissions', async () => {
      const response = await request(app)
        .post(`/v1/experiments/${mockExperiments.active.id}/events`)
        .set(createRequestHeaders(tokens.viewer))
        .send(mockRequestPayloads.trackEvent.valid)
        .expect(403);

      assertErrorResponse(response, 'FORBIDDEN', 403);
    });

    it('should enforce tenant isolation', async () => {
      const response = await request(app)
        .post(`/v1/experiments/${mockExperiments.otherTenant.id}/events`)
        .set(createRequestHeaders(tokens.admin))
        .send(mockRequestPayloads.trackEvent.valid)
        .expect(404);

      assertErrorResponse(response, 'EXPERIMENT_NOT_FOUND', 404);
    });

    it('should handle non-existent experiment', async () => {
      const response = await request(app)
        .post('/v1/experiments/123e4567-e89b-12d3-a456-426614174999/events')
        .set(createRequestHeaders(tokens.admin))
        .send(mockRequestPayloads.trackEvent.valid)
        .expect(404);

      assertErrorResponse(response, 'EXPERIMENT_NOT_FOUND', 404);
    });
  });

  describe('GET /v1/experiments/:experimentId/events', () => {
    it('should return paginated list of events', async () => {
      mockPrismaClient.event.findMany.mockResolvedValue([
        mockEvents.buttonClick1,
        mockEvents.buttonClick2,
        mockEvents.purchase1,
      ]);
      mockPrismaClient.event.count.mockResolvedValue(3);

      const response = await request(app)
        .get(`/v1/experiments/${mockExperiments.active.id}/events`)
        .set(createRequestHeaders(tokens.admin))
        .expect(200);

      assertResponseStructure(response, true, true);
      expect(response.body.data).toHaveLength(3);
      expect(response.body.pagination.total).toBe(3);
      assertTenantIsolation(response.body.data, mockUsers.admin.tenantId);
    });

    it('should filter events by event name', async () => {
      mockPrismaClient.event.findMany.mockResolvedValue([
        mockEvents.buttonClick1,
        mockEvents.buttonClick2,
      ]);
      mockPrismaClient.event.count.mockResolvedValue(2);

      const response = await request(app)
        .get(`/v1/experiments/${mockExperiments.active.id}/events`)
        .query({ event_name: 'button_click' })
        .set(createRequestHeaders(tokens.admin))
        .expect(200);

      assertResponseStructure(response, true, true);
      expect(response.body.data).toHaveLength(2);
      expect(response.body.data.every((e: any) => e.eventName === 'button_click')).toBe(true);
    });

    it('should filter events by variant', async () => {
      mockPrismaClient.event.findMany.mockResolvedValue([
        mockEvents.buttonClick2,
        mockEvents.purchase1,
      ]);
      mockPrismaClient.event.count.mockResolvedValue(2);

      const response = await request(app)
        .get(`/v1/experiments/${mockExperiments.active.id}/events`)
        .query({ variant_id: mockEvents.buttonClick2.variantId })
        .set(createRequestHeaders(tokens.admin))
        .expect(200);

      assertResponseStructure(response, true, true);
      expect(response.body.data).toHaveLength(2);
      expect(response.body.data.every((e: any) => e.variantId === mockEvents.buttonClick2.variantId)).toBe(true);
    });

    it('should filter events by date range', async () => {
      mockPrismaClient.event.findMany.mockResolvedValue([mockEvents.buttonClick1]);
      mockPrismaClient.event.count.mockResolvedValue(1);

      const response = await request(app)
        .get(`/v1/experiments/${mockExperiments.active.id}/events`)
        .query({
          start_date: '2024-01-15T00:00:00Z',
          end_date: '2024-01-15T23:59:59Z',
        })
        .set(createRequestHeaders(tokens.admin))
        .expect(200);

      assertResponseStructure(response, true, true);
      expect(response.body.data).toHaveLength(1);
    });

    it('should return events for specific user', async () => {
      mockPrismaClient.event.findMany.mockResolvedValue([
        mockEvents.buttonClick1,
      ]);

      const response = await request(app)
        .get(`/v1/experiments/${mockExperiments.active.id}/events`)
        .query({ user_id: 'user-1' })
        .set(createRequestHeaders(tokens.admin))
        .expect(200);

      assertResponseStructure(response, true, false);
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.every((e: any) => e.userId === 'user-1')).toBe(true);
    });

    it('should reject requests without event read permissions', async () => {
      const response = await request(app)
        .get(`/v1/experiments/${mockExperiments.active.id}/events`)
        .set(createRequestHeaders(tokens.viewer))
        .expect(403);

      assertErrorResponse(response, 'FORBIDDEN', 403);
    });

    it('should enforce tenant isolation', async () => {
      mockPrismaClient.event.findMany.mockResolvedValue([
        mockEvents.buttonClick1, // tenant-1
        // Should not include events from other tenants
      ]);

      const response = await request(app)
        .get(`/v1/experiments/${mockExperiments.active.id}/events`)
        .set(createRequestHeaders(tokens.admin))
        .expect(200);

      assertTenantIsolation(response.body.data, mockUsers.admin.tenantId);
    });
  });

  describe('GET /v1/experiments/:experimentId/events/stats', () => {
    it('should return event statistics', async () => {
      const mockStats = {
        totalEvents: 250,
        uniqueUsers: 100,
        eventBreakdown: [
          {
            eventName: 'button_click',
            count: 200,
            uniqueUsers: 90,
            averageValue: 1.0,
          },
          {
            eventName: 'purchase',
            count: 50,
            uniqueUsers: 45,
            averageValue: 89.99,
          },
        ],
        variantBreakdown: [
          {
            variantId: 'variant-control',
            variantName: 'Control',
            eventCount: 120,
            conversionRate: 0.48,
          },
          {
            variantId: 'variant-treatment',
            variantName: 'Treatment',
            eventCount: 130,
            conversionRate: 0.52,
          },
        ],
      };

      const response = await request(app)
        .get(`/v1/experiments/${mockExperiments.active.id}/events/stats`)
        .set(createRequestHeaders(tokens.admin))
        .expect(200);

      assertResponseStructure(response, true, false);
      expect(response.body.data).toHaveProperty('totalEvents');
      expect(response.body.data).toHaveProperty('eventBreakdown');
      expect(response.body.data).toHaveProperty('variantBreakdown');
      expect(Array.isArray(response.body.data.eventBreakdown)).toBe(true);
      expect(Array.isArray(response.body.data.variantBreakdown)).toBe(true);
    });

    it('should filter stats by event name', async () => {
      const response = await request(app)
        .get(`/v1/experiments/${mockExperiments.active.id}/events/stats`)
        .query({ event_name: 'button_click' })
        .set(createRequestHeaders(tokens.admin))
        .expect(200);

      assertResponseStructure(response, true, false);
      expect(response.body.data).toHaveProperty('totalEvents');
    });

    it('should filter stats by date range', async () => {
      const response = await request(app)
        .get(`/v1/experiments/${mockExperiments.active.id}/events/stats`)
        .query({
          start_date: '2024-01-15T00:00:00Z',
          end_date: '2024-01-15T23:59:59Z',
        })
        .set(createRequestHeaders(tokens.admin))
        .expect(200);

      assertResponseStructure(response, true, false);
      expect(response.body.data).toHaveProperty('totalEvents');
    });
  });

  describe('GET /v1/experiments/:experimentId/events/funnel', () => {
    it('should return conversion funnel data', async () => {
      const mockFunnelData = {
        steps: [
          {
            eventName: 'page_view',
            totalUsers: 1000,
            conversionRate: 1.0,
          },
          {
            eventName: 'button_click',
            totalUsers: 500,
            conversionRate: 0.5,
          },
          {
            eventName: 'purchase',
            totalUsers: 100,
            conversionRate: 0.1,
          },
        ],
        variantComparison: [
          {
            variantId: 'variant-control',
            variantName: 'Control',
            steps: [
              { eventName: 'page_view', users: 500, rate: 1.0 },
              { eventName: 'button_click', users: 240, rate: 0.48 },
              { eventName: 'purchase', users: 48, rate: 0.096 },
            ],
          },
          {
            variantId: 'variant-treatment',
            variantName: 'Treatment',
            steps: [
              { eventName: 'page_view', users: 500, rate: 1.0 },
              { eventName: 'button_click', users: 260, rate: 0.52 },
              { eventName: 'purchase', users: 52, rate: 0.104 },
            ],
          },
        ],
      };

      const response = await request(app)
        .get(`/v1/experiments/${mockExperiments.active.id}/events/funnel`)
        .query({ event_names: 'page_view,button_click,purchase' })
        .set(createRequestHeaders(tokens.admin))
        .expect(200);

      assertResponseStructure(response, true, false);
      expect(response.body.data).toHaveProperty('steps');
      expect(response.body.data).toHaveProperty('variantComparison');
      expect(Array.isArray(response.body.data.steps)).toBe(true);
      expect(Array.isArray(response.body.data.variantComparison)).toBe(true);
    });

    it('should require event_names parameter', async () => {
      const response = await request(app)
        .get(`/v1/experiments/${mockExperiments.active.id}/events/funnel`)
        .set(createRequestHeaders(tokens.admin))
        .expect(400);

      assertErrorResponse(response, 'VALIDATION_ERROR', 400);
    });

    it('should accept array of event names', async () => {
      const response = await request(app)
        .get(`/v1/experiments/${mockExperiments.active.id}/events/funnel`)
        .query({ event_names: ['page_view', 'button_click', 'purchase'] })
        .set(createRequestHeaders(tokens.admin))
        .expect(200);

      assertResponseStructure(response, true, false);
    });
  });

  describe('GET /v1/experiments/:experimentId/events/names', () => {
    it('should return unique event names', async () => {
      const mockEventNames = [
        'page_view',
        'button_click',
        'purchase',
        'signup',
        'add_to_cart',
      ];

      const response = await request(app)
        .get(`/v1/experiments/${mockExperiments.active.id}/events/names`)
        .set(createRequestHeaders(tokens.admin))
        .expect(200);

      assertResponseStructure(response, true, false);
      expect(response.body.data).toHaveProperty('event_names');
      expect(Array.isArray(response.body.data.event_names)).toBe(true);
    });

    it('should limit number of event names returned', async () => {
      const response = await request(app)
        .get(`/v1/experiments/${mockExperiments.active.id}/events/names`)
        .query({ limit: 10 })
        .set(createRequestHeaders(tokens.admin))
        .expect(200);

      assertResponseStructure(response, true, false);
      expect(response.body.data.event_names.length).toBeLessThanOrEqual(10);
    });
  });

  describe('POST /v1/experiments/events/bulk', () => {
    it('should bulk track multiple events', async () => {
      const bulkEvents = [
        {
          experiment_id: mockExperiments.active.id,
          user_id: 'user-1',
          event_name: 'page_view',
          event_value: 1,
        },
        {
          experiment_id: mockExperiments.active.id,
          user_id: 'user-1',
          event_name: 'button_click',
          event_value: 1,
        },
        {
          experiment_id: mockExperiments.active.id,
          user_id: 'user-2',
          event_name: 'page_view',
          event_value: 1,
        },
      ];

      mockPrismaClient.event.createMany.mockResolvedValue({ count: 3 });

      const response = await request(app)
        .post('/v1/experiments/events/bulk')
        .set(createRequestHeaders(tokens.admin))
        .send({ events: bulkEvents })
        .expect(201);

      assertResponseStructure(response, true, false);
      expect(response.body.data.tracked_count).toBe(3);
    });

    it('should validate bulk events payload', async () => {
      const response = await request(app)
        .post('/v1/experiments/events/bulk')
        .set(createRequestHeaders(tokens.admin))
        .send({ events: [] }) // Empty array
        .expect(400);

      assertErrorResponse(response, 'VALIDATION_ERROR', 400);
    });

    it('should reject requests without event write permissions', async () => {
      const response = await request(app)
        .post('/v1/experiments/events/bulk')
        .set(createRequestHeaders(tokens.viewer))
        .send({ events: [{ user_id: 'user-1', event_name: 'test' }] })
        .expect(403);

      assertErrorResponse(response, 'FORBIDDEN', 403);
    });
  });

  describe('Rate Limiting', () => {
    it('should apply event tracking rate limits', async () => {
      const response = await request(app)
        .post(`/v1/experiments/${mockExperiments.active.id}/events`)
        .set(createRequestHeaders(tokens.admin))
        .send(mockRequestPayloads.trackEvent.valid)
        .expect(201);

      expect(response.headers).toHaveProperty('x-ratelimit-policy');
    });
  });

  describe('Error Handling', () => {
    it('should handle event service errors gracefully', async () => {
      mockPrismaClient.event.create.mockRejectedValue(new Error('Event service unavailable'));

      const response = await request(app)
        .post(`/v1/experiments/${mockExperiments.active.id}/events`)
        .set(createRequestHeaders(tokens.admin))
        .send(mockRequestPayloads.trackEvent.valid)
        .expect(500);

      assertErrorResponse(response, 'INTERNAL_SERVER_ERROR', 500);
    });

    it('should handle database errors in event listing', async () => {
      mockPrismaClient.event.findMany.mockRejectedValue(
        new Error('Database connection failed')
      );

      const response = await request(app)
        .get(`/v1/experiments/${mockExperiments.active.id}/events`)
        .set(createRequestHeaders(tokens.admin))
        .expect(500);

      assertErrorResponse(response, 'INTERNAL_SERVER_ERROR', 500);
    });

    it('should handle invalid timestamp format', async () => {
      const response = await request(app)
        .post(`/v1/experiments/${mockExperiments.active.id}/events`)
        .set(createRequestHeaders(tokens.admin))
        .send({
          ...mockRequestPayloads.trackEvent.valid,
          timestamp: 'invalid-date-format',
        })
        .expect(422);

      assertValidationError(response, ['timestamp']);
    });
  });
});

// Integration tests for user assignments API
import request from 'supertest';
import { createApp } from '../../app';
import { mockPrismaClient } from '../__mocks__/prisma';
import { 
  mockExperiments, 
  mockUsers, 
  mockRequestPayloads,
  mockUserAssignments,
  mockVariants 
} from '../fixtures/mockData';
import { 
  generateTestTokens,
  createRequestHeaders,
  assertResponseStructure,
  assertErrorResponse,
  assertValidationError,
  assertTenantIsolation,
  setupPrismaMocks,
  resetPrismaMocks 
} from '../helpers/testHelpers';

describe('User Assignments API Integration Tests', () => {
  let app: any;
  let tokens: any;

  beforeAll(() => {
    app = createApp();
    tokens = generateTestTokens();
  });

  beforeEach(() => {
    setupPrismaMocks(mockPrismaClient);
  });

  afterEach(() => {
    resetPrismaMocks(mockPrismaClient);
    jest.clearAllMocks();
  });

  describe('POST /v1/experiments/:experimentId/assignments', () => {
    it('should assign user to experiment variant', async () => {
      // Mock experiment service response
      const mockAssignmentResult = {
        variantId: mockVariants.treatment.id,
        isExcluded: false,
        exclusionReason: null,
        assignment: {
          id: 'new-assignment-id',
          experimentId: mockExperiments.active.id,
          variantId: mockVariants.treatment.id,
          variant: {
            name: mockVariants.treatment.name,
            configuration: mockVariants.treatment.configuration,
          },
        },
      };

      // Mock the experiment service method
      const mockExperimentService = {
        assignUserToExperiment: jest.fn().mockResolvedValue(mockAssignmentResult),
      };

      const response = await request(app)
        .post(`/v1/experiments/${mockExperiments.active.id}/assignments`)
        .set(createRequestHeaders(tokens.admin))
        .send(mockRequestPayloads.assignUser.valid)
        .expect(200);

      assertResponseStructure(response, true, false);
      expect(response.body.data.variantId).toBe(mockVariants.treatment.id);
      expect(response.body.data.isExcluded).toBe(false);
      expect(response.body.data.assignment).toBeDefined();
    });

    it('should exclude user who fails targeting rules', async () => {
      const mockExcludedResult = {
        variantId: null,
        isExcluded: true,
        exclusionReason: 'Failed targeting rules',
        assignment: null,
      };

      const response = await request(app)
        .post(`/v1/experiments/${mockExperiments.active.id}/assignments`)
        .set(createRequestHeaders(tokens.admin))
        .send({
          user_id: 'user-456',
          user_attributes: {
            country: 'CA', // Doesn't match US targeting rule
            device_type: 'mobile',
          },
        })
        .expect(200);

      assertResponseStructure(response, true, false);
      expect(response.body.data.isExcluded).toBe(true);
      expect(response.body.data.exclusionReason).toBe('Failed targeting rules');
    });

    it('should validate required fields', async () => {
      const response = await request(app)
        .post(`/v1/experiments/${mockExperiments.active.id}/assignments`)
        .set(createRequestHeaders(tokens.admin))
        .send(mockRequestPayloads.assignUser.missingUserId)
        .expect(422);

      assertValidationError(response, ['user_id']);
    });

    it('should validate experiment ID format', async () => {
      const response = await request(app)
        .post('/v1/experiments/invalid-uuid/assignments')
        .set(createRequestHeaders(tokens.admin))
        .send(mockRequestPayloads.assignUser.valid)
        .expect(422);

      assertValidationError(response, ['experimentId']);
    });

    it('should reject requests without assignment permissions', async () => {
      const response = await request(app)
        .post(`/v1/experiments/${mockExperiments.active.id}/assignments`)
        .set(createRequestHeaders(tokens.viewer))
        .send(mockRequestPayloads.assignUser.valid)
        .expect(403);

      assertErrorResponse(response, 'FORBIDDEN', 403);
    });

    it('should enforce tenant isolation', async () => {
      // Try to assign user to experiment from different tenant
      const response = await request(app)
        .post(`/v1/experiments/${mockExperiments.otherTenant.id}/assignments`)
        .set(createRequestHeaders(tokens.admin))
        .send(mockRequestPayloads.assignUser.valid)
        .expect(404);

      assertErrorResponse(response, 'EXPERIMENT_NOT_FOUND', 404);
    });

    it('should handle assignment to non-existent experiment', async () => {
      const response = await request(app)
        .post('/v1/experiments/123e4567-e89b-12d3-a456-426614174999/assignments')
        .set(createRequestHeaders(tokens.admin))
        .send(mockRequestPayloads.assignUser.valid)
        .expect(404);

      assertErrorResponse(response, 'EXPERIMENT_NOT_FOUND', 404);
    });

    it('should handle consistent assignment for same user', async () => {
      // First assignment
      const firstResponse = await request(app)
        .post(`/v1/experiments/${mockExperiments.active.id}/assignments`)
        .set(createRequestHeaders(tokens.admin))
        .send(mockRequestPayloads.assignUser.valid)
        .expect(200);

      // Second assignment for same user should return same variant
      const secondResponse = await request(app)
        .post(`/v1/experiments/${mockExperiments.active.id}/assignments`)
        .set(createRequestHeaders(tokens.admin))
        .send(mockRequestPayloads.assignUser.valid)
        .expect(200);

      expect(firstResponse.body.data.variantId).toBe(secondResponse.body.data.variantId);
    });
  });

  describe('GET /v1/experiments/:experimentId/assignments', () => {
    it('should return paginated list of assignments', async () => {
      mockPrismaClient.userAssignment.findMany.mockResolvedValue([
        mockUserAssignments.user1Control,
        mockUserAssignments.user2Treatment,
      ]);
      mockPrismaClient.userAssignment.count.mockResolvedValue(2);

      const response = await request(app)
        .get(`/v1/experiments/${mockExperiments.active.id}/assignments`)
        .set(createRequestHeaders(tokens.admin))
        .expect(200);

      assertResponseStructure(response, true, true);
      expect(response.body.data).toHaveLength(2);
      expect(response.body.pagination.total).toBe(2);
      assertTenantIsolation(response.body.data, mockUsers.admin.tenantId);
    });

    it('should filter assignments by variant', async () => {
      mockPrismaClient.userAssignment.findMany.mockResolvedValue([
        mockUserAssignments.user1Control,
      ]);
      mockPrismaClient.userAssignment.count.mockResolvedValue(1);

      const response = await request(app)
        .get(`/v1/experiments/${mockExperiments.active.id}/assignments`)
        .query({ variant_id: mockVariants.control.id })
        .set(createRequestHeaders(tokens.admin))
        .expect(200);

      assertResponseStructure(response, true, true);
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].variantId).toBe(mockVariants.control.id);
    });

    it('should include excluded users when requested', async () => {
      mockPrismaClient.userAssignment.findMany.mockResolvedValue([
        mockUserAssignments.user1Control,
        mockUserAssignments.user3Excluded,
      ]);
      mockPrismaClient.userAssignment.count.mockResolvedValue(2);

      const response = await request(app)
        .get(`/v1/experiments/${mockExperiments.active.id}/assignments`)
        .query({ include_excluded: 'true' })
        .set(createRequestHeaders(tokens.admin))
        .expect(200);

      assertResponseStructure(response, true, true);
      expect(response.body.data).toHaveLength(2);
      expect(response.body.data.some((a: any) => a.isExcluded)).toBe(true);
    });

    it('should return specific user assignment', async () => {
      mockPrismaClient.userAssignment.findFirst.mockResolvedValue(
        mockUserAssignments.user1Control
      );

      const response = await request(app)
        .get(`/v1/experiments/${mockExperiments.active.id}/assignments`)
        .query({ user_id: 'user-1' })
        .set(createRequestHeaders(tokens.admin))
        .expect(200);

      assertResponseStructure(response, true, false);
      expect(response.body.data.userId).toBe('user-1');
    });

    it('should return 404 for non-existent user assignment', async () => {
      mockPrismaClient.userAssignment.findFirst.mockResolvedValue(null);

      const response = await request(app)
        .get(`/v1/experiments/${mockExperiments.active.id}/assignments`)
        .query({ user_id: 'non-existent-user' })
        .set(createRequestHeaders(tokens.admin))
        .expect(404);

      assertErrorResponse(response, 'USER_ASSIGNMENT_NOT_FOUND', 404);
    });

    it('should reject requests without read permissions', async () => {
      // Create token without assignment read permissions
      const limitedToken = tokens.viewer; // Assuming viewer has limited permissions

      const response = await request(app)
        .get(`/v1/experiments/${mockExperiments.active.id}/assignments`)
        .set(createRequestHeaders(limitedToken))
        .expect(403);

      assertErrorResponse(response, 'FORBIDDEN', 403);
    });

    it('should enforce tenant isolation', async () => {
      // Mock assignments from different tenants
      mockPrismaClient.userAssignment.findMany.mockResolvedValue([
        mockUserAssignments.user1Control, // tenant-1
        // Should not include assignments from other tenants
      ]);

      const response = await request(app)
        .get(`/v1/experiments/${mockExperiments.active.id}/assignments`)
        .set(createRequestHeaders(tokens.admin))
        .expect(200);

      assertTenantIsolation(response.body.data, mockUsers.admin.tenantId);
    });
  });

  describe('GET /v1/experiments/:experimentId/assignments/stats', () => {
    it('should return assignment statistics', async () => {
      const mockStats = {
        totalAssignments: 100,
        excludedUsers: 10,
        variantBreakdown: [
          {
            variantId: mockVariants.control.id,
            variantName: mockVariants.control.name,
            assignmentCount: 45,
            percentage: 0.45,
          },
          {
            variantId: mockVariants.treatment.id,
            variantName: mockVariants.treatment.name,
            assignmentCount: 45,
            percentage: 0.45,
          },
        ],
      };

      const response = await request(app)
        .get(`/v1/experiments/${mockExperiments.active.id}/assignments/stats`)
        .set(createRequestHeaders(tokens.admin))
        .expect(200);

      assertResponseStructure(response, true, false);
      expect(response.body.data).toHaveProperty('totalAssignments');
      expect(response.body.data).toHaveProperty('variantBreakdown');
      expect(Array.isArray(response.body.data.variantBreakdown)).toBe(true);
    });

    it('should reject requests without read permissions', async () => {
      const response = await request(app)
        .get(`/v1/experiments/${mockExperiments.active.id}/assignments/stats`)
        .set(createRequestHeaders(tokens.viewer))
        .expect(403);

      assertErrorResponse(response, 'FORBIDDEN', 403);
    });
  });

  describe('POST /v1/experiments/:experimentId/assignments/bulk', () => {
    it('should bulk assign multiple users', async () => {
      const bulkAssignments = [
        {
          user_id: 'user-1',
          user_attributes: { country: 'US', device_type: 'desktop' },
        },
        {
          user_id: 'user-2',
          user_attributes: { country: 'US', device_type: 'mobile' },
        },
        {
          user_id: 'user-3',
          user_attributes: { country: 'CA', device_type: 'desktop' },
        },
      ];

      mockPrismaClient.userAssignment.createMany.mockResolvedValue({ count: 3 });

      const response = await request(app)
        .post(`/v1/experiments/${mockExperiments.active.id}/assignments/bulk`)
        .set(createRequestHeaders(tokens.admin))
        .send({ assignments: bulkAssignments })
        .expect(201);

      assertResponseStructure(response, true, false);
      expect(response.body.data.assigned_count).toBe(3);
    });

    it('should validate bulk assignment payload', async () => {
      const response = await request(app)
        .post(`/v1/experiments/${mockExperiments.active.id}/assignments/bulk`)
        .set(createRequestHeaders(tokens.admin))
        .send({ assignments: [] }) // Empty array
        .expect(400);

      assertErrorResponse(response, 'VALIDATION_ERROR', 400);
    });

    it('should reject requests without write permissions', async () => {
      const response = await request(app)
        .post(`/v1/experiments/${mockExperiments.active.id}/assignments/bulk`)
        .set(createRequestHeaders(tokens.viewer))
        .send({ assignments: [{ user_id: 'user-1' }] })
        .expect(403);

      assertErrorResponse(response, 'FORBIDDEN', 403);
    });
  });

  describe('GET /v1/experiments/assignments/history', () => {
    it('should return user assignment history across experiments', async () => {
      const mockHistory = [
        mockUserAssignments.user1Control,
        {
          ...mockUserAssignments.user1Control,
          experimentId: 'other-experiment-id',
          variantId: 'other-variant-id',
        },
      ];

      mockPrismaClient.userAssignment.findMany.mockResolvedValue(mockHistory);

      const response = await request(app)
        .get('/v1/experiments/assignments/history')
        .query({ user_id: 'user-1' })
        .set(createRequestHeaders(tokens.admin))
        .expect(200);

      assertResponseStructure(response, true, false);
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.every((a: any) => a.userId === 'user-1')).toBe(true);
      assertTenantIsolation(response.body.data, mockUsers.admin.tenantId);
    });

    it('should require user_id parameter', async () => {
      const response = await request(app)
        .get('/v1/experiments/assignments/history')
        .set(createRequestHeaders(tokens.admin))
        .expect(404);

      assertErrorResponse(response, 'USER_ID_REQUIRED', 404);
    });

    it('should limit history results', async () => {
      const mockHistory = Array.from({ length: 100 }, (_, i) => ({
        ...mockUserAssignments.user1Control,
        id: `assignment-${i}`,
      }));

      mockPrismaClient.userAssignment.findMany.mockResolvedValue(mockHistory.slice(0, 50));

      const response = await request(app)
        .get('/v1/experiments/assignments/history')
        .query({ user_id: 'user-1', limit: '50' })
        .set(createRequestHeaders(tokens.admin))
        .expect(200);

      assertResponseStructure(response, true, false);
      expect(response.body.data).toHaveLength(50);
    });
  });

  describe('Rate Limiting', () => {
    it('should apply user assignment rate limits', async () => {
      // Note: This test would require actual rate limiting setup
      // In a real test environment, you'd make multiple rapid requests
      // and verify that rate limiting kicks in

      const response = await request(app)
        .post(`/v1/experiments/${mockExperiments.active.id}/assignments`)
        .set(createRequestHeaders(tokens.admin))
        .send(mockRequestPayloads.assignUser.valid)
        .expect(200);

      expect(response.headers).toHaveProperty('x-ratelimit-policy');
    });
  });

  describe('Error Handling', () => {
    it('should handle assignment service errors gracefully', async () => {
      // Mock service to throw an error
      const mockError = new Error('Assignment service unavailable');
      
      const response = await request(app)
        .post(`/v1/experiments/${mockExperiments.active.id}/assignments`)
        .set(createRequestHeaders(tokens.admin))
        .send(mockRequestPayloads.assignUser.valid)
        .expect(500);

      assertErrorResponse(response, 'INTERNAL_SERVER_ERROR', 500);
    });

    it('should handle database errors in assignment listing', async () => {
      mockPrismaClient.userAssignment.findMany.mockRejectedValue(
        new Error('Database connection failed')
      );

      const response = await request(app)
        .get(`/v1/experiments/${mockExperiments.active.id}/assignments`)
        .set(createRequestHeaders(tokens.admin))
        .expect(500);

      assertErrorResponse(response, 'INTERNAL_SERVER_ERROR', 500);
    });
  });
});

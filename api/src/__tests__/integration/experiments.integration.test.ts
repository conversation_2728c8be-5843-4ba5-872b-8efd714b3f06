// Integration tests for experiments API
import request from 'supertest';
import { createApp } from '../../app';
import { mockPrismaClient } from '../__mocks__/prisma';
import { 
  mockExperiments, 
  mockUsers, 
  mockRequestPayloads,
  mockVariants 
} from '../fixtures/mockData';
import { 
  generateTestTokens,
  createRequestHeaders,
  assertResponseStructure,
  assertErrorResponse,
  assertValidationError,
  assertTenantIsolation,
  setupPrismaMocks,
  resetPrismaMocks 
} from '../helpers/testHelpers';

describe('Experiments API Integration Tests', () => {
  let app: any;
  let tokens: any;

  beforeAll(() => {
    app = createApp();
    tokens = generateTestTokens();
  });

  beforeEach(() => {
    setupPrismaMocks(mockPrismaClient);
  });

  afterEach(() => {
    resetPrismaMocks(mockPrismaClient);
    jest.clearAllMocks();
  });

  describe('GET /v1/experiments', () => {
    it('should return paginated list of experiments for authenticated user', async () => {
      mockPrismaClient.experiment.findMany.mockResolvedValue([
        mockExperiments.draft,
        mockExperiments.active,
      ]);
      mockPrismaClient.experiment.count.mockResolvedValue(2);

      const response = await request(app)
        .get('/v1/experiments')
        .set(createRequestHeaders(tokens.admin))
        .expect(200);

      assertResponseStructure(response, true, true);
      expect(response.body.data).toHaveLength(2);
      expect(response.body.pagination.total).toBe(2);
      assertTenantIsolation(response.body.data, mockUsers.admin.tenantId);
    });

    it('should apply filters correctly', async () => {
      mockPrismaClient.experiment.findMany.mockResolvedValue([mockExperiments.active]);
      mockPrismaClient.experiment.count.mockResolvedValue(1);

      const response = await request(app)
        .get('/v1/experiments')
        .query({
          status: 'ACTIVE',
          tags: 'test,ui',
          search: 'button',
          page: 1,
          limit: 10,
        })
        .set(createRequestHeaders(tokens.admin))
        .expect(200);

      assertResponseStructure(response, true, true);
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].status).toBe('ACTIVE');
    });

    it('should reject unauthenticated requests', async () => {
      const response = await request(app)
        .get('/v1/experiments')
        .expect(401);

      assertErrorResponse(response, 'UNAUTHORIZED', 401);
    });

    it('should reject requests with insufficient permissions', async () => {
      // Create token without read permissions
      const limitedToken = generateTestTokens().viewer;
      // Mock user with no permissions
      const noPermissionsUser = {
        ...mockUsers.viewer,
        permissions: [], // No permissions
      };

      const response = await request(app)
        .get('/v1/experiments')
        .set(createRequestHeaders(limitedToken))
        .expect(403);

      assertErrorResponse(response, 'FORBIDDEN', 403);
    });

    it('should enforce tenant isolation', async () => {
      // Mock experiments from different tenants
      mockPrismaClient.experiment.findMany.mockResolvedValue([
        mockExperiments.draft, // tenant-1
        // Should not include mockExperiments.otherTenant
      ]);

      const response = await request(app)
        .get('/v1/experiments')
        .set(createRequestHeaders(tokens.admin))
        .expect(200);

      // Should only return experiments from user's tenant
      assertTenantIsolation(response.body.data, mockUsers.admin.tenantId);
    });

    it('should validate pagination parameters', async () => {
      const response = await request(app)
        .get('/v1/experiments')
        .query({
          page: 0, // Invalid - must be >= 1
          limit: 101, // Invalid - max is 100
        })
        .set(createRequestHeaders(tokens.admin))
        .expect(422);

      assertValidationError(response, ['page', 'limit']);
    });

    it('should validate sort parameters', async () => {
      const response = await request(app)
        .get('/v1/experiments')
        .query({
          sort_by: 'invalid_field',
          sort_order: 'invalid_order',
        })
        .set(createRequestHeaders(tokens.admin))
        .expect(422);

      assertValidationError(response, ['sort_by', 'sort_order']);
    });
  });

  describe('POST /v1/experiments', () => {
    it('should create experiment with valid data', async () => {
      const mockCreatedExperiment = {
        ...mockExperiments.draft,
        id: 'new-experiment-id',
        name: mockRequestPayloads.createExperiment.valid.name,
      };

      mockPrismaClient.$transaction.mockResolvedValue({
        experiment: mockCreatedExperiment,
        variants: [mockVariants.control, mockVariants.treatment],
        targetingRules: [],
      });

      const response = await request(app)
        .post('/v1/experiments')
        .set(createRequestHeaders(tokens.admin))
        .send(mockRequestPayloads.createExperiment.valid)
        .expect(201);

      assertResponseStructure(response, true, false);
      expect(response.body.data.experiment.name).toBe(
        mockRequestPayloads.createExperiment.valid.name
      );
    });

    it('should reject experiment creation with invalid data', async () => {
      const response = await request(app)
        .post('/v1/experiments')
        .set(createRequestHeaders(tokens.admin))
        .send(mockRequestPayloads.createExperiment.invalidName)
        .expect(422);

      assertValidationError(response, ['name', 'variants']);
    });

    it('should validate variant traffic weights', async () => {
      const response = await request(app)
        .post('/v1/experiments')
        .set(createRequestHeaders(tokens.admin))
        .send(mockRequestPayloads.createExperiment.invalidTrafficWeights)
        .expect(422);

      assertValidationError(response);
      expect(response.body.error.message).toContain('traffic weights must sum to 1.0');
    });

    it('should require minimum number of variants', async () => {
      const response = await request(app)
        .post('/v1/experiments')
        .set(createRequestHeaders(tokens.admin))
        .send(mockRequestPayloads.createExperiment.invalidVariants)
        .expect(422);

      assertValidationError(response, ['variants']);
    });

    it('should reject requests without write permissions', async () => {
      const response = await request(app)
        .post('/v1/experiments')
        .set(createRequestHeaders(tokens.viewer))
        .send(mockRequestPayloads.createExperiment.valid)
        .expect(403);

      assertErrorResponse(response, 'FORBIDDEN', 403);
    });

    it('should handle database errors gracefully', async () => {
      mockPrismaClient.$transaction.mockRejectedValue(new Error('Database connection failed'));

      const response = await request(app)
        .post('/v1/experiments')
        .set(createRequestHeaders(tokens.admin))
        .send(mockRequestPayloads.createExperiment.valid)
        .expect(500);

      assertErrorResponse(response, 'INTERNAL_SERVER_ERROR', 500);
    });
  });

  describe('GET /v1/experiments/:experimentId', () => {
    it('should return experiment by ID', async () => {
      mockPrismaClient.experiment.findUnique.mockResolvedValue(mockExperiments.active);

      const response = await request(app)
        .get(`/v1/experiments/${mockExperiments.active.id}`)
        .set(createRequestHeaders(tokens.admin))
        .expect(200);

      assertResponseStructure(response, true, false);
      expect(response.body.data.id).toBe(mockExperiments.active.id);
      expect(response.body.data.tenantId).toBe(mockUsers.admin.tenantId);
    });

    it('should return 404 for non-existent experiment', async () => {
      mockPrismaClient.experiment.findUnique.mockResolvedValue(null);

      const response = await request(app)
        .get('/v1/experiments/non-existent-id')
        .set(createRequestHeaders(tokens.admin))
        .expect(404);

      assertErrorResponse(response, 'EXPERIMENT_NOT_FOUND', 404);
    });

    it('should validate UUID format', async () => {
      const response = await request(app)
        .get('/v1/experiments/invalid-uuid')
        .set(createRequestHeaders(tokens.admin))
        .expect(422);

      assertValidationError(response, ['experimentId']);
    });

    it('should enforce tenant isolation', async () => {
      // Mock finding experiment from different tenant
      mockPrismaClient.experiment.findUnique.mockResolvedValue(null);

      const response = await request(app)
        .get(`/v1/experiments/${mockExperiments.otherTenant.id}`)
        .set(createRequestHeaders(tokens.admin))
        .expect(404);

      assertErrorResponse(response, 'EXPERIMENT_NOT_FOUND', 404);
    });
  });

  describe('PUT /v1/experiments/:experimentId', () => {
    it('should update experiment in DRAFT status', async () => {
      mockPrismaClient.experiment.findUnique.mockResolvedValue(mockExperiments.draft);
      mockPrismaClient.experiment.update.mockResolvedValue({
        ...mockExperiments.draft,
        ...mockRequestPayloads.updateExperiment.valid,
      });

      const response = await request(app)
        .put(`/v1/experiments/${mockExperiments.draft.id}`)
        .set(createRequestHeaders(tokens.admin))
        .send(mockRequestPayloads.updateExperiment.valid)
        .expect(200);

      assertResponseStructure(response, true, false);
      expect(response.body.data.name).toBe(mockRequestPayloads.updateExperiment.valid.name);
    });

    it('should reject update for non-DRAFT experiment', async () => {
      mockPrismaClient.experiment.findUnique.mockResolvedValue(mockExperiments.active);

      const response = await request(app)
        .put(`/v1/experiments/${mockExperiments.active.id}`)
        .set(createRequestHeaders(tokens.admin))
        .send(mockRequestPayloads.updateExperiment.valid)
        .expect(409);

      assertErrorResponse(response, 'CONFLICT', 409);
    });

    it('should reject requests without write permissions', async () => {
      const response = await request(app)
        .put(`/v1/experiments/${mockExperiments.draft.id}`)
        .set(createRequestHeaders(tokens.viewer))
        .send(mockRequestPayloads.updateExperiment.valid)
        .expect(403);

      assertErrorResponse(response, 'FORBIDDEN', 403);
    });
  });

  describe('DELETE /v1/experiments/:experimentId', () => {
    it('should delete experiment in DRAFT status without assignments', async () => {
      mockPrismaClient.experiment.findUnique.mockResolvedValue(mockExperiments.draft);
      mockPrismaClient.userAssignment.count.mockResolvedValue(0);
      mockPrismaClient.experiment.delete.mockResolvedValue({ id: mockExperiments.draft.id });

      const response = await request(app)
        .delete(`/v1/experiments/${mockExperiments.draft.id}`)
        .set(createRequestHeaders(tokens.admin))
        .expect(204);

      expect(response.body).toEqual({});
    });

    it('should reject deletion of experiment with assignments', async () => {
      mockPrismaClient.experiment.findUnique.mockResolvedValue(mockExperiments.draft);
      mockPrismaClient.userAssignment.count.mockResolvedValue(10);

      const response = await request(app)
        .delete(`/v1/experiments/${mockExperiments.draft.id}`)
        .set(createRequestHeaders(tokens.admin))
        .expect(409);

      assertErrorResponse(response, 'CONFLICT', 409);
    });

    it('should reject deletion of ACTIVE experiment', async () => {
      mockPrismaClient.experiment.findUnique.mockResolvedValue(mockExperiments.active);

      const response = await request(app)
        .delete(`/v1/experiments/${mockExperiments.active.id}`)
        .set(createRequestHeaders(tokens.admin))
        .expect(409);

      assertErrorResponse(response, 'CONFLICT', 409);
    });

    it('should reject requests without delete permissions', async () => {
      const response = await request(app)
        .delete(`/v1/experiments/${mockExperiments.draft.id}`)
        .set(createRequestHeaders(tokens.experimenter))
        .expect(403);

      assertErrorResponse(response, 'FORBIDDEN', 403);
    });
  });

  describe('Experiment Control Endpoints', () => {
    describe('POST /v1/experiments/:experimentId/start', () => {
      it('should start experiment from DRAFT status', async () => {
        mockPrismaClient.experiment.findUnique.mockResolvedValue(mockExperiments.draft);
        mockPrismaClient.variant.findMany.mockResolvedValue([
          mockVariants.control,
          mockVariants.treatment,
        ]);
        mockPrismaClient.experiment.update.mockResolvedValue({
          ...mockExperiments.draft,
          status: 'ACTIVE',
          startDate: new Date(),
        });

        const response = await request(app)
          .post(`/v1/experiments/${mockExperiments.draft.id}/start`)
          .set(createRequestHeaders(tokens.admin))
          .send({ start_date: '2024-01-15T00:00:00Z' })
          .expect(200);

        assertResponseStructure(response, true, false);
        expect(response.body.data.status).toBe('ACTIVE');
      });

      it('should reject starting experiment without sufficient variants', async () => {
        mockPrismaClient.experiment.findUnique.mockResolvedValue(mockExperiments.draft);
        mockPrismaClient.variant.findMany.mockResolvedValue([mockVariants.control]); // Only 1 variant

        const response = await request(app)
          .post(`/v1/experiments/${mockExperiments.draft.id}/start`)
          .set(createRequestHeaders(tokens.admin))
          .expect(400);

        assertErrorResponse(response, 'INSUFFICIENT_VARIANTS', 400);
      });

      it('should reject starting non-DRAFT experiment', async () => {
        mockPrismaClient.experiment.findUnique.mockResolvedValue(mockExperiments.active);

        const response = await request(app)
          .post(`/v1/experiments/${mockExperiments.active.id}/start`)
          .set(createRequestHeaders(tokens.admin))
          .expect(409);

        assertErrorResponse(response, 'INVALID_STATUS_TRANSITION', 409);
      });

      it('should reject requests without control permissions', async () => {
        const response = await request(app)
          .post(`/v1/experiments/${mockExperiments.draft.id}/start`)
          .set(createRequestHeaders(tokens.viewer))
          .expect(403);

        assertErrorResponse(response, 'FORBIDDEN', 403);
      });
    });

    describe('POST /v1/experiments/:experimentId/pause', () => {
      it('should pause ACTIVE experiment', async () => {
        mockPrismaClient.experiment.findUnique.mockResolvedValue(mockExperiments.active);
        mockPrismaClient.experiment.update.mockResolvedValue({
          ...mockExperiments.active,
          status: 'PAUSED',
        });

        const response = await request(app)
          .post(`/v1/experiments/${mockExperiments.active.id}/pause`)
          .set(createRequestHeaders(tokens.admin))
          .send({ reason: 'Technical issues' })
          .expect(200);

        assertResponseStructure(response, true, false);
        expect(response.body.data.status).toBe('PAUSED');
      });

      it('should reject pausing non-ACTIVE experiment', async () => {
        mockPrismaClient.experiment.findUnique.mockResolvedValue(mockExperiments.draft);

        const response = await request(app)
          .post(`/v1/experiments/${mockExperiments.draft.id}/pause`)
          .set(createRequestHeaders(tokens.admin))
          .expect(409);

        assertErrorResponse(response, 'INVALID_STATUS_TRANSITION', 409);
      });
    });
  });

  describe('Rate Limiting', () => {
    it('should include rate limit headers', async () => {
      mockPrismaClient.experiment.findMany.mockResolvedValue([]);
      mockPrismaClient.experiment.count.mockResolvedValue(0);

      const response = await request(app)
        .get('/v1/experiments')
        .set(createRequestHeaders(tokens.admin))
        .expect(200);

      expect(response.headers).toHaveProperty('x-ratelimit-policy');
    });

    // Note: Testing actual rate limiting would require multiple requests
    // and proper Redis setup, which is typically done in end-to-end tests
  });

  describe('Error Handling', () => {
    it('should handle malformed JSON gracefully', async () => {
      const response = await request(app)
        .post('/v1/experiments')
        .set(createRequestHeaders(tokens.admin))
        .set('Content-Type', 'application/json')
        .send('{ invalid json }')
        .expect(400);

      assertErrorResponse(response, 'INVALID_JSON', 400);
    });

    it('should handle database connection errors', async () => {
      mockPrismaClient.experiment.findMany.mockRejectedValue(
        new Error('Database connection failed')
      );

      const response = await request(app)
        .get('/v1/experiments')
        .set(createRequestHeaders(tokens.admin))
        .expect(500);

      assertErrorResponse(response, 'INTERNAL_SERVER_ERROR', 500);
    });

    it('should include request ID in error responses', async () => {
      const requestId = 'test-request-123';
      
      const response = await request(app)
        .get('/v1/experiments/invalid-uuid')
        .set(createRequestHeaders(tokens.admin, requestId))
        .expect(422);

      expect(response.body.meta.request_id).toBe(requestId);
    });
  });
});

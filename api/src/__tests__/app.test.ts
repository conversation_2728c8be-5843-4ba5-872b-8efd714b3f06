// Basic application tests
import request from 'supertest';
import { createApp } from '../app';
import { generateToken } from '../middleware/auth';

describe('A/B Testing API', () => {
  let app: any;
  let authToken: string;

  beforeAll(() => {
    app = createApp();
    
    // Generate a test JWT token
    authToken = generateToken({
      email: '<EMAIL>',
      tenantId: 'test-tenant-id',
      roles: ['admin'],
      permissions: [
        'experiments:read',
        'experiments:write',
        'experiments:delete',
        'experiments:control',
        'assignments:read',
        'assignments:write',
        'events:read',
        'events:write',
        'analytics:read',
      ],
    });
  });

  describe('Health and Info Endpoints', () => {
    it('should return health status', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      expect(response.body).toHaveProperty('status', 'healthy');
      expect(response.body).toHaveProperty('timestamp');
      expect(response.body).toHaveProperty('uptime');
    });

    it('should return API info', async () => {
      const response = await request(app)
        .get('/v1')
        .expect(200);

      expect(response.body.data).toHaveProperty('name');
      expect(response.body.data).toHaveProperty('version');
      expect(response.body.data).toHaveProperty('features');
      expect(response.body).toHaveProperty('meta');
    });

    it('should return version info', async () => {
      const response = await request(app)
        .get('/v1/version')
        .expect(200);

      expect(response.body.data).toHaveProperty('api_version', 'v1');
      expect(response.body.data).toHaveProperty('environment');
      expect(response.body.data).toHaveProperty('features');
    });

    it('should return metrics', async () => {
      const response = await request(app)
        .get('/v1/metrics')
        .expect(200);

      expect(response.body).toHaveProperty('timestamp');
      expect(response.body).toHaveProperty('uptime_seconds');
      expect(response.body).toHaveProperty('memory_usage');
    });
  });

  describe('Authentication', () => {
    it('should reject requests without authentication', async () => {
      const response = await request(app)
        .get('/v1/experiments')
        .expect(401);

      expect(response.body.error).toHaveProperty('code', 'UNAUTHORIZED');
    });

    it('should reject requests with invalid token', async () => {
      const response = await request(app)
        .get('/v1/experiments')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);

      expect(response.body.error).toHaveProperty('code', 'INVALID_TOKEN');
    });

    it('should accept requests with valid token', async () => {
      const response = await request(app)
        .get('/v1/experiments')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('meta');
    });
  });

  describe('Request Validation', () => {
    it('should validate experiment creation request', async () => {
      const invalidExperiment = {
        name: '', // Invalid: empty name
        variants: [
          {
            name: 'Variant 1',
            traffic_weight: 0.6,
          },
        ], // Invalid: only one variant
      };

      const response = await request(app)
        .post('/v1/experiments')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidExperiment)
        .expect(422);

      expect(response.body.error).toHaveProperty('code', 'VALIDATION_ERROR');
      expect(response.body.error).toHaveProperty('field_errors');
      expect(response.body.error.field_errors).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            field: expect.stringContaining('name'),
          }),
          expect.objectContaining({
            field: expect.stringContaining('variants'),
          }),
        ])
      );
    });

    it('should validate traffic weight sum', async () => {
      const invalidExperiment = {
        name: 'Test Experiment',
        variants: [
          {
            name: 'Variant 1',
            traffic_weight: 0.6,
          },
          {
            name: 'Variant 2',
            traffic_weight: 0.6, // Total = 1.2, should be 1.0
          },
        ],
      };

      const response = await request(app)
        .post('/v1/experiments')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidExperiment)
        .expect(422);

      expect(response.body.error).toHaveProperty('code', 'VALIDATION_ERROR');
    });
  });

  describe('Rate Limiting', () => {
    it('should include rate limit headers', async () => {
      const response = await request(app)
        .get('/v1/experiments')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.headers).toHaveProperty('x-ratelimit-policy');
    });
  });

  describe('Error Handling', () => {
    it('should handle 404 for unknown routes', async () => {
      const response = await request(app)
        .get('/v1/unknown-endpoint')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      expect(response.body.error).toHaveProperty('code', 'ROUTE_NOT_FOUND');
      expect(response.body.error.details).toHaveProperty('available_routes');
    });

    it('should handle malformed JSON', async () => {
      const response = await request(app)
        .post('/v1/experiments')
        .set('Authorization', `Bearer ${authToken}`)
        .set('Content-Type', 'application/json')
        .send('{ invalid json }')
        .expect(400);

      expect(response.body.error).toHaveProperty('code', 'INVALID_JSON');
    });
  });

  describe('Response Format', () => {
    it('should return consistent response format', async () => {
      const response = await request(app)
        .get('/v1/experiments')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      // Check response structure
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('meta');
      expect(response.body).toHaveProperty('pagination');

      // Check meta structure
      expect(response.body.meta).toHaveProperty('timestamp');
      expect(response.body.meta).toHaveProperty('request_id');
      expect(response.body.meta).toHaveProperty('tenant_id');

      // Check pagination structure
      expect(response.body.pagination).toHaveProperty('page');
      expect(response.body.pagination).toHaveProperty('limit');
      expect(response.body.pagination).toHaveProperty('total');
      expect(response.body.pagination).toHaveProperty('total_pages');
      expect(response.body.pagination).toHaveProperty('has_next');
      expect(response.body.pagination).toHaveProperty('has_prev');
    });

    it('should include request ID in responses', async () => {
      const requestId = 'test-request-id';
      
      const response = await request(app)
        .get('/v1/experiments')
        .set('Authorization', `Bearer ${authToken}`)
        .set('X-Request-ID', requestId)
        .expect(200);

      expect(response.body.meta.request_id).toBe(requestId);
      expect(response.headers['x-request-id']).toBe(requestId);
    });
  });

  describe('CORS', () => {
    it('should include CORS headers', async () => {
      const response = await request(app)
        .options('/v1/experiments')
        .set('Origin', 'http://localhost:3000')
        .expect(204);

      expect(response.headers).toHaveProperty('access-control-allow-origin');
      expect(response.headers).toHaveProperty('access-control-allow-methods');
      expect(response.headers).toHaveProperty('access-control-allow-headers');
    });
  });

  describe('Security Headers', () => {
    it('should include security headers', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      // Helmet.js security headers
      expect(response.headers).toHaveProperty('x-content-type-options');
      expect(response.headers).toHaveProperty('x-frame-options');
      expect(response.headers).toHaveProperty('x-xss-protection');
    });
  });
});

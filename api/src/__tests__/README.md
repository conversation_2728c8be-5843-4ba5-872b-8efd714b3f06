# A/B Testing API Test Suite

Comprehensive unit and integration tests for the A/B Testing Experiment Management API.

## 🧪 Test Structure

```
src/__tests__/
├── __mocks__/              # Mock implementations
│   ├── prisma.ts           # Prisma client mock
│   └── redis.ts            # Redis client mock
├── fixtures/               # Test data and mock objects
│   └── mockData.ts         # Comprehensive mock data
├── helpers/                # Test utility functions
│   └── testHelpers.ts      # Common test helpers
├── unit/                   # Unit tests
│   ├── middleware/         # Middleware unit tests
│   └── controllers/        # Controller unit tests
├── integration/            # Integration tests
│   ├── experiments.integration.test.ts
│   ├── assignments.integration.test.ts
│   ├── events.integration.test.ts
│   └── tenantIsolation.test.ts
├── setup.ts                # Test setup and configuration
└── README.md               # This file
```

## 🚀 Running Tests

### All Tests
```bash
npm test                    # Run all tests
npm run test:coverage       # Run all tests with coverage
```

### Unit Tests
```bash
npm run test:unit           # Run unit tests only
npm run test:unit:coverage  # Run unit tests with coverage
npm run test:watch:unit     # Watch mode for unit tests
```

### Integration Tests
```bash
npm run test:integration           # Run integration tests only
npm run test:integration:coverage  # Run integration tests with coverage
npm run test:watch:integration     # Watch mode for integration tests
```

### Watch Mode
```bash
npm run test:watch          # Watch all tests
npm run test:watch:unit     # Watch unit tests only
npm run test:watch:integration # Watch integration tests only
```

## 📋 Test Categories

### Unit Tests

**Authentication Middleware (`auth.test.ts`)**
- JWT token validation
- Permission checking
- Role-based authorization
- Token generation and verification
- Error handling for invalid tokens

**Validation Middleware (`validation.test.ts`)**
- Request schema validation
- Field-level error reporting
- Custom validation rules
- Experiment-specific validations
- Query parameter validation

**Controllers (`experiments.controller.test.ts`)**
- Business logic testing
- Repository interaction mocking
- Error handling
- Response formatting
- Status code verification

### Integration Tests

**Experiments API (`experiments.integration.test.ts`)**
- Complete CRUD operations
- Experiment lifecycle management
- Authentication and authorization
- Request/response validation
- Error scenarios

**User Assignments API (`assignments.integration.test.ts`)**
- User assignment to variants
- Assignment statistics
- Bulk operations
- User assignment history
- Targeting rule evaluation

**Events API (`events.integration.test.ts`)**
- Event tracking
- Event filtering and querying
- Conversion funnel analysis
- Bulk event tracking
- Event statistics

**Tenant Isolation (`tenantIsolation.test.ts`)**
- Cross-tenant access prevention
- Data leakage prevention
- JWT tenant validation
- Consistent tenant context
- Security boundary testing

## 🎯 Test Coverage Areas

### Happy Path Testing
- ✅ Successful experiment creation
- ✅ User assignment to variants
- ✅ Event tracking
- ✅ Analytics retrieval
- ✅ Experiment lifecycle transitions

### Error Case Testing
- ✅ Invalid request payloads
- ✅ Missing required fields
- ✅ Database connection errors
- ✅ Service unavailability
- ✅ Malformed JSON requests

### Permission Testing
- ✅ Role-based access control
- ✅ Permission-specific endpoints
- ✅ Admin vs. experimenter vs. viewer roles
- ✅ Unauthorized access attempts
- ✅ Missing authentication tokens

### Tenant Isolation Testing
- ✅ Cross-tenant data access prevention
- ✅ Tenant-specific data filtering
- ✅ JWT tenant context validation
- ✅ Data leakage prevention
- ✅ Bulk operation security

### Validation Testing
- ✅ Request schema validation
- ✅ Field-level error reporting
- ✅ Custom business rules
- ✅ UUID format validation
- ✅ Date range validation

## 🔧 Mock Data

### Test Users
- **Admin**: Full permissions across all resources
- **Experimenter**: Experiment management and control permissions
- **Viewer**: Read-only permissions
- **Other Tenant**: User from different tenant for isolation testing

### Test Experiments
- **Draft**: Experiment in draft status for creation/update testing
- **Active**: Running experiment for assignment/event testing
- **Completed**: Finished experiment for analytics testing
- **Other Tenant**: Experiment from different tenant for isolation testing

### Test Scenarios
- Valid and invalid request payloads
- Different user permission levels
- Cross-tenant access attempts
- Database error conditions
- Rate limiting scenarios

## 🛡️ Security Testing

### Authentication
- Valid JWT token acceptance
- Invalid token rejection
- Expired token handling
- Missing token scenarios
- Malformed token handling

### Authorization
- Role-based access control
- Permission-specific endpoints
- Insufficient permission handling
- Admin privilege escalation
- Cross-tenant access prevention

### Tenant Isolation
- Data segregation verification
- Cross-tenant query prevention
- Tenant context consistency
- Error message sanitization
- Bulk operation security

## 📊 Coverage Requirements

The test suite maintains high coverage standards:

- **Branches**: 80% minimum
- **Functions**: 80% minimum
- **Lines**: 80% minimum
- **Statements**: 80% minimum

### Coverage Reports
```bash
npm run test:coverage       # Generate coverage report
open coverage/lcov-report/index.html  # View HTML report
```

## 🔍 Test Utilities

### Mock Helpers
- **Prisma Client Mock**: Complete database operation mocking
- **Redis Client Mock**: Rate limiting and caching mocks
- **JWT Token Generation**: Test token creation utilities
- **Request/Response Mocks**: Express request/response mocking

### Assertion Helpers
- **Response Structure**: Consistent API response validation
- **Error Response**: Error format and code validation
- **Tenant Isolation**: Cross-tenant access verification
- **Validation Errors**: Field-level error checking

### Data Fixtures
- **Mock Users**: Different roles and permissions
- **Mock Experiments**: Various statuses and configurations
- **Mock Events**: Different event types and properties
- **Mock Assignments**: User-variant assignments

## 🐛 Debugging Tests

### Common Issues
1. **Mock Reset**: Ensure mocks are reset between tests
2. **Async Handling**: Proper async/await usage
3. **Database State**: Clean state between tests
4. **Token Expiry**: Use fresh tokens for each test

### Debug Commands
```bash
# Run specific test file
npm test -- experiments.integration.test.ts

# Run tests matching pattern
npm test -- --testNamePattern="should create experiment"

# Run with verbose output
npm test -- --verbose

# Run single test in watch mode
npm test -- --watch --testNamePattern="specific test name"
```

## 📝 Writing New Tests

### Test Structure
```typescript
describe('Feature Name', () => {
  let app: any;
  let tokens: any;

  beforeAll(() => {
    app = createApp();
    tokens = generateTestTokens();
  });

  beforeEach(() => {
    setupPrismaMocks(mockPrismaClient);
  });

  afterEach(() => {
    resetPrismaMocks(mockPrismaClient);
    jest.clearAllMocks();
  });

  describe('Happy Path', () => {
    it('should perform expected behavior', async () => {
      // Arrange
      // Act
      // Assert
    });
  });

  describe('Error Cases', () => {
    it('should handle error condition', async () => {
      // Test error scenarios
    });
  });
});
```

### Best Practices
1. **Arrange-Act-Assert**: Clear test structure
2. **Descriptive Names**: Test names should describe behavior
3. **Mock Isolation**: Reset mocks between tests
4. **Error Testing**: Test both success and failure paths
5. **Tenant Isolation**: Always test cross-tenant scenarios
6. **Permission Testing**: Verify authorization at all levels

## 🔄 Continuous Integration

Tests are designed to run in CI/CD environments:

- **No External Dependencies**: All external services are mocked
- **Deterministic**: Tests produce consistent results
- **Fast Execution**: Optimized for quick feedback
- **Comprehensive Coverage**: High confidence in code quality

### CI Configuration
```yaml
# Example GitHub Actions
- name: Run Tests
  run: |
    npm ci
    npm run test:coverage
    npm run lint
```

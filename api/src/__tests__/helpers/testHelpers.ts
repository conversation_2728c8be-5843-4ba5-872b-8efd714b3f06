// Test helper functions
import { generateToken } from '../../middleware/auth';
import { mockUsers } from '../fixtures/mockData';

/**
 * Generate JWT tokens for different user types
 */
export const generateTestTokens = () => ({
  admin: generateToken(mockUsers.admin),
  experimenter: generateToken(mockUsers.experimenter),
  viewer: generateToken(mockUsers.viewer),
  otherTenant: generateToken(mockUsers.otherTenant),
});

/**
 * Create authorization headers for requests
 */
export const createAuthHeaders = (token: string) => ({
  Authorization: `Bearer ${token}`,
});

/**
 * Create request headers with auth and request ID
 */
export const createRequestHeaders = (token: string, requestId?: string) => ({
  Authorization: `Bearer ${token}`,
  'Content-Type': 'application/json',
  'X-Request-ID': requestId || `test-req-${Date.now()}`,
});

/**
 * Mock Prisma transaction function
 */
export const mockTransaction = (callback: Function) => {
  return callback({
    experiment: {
      create: jest.fn(),
      update: jest.fn(),
      findUnique: jest.fn(),
    },
    variant: {
      createMany: jest.fn(),
    },
    targetingRule: {
      createMany: jest.fn(),
    },
  });
};

/**
 * Create mock pagination result
 */
export const createMockPaginationResult = <T>(data: T[], page = 1, limit = 20, total?: number) => ({
  data,
  pagination: {
    page,
    limit,
    total: total ?? data.length,
  },
});

/**
 * Create mock response metadata
 */
export const createMockResponseMeta = (tenantId = 'tenant-1', requestId = 'test-req') => ({
  timestamp: new Date().toISOString(),
  request_id: requestId,
  tenant_id: tenantId,
});

/**
 * Assert response structure for API responses
 */
export const assertResponseStructure = (response: any, hasData = true, hasPagination = false) => {
  expect(response.body).toHaveProperty('meta');
  expect(response.body.meta).toHaveProperty('timestamp');
  expect(response.body.meta).toHaveProperty('request_id');
  expect(response.body.meta).toHaveProperty('tenant_id');

  if (hasData) {
    expect(response.body).toHaveProperty('data');
  }

  if (hasPagination) {
    expect(response.body).toHaveProperty('pagination');
    expect(response.body.pagination).toHaveProperty('page');
    expect(response.body.pagination).toHaveProperty('limit');
    expect(response.body.pagination).toHaveProperty('total');
    expect(response.body.pagination).toHaveProperty('total_pages');
    expect(response.body.pagination).toHaveProperty('has_next');
    expect(response.body.pagination).toHaveProperty('has_prev');
  }
};

/**
 * Assert error response structure
 */
export const assertErrorResponse = (response: any, expectedCode?: string, expectedStatus?: number) => {
  expect(response.body).toHaveProperty('error');
  expect(response.body.error).toHaveProperty('code');
  expect(response.body.error).toHaveProperty('message');
  expect(response.body).toHaveProperty('meta');

  if (expectedCode) {
    expect(response.body.error.code).toBe(expectedCode);
  }

  if (expectedStatus) {
    expect(response.status).toBe(expectedStatus);
  }
};

/**
 * Assert validation error response
 */
export const assertValidationError = (response: any, expectedFields?: string[]) => {
  assertErrorResponse(response, 'VALIDATION_ERROR', 422);
  expect(response.body.error).toHaveProperty('field_errors');
  expect(Array.isArray(response.body.error.field_errors)).toBe(true);

  if (expectedFields) {
    const errorFields = response.body.error.field_errors.map((err: any) => err.field);
    expectedFields.forEach(field => {
      expect(errorFields.some((errorField: string) => errorField.includes(field))).toBe(true);
    });
  }
};

/**
 * Assert tenant isolation - response should not contain data from other tenants
 */
export const assertTenantIsolation = (responseData: any, expectedTenantId: string) => {
  if (Array.isArray(responseData)) {
    responseData.forEach(item => {
      if (item.tenantId) {
        expect(item.tenantId).toBe(expectedTenantId);
      }
    });
  } else if (responseData && responseData.tenantId) {
    expect(responseData.tenantId).toBe(expectedTenantId);
  }
};

/**
 * Create mock database query results
 */
export const createMockQueryResult = <T>(data: T | T[], count?: number) => {
  if (Array.isArray(data)) {
    return {
      data,
      count: count ?? data.length,
    };
  }
  return data;
};

/**
 * Mock Prisma client methods with common patterns
 */
export const setupPrismaMocks = (prismaClient: any) => {
  // Default successful responses
  prismaClient.experiment.findMany.mockResolvedValue([]);
  prismaClient.experiment.findUnique.mockResolvedValue(null);
  prismaClient.experiment.create.mockImplementation((args: any) => ({
    id: 'new-experiment-id',
    ...args.data,
    createdAt: new Date(),
    updatedAt: new Date(),
  }));
  prismaClient.experiment.update.mockImplementation((args: any) => ({
    id: args.where.id,
    ...args.data,
    updatedAt: new Date(),
  }));
  prismaClient.experiment.delete.mockResolvedValue({ id: 'deleted-id' });
  prismaClient.experiment.count.mockResolvedValue(0);

  // Transaction mock
  prismaClient.$transaction.mockImplementation(mockTransaction);

  return prismaClient;
};

/**
 * Reset all Prisma mocks
 */
export const resetPrismaMocks = (prismaClient: any) => {
  Object.keys(prismaClient).forEach(model => {
    if (typeof prismaClient[model] === 'object' && prismaClient[model] !== null) {
      Object.keys(prismaClient[model]).forEach(method => {
        if (jest.isMockFunction(prismaClient[model][method])) {
          prismaClient[model][method].mockReset();
        }
      });
    }
  });
};

/**
 * Create mock request object
 */
export const createMockRequest = (overrides: any = {}) => ({
  method: 'GET',
  url: '/test',
  headers: {
    'x-request-id': 'test-request-id',
    authorization: 'Bearer test-token',
  },
  params: {},
  query: {},
  body: {},
  user: mockUsers.admin,
  tenantId: 'tenant-1',
  ...overrides,
});

/**
 * Create mock response object
 */
export const createMockResponse = () => {
  const res: any = {
    status: jest.fn().mockReturnThis(),
    json: jest.fn().mockReturnThis(),
    send: jest.fn().mockReturnThis(),
    setHeader: jest.fn().mockReturnThis(),
    headersSent: false,
  };
  return res;
};

/**
 * Create mock next function
 */
export const createMockNext = () => jest.fn();

/**
 * Wait for async operations to complete
 */
export const waitForAsync = () => new Promise(resolve => setImmediate(resolve));

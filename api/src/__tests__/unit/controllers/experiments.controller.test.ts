// Experiments controller unit tests
import { ExperimentsController } from '../../../controllers/experiments.controller';
import { mockPrismaClient } from '../../__mocks__/prisma';
import { mockExperiments, mockUsers, mockRequestPayloads } from '../../fixtures/mockData';
import { 
  createMockRequest, 
  createMockResponse, 
  createMockNext,
  setupPrismaMocks,
  resetPrismaMocks 
} from '../../helpers/testHelpers';

// Mock the repositories and services
jest.mock('../../../../src/services/experiment.service');
jest.mock('../../../../src/repositories/experiment.repository');
jest.mock('../../../../src/repositories/variant.repository');
jest.mock('../../../../src/repositories/targeting-rule.repository');
jest.mock('../../../../src/repositories/user-assignment.repository');
jest.mock('../../../../src/repositories/event.repository');

describe('ExperimentsController', () => {
  let controller: ExperimentsController;
  let req: any;
  let res: any;
  let next: any;

  beforeEach(() => {
    controller = new ExperimentsController();
    req = createMockRequest();
    res = createMockResponse();
    next = createMockNext();
    
    // Setup authenticated request
    req.user = mockUsers.admin;
    req.tenantId = mockUsers.admin.tenantId;
    
    setupPrismaMocks(mockPrismaClient);
  });

  afterEach(() => {
    resetPrismaMocks(mockPrismaClient);
    jest.clearAllMocks();
  });

  describe('listExperiments', () => {
    it('should return paginated list of experiments', async () => {
      const mockExperimentList = [mockExperiments.draft, mockExperiments.active];
      
      // Mock repository response
      controller['experimentRepo'].findWithFilters = jest.fn().mockResolvedValue({
        data: mockExperimentList,
        pagination: { page: 1, limit: 20, total: 2 },
      });

      req.query = { page: '1', limit: '20' };

      await controller.listExperiments(req, res);

      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          data: mockExperimentList,
          pagination: expect.objectContaining({
            page: 1,
            limit: 20,
            total: 2,
          }),
          meta: expect.objectContaining({
            tenant_id: mockUsers.admin.tenantId,
          }),
        })
      );
    });

    it('should apply filters correctly', async () => {
      controller['experimentRepo'].findWithFilters = jest.fn().mockResolvedValue({
        data: [],
        pagination: { page: 1, limit: 20, total: 0 },
      });

      req.query = {
        status: ['ACTIVE'],
        tags: ['test'],
        search: 'button',
        start_date_from: '2024-01-01T00:00:00Z',
      };

      await controller.listExperiments(req, res);

      expect(controller['experimentRepo'].findWithFilters).toHaveBeenCalledWith(
        mockUsers.admin.tenantId,
        expect.objectContaining({
          status: ['ACTIVE'],
          tags: ['test'],
          search: 'button',
          startDateFrom: new Date('2024-01-01T00:00:00Z'),
        }),
        expect.any(Object)
      );
    });

    it('should handle repository errors', async () => {
      const error = new Error('Database error');
      controller['experimentRepo'].findWithFilters = jest.fn().mockRejectedValue(error);

      await expect(controller.listExperiments(req, res)).rejects.toThrow('Database error');
    });
  });

  describe('getExperiment', () => {
    it('should return experiment by ID', async () => {
      req.params = { experimentId: mockExperiments.active.id };
      controller['experimentRepo'].findById = jest.fn().mockResolvedValue(mockExperiments.active);

      await controller.getExperiment(req, res);

      expect(controller['experimentRepo'].findById).toHaveBeenCalledWith(
        mockExperiments.active.id,
        mockUsers.admin.tenantId
      );
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          data: mockExperiments.active,
        })
      );
    });

    it('should return 404 for non-existent experiment', async () => {
      req.params = { experimentId: 'non-existent-id' };
      controller['experimentRepo'].findById = jest.fn().mockResolvedValue(null);

      await expect(controller.getExperiment(req, res)).rejects.toThrow('Experiment not found');
    });

    it('should include relations when requested', async () => {
      req.params = { experimentId: mockExperiments.active.id };
      req.query = { include: ['variants', 'targeting_rules'] };
      
      controller['experimentRepo'].findByIdWithRelations = jest.fn().mockResolvedValue({
        ...mockExperiments.active,
        variants: [],
        targetingRules: [],
      });

      await controller.getExperiment(req, res);

      expect(controller['experimentRepo'].findByIdWithRelations).toHaveBeenCalledWith(
        mockExperiments.active.id,
        mockUsers.admin.tenantId
      );
    });
  });

  describe('createExperiment', () => {
    it('should create experiment with variants and targeting rules', async () => {
      req.body = mockRequestPayloads.createExperiment.valid;
      
      const mockResult = {
        experiment: { ...mockExperiments.draft, id: 'new-experiment-id' },
        variants: [],
        targetingRules: [],
      };
      
      controller['experimentService'].createExperiment = jest.fn().mockResolvedValue(mockResult);

      await controller.createExperiment(req, res);

      expect(controller['experimentService'].createExperiment).toHaveBeenCalledWith(
        expect.objectContaining({
          name: mockRequestPayloads.createExperiment.valid.name,
          createdBy: mockUsers.admin.id,
        }),
        mockRequestPayloads.createExperiment.valid.variants,
        mockRequestPayloads.createExperiment.valid.targeting_rules,
        { tenantId: mockUsers.admin.tenantId }
      );

      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          data: mockResult,
        })
      );
    });

    it('should handle validation errors from service', async () => {
      req.body = mockRequestPayloads.createExperiment.valid;
      
      const error = new Error('Validation failed');
      controller['experimentService'].createExperiment = jest.fn().mockRejectedValue(error);

      await expect(controller.createExperiment(req, res)).rejects.toThrow('Validation failed');
    });
  });

  describe('updateExperiment', () => {
    it('should update experiment in DRAFT status', async () => {
      req.params = { experimentId: mockExperiments.draft.id };
      req.body = mockRequestPayloads.updateExperiment.valid;

      controller['experimentRepo'].findById = jest.fn().mockResolvedValue(mockExperiments.draft);
      controller['experimentRepo'].update = jest.fn().mockResolvedValue({
        ...mockExperiments.draft,
        ...mockRequestPayloads.updateExperiment.valid,
      });

      await controller.updateExperiment(req, res);

      expect(controller['experimentRepo'].update).toHaveBeenCalledWith(
        mockExperiments.draft.id,
        mockRequestPayloads.updateExperiment.valid,
        mockUsers.admin.tenantId
      );
      expect(res.status).toHaveBeenCalledWith(200);
    });

    it('should reject update for non-DRAFT experiment', async () => {
      req.params = { experimentId: mockExperiments.active.id };
      req.body = mockRequestPayloads.updateExperiment.valid;

      controller['experimentRepo'].findById = jest.fn().mockResolvedValue(mockExperiments.active);

      await expect(controller.updateExperiment(req, res)).rejects.toThrow(
        'Only experiments in DRAFT status can be updated'
      );
    });

    it('should return 404 for non-existent experiment', async () => {
      req.params = { experimentId: 'non-existent-id' };
      req.body = mockRequestPayloads.updateExperiment.valid;

      controller['experimentRepo'].findById = jest.fn().mockResolvedValue(null);

      await expect(controller.updateExperiment(req, res)).rejects.toThrow('Experiment not found');
    });
  });

  describe('deleteExperiment', () => {
    it('should delete experiment in DRAFT status', async () => {
      req.params = { experimentId: mockExperiments.draft.id };

      controller['experimentRepo'].findById = jest.fn().mockResolvedValue(mockExperiments.draft);
      controller['userAssignmentRepo'].count = jest.fn().mockResolvedValue(0);
      controller['experimentRepo'].delete = jest.fn().mockResolvedValue({ id: mockExperiments.draft.id });

      await controller.deleteExperiment(req, res);

      expect(controller['experimentRepo'].delete).toHaveBeenCalledWith(
        mockExperiments.draft.id,
        mockUsers.admin.tenantId
      );
      expect(res.status).toHaveBeenCalledWith(204);
    });

    it('should reject deletion of experiment with user assignments', async () => {
      req.params = { experimentId: mockExperiments.draft.id };

      controller['experimentRepo'].findById = jest.fn().mockResolvedValue(mockExperiments.draft);
      controller['userAssignmentRepo'].count = jest.fn().mockResolvedValue(10); // Has assignments

      await expect(controller.deleteExperiment(req, res)).rejects.toThrow(
        'Cannot delete experiment with existing user assignments'
      );
    });

    it('should reject deletion of ACTIVE experiment', async () => {
      req.params = { experimentId: mockExperiments.active.id };

      controller['experimentRepo'].findById = jest.fn().mockResolvedValue(mockExperiments.active);

      await expect(controller.deleteExperiment(req, res)).rejects.toThrow(
        'Only experiments in DRAFT or ARCHIVED status can be deleted'
      );
    });
  });

  describe('startExperiment', () => {
    it('should start experiment from DRAFT status', async () => {
      req.params = { experimentId: mockExperiments.draft.id };
      req.body = { start_date: '2024-01-15T00:00:00Z' };

      controller['experimentRepo'].findById = jest.fn().mockResolvedValue(mockExperiments.draft);
      controller['variantRepo'].findByExperiment = jest.fn().mockResolvedValue([{}, {}]); // 2 variants
      controller['experimentRepo'].updateStatus = jest.fn().mockResolvedValue({
        ...mockExperiments.draft,
        status: 'ACTIVE',
        startDate: new Date('2024-01-15T00:00:00Z'),
      });

      await controller.startExperiment(req, res);

      expect(controller['experimentRepo'].updateStatus).toHaveBeenCalledWith(
        mockExperiments.draft.id,
        'ACTIVE',
        mockUsers.admin.tenantId
      );
      expect(res.status).toHaveBeenCalledWith(200);
    });

    it('should reject starting experiment without sufficient variants', async () => {
      req.params = { experimentId: mockExperiments.draft.id };

      controller['experimentRepo'].findById = jest.fn().mockResolvedValue(mockExperiments.draft);
      controller['variantRepo'].findByExperiment = jest.fn().mockResolvedValue([{}]); // Only 1 variant

      await expect(controller.startExperiment(req, res)).rejects.toThrow(
        'Experiment must have at least 2 variants to start'
      );
    });

    it('should reject starting non-DRAFT experiment', async () => {
      req.params = { experimentId: mockExperiments.active.id };

      controller['experimentRepo'].findById = jest.fn().mockResolvedValue(mockExperiments.active);

      await expect(controller.startExperiment(req, res)).rejects.toThrow(
        'Invalid status transition'
      );
    });
  });

  describe('pauseExperiment', () => {
    it('should pause ACTIVE experiment', async () => {
      req.params = { experimentId: mockExperiments.active.id };
      req.body = { reason: 'Technical issues' };

      controller['experimentRepo'].findById = jest.fn().mockResolvedValue(mockExperiments.active);
      controller['experimentRepo'].updateStatus = jest.fn().mockResolvedValue({
        ...mockExperiments.active,
        status: 'PAUSED',
      });

      await controller.pauseExperiment(req, res);

      expect(controller['experimentRepo'].updateStatus).toHaveBeenCalledWith(
        mockExperiments.active.id,
        'PAUSED',
        mockUsers.admin.tenantId
      );
      expect(res.status).toHaveBeenCalledWith(200);
    });

    it('should reject pausing non-ACTIVE experiment', async () => {
      req.params = { experimentId: mockExperiments.draft.id };

      controller['experimentRepo'].findById = jest.fn().mockResolvedValue(mockExperiments.draft);

      await expect(controller.pauseExperiment(req, res)).rejects.toThrow(
        'Invalid status transition'
      );
    });
  });

  describe('tenant isolation', () => {
    it('should only access experiments from user tenant', async () => {
      req.user = mockUsers.otherTenant;
      req.tenantId = mockUsers.otherTenant.tenantId;

      controller['experimentRepo'].findWithFilters = jest.fn().mockResolvedValue({
        data: [],
        pagination: { page: 1, limit: 20, total: 0 },
      });

      await controller.listExperiments(req, res);

      expect(controller['experimentRepo'].findWithFilters).toHaveBeenCalledWith(
        mockUsers.otherTenant.tenantId, // Should use the user's tenant ID
        expect.any(Object),
        expect.any(Object)
      );
    });

    it('should not find experiments from other tenants', async () => {
      req.params = { experimentId: mockExperiments.otherTenant.id };
      req.user = mockUsers.admin; // User from tenant-1
      req.tenantId = mockUsers.admin.tenantId;

      // Repository should not find experiment from other tenant
      controller['experimentRepo'].findById = jest.fn().mockResolvedValue(null);

      await expect(controller.getExperiment(req, res)).rejects.toThrow('Experiment not found');

      expect(controller['experimentRepo'].findById).toHaveBeenCalledWith(
        mockExperiments.otherTenant.id,
        mockUsers.admin.tenantId // Should filter by user's tenant
      );
    });
  });
});

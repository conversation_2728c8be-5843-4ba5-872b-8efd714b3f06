// Authentication middleware unit tests
import { authenticate, authorize, generateToken, PERMISSIONS } from '../../../middleware/auth';
import { ApiError } from '../../../utils/errors';
import { mockUsers } from '../../fixtures/mockData';
import { 
  createMockRequest, 
  createMockResponse, 
  createMockNext 
} from '../../helpers/testHelpers';

describe('Authentication Middleware', () => {
  let req: any;
  let res: any;
  let next: any;

  beforeEach(() => {
    req = createMockRequest();
    res = createMockResponse();
    next = createMockNext();
  });

  describe('authenticate', () => {
    it('should authenticate valid JWT token', async () => {
      const token = generateToken(mockUsers.admin);
      req.headers.authorization = `Bearer ${token}`;

      await authenticate(req, res, next);

      expect(req.user).toBeDefined();
      expect(req.user.id).toBe(mockUsers.admin.id);
      expect(req.user.email).toBe(mockUsers.admin.email);
      expect(req.user.tenantId).toBe(mockUsers.admin.tenantId);
      expect(req.tenantId).toBe(mockUsers.admin.tenantId);
      expect(next).toHaveBeenCalledWith();
    });

    it('should reject request without authorization header', async () => {
      delete req.headers.authorization;

      await authenticate(req, res, next);

      expect(next).toHaveBeenCalledWith(expect.any(ApiError));
      const error = next.mock.calls[0][0];
      expect(error.code).toBe('UNAUTHORIZED');
      expect(error.statusCode).toBe(401);
    });

    it('should reject request with invalid authorization header format', async () => {
      req.headers.authorization = 'InvalidFormat token';

      await authenticate(req, res, next);

      expect(next).toHaveBeenCalledWith(expect.any(ApiError));
      const error = next.mock.calls[0][0];
      expect(error.code).toBe('INVALID_AUTH_HEADER');
    });

    it('should reject request with invalid JWT token', async () => {
      req.headers.authorization = 'Bearer invalid.jwt.token';

      await authenticate(req, res, next);

      expect(next).toHaveBeenCalledWith(expect.any(ApiError));
      const error = next.mock.calls[0][0];
      expect(error.code).toBe('INVALID_TOKEN');
    });

    it('should reject expired JWT token', async () => {
      // Create an expired token (this would require mocking jwt.verify to simulate expiration)
      const expiredToken = generateToken({
        ...mockUsers.admin,
        // In a real test, you'd mock jwt.verify to throw TokenExpiredError
      });
      req.headers.authorization = `Bearer ${expiredToken}`;

      // Mock jwt.verify to throw expired error
      const jwt = require('jsonwebtoken');
      const originalVerify = jwt.verify;
      jwt.verify = jest.fn((token, secret, options, callback) => {
        const error = new jwt.TokenExpiredError('jwt expired', new Date());
        callback(error);
      });

      await authenticate(req, res, next);

      expect(next).toHaveBeenCalledWith(expect.any(ApiError));
      const error = next.mock.calls[0][0];
      expect(error.code).toBe('INVALID_TOKEN');

      // Restore original function
      jwt.verify = originalVerify;
    });

    it('should handle malformed JWT token', async () => {
      req.headers.authorization = 'Bearer malformed-token';

      await authenticate(req, res, next);

      expect(next).toHaveBeenCalledWith(expect.any(ApiError));
      const error = next.mock.calls[0][0];
      expect(error.code).toBe('INVALID_TOKEN');
    });
  });

  describe('authorize', () => {
    beforeEach(() => {
      req.user = mockUsers.admin;
      req.tenantId = mockUsers.admin.tenantId;
    });

    it('should allow access with sufficient permissions', () => {
      const middleware = authorize([PERMISSIONS.EXPERIMENTS_READ]);

      middleware(req, res, next);

      expect(next).toHaveBeenCalledWith();
    });

    it('should allow access for admin role regardless of specific permissions', () => {
      const userWithoutPermission = {
        ...mockUsers.admin,
        permissions: [], // No specific permissions
        roles: ['admin'], // But has admin role
      };
      req.user = userWithoutPermission;

      const middleware = authorize([PERMISSIONS.EXPERIMENTS_DELETE]);

      middleware(req, res, next);

      expect(next).toHaveBeenCalledWith();
    });

    it('should deny access without sufficient permissions', () => {
      req.user = mockUsers.viewer; // Only has read permissions

      const middleware = authorize([PERMISSIONS.EXPERIMENTS_WRITE]);

      middleware(req, res, next);

      expect(next).toHaveBeenCalledWith(expect.any(ApiError));
      const error = next.mock.calls[0][0];
      expect(error.code).toBe('FORBIDDEN');
      expect(error.statusCode).toBe(403);
    });

    it('should deny access for unauthenticated user', () => {
      delete req.user;

      const middleware = authorize([PERMISSIONS.EXPERIMENTS_READ]);

      middleware(req, res, next);

      expect(next).toHaveBeenCalledWith(expect.any(ApiError));
      const error = next.mock.calls[0][0];
      expect(error.code).toBe('NOT_AUTHENTICATED');
    });

    it('should allow access when no permissions required', () => {
      const middleware = authorize([]);

      middleware(req, res, next);

      expect(next).toHaveBeenCalledWith();
    });

    it('should require all specified permissions', () => {
      req.user = {
        ...mockUsers.experimenter,
        permissions: [PERMISSIONS.EXPERIMENTS_READ], // Missing EXPERIMENTS_WRITE
      };

      const middleware = authorize([
        PERMISSIONS.EXPERIMENTS_READ,
        PERMISSIONS.EXPERIMENTS_WRITE,
      ]);

      middleware(req, res, next);

      expect(next).toHaveBeenCalledWith(expect.any(ApiError));
      const error = next.mock.calls[0][0];
      expect(error.code).toBe('FORBIDDEN');
    });
  });

  describe('generateToken', () => {
    it('should generate valid JWT token', () => {
      const token = generateToken(mockUsers.admin);

      expect(typeof token).toBe('string');
      expect(token.split('.')).toHaveLength(3); // JWT has 3 parts

      // Verify token can be decoded
      const jwt = require('jsonwebtoken');
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      expect(decoded.sub).toBe(mockUsers.admin.id);
      expect(decoded.email).toBe(mockUsers.admin.email);
      expect(decoded.tenant_id).toBe(mockUsers.admin.tenantId);
    });

    it('should include all required claims in token', () => {
      const token = generateToken(mockUsers.admin);
      const jwt = require('jsonwebtoken');
      const decoded = jwt.verify(token, process.env.JWT_SECRET);

      expect(decoded).toHaveProperty('sub');
      expect(decoded).toHaveProperty('email');
      expect(decoded).toHaveProperty('tenant_id');
      expect(decoded).toHaveProperty('roles');
      expect(decoded).toHaveProperty('permissions');
      expect(decoded).toHaveProperty('iat');
      expect(decoded).toHaveProperty('exp');
      expect(decoded).toHaveProperty('iss');
      expect(decoded).toHaveProperty('aud');
    });
  });

  describe('Permission Constants', () => {
    it('should have all required permission constants', () => {
      expect(PERMISSIONS.EXPERIMENTS_READ).toBe('experiments:read');
      expect(PERMISSIONS.EXPERIMENTS_WRITE).toBe('experiments:write');
      expect(PERMISSIONS.EXPERIMENTS_DELETE).toBe('experiments:delete');
      expect(PERMISSIONS.EXPERIMENTS_CONTROL).toBe('experiments:control');
      expect(PERMISSIONS.ASSIGNMENTS_READ).toBe('assignments:read');
      expect(PERMISSIONS.ASSIGNMENTS_WRITE).toBe('assignments:write');
      expect(PERMISSIONS.EVENTS_READ).toBe('events:read');
      expect(PERMISSIONS.EVENTS_WRITE).toBe('events:write');
      expect(PERMISSIONS.ANALYTICS_READ).toBe('analytics:read');
    });
  });
});

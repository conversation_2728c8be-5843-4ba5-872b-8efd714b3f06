// Validation middleware unit tests
import { validate, experimentSchemas } from '../../../middleware/validation';
import { mockRequestPayloads } from '../../fixtures/mockData';
import { 
  createMockRequest, 
  createMockResponse, 
  createMockNext 
} from '../../helpers/testHelpers';

describe('Validation Middleware', () => {
  let req: any;
  let res: any;
  let next: any;

  beforeEach(() => {
    req = createMockRequest();
    res = createMockResponse();
    next = createMockNext();
  });

  describe('validate function', () => {
    it('should pass validation for valid request', () => {
      req.body = mockRequestPayloads.createExperiment.valid;
      
      const middleware = validate(experimentSchemas.createExperiment);
      middleware(req, res, next);

      expect(next).toHaveBeenCalledWith();
    });

    it('should fail validation for invalid request body', () => {
      req.body = mockRequestPayloads.createExperiment.invalidName;
      
      const middleware = validate(experimentSchemas.createExperiment);
      middleware(req, res, next);

      expect(next).toHaveBeenCalledWith(expect.any(Error));
      const error = next.mock.calls[0][0];
      expect(error.code).toBe('VALIDATION_ERROR');
      expect(error.statusCode).toBe(422);
      expect(error.details.field_errors).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            field: expect.stringContaining('name'),
          }),
        ])
      );
    });

    it('should validate query parameters', () => {
      req.query = { page: 'invalid', limit: 101 }; // Invalid page and limit too high
      
      const middleware = validate({
        query: experimentSchemas.listExperiments.query,
      });
      middleware(req, res, next);

      expect(next).toHaveBeenCalledWith(expect.any(Error));
      const error = next.mock.calls[0][0];
      expect(error.code).toBe('VALIDATION_ERROR');
    });

    it('should validate path parameters', () => {
      req.params = { experimentId: 'invalid-uuid' };
      
      const middleware = validate(experimentSchemas.getExperiment);
      middleware(req, res, next);

      expect(next).toHaveBeenCalledWith(expect.any(Error));
      const error = next.mock.calls[0][0];
      expect(error.code).toBe('VALIDATION_ERROR');
      expect(error.details.field_errors).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            field: expect.stringContaining('experimentId'),
          }),
        ])
      );
    });

    it('should validate multiple fields and return all errors', () => {
      req.body = {
        name: '', // Invalid
        variants: [{ name: 'Single Variant' }], // Invalid - need at least 2
        traffic_allocation: 1.5, // Invalid - max is 1.0
      };
      
      const middleware = validate(experimentSchemas.createExperiment);
      middleware(req, res, next);

      expect(next).toHaveBeenCalledWith(expect.any(Error));
      const error = next.mock.calls[0][0];
      expect(error.details.field_errors.length).toBeGreaterThan(1);
    });
  });

  describe('experiment validation schemas', () => {
    describe('createExperiment', () => {
      it('should validate required fields', () => {
        req.body = {}; // Missing required fields
        
        const middleware = validate(experimentSchemas.createExperiment);
        middleware(req, res, next);

        expect(next).toHaveBeenCalledWith(expect.any(Error));
        const error = next.mock.calls[0][0];
        expect(error.details.field_errors).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              field: expect.stringContaining('name'),
            }),
            expect.objectContaining({
              field: expect.stringContaining('variants'),
            }),
          ])
        );
      });

      it('should validate variant traffic weights sum to 1.0', () => {
        req.body = mockRequestPayloads.createExperiment.invalidTrafficWeights;
        
        const middleware = validate(experimentSchemas.createExperiment);
        middleware(req, res, next);

        expect(next).toHaveBeenCalledWith(expect.any(Error));
        const error = next.mock.calls[0][0];
        expect(error.message).toContain('traffic weights must sum to 1.0');
      });

      it('should validate minimum number of variants', () => {
        req.body = mockRequestPayloads.createExperiment.invalidVariants;
        
        const middleware = validate(experimentSchemas.createExperiment);
        middleware(req, res, next);

        expect(next).toHaveBeenCalledWith(expect.any(Error));
        const error = next.mock.calls[0][0];
        expect(error.details.field_errors).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              field: expect.stringContaining('variants'),
            }),
          ])
        );
      });

      it('should validate maximum number of variants', () => {
        req.body = {
          name: 'Test Experiment',
          variants: Array.from({ length: 11 }, (_, i) => ({
            name: `Variant ${i + 1}`,
            traffic_weight: 1 / 11,
          })),
        };
        
        const middleware = validate(experimentSchemas.createExperiment);
        middleware(req, res, next);

        expect(next).toHaveBeenCalledWith(expect.any(Error));
      });

      it('should validate only one control variant', () => {
        req.body = {
          name: 'Test Experiment',
          variants: [
            {
              name: 'Control 1',
              is_control: true,
              traffic_weight: 0.33,
            },
            {
              name: 'Control 2',
              is_control: true, // Invalid - multiple controls
              traffic_weight: 0.33,
            },
            {
              name: 'Treatment',
              is_control: false,
              traffic_weight: 0.34,
            },
          ],
        };
        
        const middleware = validate(experimentSchemas.createExperiment);
        middleware(req, res, next);

        expect(next).toHaveBeenCalledWith(expect.any(Error));
        const error = next.mock.calls[0][0];
        expect(error.message).toContain('Only one variant can be marked as control');
      });

      it('should validate date constraints', () => {
        req.body = {
          ...mockRequestPayloads.createExperiment.valid,
          start_date: '2024-02-01T00:00:00Z',
          end_date: '2024-01-01T00:00:00Z', // End before start
        };
        
        const middleware = validate(experimentSchemas.createExperiment);
        middleware(req, res, next);

        expect(next).toHaveBeenCalledWith(expect.any(Error));
      });

      it('should validate confidence level range', () => {
        req.body = {
          ...mockRequestPayloads.createExperiment.valid,
          confidence_level: 1.5, // Invalid - max is 0.99
        };
        
        const middleware = validate(experimentSchemas.createExperiment);
        middleware(req, res, next);

        expect(next).toHaveBeenCalledWith(expect.any(Error));
      });

      it('should validate targeting rule operators', () => {
        req.body = {
          ...mockRequestPayloads.createExperiment.valid,
          targeting_rules: [
            {
              name: 'Invalid Rule',
              attribute_name: 'country',
              operator: 'INVALID_OPERATOR',
              value: 'US',
            },
          ],
        };
        
        const middleware = validate(experimentSchemas.createExperiment);
        middleware(req, res, next);

        expect(next).toHaveBeenCalledWith(expect.any(Error));
      });
    });

    describe('updateExperiment', () => {
      it('should allow partial updates', () => {
        req.params = { experimentId: '123e4567-e89b-12d3-a456-************' };
        req.body = { name: 'Updated Name' };
        
        const middleware = validate(experimentSchemas.updateExperiment);
        middleware(req, res, next);

        expect(next).toHaveBeenCalledWith();
      });

      it('should validate UUID parameter', () => {
        req.params = { experimentId: 'invalid-uuid' };
        req.body = { name: 'Updated Name' };
        
        const middleware = validate(experimentSchemas.updateExperiment);
        middleware(req, res, next);

        expect(next).toHaveBeenCalledWith(expect.any(Error));
      });
    });

    describe('listExperiments', () => {
      it('should validate pagination parameters', () => {
        req.query = {
          page: 1,
          limit: 20,
          sort_by: 'created_at',
          sort_order: 'desc',
        };
        
        const middleware = validate(experimentSchemas.listExperiments);
        middleware(req, res, next);

        expect(next).toHaveBeenCalledWith();
      });

      it('should validate sort parameters', () => {
        req.query = {
          sort_by: 'invalid_field',
          sort_order: 'invalid_order',
        };
        
        const middleware = validate(experimentSchemas.listExperiments);
        middleware(req, res, next);

        expect(next).toHaveBeenCalledWith(expect.any(Error));
      });

      it('should validate status filter', () => {
        req.query = {
          status: ['INVALID_STATUS'],
        };
        
        const middleware = validate(experimentSchemas.listExperiments);
        middleware(req, res, next);

        expect(next).toHaveBeenCalledWith(expect.any(Error));
      });
    });
  });

  describe('common schemas', () => {
    it('should validate UUID format', () => {
      const validUUIDs = [
        '123e4567-e89b-12d3-a456-************',
        'f47ac10b-58cc-4372-a567-0e02b2c3d479',
      ];
      
      const invalidUUIDs = [
        'invalid-uuid',
        '123',
        '',
        'not-a-uuid-at-all',
      ];

      validUUIDs.forEach(uuid => {
        req.params = { experimentId: uuid };
        const middleware = validate({ params: experimentSchemas.getExperiment.params });
        middleware(req, res, next);
        expect(next).toHaveBeenCalledWith();
        next.mockClear();
      });

      invalidUUIDs.forEach(uuid => {
        req.params = { experimentId: uuid };
        const middleware = validate({ params: experimentSchemas.getExperiment.params });
        middleware(req, res, next);
        expect(next).toHaveBeenCalledWith(expect.any(Error));
        next.mockClear();
      });
    });

    it('should validate pagination limits', () => {
      // Valid pagination
      req.query = { page: 1, limit: 50 };
      let middleware = validate({ query: experimentSchemas.listExperiments.query });
      middleware(req, res, next);
      expect(next).toHaveBeenCalledWith();
      next.mockClear();

      // Invalid pagination - limit too high
      req.query = { page: 1, limit: 101 };
      middleware = validate({ query: experimentSchemas.listExperiments.query });
      middleware(req, res, next);
      expect(next).toHaveBeenCalledWith(expect.any(Error));
      next.mockClear();

      // Invalid pagination - negative page
      req.query = { page: 0, limit: 20 };
      middleware = validate({ query: experimentSchemas.listExperiments.query });
      middleware(req, res, next);
      expect(next).toHaveBeenCalledWith(expect.any(Error));
    });
  });
});

{"name": "ab-testing-api", "version": "1.0.0", "description": "Node.js Express API for A/B Testing Experiment Management", "main": "dist/server.js", "scripts": {"build": "tsc", "dev": "ts-node-dev --respawn --transpile-only src/server.ts", "start": "node dist/server.js", "test": "jest", "test:unit": "jest --selectProjects unit", "test:integration": "jest --selectProjects integration", "test:watch": "jest --watch", "test:watch:unit": "jest --selectProjects unit --watch", "test:watch:integration": "jest --selectProjects integration --watch", "test:coverage": "jest --coverage", "test:coverage:unit": "jest --selectProjects unit --coverage", "test:coverage:integration": "jest --selectProjects integration --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:migrate:deploy": "prisma migrate deploy", "db:seed": "ts-node prisma/seed.ts", "type-check": "tsc --noEmit"}, "keywords": ["ab-testing", "express", "api", "typescript", "prisma", "experiments"], "author": "Your Name", "license": "MIT", "dependencies": {"@prisma/client": "^5.7.0", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "cors": "^2.8.5", "compression": "^1.7.4", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "winston": "^3.11.0", "express-winston": "^4.2.0", "swagger-ui-express": "^5.0.0", "yamljs": "^0.3.0", "redis": "^4.6.10", "express-async-errors": "^3.1.1", "http-status-codes": "^2.3.0", "joi": "^17.11.0", "uuid": "^9.0.1", "dotenv": "^16.3.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.9.0", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/swagger-ui-express": "^4.1.6", "@types/yamljs": "^0.2.34", "@types/uuid": "^9.0.7", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "prettier": "^3.1.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "typescript": "^5.3.2", "prisma": "^5.7.0"}, "engines": {"node": ">=18.0.0"}}
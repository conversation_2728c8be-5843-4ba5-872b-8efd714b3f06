<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Experiment Data Table - Interactive Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .status-active { @apply bg-green-100 text-green-800; }
        .status-draft { @apply bg-gray-100 text-gray-800; }
        .status-paused { @apply bg-yellow-100 text-yellow-800; }
        .status-completed { @apply bg-blue-100 text-blue-800; }
        .status-archived { @apply bg-gray-100 text-gray-600; }
        .sort-asc::after { content: ' ↑'; }
        .sort-desc::after { content: ' ↓'; }
    </style>
</head>
<body class="bg-gray-50 p-6">
    <div class="max-w-7xl mx-auto space-y-6">
        <!-- Header -->
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Experiment Data Table</h1>
                <p class="text-gray-500">Comprehensive table with sorting, filtering, and actions</p>
            </div>
            <div class="text-sm text-gray-500">
                Tenant: <span class="font-medium text-indigo-600">tenant-1</span>
            </div>
        </div>

        <!-- Search and Filters -->
        <div class="flex flex-col sm:flex-row gap-4">
            <div class="flex-1">
                <div class="relative">
                    <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    <input
                        type="text"
                        id="searchInput"
                        placeholder="Search experiments..."
                        class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                    />
                </div>
            </div>

            <div class="flex items-center space-x-2">
                <button
                    id="filterToggle"
                    class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z"></path>
                    </svg>
                    Filters
                    <span id="filterCount" class="hidden ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800"></span>
                </button>

                <button
                    id="bulkActionsBtn"
                    class="hidden inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                    Bulk Actions (<span id="selectedCount">0</span>)
                    <svg class="ml-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Filter Panel -->
        <div id="filterPanel" class="hidden bg-gray-50 p-4 rounded-lg border">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="checkbox" value="DRAFT" class="status-filter rounded border-gray-300 text-indigo-600 focus:ring-indigo-500" />
                            <span class="ml-2 text-sm text-gray-700">Draft</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" value="ACTIVE" class="status-filter rounded border-gray-300 text-indigo-600 focus:ring-indigo-500" />
                            <span class="ml-2 text-sm text-gray-700">Active</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" value="PAUSED" class="status-filter rounded border-gray-300 text-indigo-600 focus:ring-indigo-500" />
                            <span class="ml-2 text-sm text-gray-700">Paused</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" value="COMPLETED" class="status-filter rounded border-gray-300 text-indigo-600 focus:ring-indigo-500" />
                            <span class="ml-2 text-sm text-gray-700">Completed</span>
                        </label>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
                    <div class="space-y-2">
                        <input
                            type="date"
                            id="startDate"
                            class="block w-full text-sm border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                        />
                        <input
                            type="date"
                            id="endDate"
                            class="block w-full text-sm border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                        />
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Tags</label>
                    <input
                        type="text"
                        id="tagsFilter"
                        placeholder="Enter tags..."
                        class="block w-full text-sm border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                    />
                </div>

                <div class="flex items-end">
                    <button
                        id="clearFilters"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                    >
                        Clear Filters
                    </button>
                </div>
            </div>
        </div>

        <!-- Data Table -->
        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left">
                            <input
                                type="checkbox"
                                id="selectAll"
                                class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                            />
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" data-sort="name">
                            <div class="flex items-center space-x-1">
                                <span>Name</span>
                                <span class="sort-indicator"></span>
                            </div>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" data-sort="status">
                            <div class="flex items-center space-x-1">
                                <span>Status</span>
                                <span class="sort-indicator"></span>
                            </div>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" data-sort="variants">
                            <div class="flex items-center space-x-1">
                                <span>Variants</span>
                                <span class="sort-indicator"></span>
                            </div>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" data-sort="assignments">
                            <div class="flex items-center space-x-1">
                                <span>Assignments</span>
                                <span class="sort-indicator"></span>
                            </div>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" data-sort="events">
                            <div class="flex items-center space-x-1">
                                <span>Events</span>
                                <span class="sort-indicator"></span>
                            </div>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" data-sort="startDate">
                            <div class="flex items-center space-x-1">
                                <span>Start Date</span>
                                <span class="sort-indicator"></span>
                            </div>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" data-sort="createdAt">
                            <div class="flex items-center space-x-1">
                                <span>Created</span>
                                <span class="sort-indicator"></span>
                            </div>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody id="tableBody" class="bg-white divide-y divide-gray-200">
                    <!-- Table rows will be dynamically generated -->
                </tbody>
            </table>

            <!-- Empty State -->
            <div id="emptyState" class="hidden text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No experiments found</h3>
                <p class="mt-1 text-sm text-gray-500" id="emptyStateMessage">
                    Get started by creating your first experiment.
                </p>
            </div>
        </div>

        <!-- Pagination -->
        <div id="pagination" class="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
            <div class="flex flex-1 justify-between sm:hidden">
                <button
                    id="prevPageMobile"
                    class="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    Previous
                </button>
                <button
                    id="nextPageMobile"
                    class="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    Next
                </button>
            </div>
            <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                <div class="flex items-center space-x-2">
                    <p class="text-sm text-gray-700" id="paginationInfo">
                        Showing 1 to 10 of 25 results
                    </p>
                    <select
                        id="pageSize"
                        class="border-gray-300 rounded-md text-sm"
                    >
                        <option value="10">10 per page</option>
                        <option value="20" selected>20 per page</option>
                        <option value="50">50 per page</option>
                    </select>
                </div>
                <div>
                    <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm" id="paginationNav">
                        <!-- Pagination buttons will be generated here -->
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Mock data
        const mockExperiments = [
            {
                id: '1',
                tenantId: 'tenant-1',
                name: 'Homepage Button Color Test',
                description: 'Testing different button colors to improve conversion rates',
                status: 'ACTIVE',
                startDate: '2024-01-15T00:00:00Z',
                createdAt: '2024-01-10T00:00:00Z',
                variants: [
                    { id: 'v1', name: 'Control (Blue)', trafficWeight: 0.5 },
                    { id: 'v2', name: 'Treatment (Green)', trafficWeight: 0.5 }
                ],
                tags: ['homepage', 'ui', 'conversion'],
                _count: { userAssignments: 1250, events: 3420 }
            },
            {
                id: '2',
                tenantId: 'tenant-1',
                name: 'Checkout Flow Optimization',
                description: 'Testing a simplified checkout process',
                status: 'DRAFT',
                createdAt: '2024-01-12T00:00:00Z',
                variants: [
                    { id: 'v3', name: 'Current Flow', trafficWeight: 0.5 },
                    { id: 'v4', name: 'Simplified Flow', trafficWeight: 0.5 }
                ],
                tags: ['checkout', 'ux', 'conversion'],
                _count: { userAssignments: 0, events: 0 }
            },
            {
                id: '3',
                tenantId: 'tenant-1',
                name: 'Email Subject Line Test',
                description: 'Testing different email subject lines for open rates',
                status: 'COMPLETED',
                startDate: '2024-01-01T00:00:00Z',
                createdAt: '2024-01-01T00:00:00Z',
                variants: [
                    { id: 'v5', name: 'Original Subject', trafficWeight: 0.33 },
                    { id: 'v6', name: 'Personalized Subject', trafficWeight: 0.33 },
                    { id: 'v7', name: 'Urgent Subject', trafficWeight: 0.34 }
                ],
                tags: ['email', 'marketing', 'engagement'],
                _count: { userAssignments: 5000, events: 12500 }
            },
            {
                id: '4',
                tenantId: 'tenant-1',
                name: 'Product Page Layout',
                description: 'Testing different product page layouts',
                status: 'PAUSED',
                startDate: '2024-01-08T00:00:00Z',
                createdAt: '2024-01-05T00:00:00Z',
                variants: [
                    { id: 'v8', name: 'Current Layout', trafficWeight: 0.5 },
                    { id: 'v9', name: 'New Layout', trafficWeight: 0.5 }
                ],
                tags: ['product', 'layout', 'ui'],
                _count: { userAssignments: 800, events: 2100 }
            },
            {
                id: '5',
                tenantId: 'tenant-2',
                name: 'Pricing Page Test',
                description: 'Testing different pricing displays',
                status: 'ACTIVE',
                startDate: '2024-01-20T00:00:00Z',
                createdAt: '2024-01-18T00:00:00Z',
                variants: [
                    { id: 'v10', name: 'Monthly Focus', trafficWeight: 0.5 },
                    { id: 'v11', name: 'Annual Focus', trafficWeight: 0.5 }
                ],
                tags: ['pricing', 'conversion', 'revenue'],
                _count: { userAssignments: 600, events: 1800 }
            }
        ];

        // State
        let currentTenant = 'tenant-1';
        let filteredExperiments = [];
        let sortConfig = { key: 'createdAt', direction: 'desc' };
        let filters = { status: [], tags: [], search: '', dateRange: {} };
        let selectedExperiments = new Set();
        let currentPage = 1;
        let pageSize = 20;

        // Status configuration
        const statusConfig = {
            DRAFT: { label: 'Draft', color: 'status-draft', actions: ['start'] },
            ACTIVE: { label: 'Active', color: 'status-active', actions: ['pause', 'complete'] },
            PAUSED: { label: 'Paused', color: 'status-paused', actions: ['resume', 'complete'] },
            COMPLETED: { label: 'Completed', color: 'status-completed', actions: ['archive'] },
            ARCHIVED: { label: 'Archived', color: 'status-archived', actions: [] }
        };

        const actionConfig = {
            start: { label: 'Start', status: 'ACTIVE' },
            pause: { label: 'Pause', status: 'PAUSED' },
            resume: { label: 'Resume', status: 'ACTIVE' },
            complete: { label: 'Complete', status: 'COMPLETED' },
            archive: { label: 'Archive', status: 'ARCHIVED' }
        };

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
            applyFiltersAndSort();
            renderTable();
            updatePagination();
        });

        function setupEventListeners() {
            // Search
            document.getElementById('searchInput').addEventListener('input', function(e) {
                filters.search = e.target.value;
                currentPage = 1;
                applyFiltersAndSort();
                renderTable();
                updatePagination();
            });

            // Filter toggle
            document.getElementById('filterToggle').addEventListener('click', function() {
                const panel = document.getElementById('filterPanel');
                panel.classList.toggle('hidden');
            });

            // Status filters
            document.querySelectorAll('.status-filter').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const status = this.value;
                    if (this.checked) {
                        filters.status.push(status);
                    } else {
                        filters.status = filters.status.filter(s => s !== status);
                    }
                    currentPage = 1;
                    applyFiltersAndSort();
                    renderTable();
                    updatePagination();
                    updateFilterCount();
                });
            });

            // Clear filters
            document.getElementById('clearFilters').addEventListener('click', function() {
                filters = { status: [], tags: [], search: '', dateRange: {} };
                document.getElementById('searchInput').value = '';
                document.querySelectorAll('.status-filter').forEach(cb => cb.checked = false);
                document.getElementById('tagsFilter').value = '';
                currentPage = 1;
                applyFiltersAndSort();
                renderTable();
                updatePagination();
                updateFilterCount();
            });

            // Select all
            document.getElementById('selectAll').addEventListener('change', function() {
                const isChecked = this.checked;
                selectedExperiments.clear();
                if (isChecked) {
                    filteredExperiments.forEach(exp => selectedExperiments.add(exp.id));
                }
                renderTable();
                updateBulkActions();
            });

            // Sort headers
            document.querySelectorAll('[data-sort]').forEach(header => {
                header.addEventListener('click', function() {
                    const sortKey = this.dataset.sort;
                    if (sortConfig.key === sortKey) {
                        sortConfig.direction = sortConfig.direction === 'asc' ? 'desc' : 'asc';
                    } else {
                        sortConfig.key = sortKey;
                        sortConfig.direction = 'asc';
                    }
                    applyFiltersAndSort();
                    renderTable();
                    updateSortIndicators();
                });
            });

            // Page size
            document.getElementById('pageSize').addEventListener('change', function() {
                pageSize = parseInt(this.value);
                currentPage = 1;
                renderTable();
                updatePagination();
            });
        }

        function applyFiltersAndSort() {
            let filtered = mockExperiments;

            // Apply tenant filter
            filtered = filtered.filter(exp => exp.tenantId === currentTenant);

            // Apply search filter
            if (filters.search) {
                const searchLower = filters.search.toLowerCase();
                filtered = filtered.filter(exp =>
                    exp.name.toLowerCase().includes(searchLower) ||
                    exp.description?.toLowerCase().includes(searchLower) ||
                    exp.tags.some(tag => tag.toLowerCase().includes(searchLower))
                );
            }

            // Apply status filter
            if (filters.status.length > 0) {
                filtered = filtered.filter(exp => filters.status.includes(exp.status));
            }

            // Sort experiments
            filtered.sort((a, b) => {
                let aValue, bValue;

                switch (sortConfig.key) {
                    case 'variants':
                        aValue = a.variants.length;
                        bValue = b.variants.length;
                        break;
                    case 'assignments':
                        aValue = a._count?.userAssignments || 0;
                        bValue = b._count?.userAssignments || 0;
                        break;
                    case 'events':
                        aValue = a._count?.events || 0;
                        bValue = b._count?.events || 0;
                        break;
                    default:
                        aValue = a[sortConfig.key];
                        bValue = b[sortConfig.key];
                }

                if (aValue === bValue) return 0;
                const comparison = aValue < bValue ? -1 : 1;
                return sortConfig.direction === 'desc' ? -comparison : comparison;
            });

            filteredExperiments = filtered;
        }

        function renderTable() {
            const tbody = document.getElementById('tableBody');
            const emptyState = document.getElementById('emptyState');

            if (filteredExperiments.length === 0) {
                tbody.innerHTML = '';
                emptyState.classList.remove('hidden');
                document.getElementById('emptyStateMessage').textContent =
                    filters.search || filters.status.length > 0
                        ? 'Try adjusting your search or filter criteria.'
                        : 'Get started by creating your first experiment.';
                return;
            }

            emptyState.classList.add('hidden');

            // Pagination
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const paginatedExperiments = filteredExperiments.slice(startIndex, endIndex);

            tbody.innerHTML = paginatedExperiments.map(experiment => {
                const config = statusConfig[experiment.status];
                const availableActions = config.actions.map(action => actionConfig[action]);

                return `
                    <tr class="hover:bg-gray-50 cursor-pointer" onclick="viewExperiment('${experiment.id}')">
                        <td class="px-6 py-4 whitespace-nowrap" onclick="event.stopPropagation()">
                            <input
                                type="checkbox"
                                ${selectedExperiments.has(experiment.id) ? 'checked' : ''}
                                onchange="toggleExperimentSelection('${experiment.id}', this.checked)"
                                class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                            />
                        </td>

                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex flex-col">
                                <div class="text-sm font-medium text-gray-900 truncate max-w-xs">
                                    ${experiment.name}
                                </div>
                                ${experiment.description ? `
                                    <div class="text-sm text-gray-500 truncate max-w-xs">
                                        ${experiment.description}
                                    </div>
                                ` : ''}
                                ${experiment.tags.length > 0 ? `
                                    <div class="flex flex-wrap gap-1 mt-1">
                                        ${experiment.tags.slice(0, 3).map(tag => `
                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                                                ${tag}
                                            </span>
                                        `).join('')}
                                        ${experiment.tags.length > 3 ? `
                                            <span class="text-xs text-gray-500">
                                                +${experiment.tags.length - 3} more
                                            </span>
                                        ` : ''}
                                    </div>
                                ` : ''}
                            </div>
                        </td>

                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}">
                                ${config.label}
                            </span>
                        </td>

                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <div class="flex items-center">
                                <svg class="h-4 w-4 text-gray-400 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                                ${experiment.variants.length}
                            </div>
                        </td>

                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <div class="flex items-center">
                                <svg class="h-4 w-4 text-gray-400 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                                ${(experiment._count?.userAssignments || 0).toLocaleString()}
                            </div>
                        </td>

                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <div class="flex items-center">
                                <svg class="h-4 w-4 text-gray-400 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                                ${(experiment._count?.events || 0).toLocaleString()}
                            </div>
                        </td>

                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            ${experiment.startDate ? `
                                <div class="flex items-center">
                                    <svg class="h-4 w-4 text-gray-400 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    <div>
                                        <div>${formatDate(experiment.startDate)}</div>
                                        <div class="text-xs text-gray-400">
                                            ${formatRelativeDate(experiment.startDate)}
                                        </div>
                                    </div>
                                </div>
                            ` : `
                                <span class="text-gray-400">Not started</span>
                            `}
                        </td>

                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <div class="flex items-center">
                                <svg class="h-4 w-4 text-gray-400 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                <div>
                                    <div>${formatDate(experiment.createdAt)}</div>
                                    <div class="text-xs text-gray-400">
                                        ${formatRelativeDate(experiment.createdAt)}
                                    </div>
                                </div>
                            </div>
                        </td>

                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium" onclick="event.stopPropagation()">
                            <div class="flex items-center space-x-2">
                                ${availableActions.slice(0, 1).map(action => `
                                    <button
                                        onclick="changeStatus('${experiment.id}', '${action.status}')"
                                        class="inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                                    >
                                        ${action.label}
                                    </button>
                                `).join('')}

                                <div class="relative">
                                    <button
                                        onclick="toggleActionMenu('${experiment.id}')"
                                        class="inline-flex items-center p-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                                    >
                                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');

            updateBulkActions();
        }

        function updateSortIndicators() {
            document.querySelectorAll('.sort-indicator').forEach(indicator => {
                indicator.className = 'sort-indicator';
            });

            const activeHeader = document.querySelector(`[data-sort="${sortConfig.key}"] .sort-indicator`);
            if (activeHeader) {
                activeHeader.className = `sort-indicator sort-${sortConfig.direction}`;
            }
        }

        function updateFilterCount() {
            const count = filters.status.length + (filters.search ? 1 : 0);
            const countElement = document.getElementById('filterCount');

            if (count > 0) {
                countElement.textContent = count;
                countElement.classList.remove('hidden');
            } else {
                countElement.classList.add('hidden');
            }
        }

        function updateBulkActions() {
            const bulkBtn = document.getElementById('bulkActionsBtn');
            const selectedCount = document.getElementById('selectedCount');

            selectedCount.textContent = selectedExperiments.size;

            if (selectedExperiments.size > 0) {
                bulkBtn.classList.remove('hidden');
            } else {
                bulkBtn.classList.add('hidden');
            }
        }

        function updatePagination() {
            const totalPages = Math.ceil(filteredExperiments.length / pageSize);
            const startIndex = (currentPage - 1) * pageSize + 1;
            const endIndex = Math.min(currentPage * pageSize, filteredExperiments.length);

            document.getElementById('paginationInfo').textContent =
                `Showing ${startIndex} to ${endIndex} of ${filteredExperiments.length} results`;

            // Update pagination buttons
            const nav = document.getElementById('paginationNav');
            nav.innerHTML = `
                <button
                    onclick="changePage(${currentPage - 1})"
                    ${currentPage === 1 ? 'disabled' : ''}
                    class="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    Previous
                </button>

                ${[...Array(Math.min(5, totalPages))].map((_, i) => {
                    const pageNum = i + 1;
                    return `
                        <button
                            onclick="changePage(${pageNum})"
                            class="relative inline-flex items-center px-4 py-2 text-sm font-semibold ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 ${
                                currentPage === pageNum
                                    ? 'bg-indigo-600 text-white ring-indigo-600'
                                    : 'text-gray-900'
                            }"
                        >
                            ${pageNum}
                        </button>
                    `;
                }).join('')}

                <button
                    onclick="changePage(${currentPage + 1})"
                    ${currentPage === totalPages ? 'disabled' : ''}
                    class="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    Next
                </button>
            `;
        }

        // Utility functions
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }

        function formatRelativeDate(dateString) {
            const date = new Date(dateString);
            const now = new Date();
            const diffTime = Math.abs(now - date);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            if (diffDays === 1) return '1 day ago';
            if (diffDays < 30) return `${diffDays} days ago`;
            if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
            return `${Math.floor(diffDays / 365)} years ago`;
        }

        // Event handlers
        function toggleExperimentSelection(id, selected) {
            if (selected) {
                selectedExperiments.add(id);
            } else {
                selectedExperiments.delete(id);
            }
            updateBulkActions();
        }

        function changePage(page) {
            const totalPages = Math.ceil(filteredExperiments.length / pageSize);
            if (page >= 1 && page <= totalPages) {
                currentPage = page;
                renderTable();
                updatePagination();
            }
        }

        function viewExperiment(id) {
            console.log('View experiment:', id);
            alert(`Viewing experiment ${id}`);
        }

        function changeStatus(id, newStatus) {
            console.log('Change status:', id, newStatus);
            alert(`Changing experiment ${id} status to ${newStatus}`);

            // Update mock data
            const experiment = mockExperiments.find(exp => exp.id === id);
            if (experiment) {
                experiment.status = newStatus;
                applyFiltersAndSort();
                renderTable();
            }
        }

        function toggleActionMenu(id) {
            console.log('Toggle action menu for:', id);
        }

        // Initialize sort indicators
        updateSortIndicators();
    </script>
</body>
</html>
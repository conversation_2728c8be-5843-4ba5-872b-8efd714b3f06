# A/B Testing Platform Authentication System

A comprehensive, secure, and extensible JWT-based authentication system designed specifically for multi-tenant A/B testing platforms.

## Features

- **🔐 JWT Authentication**: Secure access and refresh token implementation
- **🏢 Multi-Tenant Support**: Tenant-aware authentication and authorization
- **🛡️ Security First**: Password hashing, rate limiting, account lockout, and audit logging
- **🔄 Token Management**: Automatic token refresh and session management
- **📧 Email Flows**: Password reset and email verification
- **🎯 Role-Based Access**: Hierarchical role system with granular permissions
- **📊 Security Monitoring**: Comprehensive security event logging and reporting
- **⚡ Rate Limiting**: Configurable rate limits for all endpoints
- **🔒 Session Management**: Multi-device session tracking and management
- **🚨 Suspicious Activity Detection**: Automated threat detection

## Quick Start

### Installation

```bash
npm install @yourorg/ab-testing-auth
```

### Basic Setup

```typescript
import { createAuthApp } from '@yourorg/ab-testing-auth';

const { app, database, redis } = await createAuthApp();

app.listen(3001, () => {
  console.log('🚀 Auth service running on port 3001');
});
```

### Environment Variables

```bash
# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=ab_testing_auth
DB_USER=postgres
DB_PASSWORD=password

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT Secrets (CHANGE IN PRODUCTION!)
JWT_ACCESS_SECRET=your-super-secret-access-key
JWT_REFRESH_SECRET=your-super-secret-refresh-key

# Email Service
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Security
NODE_ENV=development
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001
```

## API Endpoints

### Authentication

#### Register User
```http
POST /api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePass123",
  "firstName": "John",
  "lastName": "Doe",
  "tenantId": "tenant_123"
}
```

#### Login
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePass123",
  "tenantId": "tenant_123",
  "rememberMe": false
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_123",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "role": "user",
      "tenantId": "tenant_123"
    },
    "tokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIs...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
      "expiresIn": 900,
      "tokenType": "Bearer"
    },
    "permissions": ["experiments:read", "experiments:write"]
  }
}
```

#### Refresh Token
```http
POST /api/auth/refresh
Content-Type: application/json

{
  "refreshToken": "eyJhbGciOiJIUzI1NiIs..."
}
```

#### Logout
```http
POST /api/auth/logout
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
```

### Password Management

#### Request Password Reset
```http
POST /api/auth/password-reset/request
Content-Type: application/json

{
  "email": "<EMAIL>",
  "tenantId": "tenant_123"
}
```

#### Confirm Password Reset
```http
POST /api/auth/password-reset/confirm
Content-Type: application/json

{
  "token": "reset_token_from_email",
  "newPassword": "NewSecurePass123"
}
```

#### Change Password
```http
POST /api/auth/change-password
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
Content-Type: application/json

{
  "currentPassword": "OldPassword123",
  "newPassword": "NewSecurePass123"
}
```

### User Profile

#### Get Profile
```http
GET /api/auth/profile
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
```

#### Get Sessions
```http
GET /api/auth/sessions
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
```

#### Revoke Session
```http
DELETE /api/auth/sessions/:sessionId
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
```

## Middleware Usage

### Basic Authentication
```typescript
import { AuthMiddleware } from '@yourorg/ab-testing-auth';

const authMiddleware = new AuthMiddleware(authService, logger);

// Require authentication
app.get('/api/experiments', 
  authMiddleware.authenticate(),
  (req, res) => {
    const user = req.auth?.user;
    // Handle request
  }
);
```

### Role-Based Access
```typescript
// Require admin role
app.get('/api/admin/users',
  authMiddleware.requireAdmin(),
  (req, res) => {
    // Only admins can access
  }
);

// Require specific permissions
app.post('/api/experiments',
  authMiddleware.requirePermissions(['experiments:create']),
  (req, res) => {
    // Only users with experiment creation permission
  }
);
```

### Tenant Isolation
```typescript
// Ensure user can only access their tenant's data
app.get('/api/tenants/:tenantId/experiments',
  authMiddleware.requireSameTenant(),
  (req, res) => {
    // User can only access their own tenant's experiments
  }
);
```

## Security Features

### Password Security
- **Minimum Requirements**: 8+ characters, uppercase, lowercase, numbers
- **Bcrypt Hashing**: 12 rounds for secure password storage
- **Password History**: Prevents reuse of last 5 passwords
- **Password Expiry**: Configurable password age limits

### Account Protection
- **Rate Limiting**: Configurable limits on login attempts
- **Account Lockout**: Temporary lockout after failed attempts
- **Session Management**: Multi-device session tracking
- **Suspicious Activity Detection**: Automated threat detection

### Token Security
- **Short-lived Access Tokens**: 15-minute default expiry
- **Secure Refresh Tokens**: 7-day default expiry with rotation
- **Token Blacklisting**: Immediate token invalidation on logout
- **Session Binding**: Tokens tied to specific sessions

## Configuration

### Authentication Config
```typescript
const config: AuthConfig = {
  jwt: {
    accessTokenSecret: 'your-secret-key',
    refreshTokenSecret: 'your-refresh-secret',
    accessTokenExpiry: '15m',
    refreshTokenExpiry: '7d',
    issuer: 'ab-testing-platform',
    audience: 'ab-testing-users'
  },
  password: {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSymbols: false,
    maxAge: 90, // days
    preventReuse: 5
  },
  security: {
    maxLoginAttempts: 5,
    lockoutDuration: 15, // minutes
    sessionTimeout: 60, // minutes
    requireEmailVerification: true,
    allowMultipleSessions: true,
    passwordResetExpiry: 60, // minutes
    emailVerificationExpiry: 1440 // minutes (24 hours)
  },
  rateLimit: {
    login: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxAttempts: 5
    },
    passwordReset: {
      windowMs: 60 * 60 * 1000, // 1 hour
      maxAttempts: 3
    }
  }
};
```

## User Roles & Permissions

### Role Hierarchy
1. **VIEWER**: Read-only access to assigned experiments
2. **USER**: Can create and manage own experiments
3. **TENANT_ADMIN**: Full access within tenant
4. **SUPER_ADMIN**: Cross-tenant administrative access

### Permission Examples
- `experiments:read` - View experiments
- `experiments:write` - Create/edit experiments
- `experiments:delete` - Delete experiments
- `users:manage` - Manage tenant users
- `analytics:view` - View analytics data

## Database Schema

### Required Tables
```sql
-- Users table
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  first_name VARCHAR(255) NOT NULL,
  last_name VARCHAR(255) NOT NULL,
  role VARCHAR(50) NOT NULL,
  tenant_id VARCHAR(255) NOT NULL,
  is_active BOOLEAN DEFAULT true,
  email_verified BOOLEAN DEFAULT false,
  last_login_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Sessions table
CREATE TABLE auth_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id),
  refresh_token_id VARCHAR(255),
  device_info JSONB,
  ip_address INET,
  user_agent TEXT,
  is_active BOOLEAN DEFAULT true,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  last_used_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Security events table
CREATE TABLE security_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  tenant_id VARCHAR(255),
  event_type VARCHAR(100) NOT NULL,
  description TEXT NOT NULL,
  ip_address INET,
  user_agent TEXT,
  metadata JSONB DEFAULT '{}',
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

## Error Handling

### Error Codes
- `INVALID_CREDENTIALS` - Wrong email/password
- `ACCOUNT_LOCKED` - Account temporarily locked
- `EMAIL_NOT_VERIFIED` - Email verification required
- `TOKEN_EXPIRED` - Token has expired
- `TOKEN_INVALID` - Invalid or malformed token
- `INSUFFICIENT_PERMISSIONS` - Missing required permissions
- `RATE_LIMIT_EXCEEDED` - Too many requests
- `WEAK_PASSWORD` - Password doesn't meet requirements

### Error Response Format
```json
{
  "success": false,
  "error": "Invalid email or password",
  "code": "INVALID_CREDENTIALS",
  "details": {
    "field": "password",
    "reason": "incorrect"
  }
}
```

## Security Best Practices

### Production Deployment
1. **Use Strong Secrets**: Generate cryptographically secure JWT secrets
2. **Enable HTTPS**: Always use TLS in production
3. **Configure CORS**: Restrict origins to your domains
4. **Rate Limiting**: Implement aggressive rate limiting
5. **Monitoring**: Set up security event monitoring
6. **Backup**: Regular database backups
7. **Updates**: Keep dependencies updated

### Security Headers
```typescript
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
    },
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));
```

## Examples

See the `examples/` directory for complete working examples:

- `basicAuthExample.ts` - Complete authentication server setup
- `middlewareExamples.ts` - Various middleware usage patterns
- `securityExamples.ts` - Security monitoring and reporting

## License

MIT

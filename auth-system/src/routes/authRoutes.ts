import { Router } from 'express';
import { AuthController } from '../controllers/AuthController';
import { AuthMiddleware } from '../middleware/AuthMiddleware';
import { RateLimitMiddleware } from '../middleware/RateLimitMiddleware';
import { ValidationMiddleware } from '../middleware/ValidationMiddleware';
import { 
  loginSchema, 
  registerSchema, 
  refreshTokenSchema, 
  passwordResetRequestSchema,
  passwordResetConfirmSchema,
  changePasswordSchema,
  emailVerificationSchema
} from '../validation/authSchemas';

export function createAuthRoutes(
  authController: AuthController,
  authMiddleware: AuthMiddleware,
  rateLimitMiddleware: RateLimitMiddleware,
  validationMiddleware: ValidationMiddleware
): Router {
  const router = Router();

  // Public routes (no authentication required)
  
  /**
   * @route POST /auth/login
   * @desc User login
   * @access Public
   */
  router.post('/login',
    rateLimitMiddleware.loginLimit(),
    validationMiddleware.validate(loginSchema),
    authController.login.bind(authController)
  );

  /**
   * @route POST /auth/register
   * @desc User registration
   * @access Public
   */
  router.post('/register',
    rateLimitMiddleware.registrationLimit(),
    validationMiddleware.validate(registerSchema),
    authController.register.bind(authController)
  );

  /**
   * @route POST /auth/refresh
   * @desc Refresh access token
   * @access Public
   */
  router.post('/refresh',
    rateLimitMiddleware.tokenRefreshLimit(),
    validationMiddleware.validate(refreshTokenSchema),
    authController.refreshToken.bind(authController)
  );

  /**
   * @route POST /auth/password-reset/request
   * @desc Request password reset
   * @access Public
   */
  router.post('/password-reset/request',
    rateLimitMiddleware.passwordResetLimit(),
    validationMiddleware.validate(passwordResetRequestSchema),
    authController.requestPasswordReset.bind(authController)
  );

  /**
   * @route POST /auth/password-reset/confirm
   * @desc Confirm password reset with token
   * @access Public
   */
  router.post('/password-reset/confirm',
    rateLimitMiddleware.passwordResetLimit(),
    validationMiddleware.validate(passwordResetConfirmSchema),
    authController.confirmPasswordReset.bind(authController)
  );

  /**
   * @route POST /auth/verify-email
   * @desc Verify email address
   * @access Public
   */
  router.post('/verify-email',
    rateLimitMiddleware.emailVerificationLimit(),
    validationMiddleware.validate(emailVerificationSchema),
    authController.verifyEmail.bind(authController)
  );

  /**
   * @route POST /auth/resend-verification
   * @desc Resend email verification
   * @access Public
   */
  router.post('/resend-verification',
    rateLimitMiddleware.emailVerificationLimit(),
    validationMiddleware.validate(passwordResetRequestSchema), // Same schema as password reset request
    authController.resendEmailVerification.bind(authController)
  );

  // Protected routes (authentication required)

  /**
   * @route POST /auth/logout
   * @desc User logout
   * @access Private
   */
  router.post('/logout',
    authMiddleware.authenticate(),
    authController.logout.bind(authController)
  );

  /**
   * @route POST /auth/change-password
   * @desc Change user password
   * @access Private
   */
  router.post('/change-password',
    authMiddleware.authenticate(),
    validationMiddleware.validate(changePasswordSchema),
    authController.changePassword.bind(authController)
  );

  /**
   * @route GET /auth/profile
   * @desc Get current user profile
   * @access Private
   */
  router.get('/profile',
    authMiddleware.authenticate(),
    authController.getProfile.bind(authController)
  );

  /**
   * @route GET /auth/sessions
   * @desc Get user sessions
   * @access Private
   */
  router.get('/sessions',
    authMiddleware.authenticate(),
    authController.getSessions.bind(authController)
  );

  /**
   * @route DELETE /auth/sessions/:sessionId
   * @desc Revoke specific session
   * @access Private
   */
  router.delete('/sessions/:sessionId',
    authMiddleware.authenticate(),
    authController.revokeSession.bind(authController)
  );

  /**
   * @route POST /auth/logout-all
   * @desc Logout from all sessions
   * @access Private
   */
  router.post('/logout-all',
    authMiddleware.authenticate(),
    async (req, res) => {
      try {
        // Implementation would revoke all user sessions
        res.status(200).json({
          success: true,
          message: 'Logged out from all sessions'
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: 'Failed to logout from all sessions'
        });
      }
    }
  );

  // Health check endpoint
  /**
   * @route GET /auth/health
   * @desc Health check for auth service
   * @access Public
   */
  router.get('/health', (req, res) => {
    res.status(200).json({
      success: true,
      message: 'Auth service is healthy',
      timestamp: new Date().toISOString()
    });
  });

  return router;
}

/**
 * Create auth routes with default middleware
 */
export function createDefaultAuthRoutes(
  authController: AuthController,
  authMiddleware: AuthMiddleware
): Router {
  const router = Router();

  // Basic routes without advanced middleware
  router.post('/login', authController.login.bind(authController));
  router.post('/register', authController.register.bind(authController));
  router.post('/refresh', authController.refreshToken.bind(authController));
  router.post('/password-reset/request', authController.requestPasswordReset.bind(authController));
  router.post('/password-reset/confirm', authController.confirmPasswordReset.bind(authController));
  
  // Protected routes
  router.post('/logout', authMiddleware.authenticate(), authController.logout.bind(authController));
  router.post('/change-password', authMiddleware.authenticate(), authController.changePassword.bind(authController));
  router.get('/profile', authMiddleware.authenticate(), authController.getProfile.bind(authController));

  return router;
}

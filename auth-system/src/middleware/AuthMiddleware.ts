import { Request, Response, NextFunction } from 'express';
import {
  AuthContext,
  AuthMiddlewareOptions,
  UserRole,
  AuthError,
  AuthErrorCode
} from '../types/auth';
import { AuthService } from '../services/AuthService';
import { Logger } from '../utils/Logger';

// Extend Express Request to include auth context
declare global {
  namespace Express {
    interface Request {
      auth?: AuthContext;
    }
  }
}

export class AuthMiddleware {
  private authService: AuthService;
  private logger: Logger;

  constructor(authService: AuthService, logger: Logger) {
    this.authService = authService;
    this.logger = logger;
  }

  /**
   * Main authentication middleware
   */
  authenticate(options: AuthMiddlewareOptions = {}) {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        const {
          requireAuth = true,
          requiredPermissions = [],
          requiredRole,
          allowSameUser = false,
          allowSameTenant = false
        } = options;

        // Extract token from Authorization header
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          if (requireAuth) {
            return this.sendAuthError(res, AuthErrorCode.TOKEN_INVALID, 'Missing or invalid authorization header');
          }
          return next();
        }

        const token = authHeader.substring(7); // Remove 'Bearer ' prefix

        try {
          // Verify token and get user context
          const authContext = await this.authService.verifyToken(token);

          // Check if user is active
          if (!authContext.user.isActive) {
            return this.sendAuthError(res, AuthErrorCode.ACCOUNT_LOCKED, 'Account is inactive');
          }

          // Check role requirements
          if (requiredRole && !this.hasRequiredRole(authContext.user.role, requiredRole)) {
            return this.sendAuthError(res, AuthErrorCode.INSUFFICIENT_PERMISSIONS, 'Insufficient role permissions');
          }

          // Check permission requirements
          if (requiredPermissions.length > 0 && !this.hasRequiredPermissions(authContext.permissions, requiredPermissions)) {
            return this.sendAuthError(res, AuthErrorCode.INSUFFICIENT_PERMISSIONS, 'Insufficient permissions');
          }

          // Check same user access
          if (allowSameUser && req.params.userId && req.params.userId !== authContext.user.id) {
            if (!this.hasRequiredRole(authContext.user.role, UserRole.TENANT_ADMIN)) {
              return this.sendAuthError(res, AuthErrorCode.INSUFFICIENT_PERMISSIONS, 'Can only access own resources');
            }
          }

          // Check same tenant access
          if (allowSameTenant && req.params.tenantId && req.params.tenantId !== authContext.user.tenantId) {
            if (!this.hasRequiredRole(authContext.user.role, UserRole.SUPER_ADMIN)) {
              return this.sendAuthError(res, AuthErrorCode.INSUFFICIENT_PERMISSIONS, 'Can only access same tenant resources');
            }
          }

          // Add auth context to request
          req.auth = authContext;

          next();

        } catch (error) {
          if (error instanceof AuthError) {
            return this.sendAuthError(res, error.code as AuthErrorCode, error.message);
          }
          
          this.logger.error('Token verification failed', { error });
          return this.sendAuthError(res, AuthErrorCode.TOKEN_INVALID, 'Token verification failed');
        }

      } catch (error) {
        this.logger.error('Authentication middleware error', { error });
        return res.status(500).json({
          error: 'Internal server error',
          code: 'INTERNAL_ERROR'
        });
      }
    };
  }

  /**
   * Middleware for optional authentication
   */
  optionalAuth() {
    return this.authenticate({ requireAuth: false });
  }

  /**
   * Middleware for admin-only routes
   */
  requireAdmin() {
    return this.authenticate({
      requireAuth: true,
      requiredRole: UserRole.TENANT_ADMIN
    });
  }

  /**
   * Middleware for super admin-only routes
   */
  requireSuperAdmin() {
    return this.authenticate({
      requireAuth: true,
      requiredRole: UserRole.SUPER_ADMIN
    });
  }

  /**
   * Middleware for specific permissions
   */
  requirePermissions(permissions: string[]) {
    return this.authenticate({
      requireAuth: true,
      requiredPermissions: permissions
    });
  }

  /**
   * Middleware for same user access
   */
  requireSameUser() {
    return this.authenticate({
      requireAuth: true,
      allowSameUser: true
    });
  }

  /**
   * Middleware for same tenant access
   */
  requireSameTenant() {
    return this.authenticate({
      requireAuth: true,
      allowSameTenant: true
    });
  }

  /**
   * Check if user has required role
   */
  private hasRequiredRole(userRole: UserRole, requiredRole: UserRole): boolean {
    const roleHierarchy = {
      [UserRole.VIEWER]: 0,
      [UserRole.USER]: 1,
      [UserRole.TENANT_ADMIN]: 2,
      [UserRole.SUPER_ADMIN]: 3
    };

    return roleHierarchy[userRole] >= roleHierarchy[requiredRole];
  }

  /**
   * Check if user has required permissions
   */
  private hasRequiredPermissions(userPermissions: string[], requiredPermissions: string[]): boolean {
    return requiredPermissions.every(permission => userPermissions.includes(permission));
  }

  /**
   * Send authentication error response
   */
  private sendAuthError(res: Response, code: AuthErrorCode, message: string): void {
    const statusCode = this.getStatusCodeForError(code);
    res.status(statusCode).json({
      error: message,
      code
    });
  }

  /**
   * Get HTTP status code for auth error
   */
  private getStatusCodeForError(code: AuthErrorCode): number {
    switch (code) {
      case AuthErrorCode.TOKEN_INVALID:
      case AuthErrorCode.TOKEN_EXPIRED:
        return 401;
      case AuthErrorCode.INSUFFICIENT_PERMISSIONS:
        return 403;
      case AuthErrorCode.USER_NOT_FOUND:
        return 404;
      case AuthErrorCode.ACCOUNT_LOCKED:
        return 423;
      case AuthErrorCode.RATE_LIMIT_EXCEEDED:
        return 429;
      default:
        return 401;
    }
  }
}

/**
 * Helper function to create auth middleware instance
 */
export function createAuthMiddleware(authService: AuthService, logger: Logger): AuthMiddleware {
  return new AuthMiddleware(authService, logger);
}

/**
 * Utility function to get current user from request
 */
export function getCurrentUser(req: Request) {
  return req.auth?.user;
}

/**
 * Utility function to get current user permissions from request
 */
export function getCurrentUserPermissions(req: Request) {
  return req.auth?.permissions || [];
}

/**
 * Utility function to check if current user has permission
 */
export function hasPermission(req: Request, permission: string): boolean {
  const permissions = getCurrentUserPermissions(req);
  return permissions.includes(permission);
}

/**
 * Utility function to check if current user has role
 */
export function hasRole(req: Request, role: UserRole): boolean {
  const user = getCurrentUser(req);
  if (!user) return false;

  const roleHierarchy = {
    [UserRole.VIEWER]: 0,
    [UserRole.USER]: 1,
    [UserRole.TENANT_ADMIN]: 2,
    [UserRole.SUPER_ADMIN]: 3
  };

  return roleHierarchy[user.role] >= roleHierarchy[role];
}

/**
 * Utility function to check if current user is same as target user
 */
export function isSameUser(req: Request, targetUserId: string): boolean {
  const user = getCurrentUser(req);
  return user?.id === targetUserId;
}

/**
 * Utility function to check if current user is in same tenant
 */
export function isSameTenant(req: Request, targetTenantId: string): boolean {
  const user = getCurrentUser(req);
  return user?.tenantId === targetTenantId;
}

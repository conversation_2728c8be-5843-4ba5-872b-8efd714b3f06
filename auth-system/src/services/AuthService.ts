import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { v4 as uuidv4 } from 'uuid';
import {
  User,
  AuthTokens,
  TokenPayload,
  RefreshTokenPayload,
  LoginRequest,
  LoginResponse,
  RefreshTokenRequest,
  PasswordResetRequest,
  PasswordResetConfirm,
  ChangePasswordRequest,
  RegisterRequest,
  AuthConfig,
  AuthError,
  AuthErrorCode,
  AuthSession,
  RefreshToken,
  DeviceInfo,
  SecurityEventType
} from '../types/auth';
import { UserRepository } from '../repositories/UserRepository';
import { SessionRepository } from '../repositories/SessionRepository';
import { TokenRepository } from '../repositories/TokenRepository';
import { PermissionService } from './PermissionService';
import { EmailService } from './EmailService';
import { SecurityService } from './SecurityService';
import { RateLimitService } from './RateLimitService';
import { Logger } from '../utils/Logger';

export class AuthService {
  private userRepository: UserRepository;
  private sessionRepository: SessionRepository;
  private tokenRepository: TokenRepository;
  private permissionService: PermissionService;
  private emailService: EmailService;
  private securityService: SecurityService;
  private rateLimitService: RateLimitService;
  private config: AuthConfig;
  private logger: Logger;

  constructor(
    userRepository: UserRepository,
    sessionRepository: SessionRepository,
    tokenRepository: TokenRepository,
    permissionService: PermissionService,
    emailService: EmailService,
    securityService: SecurityService,
    rateLimitService: RateLimitService,
    config: AuthConfig,
    logger: Logger
  ) {
    this.userRepository = userRepository;
    this.sessionRepository = sessionRepository;
    this.tokenRepository = tokenRepository;
    this.permissionService = permissionService;
    this.emailService = emailService;
    this.securityService = securityService;
    this.rateLimitService = rateLimitService;
    this.config = config;
    this.logger = logger;
  }

  /**
   * Authenticate user and return tokens
   */
  async login(request: LoginRequest, deviceInfo: DeviceInfo, ipAddress: string): Promise<LoginResponse> {
    const { email, password, tenantId, rememberMe = false } = request;

    // Rate limiting
    await this.rateLimitService.checkLimit('login', ipAddress);

    try {
      // Find user
      const user = await this.userRepository.findByEmail(email, tenantId);
      if (!user) {
        await this.securityService.logSecurityEvent({
          eventType: SecurityEventType.LOGIN_FAILURE,
          description: 'User not found',
          ipAddress,
          userAgent: deviceInfo.browser || 'unknown',
          metadata: { email, tenantId }
        });
        throw this.createAuthError(AuthErrorCode.INVALID_CREDENTIALS, 'Invalid email or password');
      }

      // Check if account is locked
      if (!user.isActive) {
        await this.securityService.logSecurityEvent({
          userId: user.id,
          tenantId: user.tenantId,
          eventType: SecurityEventType.LOGIN_FAILURE,
          description: 'Account is locked',
          ipAddress,
          userAgent: deviceInfo.browser || 'unknown'
        });
        throw this.createAuthError(AuthErrorCode.ACCOUNT_LOCKED, 'Account is locked');
      }

      // Check email verification
      if (this.config.security.requireEmailVerification && !user.emailVerified) {
        throw this.createAuthError(AuthErrorCode.EMAIL_NOT_VERIFIED, 'Email not verified');
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(password, user.password!);
      if (!isPasswordValid) {
        await this.securityService.recordFailedLogin(user.id, ipAddress);
        await this.securityService.logSecurityEvent({
          userId: user.id,
          tenantId: user.tenantId,
          eventType: SecurityEventType.LOGIN_FAILURE,
          description: 'Invalid password',
          ipAddress,
          userAgent: deviceInfo.browser || 'unknown'
        });
        throw this.createAuthError(AuthErrorCode.INVALID_CREDENTIALS, 'Invalid email or password');
      }

      // Check if user is locked due to failed attempts
      const isLocked = await this.securityService.isAccountLocked(user.id);
      if (isLocked) {
        throw this.createAuthError(AuthErrorCode.ACCOUNT_LOCKED, 'Account temporarily locked due to failed login attempts');
      }

      // Clear failed login attempts
      await this.securityService.clearFailedLogins(user.id);

      // Get user permissions
      const permissions = await this.permissionService.getUserPermissions(user.id, user.tenantId);

      // Create session
      const session = await this.createSession(user, deviceInfo, ipAddress, rememberMe);

      // Generate tokens
      const tokens = await this.generateTokens(user, permissions, session.id);

      // Update last login
      await this.userRepository.updateLastLogin(user.id);

      // Log successful login
      await this.securityService.logSecurityEvent({
        userId: user.id,
        tenantId: user.tenantId,
        eventType: SecurityEventType.LOGIN_SUCCESS,
        description: 'User logged in successfully',
        ipAddress,
        userAgent: deviceInfo.browser || 'unknown',
        metadata: { sessionId: session.id }
      });

      this.logger.info('User logged in successfully', {
        userId: user.id,
        email: user.email,
        tenantId: user.tenantId,
        sessionId: session.id
      });

      // Remove password from response
      const { password: _, ...userWithoutPassword } = user;

      return {
        user: userWithoutPassword,
        tokens,
        permissions
      };

    } catch (error) {
      if (error instanceof AuthError) {
        throw error;
      }
      this.logger.error('Login failed', { error, email, tenantId });
      throw this.createAuthError(AuthErrorCode.INVALID_CREDENTIALS, 'Login failed');
    }
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshToken(request: RefreshTokenRequest, ipAddress: string): Promise<AuthTokens> {
    const { refreshToken } = request;

    try {
      // Verify refresh token
      const payload = jwt.verify(refreshToken, this.config.jwt.refreshTokenSecret) as RefreshTokenPayload;

      // Find refresh token in database
      const storedToken = await this.tokenRepository.findRefreshToken(payload.tokenId);
      if (!storedToken || !storedToken.isActive) {
        throw this.createAuthError(AuthErrorCode.TOKEN_INVALID, 'Invalid refresh token');
      }

      // Check if token is expired
      if (storedToken.expiresAt < new Date()) {
        await this.tokenRepository.deactivateRefreshToken(storedToken.id);
        throw this.createAuthError(AuthErrorCode.TOKEN_EXPIRED, 'Refresh token expired');
      }

      // Get user and session
      const user = await this.userRepository.findById(payload.sub);
      if (!user || !user.isActive) {
        throw this.createAuthError(AuthErrorCode.USER_NOT_FOUND, 'User not found or inactive');
      }

      const session = await this.sessionRepository.findById(storedToken.sessionId);
      if (!session || !session.isActive) {
        throw this.createAuthError(AuthErrorCode.TOKEN_INVALID, 'Session not found or inactive');
      }

      // Get user permissions
      const permissions = await this.permissionService.getUserPermissions(user.id, user.tenantId);

      // Generate new tokens
      const tokens = await this.generateTokens(user, permissions, session.id);

      // Deactivate old refresh token
      await this.tokenRepository.deactivateRefreshToken(storedToken.id);

      // Update session last used
      await this.sessionRepository.updateLastUsed(session.id);

      // Log token refresh
      await this.securityService.logSecurityEvent({
        userId: user.id,
        tenantId: user.tenantId,
        eventType: SecurityEventType.TOKEN_REFRESH,
        description: 'Access token refreshed',
        ipAddress,
        userAgent: 'unknown',
        metadata: { sessionId: session.id }
      });

      this.logger.info('Token refreshed successfully', {
        userId: user.id,
        sessionId: session.id
      });

      return tokens;

    } catch (error) {
      if (error instanceof jwt.JsonWebTokenError) {
        throw this.createAuthError(AuthErrorCode.TOKEN_INVALID, 'Invalid refresh token');
      }
      if (error instanceof AuthError) {
        throw error;
      }
      this.logger.error('Token refresh failed', { error });
      throw this.createAuthError(AuthErrorCode.TOKEN_INVALID, 'Token refresh failed');
    }
  }

  /**
   * Logout user and invalidate session
   */
  async logout(sessionId: string, ipAddress: string): Promise<void> {
    try {
      const session = await this.sessionRepository.findById(sessionId);
      if (session) {
        // Deactivate session
        await this.sessionRepository.deactivateSession(sessionId);

        // Deactivate all refresh tokens for this session
        await this.tokenRepository.deactivateSessionTokens(sessionId);

        // Log logout
        await this.securityService.logSecurityEvent({
          userId: session.userId,
          eventType: SecurityEventType.LOGOUT,
          description: 'User logged out',
          ipAddress,
          userAgent: 'unknown',
          metadata: { sessionId }
        });

        this.logger.info('User logged out successfully', {
          userId: session.userId,
          sessionId
        });
      }
    } catch (error) {
      this.logger.error('Logout failed', { error, sessionId });
      throw this.createAuthError(AuthErrorCode.TOKEN_INVALID, 'Logout failed');
    }
  }

  /**
   * Request password reset
   */
  async requestPasswordReset(request: PasswordResetRequest, ipAddress: string): Promise<void> {
    const { email, tenantId } = request;

    // Rate limiting
    await this.rateLimitService.checkLimit('passwordReset', ipAddress);

    try {
      const user = await this.userRepository.findByEmail(email, tenantId);
      if (!user) {
        // Don't reveal if user exists or not
        this.logger.warn('Password reset requested for non-existent user', { email, tenantId });
        return;
      }

      // Generate reset token
      const resetToken = crypto.randomBytes(32).toString('hex');
      const tokenHash = await bcrypt.hash(resetToken, 10);
      const expiresAt = new Date(Date.now() + this.config.security.passwordResetExpiry * 60 * 1000);

      // Store reset token
      await this.tokenRepository.createPasswordResetToken({
        userId: user.id,
        tokenHash,
        expiresAt
      });

      // Send reset email
      await this.emailService.sendPasswordResetEmail(user.email, resetToken, user.firstName);

      // Log security event
      await this.securityService.logSecurityEvent({
        userId: user.id,
        tenantId: user.tenantId,
        eventType: SecurityEventType.PASSWORD_RESET_REQUESTED,
        description: 'Password reset requested',
        ipAddress,
        userAgent: 'unknown'
      });

      this.logger.info('Password reset requested', {
        userId: user.id,
        email: user.email
      });

    } catch (error) {
      this.logger.error('Password reset request failed', { error, email, tenantId });
      throw this.createAuthError(AuthErrorCode.INVALID_CREDENTIALS, 'Password reset request failed');
    }
  }

  /**
   * Confirm password reset with token
   */
  async confirmPasswordReset(request: PasswordResetConfirm, ipAddress: string): Promise<void> {
    const { token, newPassword } = request;

    try {
      // Validate password strength
      this.validatePassword(newPassword);

      // Find and verify reset token
      const resetTokens = await this.tokenRepository.findPasswordResetTokens();
      let validToken = null;
      let user = null;

      for (const storedToken of resetTokens) {
        if (!storedToken.isUsed && storedToken.expiresAt > new Date()) {
          const isValid = await bcrypt.compare(token, storedToken.tokenHash);
          if (isValid) {
            validToken = storedToken;
            user = await this.userRepository.findById(storedToken.userId);
            break;
          }
        }
      }

      if (!validToken || !user) {
        throw this.createAuthError(AuthErrorCode.TOKEN_INVALID, 'Invalid or expired reset token');
      }

      // Check password reuse
      const isPasswordReused = await this.securityService.isPasswordRecentlyUsed(user.id, newPassword);
      if (isPasswordReused) {
        throw this.createAuthError(AuthErrorCode.PASSWORD_RECENTLY_USED, 'Password was recently used');
      }

      // Hash new password
      const hashedPassword = await bcrypt.hash(newPassword, 12);

      // Update password
      await this.userRepository.updatePassword(user.id, hashedPassword);

      // Mark token as used
      await this.tokenRepository.markPasswordResetTokenUsed(validToken.id);

      // Store password in history
      await this.securityService.addPasswordToHistory(user.id, hashedPassword);

      // Invalidate all user sessions
      await this.sessionRepository.deactivateUserSessions(user.id);

      // Log security event
      await this.securityService.logSecurityEvent({
        userId: user.id,
        tenantId: user.tenantId,
        eventType: SecurityEventType.PASSWORD_RESET_COMPLETED,
        description: 'Password reset completed',
        ipAddress,
        userAgent: 'unknown'
      });

      this.logger.info('Password reset completed', {
        userId: user.id,
        email: user.email
      });

    } catch (error) {
      if (error instanceof AuthError) {
        throw error;
      }
      this.logger.error('Password reset confirmation failed', { error });
      throw this.createAuthError(AuthErrorCode.TOKEN_INVALID, 'Password reset failed');
    }
  }

  /**
   * Change user password
   */
  async changePassword(userId: string, request: ChangePasswordRequest, ipAddress: string): Promise<void> {
    const { currentPassword, newPassword } = request;

    try {
      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw this.createAuthError(AuthErrorCode.USER_NOT_FOUND, 'User not found');
      }

      // Verify current password
      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password!);
      if (!isCurrentPasswordValid) {
        throw this.createAuthError(AuthErrorCode.INVALID_CREDENTIALS, 'Current password is incorrect');
      }

      // Validate new password
      this.validatePassword(newPassword);

      // Check password reuse
      const isPasswordReused = await this.securityService.isPasswordRecentlyUsed(userId, newPassword);
      if (isPasswordReused) {
        throw this.createAuthError(AuthErrorCode.PASSWORD_RECENTLY_USED, 'Password was recently used');
      }

      // Hash new password
      const hashedPassword = await bcrypt.hash(newPassword, 12);

      // Update password
      await this.userRepository.updatePassword(userId, hashedPassword);

      // Store password in history
      await this.securityService.addPasswordToHistory(userId, hashedPassword);

      // Log security event
      await this.securityService.logSecurityEvent({
        userId,
        tenantId: user.tenantId,
        eventType: SecurityEventType.PASSWORD_CHANGED,
        description: 'Password changed by user',
        ipAddress,
        userAgent: 'unknown'
      });

      this.logger.info('Password changed successfully', {
        userId,
        email: user.email
      });

    } catch (error) {
      if (error instanceof AuthError) {
        throw error;
      }
      this.logger.error('Password change failed', { error, userId });
      throw this.createAuthError(AuthErrorCode.INVALID_CREDENTIALS, 'Password change failed');
    }
  }

  /**
   * Verify JWT token and return user context
   */
  async verifyToken(token: string): Promise<{ user: User; permissions: string[]; sessionId: string; tokenId: string }> {
    try {
      const payload = jwt.verify(token, this.config.jwt.accessTokenSecret) as TokenPayload;

      // Get user
      const user = await this.userRepository.findById(payload.sub);
      if (!user || !user.isActive) {
        throw this.createAuthError(AuthErrorCode.USER_NOT_FOUND, 'User not found or inactive');
      }

      // Verify session is still active
      const session = await this.sessionRepository.findByTokenId(payload.jti);
      if (!session || !session.isActive) {
        throw this.createAuthError(AuthErrorCode.TOKEN_INVALID, 'Session not found or inactive');
      }

      // Get current permissions (in case they changed)
      const permissions = await this.permissionService.getUserPermissions(user.id, user.tenantId);

      return {
        user,
        permissions,
        sessionId: session.id,
        tokenId: payload.jti
      };

    } catch (error) {
      if (error instanceof jwt.JsonWebTokenError) {
        throw this.createAuthError(AuthErrorCode.TOKEN_INVALID, 'Invalid token');
      }
      if (error instanceof AuthError) {
        throw error;
      }
      throw this.createAuthError(AuthErrorCode.TOKEN_INVALID, 'Token verification failed');
    }
  }

  /**
   * Generate JWT tokens
   */
  private async generateTokens(user: User, permissions: string[], sessionId: string): Promise<AuthTokens> {
    const tokenId = uuidv4();
    const now = Math.floor(Date.now() / 1000);

    // Access token payload
    const accessPayload: TokenPayload = {
      sub: user.id,
      email: user.email,
      role: user.role,
      tenantId: user.tenantId,
      permissions,
      iat: now,
      exp: now + this.parseExpiry(this.config.jwt.accessTokenExpiry),
      jti: tokenId
    };

    // Refresh token payload
    const refreshTokenId = uuidv4();
    const refreshPayload: RefreshTokenPayload = {
      sub: user.id,
      tokenId: refreshTokenId,
      iat: now,
      exp: now + this.parseExpiry(this.config.jwt.refreshTokenExpiry)
    };

    // Generate tokens
    const accessToken = jwt.sign(accessPayload, this.config.jwt.accessTokenSecret, {
      issuer: this.config.jwt.issuer,
      audience: this.config.jwt.audience
    });

    const refreshToken = jwt.sign(refreshPayload, this.config.jwt.refreshTokenSecret, {
      issuer: this.config.jwt.issuer,
      audience: this.config.jwt.audience
    });

    // Store refresh token
    const refreshTokenHash = await bcrypt.hash(refreshToken, 10);
    await this.tokenRepository.createRefreshToken({
      id: refreshTokenId,
      userId: user.id,
      tokenHash: refreshTokenHash,
      sessionId,
      expiresAt: new Date(refreshPayload.exp * 1000)
    });

    return {
      accessToken,
      refreshToken,
      expiresIn: this.parseExpiry(this.config.jwt.accessTokenExpiry),
      tokenType: 'Bearer'
    };
  }

  /**
   * Create user session
   */
  private async createSession(user: User, deviceInfo: DeviceInfo, ipAddress: string, rememberMe: boolean): Promise<AuthSession> {
    const sessionExpiry = rememberMe 
      ? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
      : new Date(Date.now() + this.config.security.sessionTimeout * 60 * 1000);

    return await this.sessionRepository.createSession({
      userId: user.id,
      deviceInfo,
      ipAddress,
      userAgent: deviceInfo.browser || 'unknown',
      expiresAt: sessionExpiry
    });
  }

  /**
   * Validate password strength
   */
  private validatePassword(password: string): void {
    const { minLength, requireUppercase, requireLowercase, requireNumbers, requireSymbols } = this.config.password;

    if (password.length < minLength) {
      throw this.createAuthError(AuthErrorCode.WEAK_PASSWORD, `Password must be at least ${minLength} characters long`);
    }

    if (requireUppercase && !/[A-Z]/.test(password)) {
      throw this.createAuthError(AuthErrorCode.WEAK_PASSWORD, 'Password must contain at least one uppercase letter');
    }

    if (requireLowercase && !/[a-z]/.test(password)) {
      throw this.createAuthError(AuthErrorCode.WEAK_PASSWORD, 'Password must contain at least one lowercase letter');
    }

    if (requireNumbers && !/\d/.test(password)) {
      throw this.createAuthError(AuthErrorCode.WEAK_PASSWORD, 'Password must contain at least one number');
    }

    if (requireSymbols && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      throw this.createAuthError(AuthErrorCode.WEAK_PASSWORD, 'Password must contain at least one special character');
    }
  }

  /**
   * Parse expiry string to seconds
   */
  private parseExpiry(expiry: string): number {
    const unit = expiry.slice(-1);
    const value = parseInt(expiry.slice(0, -1));

    switch (unit) {
      case 's': return value;
      case 'm': return value * 60;
      case 'h': return value * 60 * 60;
      case 'd': return value * 24 * 60 * 60;
      default: return value;
    }
  }

  /**
   * Register new user
   */
  async register(request: RegisterRequest, deviceInfo: DeviceInfo, ipAddress: string): Promise<LoginResponse> {
    const { email, password, firstName, lastName, tenantId, inviteToken } = request;

    // Rate limiting
    await this.rateLimitService.checkLimit('registration', ipAddress);

    try {
      // Check if user already exists
      const existingUser = await this.userRepository.findByEmail(email, tenantId);
      if (existingUser) {
        throw this.createAuthError(AuthErrorCode.EMAIL_ALREADY_EXISTS, 'Email already registered');
      }

      // Validate password
      this.validatePassword(password);

      // Validate invite token if required
      if (inviteToken) {
        const isValidInvite = await this.validateInviteToken(inviteToken, tenantId);
        if (!isValidInvite) {
          throw this.createAuthError(AuthErrorCode.INVALID_INVITE_TOKEN, 'Invalid invite token');
        }
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(password, 12);

      // Create user
      const user = await this.userRepository.createUser({
        email,
        password: hashedPassword,
        firstName,
        lastName,
        tenantId,
        emailVerified: !this.config.security.requireEmailVerification
      });

      // Store password in history
      await this.securityService.addPasswordToHistory(user.id, hashedPassword);

      // Send verification email if required
      if (this.config.security.requireEmailVerification) {
        await this.sendEmailVerification(user);
      }

      // Get permissions
      const permissions = await this.permissionService.getUserPermissions(user.id, user.tenantId);

      // Create session and tokens
      const session = await this.createSession(user, deviceInfo, ipAddress, false);
      const tokens = await this.generateTokens(user, permissions, session.id);

      this.logger.info('User registered successfully', {
        userId: user.id,
        email: user.email,
        tenantId: user.tenantId
      });

      const { password: _, ...userWithoutPassword } = user;

      return {
        user: userWithoutPassword,
        tokens,
        permissions
      };

    } catch (error) {
      if (error instanceof AuthError) {
        throw error;
      }
      this.logger.error('Registration failed', { error, email, tenantId });
      throw this.createAuthError(AuthErrorCode.INVALID_CREDENTIALS, 'Registration failed');
    }
  }

  /**
   * Send email verification
   */
  private async sendEmailVerification(user: User): Promise<void> {
    const verificationToken = crypto.randomBytes(32).toString('hex');
    const tokenHash = await bcrypt.hash(verificationToken, 10);
    const expiresAt = new Date(Date.now() + this.config.security.emailVerificationExpiry * 60 * 1000);

    await this.tokenRepository.createEmailVerificationToken({
      userId: user.id,
      tokenHash,
      expiresAt
    });

    await this.emailService.sendEmailVerification(user.email, verificationToken, user.firstName);
  }

  /**
   * Validate invite token
   */
  private async validateInviteToken(token: string, tenantId: string): Promise<boolean> {
    // Implementation depends on your invite system
    // This is a placeholder
    return true;
  }

  /**
   * Create authentication error
   */
  private createAuthError(code: AuthErrorCode, message: string, details?: Record<string, any>): AuthError {
    return {
      code,
      message,
      details
    } as AuthError;
  }
}

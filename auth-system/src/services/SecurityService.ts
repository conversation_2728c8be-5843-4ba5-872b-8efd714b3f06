import bcrypt from 'bcrypt';
import { SecurityEvent, SecurityEventType, AuthConfig } from '../types/auth';
import { SecurityRepository } from '../repositories/SecurityRepository';
import { Logger } from '../utils/Logger';

export class SecurityService {
  private securityRepository: SecurityRepository;
  private config: AuthConfig;
  private logger: Logger;

  constructor(
    securityRepository: SecurityRepository,
    config: AuthConfig,
    logger: Logger
  ) {
    this.securityRepository = securityRepository;
    this.config = config;
    this.logger = logger;
  }

  /**
   * Log security event
   */
  async logSecurityEvent(event: Omit<SecurityEvent, 'id' | 'timestamp'>): Promise<void> {
    try {
      await this.securityRepository.createSecurityEvent({
        ...event,
        timestamp: new Date()
      });

      this.logger.info('Security event logged', {
        eventType: event.eventType,
        userId: event.userId,
        tenantId: event.tenantId,
        description: event.description
      });

    } catch (error) {
      this.logger.error('Failed to log security event', { error, event });
    }
  }

  /**
   * Record failed login attempt
   */
  async recordFailedLogin(userId: string, ipAddress: string): Promise<void> {
    try {
      await this.securityRepository.recordFailedLogin(userId, ipAddress);

      // Check if account should be locked
      const failedAttempts = await this.securityRepository.getFailedLoginCount(userId);
      if (failedAttempts >= this.config.security.maxLoginAttempts) {
        await this.lockAccount(userId, ipAddress);
      }

    } catch (error) {
      this.logger.error('Failed to record failed login', { error, userId });
    }
  }

  /**
   * Clear failed login attempts
   */
  async clearFailedLogins(userId: string): Promise<void> {
    try {
      await this.securityRepository.clearFailedLogins(userId);
    } catch (error) {
      this.logger.error('Failed to clear failed logins', { error, userId });
    }
  }

  /**
   * Check if account is locked
   */
  async isAccountLocked(userId: string): Promise<boolean> {
    try {
      const lockInfo = await this.securityRepository.getAccountLockInfo(userId);
      if (!lockInfo) return false;

      const lockExpiry = new Date(lockInfo.lockedAt.getTime() + this.config.security.lockoutDuration * 60 * 1000);
      const isLocked = lockInfo.isLocked && new Date() < lockExpiry;

      // Auto-unlock if lock period has expired
      if (lockInfo.isLocked && new Date() >= lockExpiry) {
        await this.unlockAccount(userId);
        return false;
      }

      return isLocked;

    } catch (error) {
      this.logger.error('Failed to check account lock status', { error, userId });
      return false;
    }
  }

  /**
   * Lock user account
   */
  async lockAccount(userId: string, ipAddress: string): Promise<void> {
    try {
      await this.securityRepository.lockAccount(userId);

      await this.logSecurityEvent({
        userId,
        eventType: SecurityEventType.ACCOUNT_LOCKED,
        description: 'Account locked due to failed login attempts',
        ipAddress,
        userAgent: 'system'
      });

      this.logger.warn('Account locked', { userId, ipAddress });

    } catch (error) {
      this.logger.error('Failed to lock account', { error, userId });
    }
  }

  /**
   * Unlock user account
   */
  async unlockAccount(userId: string): Promise<void> {
    try {
      await this.securityRepository.unlockAccount(userId);
      await this.securityRepository.clearFailedLogins(userId);

      await this.logSecurityEvent({
        userId,
        eventType: SecurityEventType.ACCOUNT_UNLOCKED,
        description: 'Account unlocked',
        ipAddress: 'system',
        userAgent: 'system'
      });

      this.logger.info('Account unlocked', { userId });

    } catch (error) {
      this.logger.error('Failed to unlock account', { error, userId });
    }
  }

  /**
   * Add password to user's password history
   */
  async addPasswordToHistory(userId: string, hashedPassword: string): Promise<void> {
    try {
      await this.securityRepository.addPasswordToHistory(userId, hashedPassword);

      // Clean up old password history entries
      const maxHistory = this.config.password.preventReuse;
      await this.securityRepository.cleanupPasswordHistory(userId, maxHistory);

    } catch (error) {
      this.logger.error('Failed to add password to history', { error, userId });
    }
  }

  /**
   * Check if password was recently used
   */
  async isPasswordRecentlyUsed(userId: string, newPassword: string): Promise<boolean> {
    try {
      const passwordHistory = await this.securityRepository.getPasswordHistory(userId, this.config.password.preventReuse);

      for (const historicalPassword of passwordHistory) {
        const isMatch = await bcrypt.compare(newPassword, historicalPassword.hashedPassword);
        if (isMatch) {
          return true;
        }
      }

      return false;

    } catch (error) {
      this.logger.error('Failed to check password history', { error, userId });
      return false;
    }
  }

  /**
   * Detect suspicious activity
   */
  async detectSuspiciousActivity(userId: string, ipAddress: string, userAgent: string): Promise<boolean> {
    try {
      // Check for multiple login attempts from different locations
      const recentEvents = await this.securityRepository.getRecentSecurityEvents(userId, 24); // Last 24 hours
      
      const loginEvents = recentEvents.filter(event => 
        event.eventType === SecurityEventType.LOGIN_SUCCESS || 
        event.eventType === SecurityEventType.LOGIN_FAILURE
      );

      // Check for logins from multiple IP addresses
      const uniqueIPs = new Set(loginEvents.map(event => event.ipAddress));
      if (uniqueIPs.size > 5) { // More than 5 different IPs in 24 hours
        await this.logSecurityEvent({
          userId,
          eventType: SecurityEventType.SUSPICIOUS_ACTIVITY,
          description: 'Multiple login attempts from different IP addresses',
          ipAddress,
          userAgent,
          metadata: { uniqueIPs: Array.from(uniqueIPs) }
        });
        return true;
      }

      // Check for rapid login attempts
      const recentLogins = loginEvents.filter(event => 
        new Date().getTime() - event.timestamp.getTime() < 5 * 60 * 1000 // Last 5 minutes
      );

      if (recentLogins.length > 10) { // More than 10 attempts in 5 minutes
        await this.logSecurityEvent({
          userId,
          eventType: SecurityEventType.SUSPICIOUS_ACTIVITY,
          description: 'Rapid login attempts detected',
          ipAddress,
          userAgent,
          metadata: { attemptCount: recentLogins.length }
        });
        return true;
      }

      return false;

    } catch (error) {
      this.logger.error('Failed to detect suspicious activity', { error, userId });
      return false;
    }
  }

  /**
   * Get security events for user
   */
  async getUserSecurityEvents(userId: string, limit: number = 50): Promise<SecurityEvent[]> {
    try {
      return await this.securityRepository.getUserSecurityEvents(userId, limit);
    } catch (error) {
      this.logger.error('Failed to get user security events', { error, userId });
      return [];
    }
  }

  /**
   * Get security events for tenant
   */
  async getTenantSecurityEvents(tenantId: string, limit: number = 100): Promise<SecurityEvent[]> {
    try {
      return await this.securityRepository.getTenantSecurityEvents(tenantId, limit);
    } catch (error) {
      this.logger.error('Failed to get tenant security events', { error, tenantId });
      return [];
    }
  }

  /**
   * Generate security report
   */
  async generateSecurityReport(tenantId: string, startDate: Date, endDate: Date): Promise<{
    totalEvents: number;
    loginAttempts: number;
    failedLogins: number;
    successfulLogins: number;
    lockedAccounts: number;
    suspiciousActivities: number;
    passwordResets: number;
    topEventTypes: Array<{ type: string; count: number }>;
    topIpAddresses: Array<{ ip: string; count: number }>;
  }> {
    try {
      const events = await this.securityRepository.getSecurityEventsByDateRange(tenantId, startDate, endDate);

      const report = {
        totalEvents: events.length,
        loginAttempts: events.filter(e => e.eventType === SecurityEventType.LOGIN_SUCCESS || e.eventType === SecurityEventType.LOGIN_FAILURE).length,
        failedLogins: events.filter(e => e.eventType === SecurityEventType.LOGIN_FAILURE).length,
        successfulLogins: events.filter(e => e.eventType === SecurityEventType.LOGIN_SUCCESS).length,
        lockedAccounts: events.filter(e => e.eventType === SecurityEventType.ACCOUNT_LOCKED).length,
        suspiciousActivities: events.filter(e => e.eventType === SecurityEventType.SUSPICIOUS_ACTIVITY).length,
        passwordResets: events.filter(e => e.eventType === SecurityEventType.PASSWORD_RESET_COMPLETED).length,
        topEventTypes: this.getTopEventTypes(events),
        topIpAddresses: this.getTopIpAddresses(events)
      };

      return report;

    } catch (error) {
      this.logger.error('Failed to generate security report', { error, tenantId });
      throw error;
    }
  }

  /**
   * Get top event types from events
   */
  private getTopEventTypes(events: SecurityEvent[]): Array<{ type: string; count: number }> {
    const eventCounts = events.reduce((acc, event) => {
      acc[event.eventType] = (acc[event.eventType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(eventCounts)
      .map(([type, count]) => ({ type, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
  }

  /**
   * Get top IP addresses from events
   */
  private getTopIpAddresses(events: SecurityEvent[]): Array<{ ip: string; count: number }> {
    const ipCounts = events.reduce((acc, event) => {
      acc[event.ipAddress] = (acc[event.ipAddress] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(ipCounts)
      .map(([ip, count]) => ({ ip, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
  }
}

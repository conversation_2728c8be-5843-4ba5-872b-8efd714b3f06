export interface User {
  id: string;
  email: string;
  password?: string; // Excluded in responses
  firstName: string;
  lastName: string;
  role: UserRole;
  tenantId: string;
  isActive: boolean;
  emailVerified: boolean;
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export enum UserRole {
  SUPER_ADMIN = 'super_admin',
  TENANT_ADMIN = 'tenant_admin',
  USER = 'user',
  VIEWER = 'viewer'
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: 'Bearer';
}

export interface TokenPayload {
  sub: string; // User ID
  email: string;
  role: UserRole;
  tenantId: string;
  permissions: string[];
  iat: number;
  exp: number;
  jti: string; // JWT ID for tracking
}

export interface RefreshTokenPayload {
  sub: string; // User ID
  tokenId: string;
  iat: number;
  exp: number;
}

export interface LoginRequest {
  email: string;
  password: string;
  tenantId?: string; // Optional for multi-tenant login
  rememberMe?: boolean;
}

export interface LoginResponse {
  user: Omit<User, 'password'>;
  tokens: AuthTokens;
  permissions: string[];
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface PasswordResetRequest {
  email: string;
  tenantId?: string;
}

export interface PasswordResetConfirm {
  token: string;
  newPassword: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  tenantId: string;
  inviteToken?: string;
}

export interface EmailVerificationRequest {
  token: string;
}

export interface AuthSession {
  id: string;
  userId: string;
  refreshTokenId: string;
  deviceInfo?: DeviceInfo;
  ipAddress: string;
  userAgent: string;
  isActive: boolean;
  expiresAt: Date;
  createdAt: Date;
  lastUsedAt: Date;
}

export interface DeviceInfo {
  deviceId?: string;
  deviceType: 'desktop' | 'mobile' | 'tablet' | 'unknown';
  browser?: string;
  os?: string;
  location?: {
    country?: string;
    city?: string;
  };
}

export interface RefreshToken {
  id: string;
  userId: string;
  tokenHash: string;
  sessionId: string;
  isActive: boolean;
  expiresAt: Date;
  createdAt: Date;
  lastUsedAt?: Date;
}

export interface PasswordResetToken {
  id: string;
  userId: string;
  tokenHash: string;
  expiresAt: Date;
  isUsed: boolean;
  createdAt: Date;
  usedAt?: Date;
}

export interface EmailVerificationToken {
  id: string;
  userId: string;
  tokenHash: string;
  expiresAt: Date;
  isUsed: boolean;
  createdAt: Date;
  usedAt?: Date;
}

export interface AuthConfig {
  jwt: {
    accessTokenSecret: string;
    refreshTokenSecret: string;
    accessTokenExpiry: string; // e.g., '15m'
    refreshTokenExpiry: string; // e.g., '7d'
    issuer: string;
    audience: string;
  };
  password: {
    minLength: number;
    requireUppercase: boolean;
    requireLowercase: boolean;
    requireNumbers: boolean;
    requireSymbols: boolean;
    maxAge: number; // days
    preventReuse: number; // number of previous passwords to check
  };
  security: {
    maxLoginAttempts: number;
    lockoutDuration: number; // minutes
    sessionTimeout: number; // minutes
    requireEmailVerification: boolean;
    allowMultipleSessions: boolean;
    passwordResetExpiry: number; // minutes
    emailVerificationExpiry: number; // minutes
  };
  rateLimit: {
    login: {
      windowMs: number;
      maxAttempts: number;
    };
    passwordReset: {
      windowMs: number;
      maxAttempts: number;
    };
    registration: {
      windowMs: number;
      maxAttempts: number;
    };
  };
}

export interface AuthError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

export enum AuthErrorCode {
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  ACCOUNT_LOCKED = 'ACCOUNT_LOCKED',
  EMAIL_NOT_VERIFIED = 'EMAIL_NOT_VERIFIED',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  TOKEN_INVALID = 'TOKEN_INVALID',
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  USER_NOT_FOUND = 'USER_NOT_FOUND',
  EMAIL_ALREADY_EXISTS = 'EMAIL_ALREADY_EXISTS',
  WEAK_PASSWORD = 'WEAK_PASSWORD',
  PASSWORD_RECENTLY_USED = 'PASSWORD_RECENTLY_USED',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  TENANT_NOT_FOUND = 'TENANT_NOT_FOUND',
  INVALID_INVITE_TOKEN = 'INVALID_INVITE_TOKEN'
}

export interface Permission {
  id: string;
  name: string;
  description: string;
  resource: string;
  action: string;
  conditions?: Record<string, any>;
}

export interface RolePermission {
  roleId: string;
  permissionId: string;
  tenantId?: string;
  conditions?: Record<string, any>;
}

export interface LoginAttempt {
  id: string;
  email: string;
  ipAddress: string;
  userAgent: string;
  success: boolean;
  failureReason?: string;
  timestamp: Date;
  tenantId?: string;
}

export interface SecurityEvent {
  id: string;
  userId?: string;
  tenantId?: string;
  eventType: SecurityEventType;
  description: string;
  ipAddress: string;
  userAgent: string;
  metadata?: Record<string, any>;
  timestamp: Date;
}

export enum SecurityEventType {
  LOGIN_SUCCESS = 'LOGIN_SUCCESS',
  LOGIN_FAILURE = 'LOGIN_FAILURE',
  LOGOUT = 'LOGOUT',
  PASSWORD_CHANGED = 'PASSWORD_CHANGED',
  PASSWORD_RESET_REQUESTED = 'PASSWORD_RESET_REQUESTED',
  PASSWORD_RESET_COMPLETED = 'PASSWORD_RESET_COMPLETED',
  EMAIL_VERIFIED = 'EMAIL_VERIFIED',
  ACCOUNT_LOCKED = 'ACCOUNT_LOCKED',
  ACCOUNT_UNLOCKED = 'ACCOUNT_UNLOCKED',
  TOKEN_REFRESH = 'TOKEN_REFRESH',
  SUSPICIOUS_ACTIVITY = 'SUSPICIOUS_ACTIVITY',
  PERMISSION_DENIED = 'PERMISSION_DENIED'
}

export interface AuthMiddlewareOptions {
  requireAuth?: boolean;
  requiredPermissions?: string[];
  requiredRole?: UserRole;
  allowSameUser?: boolean;
  allowSameTenant?: boolean;
}

export interface AuthContext {
  user: Omit<User, 'password'>;
  permissions: string[];
  sessionId: string;
  tokenId: string;
}

import { Request, Response } from 'express';
import {
  LoginRequest,
  RefreshTokenRequest,
  PasswordResetRequest,
  PasswordResetConfirm,
  ChangePasswordRequest,
  RegisterRequest,
  EmailVerificationRequest,
  AuthError,
  DeviceInfo
} from '../types/auth';
import { AuthService } from '../services/AuthService';
import { Logger } from '../utils/Logger';
import { getCurrentUser } from '../middleware/AuthMiddleware';

export class AuthController {
  private authService: AuthService;
  private logger: Logger;

  constructor(authService: AuthService, logger: Logger) {
    this.authService = authService;
    this.logger = logger;
  }

  /**
   * User login
   */
  async login(req: Request, res: Response): Promise<void> {
    try {
      const loginRequest: LoginRequest = req.body;
      const deviceInfo = this.extractDeviceInfo(req);
      const ipAddress = this.getClientIP(req);

      const result = await this.authService.login(loginRequest, deviceInfo, ipAddress);

      res.status(200).json({
        success: true,
        data: result
      });

    } catch (error) {
      this.handleAuthError(res, error);
    }
  }

  /**
   * User registration
   */
  async register(req: Request, res: Response): Promise<void> {
    try {
      const registerRequest: RegisterRequest = req.body;
      const deviceInfo = this.extractDeviceInfo(req);
      const ipAddress = this.getClientIP(req);

      const result = await this.authService.register(registerRequest, deviceInfo, ipAddress);

      res.status(201).json({
        success: true,
        data: result
      });

    } catch (error) {
      this.handleAuthError(res, error);
    }
  }

  /**
   * Refresh access token
   */
  async refreshToken(req: Request, res: Response): Promise<void> {
    try {
      const refreshRequest: RefreshTokenRequest = req.body;
      const ipAddress = this.getClientIP(req);

      const tokens = await this.authService.refreshToken(refreshRequest, ipAddress);

      res.status(200).json({
        success: true,
        data: tokens
      });

    } catch (error) {
      this.handleAuthError(res, error);
    }
  }

  /**
   * User logout
   */
  async logout(req: Request, res: Response): Promise<void> {
    try {
      const sessionId = req.auth?.sessionId;
      const ipAddress = this.getClientIP(req);

      if (sessionId) {
        await this.authService.logout(sessionId, ipAddress);
      }

      res.status(200).json({
        success: true,
        message: 'Logged out successfully'
      });

    } catch (error) {
      this.handleAuthError(res, error);
    }
  }

  /**
   * Request password reset
   */
  async requestPasswordReset(req: Request, res: Response): Promise<void> {
    try {
      const resetRequest: PasswordResetRequest = req.body;
      const ipAddress = this.getClientIP(req);

      await this.authService.requestPasswordReset(resetRequest, ipAddress);

      res.status(200).json({
        success: true,
        message: 'Password reset email sent if account exists'
      });

    } catch (error) {
      this.handleAuthError(res, error);
    }
  }

  /**
   * Confirm password reset
   */
  async confirmPasswordReset(req: Request, res: Response): Promise<void> {
    try {
      const confirmRequest: PasswordResetConfirm = req.body;
      const ipAddress = this.getClientIP(req);

      await this.authService.confirmPasswordReset(confirmRequest, ipAddress);

      res.status(200).json({
        success: true,
        message: 'Password reset successfully'
      });

    } catch (error) {
      this.handleAuthError(res, error);
    }
  }

  /**
   * Change password
   */
  async changePassword(req: Request, res: Response): Promise<void> {
    try {
      const user = getCurrentUser(req);
      if (!user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      const changeRequest: ChangePasswordRequest = req.body;
      const ipAddress = this.getClientIP(req);

      await this.authService.changePassword(user.id, changeRequest, ipAddress);

      res.status(200).json({
        success: true,
        message: 'Password changed successfully'
      });

    } catch (error) {
      this.handleAuthError(res, error);
    }
  }

  /**
   * Get current user profile
   */
  async getProfile(req: Request, res: Response): Promise<void> {
    try {
      const user = getCurrentUser(req);
      if (!user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      res.status(200).json({
        success: true,
        data: {
          user,
          permissions: req.auth?.permissions || []
        }
      });

    } catch (error) {
      this.handleAuthError(res, error);
    }
  }

  /**
   * Verify email address
   */
  async verifyEmail(req: Request, res: Response): Promise<void> {
    try {
      const verificationRequest: EmailVerificationRequest = req.body;
      const ipAddress = this.getClientIP(req);

      // Implementation would go here
      // await this.authService.verifyEmail(verificationRequest, ipAddress);

      res.status(200).json({
        success: true,
        message: 'Email verified successfully'
      });

    } catch (error) {
      this.handleAuthError(res, error);
    }
  }

  /**
   * Resend email verification
   */
  async resendEmailVerification(req: Request, res: Response): Promise<void> {
    try {
      const { email, tenantId } = req.body;
      const ipAddress = this.getClientIP(req);

      // Implementation would go here
      // await this.authService.resendEmailVerification(email, tenantId, ipAddress);

      res.status(200).json({
        success: true,
        message: 'Verification email sent if account exists'
      });

    } catch (error) {
      this.handleAuthError(res, error);
    }
  }

  /**
   * Get user sessions
   */
  async getSessions(req: Request, res: Response): Promise<void> {
    try {
      const user = getCurrentUser(req);
      if (!user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      // Implementation would get sessions from SessionRepository
      // const sessions = await this.sessionRepository.getUserSessions(user.id);

      res.status(200).json({
        success: true,
        data: {
          sessions: [] // Placeholder
        }
      });

    } catch (error) {
      this.handleAuthError(res, error);
    }
  }

  /**
   * Revoke session
   */
  async revokeSession(req: Request, res: Response): Promise<void> {
    try {
      const user = getCurrentUser(req);
      if (!user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      const { sessionId } = req.params;
      const ipAddress = this.getClientIP(req);

      await this.authService.logout(sessionId, ipAddress);

      res.status(200).json({
        success: true,
        message: 'Session revoked successfully'
      });

    } catch (error) {
      this.handleAuthError(res, error);
    }
  }

  /**
   * Extract device information from request
   */
  private extractDeviceInfo(req: Request): DeviceInfo {
    const userAgent = req.headers['user-agent'] || 'unknown';
    
    // Simple device detection (in production, use a proper library like 'ua-parser-js')
    let deviceType: DeviceInfo['deviceType'] = 'unknown';
    if (/mobile/i.test(userAgent)) {
      deviceType = 'mobile';
    } else if (/tablet/i.test(userAgent)) {
      deviceType = 'tablet';
    } else if (/desktop|windows|mac|linux/i.test(userAgent)) {
      deviceType = 'desktop';
    }

    // Extract browser info (simplified)
    let browser = 'unknown';
    if (/chrome/i.test(userAgent)) browser = 'Chrome';
    else if (/firefox/i.test(userAgent)) browser = 'Firefox';
    else if (/safari/i.test(userAgent)) browser = 'Safari';
    else if (/edge/i.test(userAgent)) browser = 'Edge';

    // Extract OS info (simplified)
    let os = 'unknown';
    if (/windows/i.test(userAgent)) os = 'Windows';
    else if (/mac/i.test(userAgent)) os = 'macOS';
    else if (/linux/i.test(userAgent)) os = 'Linux';
    else if (/android/i.test(userAgent)) os = 'Android';
    else if (/ios/i.test(userAgent)) os = 'iOS';

    return {
      deviceType,
      browser,
      os,
      deviceId: req.headers['x-device-id'] as string
    };
  }

  /**
   * Get client IP address
   */
  private getClientIP(req: Request): string {
    return (
      req.headers['x-forwarded-for'] as string ||
      req.headers['x-real-ip'] as string ||
      req.connection.remoteAddress ||
      req.socket.remoteAddress ||
      'unknown'
    ).split(',')[0].trim();
  }

  /**
   * Handle authentication errors
   */
  private handleAuthError(res: Response, error: any): void {
    if (error instanceof AuthError) {
      const statusCode = this.getStatusCodeForError(error.code);
      res.status(statusCode).json({
        success: false,
        error: error.message,
        code: error.code,
        details: error.details
      });
    } else {
      this.logger.error('Unexpected auth error', { error });
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      });
    }
  }

  /**
   * Get HTTP status code for auth error
   */
  private getStatusCodeForError(code: string): number {
    switch (code) {
      case 'INVALID_CREDENTIALS':
      case 'TOKEN_INVALID':
      case 'TOKEN_EXPIRED':
      case 'EMAIL_NOT_VERIFIED':
        return 401;
      case 'INSUFFICIENT_PERMISSIONS':
        return 403;
      case 'USER_NOT_FOUND':
      case 'TENANT_NOT_FOUND':
        return 404;
      case 'EMAIL_ALREADY_EXISTS':
      case 'WEAK_PASSWORD':
      case 'PASSWORD_RECENTLY_USED':
        return 400;
      case 'ACCOUNT_LOCKED':
        return 423;
      case 'RATE_LIMIT_EXCEEDED':
        return 429;
      default:
        return 400;
    }
  }
}

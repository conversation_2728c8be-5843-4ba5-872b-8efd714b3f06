import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { Pool } from 'pg';
import { Redis } from 'ioredis';
import { AuthService } from '../src/services/AuthService';
import { AuthController } from '../src/controllers/AuthController';
import { AuthMiddleware } from '../src/middleware/AuthMiddleware';
import { createAuthRoutes } from '../src/routes/authRoutes';
import { UserRepository } from '../src/repositories/UserRepository';
import { SessionRepository } from '../src/repositories/SessionRepository';
import { TokenRepository } from '../src/repositories/TokenRepository';
import { SecurityRepository } from '../src/repositories/SecurityRepository';
import { PermissionService } from '../src/services/PermissionService';
import { EmailService } from '../src/services/EmailService';
import { SecurityService } from '../src/services/SecurityService';
import { RateLimitService } from '../src/services/RateLimitService';
import { ConsoleLogger } from '../src/utils/Logger';
import { AuthConfig, UserRole } from '../src/types/auth';

async function createAuthApp() {
  console.log('🚀 Starting A/B Testing Platform Authentication Service\n');

  // Configuration
  const config: AuthConfig = {
    jwt: {
      accessTokenSecret: process.env.JWT_ACCESS_SECRET || 'your-super-secret-access-key-change-in-production',
      refreshTokenSecret: process.env.JWT_REFRESH_SECRET || 'your-super-secret-refresh-key-change-in-production',
      accessTokenExpiry: '15m',
      refreshTokenExpiry: '7d',
      issuer: 'ab-testing-platform',
      audience: 'ab-testing-users'
    },
    password: {
      minLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSymbols: false,
      maxAge: 90, // days
      preventReuse: 5
    },
    security: {
      maxLoginAttempts: 5,
      lockoutDuration: 15, // minutes
      sessionTimeout: 60, // minutes
      requireEmailVerification: false, // Set to true in production
      allowMultipleSessions: true,
      passwordResetExpiry: 60, // minutes
      emailVerificationExpiry: 24 * 60 // 24 hours
    },
    rateLimit: {
      login: {
        windowMs: 15 * 60 * 1000, // 15 minutes
        maxAttempts: 5
      },
      passwordReset: {
        windowMs: 60 * 60 * 1000, // 1 hour
        maxAttempts: 3
      },
      registration: {
        windowMs: 60 * 60 * 1000, // 1 hour
        maxAttempts: 5
      }
    }
  };

  // Initialize logger
  const logger = new ConsoleLogger();

  // Database connection
  const database = new Pool({
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    database: process.env.DB_NAME || 'ab_testing_auth',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
    max: 20,
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
  });

  // Redis connection for sessions and rate limiting
  const redis = new Redis({
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD,
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 3
  });

  // Initialize repositories
  const userRepository = new UserRepository(database, logger);
  const sessionRepository = new SessionRepository(database, logger);
  const tokenRepository = new TokenRepository(database, logger);
  const securityRepository = new SecurityRepository(database, logger);

  // Initialize services
  const permissionService = new PermissionService(database, logger);
  const emailService = new EmailService(config, logger);
  const securityService = new SecurityService(securityRepository, config, logger);
  const rateLimitService = new RateLimitService(redis, config, logger);

  // Initialize auth service
  const authService = new AuthService(
    userRepository,
    sessionRepository,
    tokenRepository,
    permissionService,
    emailService,
    securityService,
    rateLimitService,
    config,
    logger
  );

  // Initialize middleware and controllers
  const authMiddleware = new AuthMiddleware(authService, logger);
  const authController = new AuthController(authService, logger);

  // Create Express app
  const app = express();

  // Security middleware
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
      },
    },
    hsts: {
      maxAge: 31536000,
      includeSubDomains: true,
      preload: true
    }
  }));

  app.use(cors({
    origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000', 'http://localhost:3001'],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Device-ID']
  }));

  // Body parsing middleware
  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true, limit: '10mb' }));

  // Request logging middleware
  app.use((req, res, next) => {
    logger.info('HTTP Request', {
      method: req.method,
      url: req.url,
      ip: req.ip,
      userAgent: req.headers['user-agent']
    });
    next();
  });

  // Auth routes
  app.use('/api/auth', createAuthRoutes(
    authController,
    authMiddleware,
    rateLimitService as any, // Type assertion for middleware compatibility
    { validate: (schema: any) => (req: any, res: any, next: any) => next() } as any // Mock validation middleware
  ));

  // Protected API routes example
  app.get('/api/experiments',
    authMiddleware.authenticate(),
    authMiddleware.requirePermissions(['experiments:read']),
    (req, res) => {
      res.json({
        success: true,
        data: {
          experiments: [
            { id: '1', name: 'Button Color Test', status: 'active' },
            { id: '2', name: 'Pricing Page Test', status: 'draft' }
          ]
        }
      });
    }
  );

  // Admin-only routes example
  app.get('/api/admin/users',
    authMiddleware.requireAdmin(),
    (req, res) => {
      res.json({
        success: true,
        data: {
          users: [
            { id: '1', email: '<EMAIL>', role: UserRole.TENANT_ADMIN },
            { id: '2', email: '<EMAIL>', role: UserRole.USER }
          ]
        }
      });
    }
  );

  // Health check
  app.get('/health', (req, res) => {
    res.json({
      success: true,
      message: 'A/B Testing Auth Service is healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    });
  });

  // Error handling middleware
  app.use((error: any, req: any, res: any, next: any) => {
    logger.error('Unhandled error', { error: error.message, stack: error.stack });
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    });
  });

  // 404 handler
  app.use('*', (req, res) => {
    res.status(404).json({
      success: false,
      error: 'Route not found',
      code: 'NOT_FOUND'
    });
  });

  return { app, database, redis, logger };
}

// Example usage
async function startServer() {
  try {
    const { app, database, redis, logger } = await createAuthApp();

    const PORT = process.env.PORT || 3001;

    // Test database connection
    await database.query('SELECT NOW()');
    logger.info('Database connected successfully');

    // Test Redis connection
    await redis.ping();
    logger.info('Redis connected successfully');

    // Start server
    app.listen(PORT, () => {
      logger.info(`🚀 Auth service running on port ${PORT}`);
      logger.info(`📚 API Documentation: http://localhost:${PORT}/api/auth`);
      logger.info(`🏥 Health Check: http://localhost:${PORT}/health`);
    });

    // Graceful shutdown
    process.on('SIGTERM', async () => {
      logger.info('SIGTERM received, shutting down gracefully');
      await database.end();
      await redis.quit();
      process.exit(0);
    });

    process.on('SIGINT', async () => {
      logger.info('SIGINT received, shutting down gracefully');
      await database.end();
      await redis.quit();
      process.exit(0);
    });

  } catch (error) {
    console.error('Failed to start auth service:', error);
    process.exit(1);
  }
}

// Demo functions
async function demonstrateAuthFlow() {
  console.log('\n🎯 Authentication Flow Demo\n');

  const { app, database, redis, logger } = await createAuthApp();
  
  // This would typically be done through HTTP requests
  console.log('1. User Registration:');
  console.log('   POST /api/auth/register');
  console.log('   Body: { email, password, firstName, lastName, tenantId }');
  
  console.log('\n2. User Login:');
  console.log('   POST /api/auth/login');
  console.log('   Body: { email, password, tenantId?, rememberMe? }');
  console.log('   Response: { user, tokens: { accessToken, refreshToken }, permissions }');
  
  console.log('\n3. Access Protected Resource:');
  console.log('   GET /api/experiments');
  console.log('   Headers: { Authorization: "Bearer <accessToken>" }');
  
  console.log('\n4. Refresh Token:');
  console.log('   POST /api/auth/refresh');
  console.log('   Body: { refreshToken }');
  console.log('   Response: { accessToken, refreshToken, expiresIn }');
  
  console.log('\n5. Password Reset:');
  console.log('   POST /api/auth/password-reset/request');
  console.log('   Body: { email, tenantId? }');
  console.log('   POST /api/auth/password-reset/confirm');
  console.log('   Body: { token, newPassword }');
  
  console.log('\n6. Change Password:');
  console.log('   POST /api/auth/change-password');
  console.log('   Headers: { Authorization: "Bearer <accessToken>" }');
  console.log('   Body: { currentPassword, newPassword }');
  
  console.log('\n7. Logout:');
  console.log('   POST /api/auth/logout');
  console.log('   Headers: { Authorization: "Bearer <accessToken>" }');

  await database.end();
  await redis.quit();
}

// Run the demo if this file is executed directly
if (require.main === module) {
  const command = process.argv[2];
  
  if (command === 'demo') {
    demonstrateAuthFlow().catch(console.error);
  } else {
    startServer().catch(console.error);
  }
}

export { createAuthApp, startServer, demonstrateAuthFlow };

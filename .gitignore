# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# IDEs and editors
.idea
.vscode
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Database
*.sqlite
*.sqlite3
*.db

# Docker
.dockerignore

# Build outputs
dist/
build/
*.tgz
*.tar.gz

# Temporary files
tmp/
temp/
.tmp/

# Backup files
*.backup
*.bak
*.orig

# Test files
test-results/
coverage/

# Local development
.env.local
.env.development
.env.test
.env.production

# Docker volumes
docker/postgres/data/
docker/redis/data/

# Application logs
logs/
*.log

# Uploads
uploads/

# Backups
backups/

# Generated files
prisma/migrations/
.prisma/

# Lock files (keep package-lock.json but ignore others)
yarn.lock
pnpm-lock.yaml

# Compiled TypeScript
*.js.map
*.d.ts.map

# ESLint cache
.eslintcache

# Prettier cache
.prettiercache

# Stylelint cache
.stylelintcache

# Jest cache
.jest/

# Webpack cache
.webpack/

# Rollup cache
.rollup.cache/

# Parcel cache
.parcel-cache/

# Storybook build outputs
storybook-static/

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# MacOS
.DS_Store

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux
*~

# Application specific
public/uploads/
storage/
.env.docker.local

# Docker Compose override for local development
docker-compose.local.yml

# SSL certificates
*.pem
*.key
*.crt
*.csr

# Monitoring data
prometheus_data/
grafana_data/
pgadmin_data/

# Temporary Docker files
.docker/

# Local configuration overrides
config.local.json
settings.local.json

/**
 * Validate Example Experiments
 * 
 * This script validates all example experiment configurations
 * and demonstrates the validation functionality.
 */

const fs = require('fs');
const { validateExperiment, validateExperimentConflicts } = require('./validate_experiment');

function main() {
    console.log('🧪 A/B Testing Experiment Configuration Validator');
    console.log('================================================\n');

    try {
        // Load example experiments
        const examplesData = JSON.parse(fs.readFileSync('./example_experiments.json', 'utf8'));
        const experiments = examplesData.examples;

        console.log(`📋 Validating ${experiments.length} example experiments...\n`);

        let allValid = true;
        const results = [];

        // Validate each experiment
        experiments.forEach((experiment, index) => {
            console.log(`🔍 Validating: "${experiment.name}"`);
            console.log('─'.repeat(50));

            const result = validateExperiment(experiment);
            results.push({ experiment: experiment.name, result });

            if (result.isValid) {
                console.log('✅ Valid configuration');
            } else {
                console.log('❌ Invalid configuration');
                allValid = false;
            }

            // Display errors
            if (result.errors.length > 0) {
                console.log('\n🚨 Errors:');
                result.errors.forEach(error => {
                    console.log(`   • ${error.path || 'root'}: ${error.message}`);
                });
            }

            // Display warnings
            if (result.warnings.length > 0) {
                console.log('\n⚠️  Warnings:');
                result.warnings.forEach(warning => {
                    console.log(`   • ${warning.path || 'root'}: ${warning.message}`);
                });
            }

            console.log('\n');
        });

        // Check for experiment conflicts
        console.log('🔄 Checking for experiment conflicts...');
        console.log('─'.repeat(50));

        const conflicts = validateExperimentConflicts(experiments);
        if (conflicts.length === 0) {
            console.log('✅ No conflicts detected between experiments');
        } else {
            console.log('⚠️  Potential conflicts detected:');
            conflicts.forEach(conflict => {
                console.log(`   • ${conflict.experiments.join(' vs ')}: ${conflict.message}`);
            });
        }

        // Summary
        console.log('\n📊 Validation Summary');
        console.log('═'.repeat(50));
        
        const validCount = results.filter(r => r.result.isValid).length;
        const invalidCount = results.length - validCount;
        const totalWarnings = results.reduce((sum, r) => sum + r.result.warnings.length, 0);
        const totalErrors = results.reduce((sum, r) => sum + r.result.errors.length, 0);

        console.log(`Total Experiments: ${experiments.length}`);
        console.log(`Valid: ${validCount}`);
        console.log(`Invalid: ${invalidCount}`);
        console.log(`Total Warnings: ${totalWarnings}`);
        console.log(`Total Errors: ${totalErrors}`);
        console.log(`Conflicts: ${conflicts.length}`);

        if (allValid && conflicts.length === 0) {
            console.log('\n🎉 All experiments are valid and conflict-free!');
            process.exit(0);
        } else {
            console.log('\n❌ Some experiments have issues that need to be addressed.');
            process.exit(1);
        }

    } catch (error) {
        console.error('💥 Error running validation:', error.message);
        process.exit(1);
    }
}

// Demonstration of individual validation
function demonstrateValidation() {
    console.log('\n🎯 Validation Feature Demonstration');
    console.log('═'.repeat(50));

    // Example of a valid configuration
    const validConfig = {
        name: "Simple Button Test",
        description: "Testing button colors",
        hypothesis: "Red buttons perform better",
        status: "draft",
        variants: [
            {
                name: "Control",
                description: "Blue button",
                isControl: true,
                allocationPercentage: 50
            },
            {
                name: "Treatment",
                description: "Red button",
                isControl: false,
                allocationPercentage: 50
            }
        ],
        metrics: {
            primary: {
                name: "Click Rate",
                mixpanelEvent: "Button Clicked",
                aggregationType: "count"
            }
        }
    };

    console.log('\n✅ Valid Configuration Example:');
    const validResult = validateExperiment(validConfig);
    console.log(`   Valid: ${validResult.isValid}`);
    console.log(`   Errors: ${validResult.errors.length}`);
    console.log(`   Warnings: ${validResult.warnings.length}`);

    // Example of an invalid configuration
    const invalidConfig = {
        name: "Broken Test",
        description: "This has issues",
        hypothesis: "This will fail validation",
        status: "invalid_status", // Invalid enum value
        variants: [
            {
                name: "Control",
                description: "Control variant",
                isControl: true,
                allocationPercentage: 60 // Doesn't sum to 100
            },
            {
                name: "Treatment",
                description: "Treatment variant",
                isControl: true, // Two controls
                allocationPercentage: 30 // Doesn't sum to 100
            }
        ],
        metrics: {
            primary: {
                name: "Revenue",
                mixpanelEvent: "Purchase",
                aggregationType: "sum"
                // Missing valueProperty for sum aggregation
            }
        }
    };

    console.log('\n❌ Invalid Configuration Example:');
    const invalidResult = validateExperiment(invalidConfig);
    console.log(`   Valid: ${invalidResult.isValid}`);
    console.log(`   Errors: ${invalidResult.errors.length}`);
    console.log(`   Warnings: ${invalidResult.warnings.length}`);
    
    if (invalidResult.errors.length > 0) {
        console.log('\n   Error Details:');
        invalidResult.errors.slice(0, 3).forEach(error => {
            console.log(`   • ${error.message}`);
        });
        if (invalidResult.errors.length > 3) {
            console.log(`   • ... and ${invalidResult.errors.length - 3} more errors`);
        }
    }
}

// Run validation if this script is executed directly
if (require.main === module) {
    main();
    
    // Uncomment to see validation demonstration
    // demonstrateValidation();
}

module.exports = {
    main,
    demonstrateValidation
};

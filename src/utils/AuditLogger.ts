import { Logger } from './Logger';

export interface AuthorizationAuditLog {
  userId: string;
  tenantId: string;
  resource: string;
  action: string;
  granted: boolean;
  reason: string;
  ipAddress: string;
  userAgent: string;
  requestId: string;
  duration: number;
  experimentId?: string;
  resourceId?: string;
  details?: any;
  timestamp?: Date;
}

export class AuditLogger {
  private database: any;
  private logger: Logger;

  constructor(database: any, logger: Logger) {
    this.database = database;
    this.logger = logger;
  }

  async logAuthorizationAttempt(log: AuthorizationAuditLog): Promise<void> {
    try {
      // For demo purposes, just log to console
      this.logger.info('Authorization attempt', {
        userId: log.userId,
        resource: log.resource,
        action: log.action,
        granted: log.granted,
        reason: log.reason,
        tenantId: log.tenantId
      });

      // In a real implementation, this would save to database
      if (this.database) {
        // Database logging would go here
      }
    } catch (error) {
      this.logger.error('Failed to log authorization attempt', { error, log });
    }
  }

  async getAuthorizationStats(tenantId: string, timeframe: 'hour' | 'day' | 'week'): Promise<any> {
    // Mock statistics for demo
    return {
      totalAttempts: 150,
      successfulAttempts: 140,
      failedAttempts: 10,
      successRate: 93.3,
      topFailureReasons: [
        { reason: 'Insufficient permissions', count: 6 },
        { reason: 'User does not own this experiment', count: 4 }
      ],
      topResources: [
        { resource: 'experiments', count: 100 },
        { resource: 'analytics', count: 30 }
      ],
      suspiciousActivity: 0
    };
  }

  async getRecentSecurityEvents(tenantId: string, limit: number): Promise<any[]> {
    // Mock security events for demo
    return [
      {
        id: 'event_1',
        type: 'unauthorized_access_attempt',
        description: 'User attempted to access admin endpoint',
        userId: 'viewer_789',
        timestamp: new Date(),
        severity: 'medium'
      }
    ];
  }
}

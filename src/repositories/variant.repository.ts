// Variant repository for managing experiment variants
import { Variant, Prisma } from '@prisma/client';
import { BaseRepositoryImpl } from './base.repository';
import { ABTestingError } from '../types';

export interface VariantCreateInput {
  experimentId: string;
  name: string;
  description?: string;
  isControl?: boolean;
  trafficWeight: number;
  configuration?: any;
}

export interface VariantUpdateInput {
  name?: string;
  description?: string;
  isControl?: boolean;
  trafficWeight?: number;
  configuration?: any;
}

export class VariantRepository extends BaseRepositoryImpl<
  Variant,
  VariantCreateInput,
  VariantUpdateInput
> {
  constructor() {
    super('Variant');
  }

  protected getDelegate() {
    return this.prisma.variant;
  }

  // Find variants by experiment
  public async findByExperiment(
    experimentId: string,
    tenantId: string
  ): Promise<Variant[]> {
    await this.validateTenant(tenantId);

    try {
      return await this.prisma.variant.findMany({
        where: {
          tenantId,
          experimentId,
        },
        orderBy: [
          { isControl: 'desc' }, // Control variant first
          { name: 'asc' },
        ],
      });
    } catch (error) {
      console.error('Error finding variants by experiment:', error);
      throw new ABTestingError(
        'Failed to find variants by experiment',
        'FIND_BY_EXPERIMENT_FAILED',
        500,
        error
      );
    }
  }

  // Find control variant for experiment
  public async findControlVariant(
    experimentId: string,
    tenantId: string
  ): Promise<Variant | null> {
    await this.validateTenant(tenantId);

    try {
      return await this.prisma.variant.findFirst({
        where: {
          tenantId,
          experimentId,
          isControl: true,
        },
      });
    } catch (error) {
      console.error('Error finding control variant:', error);
      throw new ABTestingError(
        'Failed to find control variant',
        'FIND_CONTROL_FAILED',
        500,
        error
      );
    }
  }

  // Validate traffic weights sum to 1.0 for experiment
  public async validateTrafficWeights(
    experimentId: string,
    tenantId: string,
    excludeVariantId?: string
  ): Promise<{ isValid: boolean; totalWeight: number }> {
    await this.validateTenant(tenantId);

    try {
      const where: any = {
        tenantId,
        experimentId,
      };

      if (excludeVariantId) {
        where.id = { not: excludeVariantId };
      }

      const variants = await this.prisma.variant.findMany({
        where,
        select: { trafficWeight: true },
      });

      const totalWeight = variants.reduce(
        (sum, variant) => sum + variant.trafficWeight.toNumber(),
        0
      );

      return {
        isValid: Math.abs(totalWeight - 1.0) < 0.0001,
        totalWeight,
      };
    } catch (error) {
      console.error('Error validating traffic weights:', error);
      throw new ABTestingError(
        'Failed to validate traffic weights',
        'VALIDATE_WEIGHTS_FAILED',
        500,
        error
      );
    }
  }

  // Update traffic weights for all variants in experiment
  public async updateTrafficWeights(
    experimentId: string,
    weights: Array<{ variantId: string; weight: number }>,
    tenantId: string
  ): Promise<Variant[]> {
    await this.validateTenant(tenantId);

    // Validate weights sum to 1.0
    const totalWeight = weights.reduce((sum, w) => sum + w.weight, 0);
    if (Math.abs(totalWeight - 1.0) > 0.0001) {
      throw new ABTestingError(
        `Traffic weights must sum to 1.0, got ${totalWeight}`,
        'INVALID_TRAFFIC_WEIGHTS',
        400,
        { totalWeight, weights }
      );
    }

    try {
      const updatedVariants = [];
      
      for (const { variantId, weight } of weights) {
        const variant = await this.update(
          variantId,
          { trafficWeight: weight },
          tenantId
        );
        updatedVariants.push(variant);
      }

      return updatedVariants;
    } catch (error) {
      console.error('Error updating traffic weights:', error);
      throw new ABTestingError(
        'Failed to update traffic weights',
        'UPDATE_WEIGHTS_FAILED',
        500,
        error
      );
    }
  }

  // Get variant statistics
  public async getVariantStatistics(
    variantId: string,
    tenantId: string
  ) {
    await this.validateTenant(tenantId);

    try {
      const variant = await this.prisma.variant.findFirst({
        where: { id: variantId, tenantId },
        include: {
          _count: {
            select: {
              userAssignments: true,
              events: true,
            },
          },
          experiment: {
            select: {
              id: true,
              name: true,
              status: true,
              primaryMetric: true,
            },
          },
        },
      });

      if (!variant) {
        throw new ABTestingError('Variant not found', 'VARIANT_NOT_FOUND', 404);
      }

      // Get conversion events if primary metric is defined
      let conversionRate = 0;
      let convertedUsers = 0;

      if (variant.experiment.primaryMetric) {
        const conversionEvents = await this.prisma.event.findMany({
          where: {
            tenantId,
            variantId,
            eventName: variant.experiment.primaryMetric,
          },
          select: { userId: true },
        });

        convertedUsers = new Set(conversionEvents.map(e => e.userId)).size;
        conversionRate = variant._count.userAssignments > 0 
          ? convertedUsers / variant._count.userAssignments 
          : 0;
      }

      return {
        variantId: variant.id,
        variantName: variant.name,
        experimentId: variant.experiment.id,
        experimentName: variant.experiment.name,
        isControl: variant.isControl,
        trafficWeight: variant.trafficWeight.toNumber(),
        totalAssignments: variant._count.userAssignments,
        totalEvents: variant._count.events,
        convertedUsers,
        conversionRate,
        configuration: variant.configuration,
      };
    } catch (error) {
      if (error instanceof ABTestingError) throw error;
      
      console.error('Error getting variant statistics:', error);
      throw new ABTestingError(
        'Failed to get variant statistics',
        'GET_VARIANT_STATS_FAILED',
        500,
        error
      );
    }
  }

  // Clone variant to another experiment
  public async cloneVariant(
    variantId: string,
    targetExperimentId: string,
    tenantId: string,
    newName?: string
  ): Promise<Variant> {
    await this.validateTenant(tenantId);

    try {
      const sourceVariant = await this.findById(variantId, tenantId);
      if (!sourceVariant) {
        throw new ABTestingError('Source variant not found', 'VARIANT_NOT_FOUND', 404);
      }

      return await this.create(
        {
          experimentId: targetExperimentId,
          name: newName || `${sourceVariant.name} (Copy)`,
          description: sourceVariant.description,
          isControl: false, // Cloned variants are not control by default
          trafficWeight: sourceVariant.trafficWeight.toNumber(),
          configuration: sourceVariant.configuration,
        },
        tenantId
      );
    } catch (error) {
      if (error instanceof ABTestingError) throw error;
      
      console.error('Error cloning variant:', error);
      throw new ABTestingError(
        'Failed to clone variant',
        'CLONE_VARIANT_FAILED',
        500,
        error
      );
    }
  }

  // Delete variant with validation
  public async deleteVariant(
    variantId: string,
    tenantId: string
  ): Promise<void> {
    await this.validateTenant(tenantId);

    try {
      const variant = await this.findById(variantId, tenantId);
      if (!variant) {
        throw new ABTestingError('Variant not found', 'VARIANT_NOT_FOUND', 404);
      }

      // Check if this is the only variant in the experiment
      const siblingVariants = await this.findByExperiment(
        variant.experimentId,
        tenantId
      );

      if (siblingVariants.length <= 2) {
        throw new ABTestingError(
          'Cannot delete variant. Experiment must have at least 2 variants.',
          'MINIMUM_VARIANTS_REQUIRED',
          400
        );
      }

      // Check if variant has assignments
      const assignmentCount = await this.prisma.userAssignment.count({
        where: {
          tenantId,
          variantId,
        },
      });

      if (assignmentCount > 0) {
        throw new ABTestingError(
          'Cannot delete variant with existing user assignments',
          'VARIANT_HAS_ASSIGNMENTS',
          400,
          { assignmentCount }
        );
      }

      await this.delete(variantId, tenantId);
    } catch (error) {
      if (error instanceof ABTestingError) throw error;
      
      console.error('Error deleting variant:', error);
      throw new ABTestingError(
        'Failed to delete variant',
        'DELETE_VARIANT_FAILED',
        500,
        error
      );
    }
  }

  // Rebalance traffic weights equally
  public async rebalanceTrafficWeights(
    experimentId: string,
    tenantId: string
  ): Promise<Variant[]> {
    await this.validateTenant(tenantId);

    try {
      const variants = await this.findByExperiment(experimentId, tenantId);
      
      if (variants.length === 0) {
        throw new ABTestingError('No variants found for experiment', 'NO_VARIANTS_FOUND', 404);
      }

      const equalWeight = 1.0 / variants.length;
      
      const weights = variants.map(variant => ({
        variantId: variant.id,
        weight: equalWeight,
      }));

      return await this.updateTrafficWeights(experimentId, weights, tenantId);
    } catch (error) {
      if (error instanceof ABTestingError) throw error;
      
      console.error('Error rebalancing traffic weights:', error);
      throw new ABTestingError(
        'Failed to rebalance traffic weights',
        'REBALANCE_FAILED',
        500,
        error
      );
    }
  }
}

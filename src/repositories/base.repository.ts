// Base repository with common CRUD operations and tenant isolation
import { PrismaClient, Prisma } from '@prisma/client';
import { 
  BaseRepository, 
  TenantContext, 
  PaginationOptions, 
  PaginatedResult,
  ABTestingError,
  TenantNotFoundError 
} from '../types';
import { getDatabaseClient } from '../database/client';

export abstract class BaseRepositoryImpl<T, CreateInput, UpdateInput> implements BaseRepository<T> {
  protected prisma: PrismaClient;
  protected modelName: string;

  constructor(modelName: string) {
    this.prisma = getDatabaseClient().getClient();
    this.modelName = modelName;
  }

  // Abstract method to get the Prisma delegate for the specific model
  protected abstract getDelegate(): any;

  // Validate tenant exists and is active
  protected async validateTenant(tenantId: string): Promise<void> {
    const tenant = await this.prisma.tenant.findFirst({
      where: {
        id: tenantId,
        isActive: true,
      },
    });

    if (!tenant) {
      throw new TenantNotFoundError(tenantId);
    }
  }

  // Add tenant context to where clause
  protected addTenantFilter(where: any, tenantId: string): any {
    return {
      ...where,
      tenantId,
    };
  }

  // Find by ID with tenant isolation
  public async findById(id: string, tenantId: string): Promise<T | null> {
    await this.validateTenant(tenantId);
    
    try {
      const delegate = this.getDelegate();
      return await delegate.findFirst({
        where: this.addTenantFilter({ id }, tenantId),
      });
    } catch (error) {
      console.error(`Error finding ${this.modelName} by ID:`, error);
      throw new ABTestingError(
        `Failed to find ${this.modelName}`,
        'FIND_BY_ID_FAILED',
        500,
        error
      );
    }
  }

  // Find many with tenant isolation and pagination
  public async findMany(
    tenantId: string,
    options: {
      where?: any;
      include?: any;
      orderBy?: any;
      pagination?: PaginationOptions;
    } = {}
  ): Promise<T[]> {
    await this.validateTenant(tenantId);
    
    try {
      const delegate = this.getDelegate();
      const { where = {}, include, orderBy, pagination } = options;
      
      const query: any = {
        where: this.addTenantFilter(where, tenantId),
      };

      if (include) {
        query.include = include;
      }

      if (orderBy) {
        query.orderBy = orderBy;
      }

      if (pagination) {
        const { page = 1, limit = 10 } = pagination;
        query.skip = (page - 1) * limit;
        query.take = limit;
      }

      return await delegate.findMany(query);
    } catch (error) {
      console.error(`Error finding ${this.modelName} records:`, error);
      throw new ABTestingError(
        `Failed to find ${this.modelName} records`,
        'FIND_MANY_FAILED',
        500,
        error
      );
    }
  }

  // Find many with pagination metadata
  public async findManyPaginated(
    tenantId: string,
    options: {
      where?: any;
      include?: any;
      orderBy?: any;
      pagination: PaginationOptions;
    }
  ): Promise<PaginatedResult<T>> {
    await this.validateTenant(tenantId);
    
    try {
      const { where = {}, include, orderBy, pagination } = options;
      const { page = 1, limit = 10 } = pagination;
      
      const delegate = this.getDelegate();
      const whereClause = this.addTenantFilter(where, tenantId);

      // Get total count
      const total = await delegate.count({
        where: whereClause,
      });

      // Get paginated data
      const data = await delegate.findMany({
        where: whereClause,
        include,
        orderBy,
        skip: (page - 1) * limit,
        take: limit,
      });

      const totalPages = Math.ceil(total / limit);

      return {
        data,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      };
    } catch (error) {
      console.error(`Error finding paginated ${this.modelName} records:`, error);
      throw new ABTestingError(
        `Failed to find paginated ${this.modelName} records`,
        'FIND_PAGINATED_FAILED',
        500,
        error
      );
    }
  }

  // Create with tenant isolation
  public async create(data: CreateInput, tenantId: string): Promise<T> {
    await this.validateTenant(tenantId);
    
    try {
      const delegate = this.getDelegate();
      return await delegate.create({
        data: {
          ...data,
          tenantId,
        },
      });
    } catch (error) {
      console.error(`Error creating ${this.modelName}:`, error);
      throw new ABTestingError(
        `Failed to create ${this.modelName}`,
        'CREATE_FAILED',
        500,
        error
      );
    }
  }

  // Update with tenant isolation
  public async update(id: string, data: UpdateInput, tenantId: string): Promise<T> {
    await this.validateTenant(tenantId);
    
    // Verify record exists and belongs to tenant
    const existing = await this.findById(id, tenantId);
    if (!existing) {
      throw new ABTestingError(
        `${this.modelName} not found`,
        'RECORD_NOT_FOUND',
        404
      );
    }

    try {
      const delegate = this.getDelegate();
      return await delegate.update({
        where: { id },
        data,
      });
    } catch (error) {
      console.error(`Error updating ${this.modelName}:`, error);
      throw new ABTestingError(
        `Failed to update ${this.modelName}`,
        'UPDATE_FAILED',
        500,
        error
      );
    }
  }

  // Delete with tenant isolation
  public async delete(id: string, tenantId: string): Promise<void> {
    await this.validateTenant(tenantId);
    
    // Verify record exists and belongs to tenant
    const existing = await this.findById(id, tenantId);
    if (!existing) {
      throw new ABTestingError(
        `${this.modelName} not found`,
        'RECORD_NOT_FOUND',
        404
      );
    }

    try {
      const delegate = this.getDelegate();
      await delegate.delete({
        where: { id },
      });
    } catch (error) {
      console.error(`Error deleting ${this.modelName}:`, error);
      throw new ABTestingError(
        `Failed to delete ${this.modelName}`,
        'DELETE_FAILED',
        500,
        error
      );
    }
  }

  // Soft delete (if the model supports it)
  public async softDelete(id: string, tenantId: string): Promise<T> {
    await this.validateTenant(tenantId);
    
    const existing = await this.findById(id, tenantId);
    if (!existing) {
      throw new ABTestingError(
        `${this.modelName} not found`,
        'RECORD_NOT_FOUND',
        404
      );
    }

    try {
      const delegate = this.getDelegate();
      return await delegate.update({
        where: { id },
        data: { deletedAt: new Date() },
      });
    } catch (error) {
      console.error(`Error soft deleting ${this.modelName}:`, error);
      throw new ABTestingError(
        `Failed to soft delete ${this.modelName}`,
        'SOFT_DELETE_FAILED',
        500,
        error
      );
    }
  }

  // Count records with tenant isolation
  public async count(tenantId: string, where: any = {}): Promise<number> {
    await this.validateTenant(tenantId);
    
    try {
      const delegate = this.getDelegate();
      return await delegate.count({
        where: this.addTenantFilter(where, tenantId),
      });
    } catch (error) {
      console.error(`Error counting ${this.modelName} records:`, error);
      throw new ABTestingError(
        `Failed to count ${this.modelName} records`,
        'COUNT_FAILED',
        500,
        error
      );
    }
  }

  // Check if record exists
  public async exists(id: string, tenantId: string): Promise<boolean> {
    const record = await this.findById(id, tenantId);
    return record !== null;
  }

  // Find first record matching criteria
  public async findFirst(
    tenantId: string,
    options: {
      where?: any;
      include?: any;
      orderBy?: any;
    } = {}
  ): Promise<T | null> {
    await this.validateTenant(tenantId);
    
    try {
      const delegate = this.getDelegate();
      const { where = {}, include, orderBy } = options;
      
      return await delegate.findFirst({
        where: this.addTenantFilter(where, tenantId),
        include,
        orderBy,
      });
    } catch (error) {
      console.error(`Error finding first ${this.modelName}:`, error);
      throw new ABTestingError(
        `Failed to find first ${this.modelName}`,
        'FIND_FIRST_FAILED',
        500,
        error
      );
    }
  }

  // Batch operations
  public async createMany(data: CreateInput[], tenantId: string): Promise<{ count: number }> {
    await this.validateTenant(tenantId);
    
    try {
      const delegate = this.getDelegate();
      const dataWithTenant = data.map(item => ({
        ...item,
        tenantId,
      }));
      
      return await delegate.createMany({
        data: dataWithTenant,
      });
    } catch (error) {
      console.error(`Error creating many ${this.modelName} records:`, error);
      throw new ABTestingError(
        `Failed to create many ${this.modelName} records`,
        'CREATE_MANY_FAILED',
        500,
        error
      );
    }
  }

  // Update many records
  public async updateMany(
    tenantId: string,
    where: any,
    data: Partial<UpdateInput>
  ): Promise<{ count: number }> {
    await this.validateTenant(tenantId);
    
    try {
      const delegate = this.getDelegate();
      return await delegate.updateMany({
        where: this.addTenantFilter(where, tenantId),
        data,
      });
    } catch (error) {
      console.error(`Error updating many ${this.modelName} records:`, error);
      throw new ABTestingError(
        `Failed to update many ${this.modelName} records`,
        'UPDATE_MANY_FAILED',
        500,
        error
      );
    }
  }

  // Delete many records
  public async deleteMany(tenantId: string, where: any): Promise<{ count: number }> {
    await this.validateTenant(tenantId);
    
    try {
      const delegate = this.getDelegate();
      return await delegate.deleteMany({
        where: this.addTenantFilter(where, tenantId),
      });
    } catch (error) {
      console.error(`Error deleting many ${this.modelName} records:`, error);
      throw new ABTestingError(
        `Failed to delete many ${this.modelName} records`,
        'DELETE_MANY_FAILED',
        500,
        error
      );
    }
  }
}

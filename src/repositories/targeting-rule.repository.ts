// Targeting rule repository for managing experiment targeting
import { TargetingRule, Prisma } from '@prisma/client';
import { BaseRepositoryImpl } from './base.repository';
import { ABTestingError } from '../types';

export interface TargetingRuleCreateInput {
  experimentId: string;
  name: string;
  attributeName: string;
  operator: Prisma.TargetingOperator;
  valueText?: string;
  valueNumber?: number;
  valueBoolean?: boolean;
  valueList?: string[];
  isActive?: boolean;
  priority?: number;
}

export interface TargetingRuleUpdateInput {
  name?: string;
  attributeName?: string;
  operator?: Prisma.TargetingOperator;
  valueText?: string;
  valueNumber?: number;
  valueBoolean?: boolean;
  valueList?: string[];
  isActive?: boolean;
  priority?: number;
}

export class TargetingRuleRepository extends BaseRepositoryImpl<
  TargetingRule,
  TargetingRuleCreateInput,
  TargetingRuleUpdateInput
> {
  constructor() {
    super('TargetingRule');
  }

  protected getDelegate() {
    return this.prisma.targetingRule;
  }

  // Find targeting rules by experiment
  public async findByExperiment(
    experimentId: string,
    tenantId: string,
    options: {
      activeOnly?: boolean;
      orderByPriority?: boolean;
    } = {}
  ): Promise<TargetingRule[]> {
    await this.validateTenant(tenantId);

    try {
      const where: any = {
        tenantId,
        experimentId,
      };

      if (options.activeOnly) {
        where.isActive = true;
      }

      const orderBy = options.orderByPriority 
        ? [{ priority: 'asc' as const }, { createdAt: 'asc' as const }]
        : [{ createdAt: 'asc' as const }];

      return await this.prisma.targetingRule.findMany({
        where,
        orderBy,
      });
    } catch (error) {
      console.error('Error finding targeting rules by experiment:', error);
      throw new ABTestingError(
        'Failed to find targeting rules by experiment',
        'FIND_BY_EXPERIMENT_FAILED',
        500,
        error
      );
    }
  }

  // Find active targeting rules for evaluation
  public async findActiveRulesForEvaluation(
    experimentId: string,
    tenantId: string
  ): Promise<TargetingRule[]> {
    return await this.findByExperiment(experimentId, tenantId, {
      activeOnly: true,
      orderByPriority: true,
    });
  }

  // Update rule priority
  public async updatePriority(
    ruleId: string,
    newPriority: number,
    tenantId: string
  ): Promise<TargetingRule> {
    await this.validateTenant(tenantId);

    try {
      return await this.update(ruleId, { priority: newPriority }, tenantId);
    } catch (error) {
      console.error('Error updating rule priority:', error);
      throw new ABTestingError(
        'Failed to update rule priority',
        'UPDATE_PRIORITY_FAILED',
        500,
        error
      );
    }
  }

  // Reorder rules by priority
  public async reorderRules(
    experimentId: string,
    ruleOrders: Array<{ ruleId: string; priority: number }>,
    tenantId: string
  ): Promise<TargetingRule[]> {
    await this.validateTenant(tenantId);

    try {
      const updatedRules = [];
      
      for (const { ruleId, priority } of ruleOrders) {
        const rule = await this.update(ruleId, { priority }, tenantId);
        updatedRules.push(rule);
      }

      return updatedRules;
    } catch (error) {
      console.error('Error reordering rules:', error);
      throw new ABTestingError(
        'Failed to reorder rules',
        'REORDER_RULES_FAILED',
        500,
        error
      );
    }
  }

  // Toggle rule active status
  public async toggleActive(
    ruleId: string,
    tenantId: string
  ): Promise<TargetingRule> {
    await this.validateTenant(tenantId);

    try {
      const rule = await this.findById(ruleId, tenantId);
      if (!rule) {
        throw new ABTestingError('Targeting rule not found', 'RULE_NOT_FOUND', 404);
      }

      return await this.update(ruleId, { isActive: !rule.isActive }, tenantId);
    } catch (error) {
      if (error instanceof ABTestingError) throw error;
      
      console.error('Error toggling rule active status:', error);
      throw new ABTestingError(
        'Failed to toggle rule active status',
        'TOGGLE_ACTIVE_FAILED',
        500,
        error
      );
    }
  }

  // Validate rule configuration
  public validateRuleConfiguration(rule: TargetingRuleCreateInput | TargetingRuleUpdateInput): void {
    const { operator, valueText, valueNumber, valueBoolean, valueList } = rule;

    if (!operator) return; // Skip validation if operator is not being updated

    // Check that appropriate value is provided for operator
    switch (operator) {
      case 'EQUALS':
      case 'NOT_EQUALS':
      case 'CONTAINS':
      case 'REGEX':
        if (!valueText && valueNumber === undefined && valueBoolean === undefined) {
          throw new ABTestingError(
            `Operator ${operator} requires a value`,
            'MISSING_RULE_VALUE',
            400
          );
        }
        break;

      case 'IN':
      case 'NOT_IN':
        if (!valueList || valueList.length === 0) {
          throw new ABTestingError(
            `Operator ${operator} requires a non-empty list of values`,
            'MISSING_RULE_VALUE_LIST',
            400
          );
        }
        break;

      case 'GREATER_THAN':
      case 'LESS_THAN':
        if (valueNumber === undefined) {
          throw new ABTestingError(
            `Operator ${operator} requires a numeric value`,
            'MISSING_RULE_VALUE_NUMBER',
            400
          );
        }
        break;
    }
  }

  // Create rule with validation
  public async createWithValidation(
    data: TargetingRuleCreateInput,
    tenantId: string
  ): Promise<TargetingRule> {
    this.validateRuleConfiguration(data);
    return await this.create(data, tenantId);
  }

  // Update rule with validation
  public async updateWithValidation(
    ruleId: string,
    data: TargetingRuleUpdateInput,
    tenantId: string
  ): Promise<TargetingRule> {
    this.validateRuleConfiguration(data);
    return await this.update(ruleId, data, tenantId);
  }

  // Clone rules to another experiment
  public async cloneRulesToExperiment(
    sourceExperimentId: string,
    targetExperimentId: string,
    tenantId: string
  ): Promise<TargetingRule[]> {
    await this.validateTenant(tenantId);

    try {
      const sourceRules = await this.findByExperiment(sourceExperimentId, tenantId);
      const clonedRules = [];

      for (const rule of sourceRules) {
        const clonedRule = await this.create(
          {
            experimentId: targetExperimentId,
            name: rule.name,
            attributeName: rule.attributeName,
            operator: rule.operator,
            valueText: rule.valueText,
            valueNumber: rule.valueNumber?.toNumber(),
            valueBoolean: rule.valueBoolean,
            valueList: rule.valueList,
            isActive: rule.isActive,
            priority: rule.priority,
          },
          tenantId
        );
        clonedRules.push(clonedRule);
      }

      return clonedRules;
    } catch (error) {
      console.error('Error cloning rules to experiment:', error);
      throw new ABTestingError(
        'Failed to clone rules to experiment',
        'CLONE_RULES_FAILED',
        500,
        error
      );
    }
  }

  // Get rule usage statistics
  public async getRuleUsageStats(
    ruleId: string,
    tenantId: string
  ) {
    await this.validateTenant(tenantId);

    try {
      const rule = await this.findById(ruleId, tenantId);
      if (!rule) {
        throw new ABTestingError('Targeting rule not found', 'RULE_NOT_FOUND', 404);
      }

      // Get total assignments for the experiment
      const totalAssignments = await this.prisma.userAssignment.count({
        where: {
          tenantId,
          experimentId: rule.experimentId,
        },
      });

      // Get excluded assignments (approximation of rule impact)
      const excludedAssignments = await this.prisma.userAssignment.count({
        where: {
          tenantId,
          experimentId: rule.experimentId,
          isExcluded: true,
        },
      });

      return {
        ruleId: rule.id,
        ruleName: rule.name,
        experimentId: rule.experimentId,
        isActive: rule.isActive,
        priority: rule.priority,
        totalAssignments,
        excludedAssignments,
        inclusionRate: totalAssignments > 0 
          ? (totalAssignments - excludedAssignments) / totalAssignments 
          : 0,
      };
    } catch (error) {
      if (error instanceof ABTestingError) throw error;
      
      console.error('Error getting rule usage stats:', error);
      throw new ABTestingError(
        'Failed to get rule usage stats',
        'GET_RULE_STATS_FAILED',
        500,
        error
      );
    }
  }

  // Find rules by attribute name across experiments
  public async findByAttributeName(
    attributeName: string,
    tenantId: string,
    options: {
      activeOnly?: boolean;
      limit?: number;
    } = {}
  ): Promise<TargetingRule[]> {
    await this.validateTenant(tenantId);

    try {
      const where: any = {
        tenantId,
        attributeName,
      };

      if (options.activeOnly) {
        where.isActive = true;
      }

      const query: any = {
        where,
        include: {
          experiment: {
            select: {
              id: true,
              name: true,
              status: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      };

      if (options.limit) {
        query.take = options.limit;
      }

      return await this.prisma.targetingRule.findMany(query);
    } catch (error) {
      console.error('Error finding rules by attribute name:', error);
      throw new ABTestingError(
        'Failed to find rules by attribute name',
        'FIND_BY_ATTRIBUTE_FAILED',
        500,
        error
      );
    }
  }

  // Get unique attribute names used in targeting rules
  public async getUniqueAttributeNames(tenantId: string): Promise<string[]> {
    await this.validateTenant(tenantId);

    try {
      const result = await this.prisma.targetingRule.findMany({
        where: { tenantId },
        select: { attributeName: true },
        distinct: ['attributeName'],
        orderBy: { attributeName: 'asc' },
      });

      return result.map(r => r.attributeName);
    } catch (error) {
      console.error('Error getting unique attribute names:', error);
      throw new ABTestingError(
        'Failed to get unique attribute names',
        'GET_ATTRIBUTE_NAMES_FAILED',
        500,
        error
      );
    }
  }
}

// User assignment repository for managing experiment assignments
import { UserAssignment, Prisma } from '@prisma/client';
import { BaseRepositoryImpl } from './base.repository';
import { ABTestingError } from '../types';

export interface UserAssignmentCreateInput {
  experimentId: string;
  variantId?: string;
  userId: string;
  sessionId?: string;
  assignmentTimestamp?: Date;
  bucketingKey?: string;
  userAttributes?: any;
  isExcluded?: boolean;
  exclusionReason?: string;
}

export interface UserAssignmentUpdateInput {
  variantId?: string;
  sessionId?: string;
  userAttributes?: any;
  isExcluded?: boolean;
  exclusionReason?: string;
}

export interface UserAssignmentWithRelations extends UserAssignment {
  experiment?: any;
  variant?: any;
}

export class UserAssignmentRepository extends BaseRepositoryImpl<
  UserAssignment,
  UserAssignmentCreateInput,
  UserAssignmentUpdateInput
> {
  constructor() {
    super('UserAssignment');
  }

  protected getDelegate() {
    return this.prisma.userAssignment;
  }

  // Find assignment by user and experiment
  public async findByUserAndExperiment(
    userId: string,
    experimentId: string,
    tenantId: string
  ): Promise<UserAssignmentWithRelations | null> {
    await this.validateTenant(tenantId);

    try {
      return await this.prisma.userAssignment.findFirst({
        where: {
          tenantId,
          userId,
          experimentId,
        },
        include: {
          experiment: {
            select: {
              id: true,
              name: true,
              status: true,
            },
          },
          variant: {
            select: {
              id: true,
              name: true,
              isControl: true,
              configuration: true,
            },
          },
        },
      });
    } catch (error) {
      console.error('Error finding user assignment:', error);
      throw new ABTestingError(
        'Failed to find user assignment',
        'FIND_USER_ASSIGNMENT_FAILED',
        500,
        error
      );
    }
  }

  // Find all assignments for a user
  public async findByUser(
    userId: string,
    tenantId: string,
    options: {
      includeExcluded?: boolean;
      activeOnly?: boolean;
    } = {}
  ): Promise<UserAssignmentWithRelations[]> {
    await this.validateTenant(tenantId);

    try {
      const where: any = {
        tenantId,
        userId,
      };

      if (!options.includeExcluded) {
        where.isExcluded = false;
      }

      if (options.activeOnly) {
        where.experiment = {
          status: 'ACTIVE',
        };
      }

      return await this.prisma.userAssignment.findMany({
        where,
        include: {
          experiment: {
            select: {
              id: true,
              name: true,
              status: true,
              startDate: true,
              endDate: true,
            },
          },
          variant: {
            select: {
              id: true,
              name: true,
              isControl: true,
              configuration: true,
            },
          },
        },
        orderBy: { assignmentTimestamp: 'desc' },
      });
    } catch (error) {
      console.error('Error finding user assignments:', error);
      throw new ABTestingError(
        'Failed to find user assignments',
        'FIND_USER_ASSIGNMENTS_FAILED',
        500,
        error
      );
    }
  }

  // Find assignments for an experiment
  public async findByExperiment(
    experimentId: string,
    tenantId: string,
    options: {
      variantId?: string;
      includeExcluded?: boolean;
      pagination?: { page?: number; limit?: number };
    } = {}
  ) {
    await this.validateTenant(tenantId);

    try {
      const where: any = {
        tenantId,
        experimentId,
      };

      if (options.variantId) {
        where.variantId = options.variantId;
      }

      if (!options.includeExcluded) {
        where.isExcluded = false;
      }

      return await this.findManyPaginated(tenantId, {
        where,
        include: {
          variant: {
            select: {
              id: true,
              name: true,
              isControl: true,
            },
          },
        },
        orderBy: { assignmentTimestamp: 'desc' },
        pagination: options.pagination,
      });
    } catch (error) {
      console.error('Error finding experiment assignments:', error);
      throw new ABTestingError(
        'Failed to find experiment assignments',
        'FIND_EXPERIMENT_ASSIGNMENTS_FAILED',
        500,
        error
      );
    }
  }

  // Create or update assignment (upsert)
  public async upsertAssignment(
    data: UserAssignmentCreateInput,
    tenantId: string
  ): Promise<UserAssignment> {
    await this.validateTenant(tenantId);

    try {
      return await this.prisma.userAssignment.upsert({
        where: {
          tenantId_experimentId_userId: {
            tenantId,
            experimentId: data.experimentId,
            userId: data.userId,
          },
        },
        update: {
          variantId: data.variantId,
          sessionId: data.sessionId,
          userAttributes: data.userAttributes,
          isExcluded: data.isExcluded,
          exclusionReason: data.exclusionReason,
          updatedAt: new Date(),
        },
        create: {
          ...data,
          tenantId,
        },
      });
    } catch (error) {
      console.error('Error upserting user assignment:', error);
      throw new ABTestingError(
        'Failed to upsert user assignment',
        'UPSERT_ASSIGNMENT_FAILED',
        500,
        error
      );
    }
  }

  // Exclude user from experiment
  public async excludeUser(
    userId: string,
    experimentId: string,
    reason: string,
    tenantId: string
  ): Promise<UserAssignment> {
    await this.validateTenant(tenantId);

    try {
      return await this.prisma.userAssignment.upsert({
        where: {
          tenantId_experimentId_userId: {
            tenantId,
            experimentId,
            userId,
          },
        },
        update: {
          isExcluded: true,
          exclusionReason: reason,
          variantId: null,
          updatedAt: new Date(),
        },
        create: {
          tenantId,
          experimentId,
          userId,
          isExcluded: true,
          exclusionReason: reason,
          variantId: null,
        },
      });
    } catch (error) {
      console.error('Error excluding user from experiment:', error);
      throw new ABTestingError(
        'Failed to exclude user from experiment',
        'EXCLUDE_USER_FAILED',
        500,
        error
      );
    }
  }

  // Get assignment statistics for an experiment
  public async getExperimentAssignmentStats(
    experimentId: string,
    tenantId: string
  ) {
    await this.validateTenant(tenantId);

    try {
      const stats = await this.prisma.userAssignment.groupBy({
        by: ['variantId', 'isExcluded'],
        where: {
          tenantId,
          experimentId,
        },
        _count: {
          userId: true,
        },
      });

      // Get variant details
      const variants = await this.prisma.variant.findMany({
        where: {
          tenantId,
          experimentId,
        },
        select: {
          id: true,
          name: true,
          isControl: true,
        },
      });

      // Process statistics
      const variantStats = variants.map(variant => {
        const assignedCount = stats
          .filter(s => s.variantId === variant.id && !s.isExcluded)
          .reduce((sum, s) => sum + s._count.userId, 0);

        return {
          variantId: variant.id,
          variantName: variant.name,
          isControl: variant.isControl,
          assignedUsers: assignedCount,
        };
      });

      const excludedCount = stats
        .filter(s => s.isExcluded)
        .reduce((sum, s) => sum + s._count.userId, 0);

      const totalAssigned = variantStats.reduce((sum, v) => sum + v.assignedUsers, 0);

      return {
        experimentId,
        totalAssigned,
        excludedUsers: excludedCount,
        variants: variantStats,
      };
    } catch (error) {
      console.error('Error getting assignment statistics:', error);
      throw new ABTestingError(
        'Failed to get assignment statistics',
        'GET_ASSIGNMENT_STATS_FAILED',
        500,
        error
      );
    }
  }

  // Bulk assign users to variants
  public async bulkAssign(
    assignments: Array<{
      userId: string;
      experimentId: string;
      variantId: string;
      userAttributes?: any;
      sessionId?: string;
    }>,
    tenantId: string
  ): Promise<{ count: number }> {
    await this.validateTenant(tenantId);

    try {
      const assignmentsWithTenant = assignments.map(assignment => ({
        ...assignment,
        tenantId,
        assignmentTimestamp: new Date(),
        bucketingKey: assignment.userId,
      }));

      return await this.prisma.userAssignment.createMany({
        data: assignmentsWithTenant,
        skipDuplicates: true,
      });
    } catch (error) {
      console.error('Error bulk assigning users:', error);
      throw new ABTestingError(
        'Failed to bulk assign users',
        'BULK_ASSIGN_FAILED',
        500,
        error
      );
    }
  }

  // Remove assignments for completed experiments
  public async cleanupCompletedExperiments(
    tenantId: string,
    daysOld: number = 30
  ): Promise<{ count: number }> {
    await this.validateTenant(tenantId);

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    try {
      return await this.prisma.userAssignment.deleteMany({
        where: {
          tenantId,
          experiment: {
            status: 'COMPLETED',
            endDate: { lt: cutoffDate },
          },
        },
      });
    } catch (error) {
      console.error('Error cleaning up completed experiments:', error);
      throw new ABTestingError(
        'Failed to cleanup completed experiments',
        'CLEANUP_FAILED',
        500,
        error
      );
    }
  }

  // Get user assignment history
  public async getUserAssignmentHistory(
    userId: string,
    tenantId: string,
    options: {
      limit?: number;
      includeExcluded?: boolean;
    } = {}
  ) {
    await this.validateTenant(tenantId);

    try {
      const { limit = 50, includeExcluded = false } = options;

      const where: any = {
        tenantId,
        userId,
      };

      if (!includeExcluded) {
        where.isExcluded = false;
      }

      return await this.prisma.userAssignment.findMany({
        where,
        include: {
          experiment: {
            select: {
              id: true,
              name: true,
              status: true,
              startDate: true,
              endDate: true,
            },
          },
          variant: {
            select: {
              id: true,
              name: true,
              isControl: true,
            },
          },
        },
        orderBy: { assignmentTimestamp: 'desc' },
        take: limit,
      });
    } catch (error) {
      console.error('Error getting user assignment history:', error);
      throw new ABTestingError(
        'Failed to get user assignment history',
        'GET_ASSIGNMENT_HISTORY_FAILED',
        500,
        error
      );
    }
  }
}

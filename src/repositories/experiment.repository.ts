// Experiment repository with specialized methods
import { Experiment, Prisma, ExperimentStatus } from '@prisma/client';
import { BaseRepositoryImpl } from './base.repository';
import { ExperimentFilters, ABTestingError } from '../types';

export interface ExperimentCreateInput {
  name: string;
  description?: string;
  hypothesis?: string;
  status?: ExperimentStatus;
  trafficAllocation?: number;
  assignmentMethod?: Prisma.AssignmentMethod;
  startDate?: Date;
  endDate?: Date;
  sampleSize?: number;
  confidenceLevel?: number;
  minimumDetectableEffect?: number;
  primaryMetric?: string;
  secondaryMetrics?: string[];
  tags?: string[];
  metadata?: any;
  createdBy?: string;
}

export interface ExperimentUpdateInput {
  name?: string;
  description?: string;
  hypothesis?: string;
  status?: ExperimentStatus;
  trafficAllocation?: number;
  assignmentMethod?: Prisma.AssignmentMethod;
  startDate?: Date;
  endDate?: Date;
  sampleSize?: number;
  confidenceLevel?: number;
  minimumDetectableEffect?: number;
  primaryMetric?: string;
  secondaryMetrics?: string[];
  tags?: string[];
  metadata?: any;
}

export interface ExperimentWithRelations extends Experiment {
  variants?: any[];
  targetingRules?: any[];
  userAssignments?: any[];
  _count?: {
    variants: number;
    userAssignments: number;
    events: number;
  };
}

export class ExperimentRepository extends BaseRepositoryImpl<
  Experiment,
  ExperimentCreateInput,
  ExperimentUpdateInput
> {
  constructor() {
    super('Experiment');
  }

  protected getDelegate() {
    return this.prisma.experiment;
  }

  // Find experiment with all relations
  public async findByIdWithRelations(
    id: string,
    tenantId: string
  ): Promise<ExperimentWithRelations | null> {
    await this.validateTenant(tenantId);

    try {
      return await this.prisma.experiment.findFirst({
        where: this.addTenantFilter({ id }, tenantId),
        include: {
          variants: {
            orderBy: { isControl: 'desc' },
          },
          targetingRules: {
            where: { isActive: true },
            orderBy: { priority: 'asc' },
          },
          _count: {
            select: {
              variants: true,
              userAssignments: true,
              events: true,
            },
          },
        },
      });
    } catch (error) {
      console.error('Error finding experiment with relations:', error);
      throw new ABTestingError(
        'Failed to find experiment with relations',
        'FIND_WITH_RELATIONS_FAILED',
        500,
        error
      );
    }
  }

  // Find experiments with filters
  public async findWithFilters(
    tenantId: string,
    filters: ExperimentFilters = {},
    options: {
      include?: any;
      orderBy?: any;
      pagination?: { page?: number; limit?: number };
    } = {}
  ) {
    await this.validateTenant(tenantId);

    try {
      const where: any = { tenantId };

      // Apply filters
      if (filters.status && filters.status.length > 0) {
        where.status = { in: filters.status };
      }

      if (filters.tags && filters.tags.length > 0) {
        where.tags = { hasSome: filters.tags };
      }

      if (filters.createdBy) {
        where.createdBy = filters.createdBy;
      }

      if (filters.startDateFrom || filters.startDateTo) {
        where.startDate = {};
        if (filters.startDateFrom) {
          where.startDate.gte = filters.startDateFrom;
        }
        if (filters.startDateTo) {
          where.startDate.lte = filters.startDateTo;
        }
      }

      if (filters.search) {
        where.OR = [
          { name: { contains: filters.search, mode: 'insensitive' } },
          { description: { contains: filters.search, mode: 'insensitive' } },
          { hypothesis: { contains: filters.search, mode: 'insensitive' } },
        ];
      }

      return await this.findManyPaginated(tenantId, {
        where,
        ...options,
      });
    } catch (error) {
      console.error('Error finding experiments with filters:', error);
      throw new ABTestingError(
        'Failed to find experiments with filters',
        'FIND_WITH_FILTERS_FAILED',
        500,
        error
      );
    }
  }

  // Find active experiments
  public async findActive(tenantId: string) {
    return await this.findMany(tenantId, {
      where: {
        status: ExperimentStatus.ACTIVE,
        OR: [
          { startDate: null },
          { startDate: { lte: new Date() } },
        ],
        OR: [
          { endDate: null },
          { endDate: { gte: new Date() } },
        ],
      },
      include: {
        variants: true,
        targetingRules: {
          where: { isActive: true },
          orderBy: { priority: 'asc' },
        },
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  // Find experiments by status
  public async findByStatus(tenantId: string, status: ExperimentStatus[]) {
    return await this.findMany(tenantId, {
      where: { status: { in: status } },
      include: {
        _count: {
          select: {
            variants: true,
            userAssignments: true,
          },
        },
      },
      orderBy: { updatedAt: 'desc' },
    });
  }

  // Update experiment status
  public async updateStatus(
    id: string,
    status: ExperimentStatus,
    tenantId: string
  ): Promise<Experiment> {
    await this.validateTenant(tenantId);

    // Validate status transition
    const experiment = await this.findById(id, tenantId);
    if (!experiment) {
      throw new ABTestingError('Experiment not found', 'EXPERIMENT_NOT_FOUND', 404);
    }

    this.validateStatusTransition(experiment.status, status);

    try {
      return await this.prisma.experiment.update({
        where: { id },
        data: { 
          status,
          ...(status === ExperimentStatus.ACTIVE && !experiment.startDate 
            ? { startDate: new Date() } 
            : {}),
          ...(status === ExperimentStatus.COMPLETED && !experiment.endDate 
            ? { endDate: new Date() } 
            : {}),
        },
      });
    } catch (error) {
      console.error('Error updating experiment status:', error);
      throw new ABTestingError(
        'Failed to update experiment status',
        'UPDATE_STATUS_FAILED',
        500,
        error
      );
    }
  }

  // Get experiment statistics
  public async getStatistics(id: string, tenantId: string) {
    await this.validateTenant(tenantId);

    try {
      const experiment = await this.prisma.experiment.findFirst({
        where: this.addTenantFilter({ id }, tenantId),
        include: {
          variants: {
            include: {
              _count: {
                select: {
                  userAssignments: true,
                  events: true,
                },
              },
            },
          },
          _count: {
            select: {
              userAssignments: true,
              events: true,
            },
          },
        },
      });

      if (!experiment) {
        throw new ABTestingError('Experiment not found', 'EXPERIMENT_NOT_FOUND', 404);
      }

      // Calculate additional statistics
      const totalAssignments = experiment._count.userAssignments;
      const totalEvents = experiment._count.events;
      
      const variantStats = experiment.variants.map(variant => ({
        variantId: variant.id,
        variantName: variant.name,
        isControl: variant.isControl,
        assignments: variant._count.userAssignments,
        events: variant._count.events,
        assignmentPercentage: totalAssignments > 0 
          ? (variant._count.userAssignments / totalAssignments) * 100 
          : 0,
      }));

      return {
        experimentId: experiment.id,
        experimentName: experiment.name,
        status: experiment.status,
        totalAssignments,
        totalEvents,
        variants: variantStats,
        startDate: experiment.startDate,
        endDate: experiment.endDate,
        duration: experiment.startDate && experiment.endDate
          ? Math.ceil((experiment.endDate.getTime() - experiment.startDate.getTime()) / (1000 * 60 * 60 * 24))
          : null,
      };
    } catch (error) {
      if (error instanceof ABTestingError) throw error;
      
      console.error('Error getting experiment statistics:', error);
      throw new ABTestingError(
        'Failed to get experiment statistics',
        'GET_STATISTICS_FAILED',
        500,
        error
      );
    }
  }

  // Archive old experiments
  public async archiveOldExperiments(tenantId: string, daysOld: number = 90) {
    await this.validateTenant(tenantId);

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    try {
      return await this.prisma.experiment.updateMany({
        where: {
          tenantId,
          status: ExperimentStatus.COMPLETED,
          endDate: { lt: cutoffDate },
        },
        data: { status: ExperimentStatus.ARCHIVED },
      });
    } catch (error) {
      console.error('Error archiving old experiments:', error);
      throw new ABTestingError(
        'Failed to archive old experiments',
        'ARCHIVE_FAILED',
        500,
        error
      );
    }
  }

  // Validate status transitions
  private validateStatusTransition(currentStatus: ExperimentStatus, newStatus: ExperimentStatus): void {
    const validTransitions: Record<ExperimentStatus, ExperimentStatus[]> = {
      [ExperimentStatus.DRAFT]: [ExperimentStatus.ACTIVE, ExperimentStatus.ARCHIVED],
      [ExperimentStatus.ACTIVE]: [ExperimentStatus.PAUSED, ExperimentStatus.COMPLETED],
      [ExperimentStatus.PAUSED]: [ExperimentStatus.ACTIVE, ExperimentStatus.COMPLETED],
      [ExperimentStatus.COMPLETED]: [ExperimentStatus.ARCHIVED],
      [ExperimentStatus.ARCHIVED]: [], // No transitions from archived
    };

    if (!validTransitions[currentStatus].includes(newStatus)) {
      throw new ABTestingError(
        `Invalid status transition from ${currentStatus} to ${newStatus}`,
        'INVALID_STATUS_TRANSITION',
        400,
        { currentStatus, newStatus }
      );
    }
  }

  // Find experiments that need to be automatically stopped
  public async findExperimentsToStop(tenantId: string) {
    await this.validateTenant(tenantId);

    try {
      return await this.prisma.experiment.findMany({
        where: {
          tenantId,
          status: ExperimentStatus.ACTIVE,
          endDate: { lt: new Date() },
        },
        include: {
          variants: true,
        },
      });
    } catch (error) {
      console.error('Error finding experiments to stop:', error);
      throw new ABTestingError(
        'Failed to find experiments to stop',
        'FIND_TO_STOP_FAILED',
        500,
        error
      );
    }
  }
}

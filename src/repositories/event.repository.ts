// Event repository for tracking user interactions and conversions
import { Event, Prisma } from '@prisma/client';
import { BaseRepositoryImpl } from './base.repository';
import { EventFilters, ABTestingError } from '../types';

export interface EventCreateInput {
  experimentId?: string;
  variantId?: string;
  userId: string;
  sessionId?: string;
  eventName: string;
  eventValue?: number;
  eventProperties?: any;
  timestamp?: Date;
}

export interface EventUpdateInput {
  eventValue?: number;
  eventProperties?: any;
}

export class EventRepository extends BaseRepositoryImpl<
  Event,
  EventCreateInput,
  EventUpdateInput
> {
  constructor() {
    super('Event');
  }

  protected getDelegate() {
    return this.prisma.event;
  }

  // Find events with filters
  public async findWithFilters(
    tenantId: string,
    filters: EventFilters = {},
    options: {
      include?: any;
      orderBy?: any;
      pagination?: { page?: number; limit?: number };
    } = {}
  ) {
    await this.validateTenant(tenantId);

    try {
      const where: any = { tenantId };

      // Apply filters
      if (filters.experimentId) {
        where.experimentId = filters.experimentId;
      }

      if (filters.variantId) {
        where.variantId = filters.variantId;
      }

      if (filters.eventName && filters.eventName.length > 0) {
        where.eventName = { in: filters.eventName };
      }

      if (filters.userId) {
        where.userId = filters.userId;
      }

      if (filters.timestampFrom || filters.timestampTo) {
        where.timestamp = {};
        if (filters.timestampFrom) {
          where.timestamp.gte = filters.timestampFrom;
        }
        if (filters.timestampTo) {
          where.timestamp.lte = filters.timestampTo;
        }
      }

      return await this.findManyPaginated(tenantId, {
        where,
        ...options,
      });
    } catch (error) {
      console.error('Error finding events with filters:', error);
      throw new ABTestingError(
        'Failed to find events with filters',
        'FIND_WITH_FILTERS_FAILED',
        500,
        error
      );
    }
  }

  // Find events by experiment
  public async findByExperiment(
    experimentId: string,
    tenantId: string,
    options: {
      eventName?: string;
      variantId?: string;
      startDate?: Date;
      endDate?: Date;
      pagination?: { page?: number; limit?: number };
    } = {}
  ) {
    await this.validateTenant(tenantId);

    try {
      const where: any = {
        tenantId,
        experimentId,
      };

      if (options.eventName) {
        where.eventName = options.eventName;
      }

      if (options.variantId) {
        where.variantId = options.variantId;
      }

      if (options.startDate || options.endDate) {
        where.timestamp = {};
        if (options.startDate) {
          where.timestamp.gte = options.startDate;
        }
        if (options.endDate) {
          where.timestamp.lte = options.endDate;
        }
      }

      return await this.findManyPaginated(tenantId, {
        where,
        include: {
          variant: {
            select: {
              id: true,
              name: true,
              isControl: true,
            },
          },
        },
        orderBy: { timestamp: 'desc' },
        pagination: options.pagination,
      });
    } catch (error) {
      console.error('Error finding events by experiment:', error);
      throw new ABTestingError(
        'Failed to find events by experiment',
        'FIND_BY_EXPERIMENT_FAILED',
        500,
        error
      );
    }
  }

  // Find events by user
  public async findByUser(
    userId: string,
    tenantId: string,
    options: {
      experimentId?: string;
      eventName?: string;
      startDate?: Date;
      endDate?: Date;
      limit?: number;
    } = {}
  ): Promise<Event[]> {
    await this.validateTenant(tenantId);

    try {
      const where: any = {
        tenantId,
        userId,
      };

      if (options.experimentId) {
        where.experimentId = options.experimentId;
      }

      if (options.eventName) {
        where.eventName = options.eventName;
      }

      if (options.startDate || options.endDate) {
        where.timestamp = {};
        if (options.startDate) {
          where.timestamp.gte = options.startDate;
        }
        if (options.endDate) {
          where.timestamp.lte = options.endDate;
        }
      }

      const query: any = {
        where,
        include: {
          experiment: {
            select: {
              id: true,
              name: true,
            },
          },
          variant: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: { timestamp: 'desc' },
      };

      if (options.limit) {
        query.take = options.limit;
      }

      return await this.prisma.event.findMany(query);
    } catch (error) {
      console.error('Error finding events by user:', error);
      throw new ABTestingError(
        'Failed to find events by user',
        'FIND_BY_USER_FAILED',
        500,
        error
      );
    }
  }

  // Get event statistics for experiment
  public async getExperimentEventStats(
    experimentId: string,
    tenantId: string,
    options: {
      eventName?: string;
      startDate?: Date;
      endDate?: Date;
    } = {}
  ) {
    await this.validateTenant(tenantId);

    try {
      const where: any = {
        tenantId,
        experimentId,
      };

      if (options.eventName) {
        where.eventName = options.eventName;
      }

      if (options.startDate || options.endDate) {
        where.timestamp = {};
        if (options.startDate) {
          where.timestamp.gte = options.startDate;
        }
        if (options.endDate) {
          where.timestamp.lte = options.endDate;
        }
      }

      // Get event counts by variant
      const eventsByVariant = await this.prisma.event.groupBy({
        by: ['variantId'],
        where,
        _count: {
          id: true,
          userId: true,
        },
        _sum: {
          eventValue: true,
        },
      });

      // Get unique users by variant
      const uniqueUsersByVariant = await this.prisma.event.groupBy({
        by: ['variantId'],
        where,
        _count: {
          userId: true,
        },
      });

      // Get variant details
      const variants = await this.prisma.variant.findMany({
        where: {
          tenantId,
          experimentId,
        },
        select: {
          id: true,
          name: true,
          isControl: true,
        },
      });

      // Combine statistics
      const variantStats = variants.map(variant => {
        const eventStats = eventsByVariant.find(e => e.variantId === variant.id);
        const userStats = uniqueUsersByVariant.find(u => u.variantId === variant.id);

        return {
          variantId: variant.id,
          variantName: variant.name,
          isControl: variant.isControl,
          totalEvents: eventStats?._count.id || 0,
          uniqueUsers: userStats?._count.userId || 0,
          totalValue: eventStats?._sum.eventValue || 0,
          averageValue: eventStats?._count.id 
            ? (eventStats._sum.eventValue || 0) / eventStats._count.id 
            : 0,
        };
      });

      const totalEvents = variantStats.reduce((sum, v) => sum + v.totalEvents, 0);
      const totalUniqueUsers = variantStats.reduce((sum, v) => sum + v.uniqueUsers, 0);
      const totalValue = variantStats.reduce((sum, v) => sum + v.totalValue, 0);

      return {
        experimentId,
        eventName: options.eventName,
        totalEvents,
        totalUniqueUsers,
        totalValue,
        averageValue: totalEvents > 0 ? totalValue / totalEvents : 0,
        variants: variantStats,
      };
    } catch (error) {
      console.error('Error getting experiment event stats:', error);
      throw new ABTestingError(
        'Failed to get experiment event stats',
        'GET_EVENT_STATS_FAILED',
        500,
        error
      );
    }
  }

  // Get conversion funnel data
  public async getConversionFunnel(
    experimentId: string,
    eventNames: string[],
    tenantId: string,
    options: {
      startDate?: Date;
      endDate?: Date;
    } = {}
  ) {
    await this.validateTenant(tenantId);

    try {
      const funnelData = [];

      for (let i = 0; i < eventNames.length; i++) {
        const eventName = eventNames[i];
        const stats = await this.getExperimentEventStats(
          experimentId,
          tenantId,
          {
            eventName,
            startDate: options.startDate,
            endDate: options.endDate,
          }
        );

        funnelData.push({
          step: i + 1,
          eventName,
          ...stats,
        });
      }

      return {
        experimentId,
        funnel: funnelData,
        conversionRates: funnelData.map((step, index) => {
          if (index === 0) return 1.0; // First step is 100%
          
          const previousStep = funnelData[index - 1];
          return previousStep.totalUniqueUsers > 0 
            ? step.totalUniqueUsers / previousStep.totalUniqueUsers 
            : 0;
        }),
      };
    } catch (error) {
      console.error('Error getting conversion funnel:', error);
      throw new ABTestingError(
        'Failed to get conversion funnel',
        'GET_FUNNEL_FAILED',
        500,
        error
      );
    }
  }

  // Get unique event names for tenant
  public async getUniqueEventNames(
    tenantId: string,
    options: {
      experimentId?: string;
      limit?: number;
    } = {}
  ): Promise<string[]> {
    await this.validateTenant(tenantId);

    try {
      const where: any = { tenantId };

      if (options.experimentId) {
        where.experimentId = options.experimentId;
      }

      const query: any = {
        where,
        select: { eventName: true },
        distinct: ['eventName'],
        orderBy: { eventName: 'asc' },
      };

      if (options.limit) {
        query.take = options.limit;
      }

      const result = await this.prisma.event.findMany(query);
      return result.map(r => r.eventName);
    } catch (error) {
      console.error('Error getting unique event names:', error);
      throw new ABTestingError(
        'Failed to get unique event names',
        'GET_EVENT_NAMES_FAILED',
        500,
        error
      );
    }
  }

  // Bulk create events
  public async bulkCreate(
    events: EventCreateInput[],
    tenantId: string
  ): Promise<{ count: number }> {
    await this.validateTenant(tenantId);

    try {
      const eventsWithTenant = events.map(event => ({
        ...event,
        tenantId,
        timestamp: event.timestamp || new Date(),
      }));

      return await this.prisma.event.createMany({
        data: eventsWithTenant,
      });
    } catch (error) {
      console.error('Error bulk creating events:', error);
      throw new ABTestingError(
        'Failed to bulk create events',
        'BULK_CREATE_FAILED',
        500,
        error
      );
    }
  }

  // Clean up old events
  public async cleanupOldEvents(
    tenantId: string,
    daysOld: number = 90
  ): Promise<{ count: number }> {
    await this.validateTenant(tenantId);

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    try {
      return await this.prisma.event.deleteMany({
        where: {
          tenantId,
          timestamp: { lt: cutoffDate },
        },
      });
    } catch (error) {
      console.error('Error cleaning up old events:', error);
      throw new ABTestingError(
        'Failed to cleanup old events',
        'CLEANUP_FAILED',
        500,
        error
      );
    }
  }
}

import { Request, Response, NextFunction } from 'express';
import { MockRBACService } from '../services/MockRBACService';
import { MockExperimentService } from '../services/MockExperimentService';
import { AuditLogger } from '../utils/AuditLogger';
import { Logger } from '../utils/Logger';

export interface AuthorizationOptions {
  resource: string;
  action: string;
  requireOwnership?: boolean;
  allowSameTenant?: boolean;
  allowedRoles?: string[];
  customCheck?: (context: any) => Promise<boolean>;
}

export class AuthorizationMiddleware {
  private rbacService: MockRBACService;
  private experimentService: MockExperimentService;
  private auditLogger: AuditLogger;
  private logger: Logger;

  constructor(
    rbacService: MockRBACService,
    experimentService: MockExperimentService,
    auditLogger: AuditLogger,
    logger: Logger
  ) {
    this.rbacService = rbacService;
    this.experimentService = experimentService;
    this.auditLogger = auditLogger;
    this.logger = logger;
  }

  authorize(options: AuthorizationOptions) {
    return async (req: Request, res: Response, next: NextFunction) => {
      const startTime = Date.now();
      
      try {
        const user = (req as any).auth?.user;
        
        if (!user) {
          return res.status(401).json({
            success: false,
            error: 'Authentication required',
            code: 'AUTHENTICATION_REQUIRED',
            requestId: req.headers['x-request-id']
          });
        }

        // Check RBAC permissions
        const permissionResult = await this.rbacService.checkPermission({
          userId: user.id,
          tenantId: user.tenantId,
          resource: options.resource,
          action: options.action
        });

        if (!permissionResult.granted) {
          await this.auditLogger.logAuthorizationAttempt({
            userId: user.id,
            tenantId: user.tenantId,
            resource: options.resource,
            action: options.action,
            granted: false,
            reason: permissionResult.reason,
            ipAddress: req.ip || 'unknown',
            userAgent: req.headers['user-agent'] || 'unknown',
            requestId: req.headers['x-request-id'] as string,
            duration: Date.now() - startTime
          });

          return res.status(403).json({
            success: false,
            error: permissionResult.reason,
            code: 'INSUFFICIENT_PERMISSIONS',
            requestId: req.headers['x-request-id'],
            details: {
              requiredPermission: `${options.resource}:${options.action}`,
              denyReasons: permissionResult.denyReasons
            }
          });
        }

        // Check ownership if required
        if (options.requireOwnership && req.params.id) {
          const experiment = await this.experimentService.getExperiment(req.params.id, user.tenantId);
          
          if (!experiment) {
            return res.status(404).json({
              success: false,
              error: 'Experiment not found',
              code: 'EXPERIMENT_NOT_FOUND',
              requestId: req.headers['x-request-id']
            });
          }

          const isOwner = experiment.createdBy === user.id;
          const userRoles = await this.rbacService.getUserRoles(user.id, user.tenantId);
          const isAdmin = userRoles.some(role => ['admin'].includes(role.name));

          if (!isOwner && !isAdmin) {
            await this.auditLogger.logAuthorizationAttempt({
              userId: user.id,
              tenantId: user.tenantId,
              resource: options.resource,
              action: options.action,
              granted: false,
              reason: 'User does not own this experiment',
              ipAddress: req.ip || 'unknown',
              userAgent: req.headers['user-agent'] || 'unknown',
              requestId: req.headers['x-request-id'] as string,
              duration: Date.now() - startTime,
              experimentId: req.params.id
            });

            return res.status(403).json({
              success: false,
              error: 'User does not own this experiment',
              code: 'NOT_EXPERIMENT_OWNER',
              requestId: req.headers['x-request-id'],
              details: {
                experimentId: req.params.id,
                experimentOwner: experiment.createdBy,
                requestingUser: user.id
              }
            });
          }
        }

        // Log successful authorization
        await this.auditLogger.logAuthorizationAttempt({
          userId: user.id,
          tenantId: user.tenantId,
          resource: options.resource,
          action: options.action,
          granted: true,
          reason: 'Access granted',
          ipAddress: req.ip || 'unknown',
          userAgent: req.headers['user-agent'] || 'unknown',
          requestId: req.headers['x-request-id'] as string,
          duration: Date.now() - startTime,
          experimentId: req.params.id
        });

        next();
      } catch (error) {
        this.logger.error('Authorization middleware error', { error });
        return res.status(500).json({
          success: false,
          error: 'Authorization check failed',
          code: 'AUTHORIZATION_ERROR',
          requestId: req.headers['x-request-id']
        });
      }
    };
  }

  authorizeExperiment(action: string, requireOwnership: boolean = false) {
    return this.authorize({
      resource: 'experiments',
      action,
      requireOwnership
    });
  }

  authorizeUserManagement(action: string) {
    return this.authorize({
      resource: 'users',
      action
    });
  }

  authorizeAnalytics(action: string = 'view') {
    return this.authorize({
      resource: 'analytics',
      action
    });
  }

  authorizeAdmin(resource: string = 'settings', action: string = 'read') {
    return this.authorize({
      resource,
      action
    });
  }

  authorizeMultiple(permissions: Array<{resource: string, action: string}>, requireAll: boolean = true) {
    return async (req: Request, res: Response, next: NextFunction) => {
      const user = (req as any).auth?.user;
      
      if (!user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'AUTHENTICATION_REQUIRED'
        });
      }

      const results = [];
      for (const perm of permissions) {
        const result = await this.rbacService.checkPermission({
          userId: user.id,
          tenantId: user.tenantId,
          resource: perm.resource,
          action: perm.action
        });
        results.push({ ...perm, granted: result.granted });
      }

      const grantedCount = results.filter(r => r.granted).length;
      const hasAccess = requireAll ? grantedCount === permissions.length : grantedCount > 0;

      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          error: `Access denied (${grantedCount}/${permissions.length} permissions)`,
          code: 'INSUFFICIENT_PERMISSIONS',
          details: {
            requiredPermissions: permissions,
            grantedCount,
            requireAll,
            results
          }
        });
      }

      next();
    };
  }
}

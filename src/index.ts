// Main entry point for the A/B Testing Data Access Layer
export * from './types';
export * from './database/client';

// Repositories
export { BaseRepositoryImpl } from './repositories/base.repository';
export { ExperimentRepository } from './repositories/experiment.repository';
export { VariantRepository } from './repositories/variant.repository';
export { TargetingRuleRepository } from './repositories/targeting-rule.repository';
export { UserAssignmentRepository } from './repositories/user-assignment.repository';
export { EventRepository } from './repositories/event.repository';

// Services
export { ExperimentService } from './services/experiment.service';

// Database client factory
export { createDatabaseClient, getDatabaseClient } from './database/client';

// Re-export Prisma client for direct access if needed
export { PrismaClient } from '@prisma/client';

// Version
export const VERSION = '1.0.0';

// Default export for convenience
import { ExperimentService } from './services/experiment.service';
import { createDatabaseClient, DatabaseConfig } from './database/client';

export default class ABTestingPlatform {
  private experimentService: ExperimentService;

  constructor(config: DatabaseConfig) {
    // Initialize database client
    createDatabaseClient(config);
    
    // Initialize services
    this.experimentService = new ExperimentService();
  }

  get experiments() {
    return this.experimentService;
  }

  async initialize() {
    const client = createDatabaseClient({
      url: process.env.DATABASE_URL || '',
    });
    await client.connect();
    return this;
  }

  async shutdown() {
    const client = createDatabaseClient({
      url: process.env.DATABASE_URL || '',
    });
    await client.disconnect();
  }
}

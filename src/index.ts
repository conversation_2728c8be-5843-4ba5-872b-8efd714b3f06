import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import * as dotenv from 'dotenv';
import { AuthorizationMiddleware } from './middleware/AuthorizationMiddleware';
import { MockRBACService } from './services/MockRBACService';
import { MockExperimentService } from './services/MockExperimentService';
import { AuditLogger } from './utils/AuditLogger';
import { ConsoleLogger } from './utils/Logger';

// Load environment variables
dotenv.config();

// Mock audit logger for when database is not available
class MockAuditLogger {
  constructor(private logger: any) {}

  async logAuthorizationAttempt(log: any): Promise<void> {
    this.logger.info('Authorization attempt (mock)', log);
  }

  async getAuthorizationStats(): Promise<any> {
    return {
      totalAttempts: 0,
      successfulAttempts: 0,
      failedAttempts: 0,
      successRate: 0,
      topFailureReasons: [],
      topResources: [],
      suspiciousActivity: 0
    };
  }

  async getRecentSecurityEvents(): Promise<any[]> {
    return [];
  }
}

async function createAuthorizationApp() {
  console.log('🚀 Starting Authorization Middleware Demo Server\n');

  // Initialize logger
  const logger = new ConsoleLogger();

  // For demo purposes, use mock audit logger (no database required)
  const auditLogger = new AuditLogger(null, logger);
  logger.info('Using mock audit logger - no database required for demo');

  // Initialize services
  const rbacService = new MockRBACService(logger);
  const experimentService = new MockExperimentService(logger);

  // Initialize middleware
  const authMiddleware = new AuthorizationMiddleware(
    rbacService,
    experimentService,
    auditLogger,
    logger
  );

  // Create Express app
  const app = express();

  // Security middleware
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
      },
    }
  }));

  app.use(cors({
    origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000', 'http://localhost:3001'],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Request-ID']
  }));

  // Body parsing middleware
  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true }));

  // Request ID middleware
  app.use((req, res, next) => {
    req.headers['x-request-id'] = req.headers['x-request-id'] ||
      `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    next();
  });

  // Request logging middleware
  app.use((req, res, next) => {
    logger.info('HTTP Request', {
      method: req.method,
      url: req.url,
      ip: req.ip,
      userAgent: req.headers['user-agent'],
      requestId: req.headers['x-request-id']
    });
    next();
  });

  // Mock authentication middleware
  app.use((req, res, next) => {
    const authHeader = req.headers.authorization;

    if (!authHeader) {
      return res.status(401).json({
        success: false,
        error: 'Authorization header required',
        code: 'MISSING_AUTH_HEADER',
        requestId: req.headers['x-request-id']
      });
    }

    // Mock user based on token
    const token = authHeader.replace('Bearer ', '');
    let mockUser;

    switch (token) {
      case 'admin_token':
        mockUser = {
          id: 'admin_123',
          email: '<EMAIL>',
          tenantId: 'tenant_123',
          role: 'admin'
        };
        break;
      case 'experimenter_token':
        mockUser = {
          id: 'experimenter_456',
          email: '<EMAIL>',
          tenantId: 'tenant_123',
          role: 'experimenter'
        };
        break;
      case 'viewer_token':
        mockUser = {
          id: 'viewer_789',
          email: '<EMAIL>',
          tenantId: 'tenant_123',
          role: 'viewer'
        };
        break;
      case 'other_tenant_token':
        mockUser = {
          id: 'user_999',
          email: '<EMAIL>',
          tenantId: 'tenant_999',
          role: 'experimenter'
        };
        break;
      default:
        return res.status(401).json({
          success: false,
          error: 'Invalid token',
          code: 'INVALID_TOKEN',
          requestId: req.headers['x-request-id']
        });
    }

    req.auth = { user: mockUser };
    next();
  });

  // Experiment routes with authorization
  app.get('/api/experiments',
    authMiddleware.authorizeExperiment('read'),
    async (req, res) => {
      try {
        const tenantId = req.auth?.user?.tenantId;
        const experiments = await experimentService.listExperiments({
          tenantId: tenantId!,
          page: 1,
          limit: 20
        });

        res.json({
          success: true,
          data: experiments
        });
      } catch (error) {
        logger.error('List experiments failed', { error });
        res.status(500).json({
          success: false,
          error: 'Failed to list experiments'
        });
      }
    }
  );

  app.get('/api/experiments/:id',
    authMiddleware.authorizeExperiment('read', true), // Require ownership
    async (req, res) => {
      try {
        const { id } = req.params;
        const tenantId = req.auth?.user?.tenantId;
        const experiment = await experimentService.getExperiment(id, tenantId!);

        if (!experiment) {
          return res.status(404).json({
            success: false,
            error: 'Experiment not found',
            code: 'EXPERIMENT_NOT_FOUND'
          });
        }

        res.json({
          success: true,
          data: { experiment }
        });
      } catch (error) {
        logger.error('Get experiment failed', { error });
        res.status(500).json({
          success: false,
          error: 'Failed to get experiment'
        });
      }
    }
  );

  app.post('/api/experiments',
    authMiddleware.authorizeExperiment('create'),
    async (req, res) => {
      try {
        const tenantId = req.auth?.user?.tenantId;
        const userId = req.auth?.user?.id;
        const experiment = await experimentService.createExperiment({
          ...req.body,
          tenantId,
          createdBy: userId
        });

        res.status(201).json({
          success: true,
          data: { experiment }
        });
      } catch (error) {
        logger.error('Create experiment failed', { error });
        res.status(500).json({
          success: false,
          error: 'Failed to create experiment'
        });
      }
    }
  );

  app.put('/api/experiments/:id',
    authMiddleware.authorizeExperiment('update', true), // Require ownership
    async (req, res) => {
      try {
        const { id } = req.params;
        const tenantId = req.auth?.user?.tenantId;
        const userId = req.auth?.user?.id;

        const experiment = await experimentService.updateExperiment(id, tenantId!, {
          ...req.body,
          updatedBy: userId
        });

        if (!experiment) {
          return res.status(404).json({
            success: false,
            error: 'Experiment not found',
            code: 'EXPERIMENT_NOT_FOUND'
          });
        }

        res.json({
          success: true,
          data: { experiment }
        });
      } catch (error) {
        logger.error('Update experiment failed', { error });
        res.status(500).json({
          success: false,
          error: 'Failed to update experiment'
        });
      }
    }
  );

  app.delete('/api/experiments/:id',
    authMiddleware.authorizeExperiment('delete', true), // Require ownership
    async (req, res) => {
      try {
        const { id } = req.params;
        const tenantId = req.auth?.user?.tenantId;

        const deleted = await experimentService.deleteExperiment(id, tenantId!);

        if (!deleted) {
          return res.status(404).json({
            success: false,
            error: 'Experiment not found',
            code: 'EXPERIMENT_NOT_FOUND'
          });
        }

        res.json({
          success: true,
          message: 'Experiment deleted successfully'
        });
      } catch (error) {
        logger.error('Delete experiment failed', { error });
        res.status(500).json({
          success: false,
          error: 'Failed to delete experiment'
        });
      }
    }
  );

  // Multiple permissions example
  app.post('/api/experiments/:id/publish',
    authMiddleware.authorizeMultiple([
      { resource: 'experiments', action: 'update' },
      { resource: 'experiments', action: 'publish' }
    ], true), // Require ALL permissions
    async (req, res) => {
      try {
        const { id } = req.params;
        const tenantId = req.auth?.user?.tenantId;
        const userId = req.auth?.user?.id;

        const experiment = await experimentService.publishExperiment(id, tenantId!, userId!);

        if (!experiment) {
          return res.status(404).json({
            success: false,
            error: 'Experiment not found',
            code: 'EXPERIMENT_NOT_FOUND'
          });
        }

        res.json({
          success: true,
          data: { experiment },
          message: 'Experiment published successfully'
        });
      } catch (error) {
        logger.error('Publish experiment failed', { error });
        res.status(500).json({
          success: false,
          error: 'Failed to publish experiment'
        });
      }
    }
  );

  // User management routes
  app.get('/api/users',
    authMiddleware.authorizeUserManagement('read'),
    (req, res) => {
      res.json({
        success: true,
        data: {
          users: [
            { id: 'admin_123', email: '<EMAIL>', role: 'admin' },
            { id: 'experimenter_456', email: '<EMAIL>', role: 'experimenter' },
            { id: 'viewer_789', email: '<EMAIL>', role: 'viewer' }
          ]
        }
      });
    }
  );

  app.post('/api/users/invite',
    authMiddleware.authorizeUserManagement('invite'),
    (req, res) => {
      const { email, role } = req.body;

      logger.info('User invitation sent', {
        invitedEmail: email,
        role,
        invitedBy: req.auth?.user?.id,
        tenantId: req.auth?.user?.tenantId
      });

      res.json({
        success: true,
        message: `Invitation sent to ${email} with role ${role}`
      });
    }
  );

  // Analytics routes
  app.get('/api/analytics/dashboard',
    authMiddleware.authorizeAnalytics('view'),
    (req, res) => {
      res.json({
        success: true,
        data: {
          totalExperiments: 25,
          activeExperiments: 8,
          totalConversions: 1250,
          conversionRate: 0.15
        }
      });
    }
  );

  app.get('/api/analytics/export',
    authMiddleware.authorizeAnalytics('export'),
    async (req, res) => {
      // Log analytics export
      await auditLogger.logAuthorizationAttempt({
        userId: req.auth!.user!.id,
        tenantId: req.auth!.user!.tenantId,
        resource: 'analytics',
        action: 'export',
        granted: true,
        reason: 'Analytics data exported',
        ipAddress: req.ip,
        userAgent: req.headers['user-agent'] || 'unknown',
        requestId: req.headers['x-request-id'] as string,
        duration: 0,
        details: { exportType: 'dashboard_analytics' }
      });

      res.json({
        success: true,
        data: {
          exportUrl: 'https://example.com/exports/analytics_2023.csv',
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)
        }
      });
    }
  );

  // Admin routes
  app.get('/api/admin/settings',
    authMiddleware.authorizeAdmin('settings', 'read'),
    (req, res) => {
      res.json({
        success: true,
        data: {
          tenantName: 'Example Corp',
          maxExperiments: 100,
          retentionDays: 90,
          features: ['advanced_analytics', 'custom_events', 'api_access']
        }
      });
    }
  );

  // Authorization statistics
  app.get('/api/admin/authorization-stats',
    authMiddleware.authorizeAdmin('analytics', 'view'),
    async (req, res) => {
      try {
        const { timeframe = 'day' } = req.query;
        const tenantId = req.auth?.user?.tenantId;

        const stats = await auditLogger.getAuthorizationStats(
          tenantId!,
          timeframe as 'hour' | 'day' | 'week'
        );

        res.json({
          success: true,
          data: { stats }
        });
      } catch (error) {
        logger.error('Failed to get authorization stats', { error });
        res.status(500).json({
          success: false,
          error: 'Failed to get authorization statistics'
        });
      }
    }
  );

  // Health check
  app.get('/health', (req, res) => {
    res.json({
      success: true,
      message: 'Authorization service is healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    });
  });

  // Error handling middleware
  app.use((error: any, req: any, res: any, next: any) => {
    logger.error('Unhandled error', {
      error: error.message,
      stack: error.stack,
      url: req.url,
      method: req.method,
      userId: req.auth?.user?.id,
      requestId: req.headers['x-request-id']
    });

    res.status(500).json({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR',
      requestId: req.headers['x-request-id']
    });
  });

  // 404 handler
  app.use('*', (req, res) => {
    res.status(404).json({
      success: false,
      error: 'Route not found',
      code: 'NOT_FOUND',
      requestId: req.headers['x-request-id']
    });
  });

  return { app, logger, auditLogger };
}

// Start the server
async function startServer() {
  try {
    const { app, logger } = await createAuthorizationApp();

    const PORT = process.env.PORT || 3003;

    // Start server
    app.listen(PORT, () => {
      logger.info(`🚀 Authorization demo server running on port ${PORT}`);

      console.log('\n📚 API Endpoints:');
      console.log('   GET  /health                     - Health check');
      console.log('   GET  /api/experiments            - List experiments (requires experiments:read)');
      console.log('   GET  /api/experiments/:id        - Get experiment (requires ownership)');
      console.log('   POST /api/experiments            - Create experiment (requires experiments:create)');
      console.log('   PUT  /api/experiments/:id        - Update experiment (requires ownership)');
      console.log('   DELETE /api/experiments/:id      - Delete experiment (requires ownership)');
      console.log('   POST /api/experiments/:id/publish - Publish experiment (requires multiple permissions)');
      console.log('   GET  /api/users                  - List users (requires admin role)');
      console.log('   POST /api/users/invite           - Invite user (requires admin role)');
      console.log('   GET  /api/analytics/dashboard    - View analytics (requires analytics:view)');
      console.log('   GET  /api/analytics/export       - Export analytics (requires analytics:export)');
      console.log('   GET  /api/admin/settings         - Admin settings (requires admin role)');
      console.log('   GET  /api/admin/authorization-stats - Authorization statistics');

      console.log('\n🔑 Test Tokens:');
      console.log('   admin_token           - Admin user (full access)');
      console.log('   experimenter_token    - Experimenter user (experiment management)');
      console.log('   viewer_token          - Viewer user (read-only access)');
      console.log('   other_tenant_token    - User from different tenant');

      console.log('\n🧪 Example Requests:');
      console.log('   curl -H "Authorization: Bearer admin_token" http://localhost:3003/api/experiments');
      console.log('   curl -H "Authorization: Bearer viewer_token" http://localhost:3003/api/users');
      console.log('   curl -H "Authorization: Bearer other_tenant_token" http://localhost:3003/api/experiments');
      console.log('   curl -X POST -H "Authorization: Bearer experimenter_token" -H "Content-Type: application/json" \\');
      console.log('        -d \'{"name":"Test Experiment","description":"Testing authorization"}\' \\');
      console.log('        http://localhost:3003/api/experiments');

      console.log(`\n🏥 Health Check: http://localhost:${PORT}/health`);
    });

    // Graceful shutdown
    process.on('SIGTERM', async () => {
      logger.info('SIGTERM received, shutting down gracefully');
      process.exit(0);
    });

    process.on('SIGINT', async () => {
      logger.info('SIGINT received, shutting down gracefully');
      process.exit(0);
    });

  } catch (error) {
    console.error('Failed to start authorization demo server:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  startServer();
}

export { createAuthorizationApp, startServer };

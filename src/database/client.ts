// Database client configuration and initialization
import { <PERSON>rismaClient, Prisma } from '@prisma/client';
import { DatabaseConfig, ABTestingError } from '../types';

// Singleton database client
class DatabaseClient {
  private static instance: DatabaseClient;
  private prisma: PrismaClient;
  private config: DatabaseConfig;

  private constructor(config: DatabaseConfig) {
    this.config = config;
    this.prisma = new PrismaClient({
      datasources: {
        db: {
          url: config.url,
        },
      },
      log: this.getLogConfig(config.logLevel || 'warn'),
      errorFormat: 'pretty',
    });

    // Add middleware for tenant isolation
    this.addTenantIsolationMiddleware();
    
    // Add performance monitoring middleware
    this.addPerformanceMiddleware();
    
    // Add error handling middleware
    this.addErrorHandlingMiddleware();
  }

  public static getInstance(config?: DatabaseConfig): DatabaseClient {
    if (!DatabaseClient.instance) {
      if (!config) {
        throw new ABTestingError(
          'Database configuration required for first initialization',
          'CONFIG_REQUIRED',
          500
        );
      }
      DatabaseClient.instance = new DatabaseClient(config);
    }
    return DatabaseClient.instance;
  }

  public getClient(): PrismaClient {
    return this.prisma;
  }

  public async connect(): Promise<void> {
    try {
      await this.prisma.$connect();
      console.log('Database connected successfully');
    } catch (error) {
      console.error('Failed to connect to database:', error);
      throw new ABTestingError(
        'Database connection failed',
        'CONNECTION_FAILED',
        500,
        error
      );
    }
  }

  public async disconnect(): Promise<void> {
    try {
      await this.prisma.$disconnect();
      console.log('Database disconnected successfully');
    } catch (error) {
      console.error('Failed to disconnect from database:', error);
    }
  }

  public async healthCheck(): Promise<boolean> {
    try {
      await this.prisma.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      console.error('Database health check failed:', error);
      return false;
    }
  }

  // Execute operations within a transaction
  public async transaction<T>(
    fn: (tx: Prisma.TransactionClient) => Promise<T>,
    options?: {
      maxWait?: number;
      timeout?: number;
      isolationLevel?: Prisma.TransactionIsolationLevel;
    }
  ): Promise<T> {
    try {
      return await this.prisma.$transaction(fn, {
        maxWait: options?.maxWait || 5000,
        timeout: options?.timeout || 10000,
        isolationLevel: options?.isolationLevel || Prisma.TransactionIsolationLevel.ReadCommitted,
      });
    } catch (error) {
      console.error('Transaction failed:', error);
      throw new ABTestingError(
        'Transaction execution failed',
        'TRANSACTION_FAILED',
        500,
        error
      );
    }
  }

  private getLogConfig(level: string): Prisma.LogLevel[] {
    const logLevels: Record<string, Prisma.LogLevel[]> = {
      info: ['info', 'warn', 'error'],
      query: ['query', 'info', 'warn', 'error'],
      warn: ['warn', 'error'],
      error: ['error'],
    };
    return logLevels[level] || ['warn', 'error'];
  }

  private addTenantIsolationMiddleware(): void {
    this.prisma.$use(async (params, next) => {
      // Skip tenant isolation for certain models or operations
      const skipTenantIsolation = [
        'Tenant', // Tenant model itself
      ];

      if (skipTenantIsolation.includes(params.model || '')) {
        return next(params);
      }

      // Ensure tenant isolation for all operations
      if (params.action === 'findMany' || params.action === 'findFirst') {
        if (!params.args.where) {
          params.args.where = {};
        }
        
        // Add tenant filter if not already present
        if (!params.args.where.tenantId && !params.args.where.tenant) {
          console.warn(`Query without tenant isolation detected for ${params.model}.${params.action}`);
        }
      }

      return next(params);
    });
  }

  private addPerformanceMiddleware(): void {
    this.prisma.$use(async (params, next) => {
      const start = Date.now();
      const result = await next(params);
      const end = Date.now();
      
      const duration = end - start;
      
      // Log slow queries
      if (duration > 1000) {
        console.warn(`Slow query detected: ${params.model}.${params.action} took ${duration}ms`);
      }
      
      // Add performance metrics
      if (process.env.NODE_ENV === 'development') {
        console.log(`Query: ${params.model}.${params.action} - ${duration}ms`);
      }
      
      return result;
    });
  }

  private addErrorHandlingMiddleware(): void {
    this.prisma.$use(async (params, next) => {
      try {
        return await next(params);
      } catch (error) {
        // Transform Prisma errors into application errors
        if (error instanceof Prisma.PrismaClientKnownRequestError) {
          switch (error.code) {
            case 'P2002':
              throw new ABTestingError(
                'Unique constraint violation',
                'UNIQUE_CONSTRAINT_VIOLATION',
                409,
                { field: error.meta?.target }
              );
            case 'P2025':
              throw new ABTestingError(
                'Record not found',
                'RECORD_NOT_FOUND',
                404,
                error.meta
              );
            case 'P2003':
              throw new ABTestingError(
                'Foreign key constraint violation',
                'FOREIGN_KEY_VIOLATION',
                400,
                error.meta
              );
            default:
              throw new ABTestingError(
                `Database error: ${error.message}`,
                'DATABASE_ERROR',
                500,
                { code: error.code, meta: error.meta }
              );
          }
        }
        
        if (error instanceof Prisma.PrismaClientValidationError) {
          throw new ABTestingError(
            'Invalid data provided',
            'VALIDATION_ERROR',
            400,
            { message: error.message }
          );
        }
        
        // Re-throw application errors
        if (error instanceof ABTestingError) {
          throw error;
        }
        
        // Handle unknown errors
        console.error('Unknown database error:', error);
        throw new ABTestingError(
          'An unexpected database error occurred',
          'UNKNOWN_DATABASE_ERROR',
          500,
          error
        );
      }
    });
  }

  // Utility methods for common operations
  public async executeRawQuery<T = any>(
    query: string,
    parameters?: any[]
  ): Promise<T[]> {
    try {
      return await this.prisma.$queryRawUnsafe(query, ...(parameters || []));
    } catch (error) {
      console.error('Raw query execution failed:', error);
      throw new ABTestingError(
        'Raw query execution failed',
        'RAW_QUERY_FAILED',
        500,
        error
      );
    }
  }

  public async executeRawCommand(
    command: string,
    parameters?: any[]
  ): Promise<number> {
    try {
      return await this.prisma.$executeRawUnsafe(command, ...(parameters || []));
    } catch (error) {
      console.error('Raw command execution failed:', error);
      throw new ABTestingError(
        'Raw command execution failed',
        'RAW_COMMAND_FAILED',
        500,
        error
      );
    }
  }

  // Batch operations
  public async batchExecute(operations: any[]): Promise<any[]> {
    try {
      return await this.prisma.$transaction(operations);
    } catch (error) {
      console.error('Batch execution failed:', error);
      throw new ABTestingError(
        'Batch execution failed',
        'BATCH_EXECUTION_FAILED',
        500,
        error
      );
    }
  }

  // Connection pool information
  public getConnectionInfo(): any {
    return {
      url: this.config.url.replace(/\/\/.*@/, '//***:***@'), // Hide credentials
      maxConnections: this.config.maxConnections,
      connectionTimeout: this.config.connectionTimeout,
      queryTimeout: this.config.queryTimeout,
    };
  }
}

// Factory function for creating database client
export function createDatabaseClient(config: DatabaseConfig): DatabaseClient {
  return DatabaseClient.getInstance(config);
}

// Get existing database client instance
export function getDatabaseClient(): DatabaseClient {
  return DatabaseClient.getInstance();
}

// Export the client class
export { DatabaseClient };

// Default export
export default DatabaseClient;

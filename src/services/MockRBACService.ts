import { Logger } from '../utils/Logger';

export interface AccessRequest {
  userId: string;
  tenantId: string;
  resource: string;
  action: string;
  context?: any;
}

export interface AccessResult {
  granted: boolean;
  reason: string;
  matchedPermissions: any[];
  appliedConditions: any[];
  denyReasons?: string[];
}

export class MockRBACService {
  private logger: Logger;
  
  // Mock user roles
  private userRoles = new Map<string, string[]>([
    ['admin_123', ['admin']],
    ['experimenter_456', ['experimenter']],
    ['viewer_789', ['viewer']],
    ['user_999', ['experimenter']]
  ]);

  // Mock role permissions
  private rolePermissions = new Map<string, string[]>([
    ['viewer', ['experiments:read', 'analytics:view']],
    ['experimenter', [
      'experiments:read', 'experiments:create', 'experiments:update', 'experiments:publish',
      'analytics:view', 'analytics:export'
    ]],
    ['admin', [
      'experiments:read', 'experiments:create', 'experiments:update', 'experiments:delete',
      'experiments:publish', 'experiments:archive',
      'analytics:view', 'analytics:export',
      'users:read', 'users:create', 'users:update', 'users:delete', 'users:invite',
      'settings:read', 'settings:update',
      'roles:assign', 'roles:unassign'
    ]]
  ]);

  constructor(logger: Logger) {
    this.logger = logger;
  }

  async checkPermission(request: AccessRequest): Promise<AccessResult> {
    const { userId, tenantId, resource, action } = request;
    
    try {
      // Get user roles
      const userRoles = this.userRoles.get(userId) || [];
      
      // Get all permissions for user's roles
      const userPermissions = new Set<string>();
      for (const role of userRoles) {
        const rolePerms = this.rolePermissions.get(role) || [];
        rolePerms.forEach(perm => userPermissions.add(perm));
      }

      // Check if user has the required permission
      const requiredPermission = `${resource}:${action}`;
      const hasPermission = userPermissions.has(requiredPermission);

      if (hasPermission) {
        return {
          granted: true,
          reason: `Permission granted for ${action} on ${resource}`,
          matchedPermissions: [{ resource, action, name: requiredPermission }],
          appliedConditions: []
        };
      } else {
        return {
          granted: false,
          reason: `Missing permission: ${requiredPermission}`,
          matchedPermissions: [],
          appliedConditions: [],
          denyReasons: [`User does not have permission: ${requiredPermission}`]
        };
      }

    } catch (error) {
      this.logger.error('Permission check failed', { error, userId, resource, action });
      return {
        granted: false,
        reason: 'Permission check failed due to system error',
        matchedPermissions: [],
        appliedConditions: [],
        denyReasons: ['System error during permission check']
      };
    }
  }

  async getUserRoles(userId: string, tenantId: string): Promise<any[]> {
    const userRoleNames = this.userRoles.get(userId) || [];
    
    return userRoleNames.map(roleName => ({
      id: `role_${roleName}`,
      name: roleName,
      displayName: this.capitalizeFirst(roleName),
      description: `${this.capitalizeFirst(roleName)} role`,
      permissions: this.rolePermissions.get(roleName) || []
    }));
  }

  private capitalizeFirst(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }
}

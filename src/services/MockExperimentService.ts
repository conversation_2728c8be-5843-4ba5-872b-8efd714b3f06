import { Logger } from '../utils/Logger';

export interface Experiment {
  id: string;
  name: string;
  description: string;
  type: string;
  status: string;
  tenantId: string;
  createdBy: string;
  updatedBy?: string;
  createdAt: Date;
  updatedAt: Date;
}

export class MockExperimentService {
  private logger: Logger;
  
  // Mock experiments data
  private experiments: Experiment[] = [
    {
      id: 'exp_001',
      name: 'Button Color Test',
      description: 'Testing different button colors for conversion',
      type: 'ab_test',
      status: 'active',
      tenantId: 'tenant_123',
      createdBy: 'experimenter_456',
      createdAt: new Date('2023-12-01'),
      updatedAt: new Date('2023-12-01')
    },
    {
      id: 'exp_002',
      name: 'Pricing Page Layout',
      description: 'Testing different pricing page layouts',
      type: 'multivariate',
      status: 'draft',
      tenantId: 'tenant_123',
      createdBy: 'admin_123',
      createdAt: new Date('2023-12-02'),
      updatedAt: new Date('2023-12-02')
    },
    {
      id: 'exp_003',
      name: 'Checkout Flow Optimization',
      description: 'Optimizing the checkout flow for better conversion',
      type: 'ab_test',
      status: 'paused',
      tenantId: 'tenant_123',
      createdBy: 'experimenter_456',
      createdAt: new Date('2023-12-03'),
      updatedAt: new Date('2023-12-03')
    }
  ];

  constructor(logger: Logger) {
    this.logger = logger;
  }

  async getExperiment(experimentId: string, tenantId: string): Promise<Experiment | null> {
    try {
      const experiment = this.experiments.find(exp => 
        exp.id === experimentId && exp.tenantId === tenantId
      );
      
      if (experiment) {
        this.logger.debug('Experiment found', { experimentId, tenantId });
        return experiment;
      } else {
        this.logger.debug('Experiment not found', { experimentId, tenantId });
        return null;
      }
    } catch (error) {
      this.logger.error('Failed to get experiment', { error, experimentId, tenantId });
      throw error;
    }
  }

  async listExperiments(options: {
    tenantId: string;
    userId?: string;
    page?: number;
    limit?: number;
    status?: string;
    search?: string;
  }): Promise<{
    data: Experiment[];
    total: number;
    page: number;
    limit: number;
  }> {
    try {
      let filteredExperiments = this.experiments.filter(exp => 
        exp.tenantId === options.tenantId
      );

      // Apply status filter
      if (options.status) {
        filteredExperiments = filteredExperiments.filter(exp => 
          exp.status === options.status
        );
      }

      // Apply search filter
      if (options.search) {
        const searchLower = options.search.toLowerCase();
        filteredExperiments = filteredExperiments.filter(exp => 
          exp.name.toLowerCase().includes(searchLower) ||
          exp.description.toLowerCase().includes(searchLower)
        );
      }

      const total = filteredExperiments.length;
      const page = options.page || 1;
      const limit = options.limit || 20;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;

      const data = filteredExperiments.slice(startIndex, endIndex);

      this.logger.debug('Listed experiments', {
        tenantId: options.tenantId,
        total,
        page,
        limit,
        returned: data.length
      });

      return { data, total, page, limit };
    } catch (error) {
      this.logger.error('Failed to list experiments', { error, options });
      throw error;
    }
  }

  async createExperiment(experimentData: Partial<Experiment>): Promise<Experiment> {
    try {
      const newExperiment: Experiment = {
        id: `exp_${Date.now()}`,
        name: experimentData.name || 'New Experiment',
        description: experimentData.description || '',
        type: experimentData.type || 'ab_test',
        status: 'draft',
        tenantId: experimentData.tenantId!,
        createdBy: experimentData.createdBy!,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      this.experiments.push(newExperiment);

      this.logger.info('Experiment created', {
        experimentId: newExperiment.id,
        name: newExperiment.name,
        tenantId: newExperiment.tenantId,
        createdBy: newExperiment.createdBy
      });

      return newExperiment;
    } catch (error) {
      this.logger.error('Failed to create experiment', { error, experimentData });
      throw error;
    }
  }
}

// Experiment service with business logic and orchestration
import { Experiment<PERSON>tatus, Prisma } from '@prisma/client';
import { ExperimentRepository } from '../repositories/experiment.repository';
import { VariantRepository } from '../repositories/variant.repository';
import { TargetingRuleRepository } from '../repositories/targeting-rule.repository';
import { UserAssignmentRepository } from '../repositories/user-assignment.repository';
import { EventRepository } from '../repositories/event.repository';
import { getDatabaseClient } from '../database/client';
import {
  TenantContext,
  ExperimentConfig,
  VariantConfig,
  TargetingRuleConfig,
  UserAssignmentRequest,
  UserAssignmentResult,
  EventTrackingRequest,
  ExperimentAnalytics,
  ABTestingError,
  ValidationError,
  UserAssignmentError,
} from '../types';

export class ExperimentService {
  private experimentRepo: ExperimentRepository;
  private variantRepo: VariantRepository;
  private targetingRuleRepo: TargetingRuleRepository;
  private userAssignmentRepo: UserAssignmentRepository;
  private eventRepo: EventRepository;
  private dbClient = getDatabaseClient();

  constructor() {
    this.experimentRepo = new ExperimentRepository();
    this.variantRepo = new VariantRepository();
    this.targetingRuleRepo = new TargetingRuleRepository();
    this.userAssignmentRepo = new UserAssignmentRepository();
    this.eventRepo = new EventRepository();
  }

  // Create a complete experiment with variants and targeting rules
  public async createExperiment(
    config: ExperimentConfig,
    variants: VariantConfig[],
    targetingRules: TargetingRuleConfig[] = [],
    context: TenantContext
  ) {
    // Validate experiment configuration
    this.validateExperimentConfig(config, variants);

    return await this.dbClient.transaction(async (tx) => {
      // Create experiment
      const experiment = await this.experimentRepo.create(
        {
          name: config.name,
          description: config.description,
          hypothesis: config.hypothesis,
          status: config.status || ExperimentStatus.DRAFT,
          trafficAllocation: config.trafficAllocation || 1.0,
          assignmentMethod: config.assignmentMethod || 'STICKY',
          startDate: config.startDate,
          endDate: config.endDate,
          sampleSize: config.sampleSize,
          confidenceLevel: config.confidenceLevel || 0.95,
          minimumDetectableEffect: config.minimumDetectableEffect,
          primaryMetric: config.primaryMetric,
          secondaryMetrics: config.secondaryMetrics || [],
          tags: config.tags || [],
          metadata: config.metadata || {},
          createdBy: config.createdBy,
        },
        context.tenantId
      );

      // Create variants
      const createdVariants = [];
      for (const variantConfig of variants) {
        const variant = await this.variantRepo.create(
          {
            experimentId: experiment.id,
            name: variantConfig.name,
            description: variantConfig.description,
            isControl: variantConfig.isControl || false,
            trafficWeight: variantConfig.trafficWeight,
            configuration: variantConfig.configuration || {},
          },
          context.tenantId
        );
        createdVariants.push(variant);
      }

      // Create targeting rules
      const createdRules = [];
      for (const ruleConfig of targetingRules) {
        const rule = await this.targetingRuleRepo.create(
          {
            experimentId: experiment.id,
            name: ruleConfig.name,
            attributeName: ruleConfig.attributeName,
            operator: ruleConfig.operator,
            ...this.parseTargetingValue(ruleConfig.value),
            isActive: ruleConfig.isActive !== false,
            priority: ruleConfig.priority || 0,
          },
          context.tenantId
        );
        createdRules.push(rule);
      }

      return {
        experiment,
        variants: createdVariants,
        targetingRules: createdRules,
      };
    });
  }

  // Assign user to experiment
  public async assignUserToExperiment(
    experimentId: string,
    request: UserAssignmentRequest,
    context: TenantContext
  ): Promise<UserAssignmentResult> {
    // Check if user is already assigned
    const existingAssignment = await this.userAssignmentRepo.findByUserAndExperiment(
      request.userId,
      experimentId,
      context.tenantId
    );

    if (existingAssignment) {
      if (existingAssignment.isExcluded) {
        return {
          variantId: null,
          isExcluded: true,
          exclusionReason: existingAssignment.exclusionReason || 'Previously excluded',
        };
      }

      return {
        variantId: existingAssignment.variantId,
        isExcluded: false,
        assignment: existingAssignment.variant ? {
          id: existingAssignment.id,
          experimentId: existingAssignment.experimentId,
          variantId: existingAssignment.variantId!,
          variant: {
            name: existingAssignment.variant.name,
            configuration: existingAssignment.variant.configuration,
          },
        } : undefined,
      };
    }

    // Get experiment with targeting rules and variants
    const experiment = await this.experimentRepo.findByIdWithRelations(
      experimentId,
      context.tenantId
    );

    if (!experiment) {
      throw new ABTestingError('Experiment not found', 'EXPERIMENT_NOT_FOUND', 404);
    }

    if (experiment.status !== ExperimentStatus.ACTIVE) {
      throw new UserAssignmentError('Experiment is not active', { status: experiment.status });
    }

    // Check if experiment is within date range
    const now = new Date();
    if (experiment.startDate && experiment.startDate > now) {
      throw new UserAssignmentError('Experiment has not started yet');
    }
    if (experiment.endDate && experiment.endDate < now) {
      throw new UserAssignmentError('Experiment has ended');
    }

    // Evaluate targeting rules
    const targetingResult = await this.evaluateTargetingRules(
      experiment.targetingRules || [],
      request.userAttributes || {}
    );

    if (!targetingResult.isEligible) {
      // Exclude user
      await this.userAssignmentRepo.excludeUser(
        request.userId,
        experimentId,
        targetingResult.exclusionReason || 'Failed targeting rules',
        context.tenantId
      );

      return {
        variantId: null,
        isExcluded: true,
        exclusionReason: targetingResult.exclusionReason,
      };
    }

    // Check traffic allocation
    const hash = this.generateUserHash(request.userId, experimentId);
    const bucket = (hash % 10000) / 10000;

    if (bucket >= experiment.trafficAllocation) {
      await this.userAssignmentRepo.excludeUser(
        request.userId,
        experimentId,
        'Outside traffic allocation',
        context.tenantId
      );

      return {
        variantId: null,
        isExcluded: true,
        exclusionReason: 'Outside traffic allocation',
      };
    }

    // Assign to variant based on traffic weights
    const selectedVariant = this.selectVariant(experiment.variants || [], bucket);
    if (!selectedVariant) {
      throw new UserAssignmentError('No variant could be selected');
    }

    // Create assignment
    const assignment = await this.userAssignmentRepo.create(
      {
        experimentId,
        variantId: selectedVariant.id,
        userId: request.userId,
        sessionId: request.sessionId,
        bucketingKey: request.userId,
        userAttributes: request.userAttributes || {},
        isExcluded: false,
      },
      context.tenantId
    );

    return {
      variantId: selectedVariant.id,
      isExcluded: false,
      assignment: {
        id: assignment.id,
        experimentId: assignment.experimentId,
        variantId: assignment.variantId!,
        variant: {
          name: selectedVariant.name,
          configuration: selectedVariant.configuration,
        },
      },
    };
  }

  // Track event
  public async trackEvent(
    request: EventTrackingRequest,
    context: TenantContext
  ): Promise<void> {
    // Get user's active assignments
    const assignments = await this.userAssignmentRepo.findByUser(
      request.userId,
      context.tenantId,
      { activeOnly: true, includeExcluded: false }
    );

    // Track event for each active assignment
    for (const assignment of assignments) {
      await this.eventRepo.create(
        {
          experimentId: assignment.experimentId,
          variantId: assignment.variantId!,
          userId: request.userId,
          sessionId: request.sessionId,
          eventName: request.eventName,
          eventValue: request.eventValue,
          eventProperties: request.eventProperties || {},
          timestamp: request.timestamp || new Date(),
        },
        context.tenantId
      );
    }

    // Also track general event without experiment context
    await this.eventRepo.create(
      {
        userId: request.userId,
        sessionId: request.sessionId,
        eventName: request.eventName,
        eventValue: request.eventValue,
        eventProperties: request.eventProperties || {},
        timestamp: request.timestamp || new Date(),
      },
      context.tenantId
    );
  }

  // Get experiment analytics
  public async getExperimentAnalytics(
    experimentId: string,
    context: TenantContext
  ): Promise<ExperimentAnalytics> {
    const experiment = await this.experimentRepo.findByIdWithRelations(
      experimentId,
      context.tenantId
    );

    if (!experiment) {
      throw new ABTestingError('Experiment not found', 'EXPERIMENT_NOT_FOUND', 404);
    }

    // Get assignment statistics
    const assignmentStats = await this.userAssignmentRepo.getExperimentAssignmentStats(
      experimentId,
      context.tenantId
    );

    // Get conversion metrics for each variant
    const variantAnalytics = [];
    for (const variant of experiment.variants || []) {
      const metrics = await this.calculateVariantMetrics(
        experimentId,
        variant.id,
        experiment.primaryMetric,
        context.tenantId
      );

      variantAnalytics.push({
        variantId: variant.id,
        variantName: variant.name,
        isControl: variant.isControl,
        metrics,
      });
    }

    return {
      experimentId: experiment.id,
      experimentName: experiment.name,
      status: experiment.status,
      variants: variantAnalytics,
      totalUsers: assignmentStats.totalAssigned,
      startDate: experiment.startDate,
      endDate: experiment.endDate,
    };
  }

  // Private helper methods
  private validateExperimentConfig(config: ExperimentConfig, variants: VariantConfig[]): void {
    if (!config.name || config.name.trim().length === 0) {
      throw new ValidationError('Experiment name is required');
    }

    if (variants.length < 2) {
      throw new ValidationError('At least 2 variants are required');
    }

    // Validate traffic weights sum to 1.0
    const totalWeight = variants.reduce((sum, v) => sum + v.trafficWeight, 0);
    if (Math.abs(totalWeight - 1.0) > 0.0001) {
      throw new ValidationError(`Variant traffic weights must sum to 1.0, got ${totalWeight}`);
    }

    // Validate control variant
    const controlVariants = variants.filter(v => v.isControl);
    if (controlVariants.length > 1) {
      throw new ValidationError('Only one variant can be marked as control');
    }

    // Validate date range
    if (config.startDate && config.endDate && config.startDate >= config.endDate) {
      throw new ValidationError('End date must be after start date');
    }
  }

  private parseTargetingValue(value: string | number | boolean | string[]) {
    if (typeof value === 'string') {
      return { valueText: value };
    } else if (typeof value === 'number') {
      return { valueNumber: value };
    } else if (typeof value === 'boolean') {
      return { valueBoolean: value };
    } else if (Array.isArray(value)) {
      return { valueList: value };
    }
    throw new ValidationError('Invalid targeting rule value type');
  }

  private async evaluateTargetingRules(
    rules: any[],
    userAttributes: Record<string, any>
  ): Promise<{ isEligible: boolean; exclusionReason?: string }> {
    for (const rule of rules.filter(r => r.isActive)) {
      const isMatch = this.evaluateRule(rule, userAttributes);
      if (!isMatch) {
        return {
          isEligible: false,
          exclusionReason: `Failed targeting rule: ${rule.name}`,
        };
      }
    }
    return { isEligible: true };
  }

  private evaluateRule(rule: any, userAttributes: Record<string, any>): boolean {
    const attributeValue = userAttributes[rule.attributeName];
    
    switch (rule.operator) {
      case 'EQUALS':
        return attributeValue === (rule.valueText || rule.valueNumber || rule.valueBoolean);
      case 'NOT_EQUALS':
        return attributeValue !== (rule.valueText || rule.valueNumber || rule.valueBoolean);
      case 'IN':
        return rule.valueList && rule.valueList.includes(attributeValue);
      case 'NOT_IN':
        return rule.valueList && !rule.valueList.includes(attributeValue);
      case 'GREATER_THAN':
        return typeof attributeValue === 'number' && attributeValue > rule.valueNumber;
      case 'LESS_THAN':
        return typeof attributeValue === 'number' && attributeValue < rule.valueNumber;
      case 'CONTAINS':
        return typeof attributeValue === 'string' && 
               attributeValue.toLowerCase().includes((rule.valueText || '').toLowerCase());
      default:
        return false;
    }
  }

  private generateUserHash(userId: string, experimentId: string): number {
    // Simple hash function for consistent bucketing
    const str = `${userId}:${experimentId}`;
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  private selectVariant(variants: any[], bucket: number): any {
    let cumulativeWeight = 0;
    
    // Sort variants by control first, then by name for consistency
    const sortedVariants = [...variants].sort((a, b) => {
      if (a.isControl && !b.isControl) return -1;
      if (!a.isControl && b.isControl) return 1;
      return a.name.localeCompare(b.name);
    });

    for (const variant of sortedVariants) {
      cumulativeWeight += variant.trafficWeight;
      if (bucket <= cumulativeWeight) {
        return variant;
      }
    }

    // Fallback to last variant
    return sortedVariants[sortedVariants.length - 1];
  }

  private async calculateVariantMetrics(
    experimentId: string,
    variantId: string,
    primaryMetric: string | null,
    tenantId: string
  ) {
    // Get total users assigned to this variant
    const totalUsers = await this.userAssignmentRepo.count(tenantId, {
      experimentId,
      variantId,
      isExcluded: false,
    });

    if (!primaryMetric) {
      return {
        totalUsers,
        convertedUsers: 0,
        conversionRate: 0,
      };
    }

    // Get conversion events
    const conversionEvents = await this.eventRepo.findMany(tenantId, {
      where: {
        experimentId,
        variantId,
        eventName: primaryMetric,
      },
    });

    const convertedUsers = new Set(conversionEvents.map(e => e.userId)).size;
    const conversionRate = totalUsers > 0 ? convertedUsers / totalUsers : 0;
    const totalValue = conversionEvents.reduce((sum, e) => sum + (e.eventValue || 0), 0);
    const averageValue = convertedUsers > 0 ? totalValue / convertedUsers : 0;

    return {
      totalUsers,
      convertedUsers,
      conversionRate,
      averageValue,
      totalValue,
    };
  }
}

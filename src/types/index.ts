// Type definitions for A/B Testing Platform
import { Prisma } from '@prisma/client';

// Re-export Prisma types
export * from '@prisma/client';

// Custom error types
export class ABTestingError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500,
    public details?: any
  ) {
    super(message);
    this.name = 'ABTestingError';
  }
}

export class TenantNotFoundError extends ABTestingError {
  constructor(tenantId: string) {
    super(`Tenant not found: ${tenantId}`, 'TENANT_NOT_FOUND', 404);
  }
}

export class ExperimentNotFoundError extends ABTestingError {
  constructor(experimentId: string) {
    super(`Experiment not found: ${experimentId}`, 'EXPERIMENT_NOT_FOUND', 404);
  }
}

export class VariantNotFoundError extends ABTestingError {
  constructor(variantId: string) {
    super(`Variant not found: ${variantId}`, 'VARIANT_NOT_FOUND', 404);
  }
}

export class UserAssignmentError extends ABTestingError {
  constructor(message: string, details?: any) {
    super(message, 'USER_ASSIGNMENT_ERROR', 400, details);
  }
}

export class ValidationError extends ABTestingError {
  constructor(message: string, details?: any) {
    super(message, 'VALIDATION_ERROR', 400, details);
  }
}

// Tenant context for all operations
export interface TenantContext {
  tenantId: string;
  tenantSlug?: string;
}

// User assignment types
export interface UserAssignmentRequest {
  userId: string;
  sessionId?: string;
  userAttributes?: Record<string, any>;
}

export interface UserAssignmentResult {
  variantId: string | null;
  isExcluded: boolean;
  exclusionReason?: string;
  assignment?: {
    id: string;
    experimentId: string;
    variantId: string;
    variant: {
      name: string;
      configuration: Record<string, any>;
    };
  };
}

// Event tracking types
export interface EventTrackingRequest {
  userId: string;
  sessionId?: string;
  eventName: string;
  eventValue?: number;
  eventProperties?: Record<string, any>;
  timestamp?: Date;
}

// Experiment configuration types
export interface ExperimentConfig {
  name: string;
  description?: string;
  hypothesis?: string;
  status?: Prisma.ExperimentStatus;
  trafficAllocation?: number;
  assignmentMethod?: Prisma.AssignmentMethod;
  startDate?: Date;
  endDate?: Date;
  sampleSize?: number;
  confidenceLevel?: number;
  minimumDetectableEffect?: number;
  primaryMetric?: string;
  secondaryMetrics?: string[];
  tags?: string[];
  metadata?: Record<string, any>;
  createdBy?: string;
}

export interface VariantConfig {
  name: string;
  description?: string;
  isControl?: boolean;
  trafficWeight: number;
  configuration?: Record<string, any>;
}

export interface TargetingRuleConfig {
  name: string;
  attributeName: string;
  operator: Prisma.TargetingOperator;
  value: string | number | boolean | string[];
  isActive?: boolean;
  priority?: number;
}

// Repository interfaces
export interface BaseRepository<T> {
  findById(id: string, tenantId: string): Promise<T | null>;
  findMany(tenantId: string, options?: any): Promise<T[]>;
  create(data: any, tenantId: string): Promise<T>;
  update(id: string, data: any, tenantId: string): Promise<T>;
  delete(id: string, tenantId: string): Promise<void>;
}

// Pagination types
export interface PaginationOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Filter types
export interface ExperimentFilters {
  status?: Prisma.ExperimentStatus[];
  tags?: string[];
  createdBy?: string;
  startDateFrom?: Date;
  startDateTo?: Date;
  search?: string;
}

export interface EventFilters {
  experimentId?: string;
  variantId?: string;
  eventName?: string[];
  userId?: string;
  timestampFrom?: Date;
  timestampTo?: Date;
}

// Analytics types
export interface ConversionMetrics {
  totalUsers: number;
  convertedUsers: number;
  conversionRate: number;
  averageValue?: number;
  totalValue?: number;
}

export interface ExperimentAnalytics {
  experimentId: string;
  experimentName: string;
  status: Prisma.ExperimentStatus;
  variants: Array<{
    variantId: string;
    variantName: string;
    isControl: boolean;
    metrics: ConversionMetrics;
    statisticalSignificance?: boolean;
    pValue?: number;
    confidenceInterval?: {
      lower: number;
      upper: number;
    };
  }>;
  totalUsers: number;
  startDate?: Date;
  endDate?: Date;
}

// Transaction context
export interface TransactionContext {
  tx: Prisma.TransactionClient;
}

// Service interfaces
export interface ExperimentService {
  createExperiment(
    config: ExperimentConfig,
    variants: VariantConfig[],
    targetingRules?: TargetingRuleConfig[],
    context: TenantContext
  ): Promise<any>;
  
  assignUserToExperiment(
    experimentId: string,
    request: UserAssignmentRequest,
    context: TenantContext
  ): Promise<UserAssignmentResult>;
  
  trackEvent(
    request: EventTrackingRequest,
    context: TenantContext
  ): Promise<void>;
  
  getExperimentAnalytics(
    experimentId: string,
    context: TenantContext
  ): Promise<ExperimentAnalytics>;
}

// Database transaction types
export type DatabaseTransaction = Prisma.TransactionClient;

// Configuration types
export interface DatabaseConfig {
  url: string;
  maxConnections?: number;
  connectionTimeout?: number;
  queryTimeout?: number;
  logLevel?: 'info' | 'query' | 'warn' | 'error';
}

// Audit log types
export interface AuditLogEntry {
  action: string;
  entityType: string;
  entityId: string;
  tenantId: string;
  userId?: string;
  changes?: Record<string, any>;
  timestamp: Date;
  metadata?: Record<string, any>;
}

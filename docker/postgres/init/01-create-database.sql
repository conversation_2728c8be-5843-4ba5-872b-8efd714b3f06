-- A/B Testing Platform Database Schema
-- This script creates the complete database schema for the A/B testing platform

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- <PERSON><PERSON> custom types
CREATE TYPE experiment_status AS ENUM ('draft', 'active', 'paused', 'completed', 'archived');
CREATE TYPE experiment_type AS ENUM ('ab_test', 'multivariate', 'feature_flag', 'split_test');
CREATE TYPE user_role AS ENUM ('admin', 'experimenter', 'viewer', 'analyst');
CREATE TYPE assignment_method AS ENUM ('random', 'sticky', 'deterministic');

-- Tenants table (multi-tenant support)
CREATE TABLE tenants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255),
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    role user_role NOT NULL DEFAULT 'viewer',
    is_active BOOLEAN DEFAULT true,
    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Experiments table
CREATE TABLE experiments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    hypothesis TEXT,
    type experiment_type NOT NULL DEFAULT 'ab_test',
    status experiment_status NOT NULL DEFAULT 'draft',
    primary_metric VARCHAR(100),
    secondary_metrics JSONB DEFAULT '[]',
    assignment_method assignment_method DEFAULT 'sticky',
    traffic_allocation DECIMAL(5,4) DEFAULT 1.0000,
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    created_by UUID NOT NULL REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    configuration JSONB DEFAULT '{}',
    targeting_rules JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT valid_traffic_allocation CHECK (traffic_allocation >= 0 AND traffic_allocation <= 1),
    CONSTRAINT valid_date_range CHECK (end_date IS NULL OR end_date > start_date)
);

-- Variants table
CREATE TABLE variants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    experiment_id UUID NOT NULL REFERENCES experiments(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    is_control BOOLEAN DEFAULT false,
    traffic_weight DECIMAL(5,4) NOT NULL DEFAULT 0.5000,
    configuration JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT valid_traffic_weight CHECK (traffic_weight >= 0 AND traffic_weight <= 1),
    UNIQUE(experiment_id, name)
);

-- User assignments table
CREATE TABLE user_assignments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    experiment_id UUID NOT NULL REFERENCES experiments(id) ON DELETE CASCADE,
    variant_id UUID NOT NULL REFERENCES variants(id) ON DELETE CASCADE,
    user_id VARCHAR(255) NOT NULL,
    session_id VARCHAR(255),
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_excluded BOOLEAN DEFAULT false,
    exclusion_reason TEXT,
    user_attributes JSONB DEFAULT '{}',
    
    UNIQUE(experiment_id, user_id)
);

-- Events table (for tracking conversions and metrics)
CREATE TABLE events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    experiment_id UUID REFERENCES experiments(id) ON DELETE CASCADE,
    variant_id UUID REFERENCES variants(id) ON DELETE CASCADE,
    user_id VARCHAR(255) NOT NULL,
    session_id VARCHAR(255),
    event_name VARCHAR(255) NOT NULL,
    event_value DECIMAL(15,4),
    event_properties JSONB DEFAULT '{}',
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_events_experiment_variant (experiment_id, variant_id),
    INDEX idx_events_user_session (user_id, session_id),
    INDEX idx_events_name_timestamp (event_name, timestamp)
);

-- Targeting rules table
CREATE TABLE targeting_rules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    experiment_id UUID NOT NULL REFERENCES experiments(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    attribute_name VARCHAR(100) NOT NULL,
    operator VARCHAR(50) NOT NULL,
    value JSONB NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_targeting_rules_experiment (experiment_id)
);

-- Audit log table
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(100) NOT NULL,
    resource_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_audit_logs_tenant_timestamp (tenant_id, timestamp),
    INDEX idx_audit_logs_user_timestamp (user_id, timestamp),
    INDEX idx_audit_logs_resource (resource_type, resource_id)
);

-- Authorization audit log table
CREATE TABLE authorization_audit_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id VARCHAR(255) NOT NULL,
    tenant_id VARCHAR(255) NOT NULL,
    resource VARCHAR(255) NOT NULL,
    action VARCHAR(255) NOT NULL,
    granted BOOLEAN NOT NULL,
    reason TEXT NOT NULL,
    ip_address INET,
    user_agent TEXT,
    request_id VARCHAR(255),
    duration INTEGER,
    experiment_id VARCHAR(255),
    resource_id VARCHAR(255),
    details JSONB DEFAULT '{}',
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_auth_audit_user_tenant (user_id, tenant_id),
    INDEX idx_auth_audit_timestamp (timestamp),
    INDEX idx_auth_audit_resource_action (resource, action)
);

-- Security events table
CREATE TABLE security_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id VARCHAR(255),
    tenant_id VARCHAR(255),
    event_type VARCHAR(100) NOT NULL,
    severity VARCHAR(20) NOT NULL,
    description TEXT NOT NULL,
    ip_address INET,
    user_agent TEXT,
    request_id VARCHAR(255),
    metadata JSONB DEFAULT '{}',
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_security_events_tenant (tenant_id),
    INDEX idx_security_events_timestamp (timestamp),
    INDEX idx_security_events_type_severity (event_type, severity)
);

-- Create indexes for performance
CREATE INDEX idx_experiments_tenant_status ON experiments(tenant_id, status);
CREATE INDEX idx_experiments_created_by ON experiments(created_by);
CREATE INDEX idx_experiments_dates ON experiments(start_date, end_date);

CREATE INDEX idx_variants_experiment ON variants(experiment_id);
CREATE INDEX idx_user_assignments_experiment ON user_assignments(experiment_id);
CREATE INDEX idx_user_assignments_user ON user_assignments(user_id);

CREATE INDEX idx_events_experiment_timestamp ON events(experiment_id, timestamp);
CREATE INDEX idx_events_user_timestamp ON events(user_id, timestamp);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at columns
CREATE TRIGGER update_tenants_updated_at BEFORE UPDATE ON tenants
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_experiments_updated_at BEFORE UPDATE ON experiments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_variants_updated_at BEFORE UPDATE ON variants
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create views for analytics
CREATE VIEW experiment_stats AS
SELECT 
    e.id,
    e.name,
    e.status,
    e.type,
    COUNT(DISTINCT ua.user_id) as total_users,
    COUNT(DISTINCT v.id) as variant_count,
    e.created_at,
    e.start_date,
    e.end_date
FROM experiments e
LEFT JOIN user_assignments ua ON e.id = ua.experiment_id
LEFT JOIN variants v ON e.id = v.experiment_id
GROUP BY e.id, e.name, e.status, e.type, e.created_at, e.start_date, e.end_date;

CREATE VIEW conversion_rates AS
SELECT 
    e.id as experiment_id,
    e.name as experiment_name,
    v.id as variant_id,
    v.name as variant_name,
    COUNT(DISTINCT ua.user_id) as total_users,
    COUNT(DISTINCT CASE WHEN ev.event_name = e.primary_metric THEN ev.user_id END) as conversions,
    CASE 
        WHEN COUNT(DISTINCT ua.user_id) > 0 
        THEN ROUND(COUNT(DISTINCT CASE WHEN ev.event_name = e.primary_metric THEN ev.user_id END)::DECIMAL / COUNT(DISTINCT ua.user_id) * 100, 2)
        ELSE 0 
    END as conversion_rate
FROM experiments e
JOIN variants v ON e.id = v.experiment_id
LEFT JOIN user_assignments ua ON v.id = ua.variant_id
LEFT JOIN events ev ON ua.user_id = ev.user_id AND e.id = ev.experiment_id
GROUP BY e.id, e.name, v.id, v.name, e.primary_metric;

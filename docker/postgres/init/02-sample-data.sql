-- Sample data for A/B Testing Platform
-- This script loads sample data for testing and demonstration

-- Insert sample tenants
INSERT INTO tenants (id, name, slug, settings) VALUES
('550e8400-e29b-41d4-a716-446655440001', 'Acme Corporation', 'acme-corp', '{"features": ["advanced_analytics", "custom_events"], "plan": "enterprise"}'),
('550e8400-e29b-41d4-a716-446655440002', 'Beta Company', 'beta-company', '{"features": ["basic_analytics"], "plan": "starter"}'),
('550e8400-e29b-41d4-a716-446655440003', 'Gamma Industries', 'gamma-industries', '{"features": ["advanced_analytics", "api_access"], "plan": "professional"}');

-- Insert sample users
INSERT INTO users (id, tenant_id, email, password_hash, first_name, last_name, role, is_active) VALUES
-- Acme Corporation users
('550e8400-e29b-41d4-a716-446655440011', '550e8400-e29b-41d4-a716-446655440001', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qK', 'John', 'Admin', 'admin', true),
('550e8400-e29b-41d4-a716-446655440012', '550e8400-e29b-41d4-a716-446655440001', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qK', 'Jane', 'Experimenter', 'experimenter', true),
('550e8400-e29b-41d4-a716-446655440013', '550e8400-e29b-41d4-a716-446655440001', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qK', 'Bob', 'Viewer', 'viewer', true),
('550e8400-e29b-41d4-a716-446655440014', '550e8400-e29b-41d4-a716-446655440001', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qK', 'Alice', 'Analyst', 'analyst', true),

-- Beta Company users
('550e8400-e29b-41d4-a716-446655440021', '550e8400-e29b-41d4-a716-446655440002', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qK', 'Charlie', 'Beta', 'admin', true),
('550e8400-e29b-41d4-a716-446655440022', '550e8400-e29b-41d4-a716-446655440002', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qK', 'Diana', 'Beta', 'experimenter', true);

-- Insert sample experiments
INSERT INTO experiments (id, tenant_id, name, description, hypothesis, type, status, primary_metric, assignment_method, traffic_allocation, start_date, end_date, created_by, configuration) VALUES
-- Acme Corporation experiments
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440001', 'Button Color Test', 'Testing different button colors for conversion optimization', 'Red buttons will increase click-through rate by 15%', 'ab_test', 'active', 'button_click', 'sticky', 1.0000, '2024-01-01 00:00:00+00', '2024-02-01 00:00:00+00', '550e8400-e29b-41d4-a716-446655440012', '{"page": "homepage", "element": "cta_button"}'),

('550e8400-e29b-41d4-a716-446655440102', '550e8400-e29b-41d4-a716-446655440001', 'Pricing Page Layout', 'Testing different pricing page layouts', 'Grid layout will improve conversion rate by 20%', 'multivariate', 'draft', 'purchase', 'deterministic', 0.8000, NULL, NULL, '550e8400-e29b-41d4-a716-446655440012', '{"page": "pricing", "variations": ["grid", "list", "cards"]}'),

('550e8400-e29b-41d4-a716-446655440103', '550e8400-e29b-41d4-a716-446655440001', 'Checkout Flow Optimization', 'Streamlining the checkout process', 'Single-page checkout will reduce abandonment by 25%', 'ab_test', 'paused', 'checkout_complete', 'sticky', 0.5000, '2024-01-15 00:00:00+00', '2024-03-15 00:00:00+00', '550e8400-e29b-41d4-a716-446655440011', '{"flow": "checkout", "steps": [1, 3]}'),

('550e8400-e29b-41d4-a716-446655440104', '550e8400-e29b-41d4-a716-446655440001', 'Email Subject Line Test', 'Testing different email subject lines', 'Personalized subject lines will increase open rates by 30%', 'ab_test', 'completed', 'email_open', 'random', 1.0000, '2023-12-01 00:00:00+00', '2023-12-31 00:00:00+00', '550e8400-e29b-41d4-a716-446655440012', '{"channel": "email", "campaign": "newsletter"}'),

-- Beta Company experiments
('550e8400-e29b-41d4-a716-446655440201', '550e8400-e29b-41d4-a716-446655440002', 'Landing Page Hero', 'Testing different hero sections', 'Video hero will increase engagement by 40%', 'ab_test', 'active', 'page_engagement', 'sticky', 1.0000, '2024-01-10 00:00:00+00', '2024-02-10 00:00:00+00', '550e8400-e29b-41d4-a716-446655440022', '{"page": "landing", "element": "hero"}');

-- Insert sample variants
INSERT INTO variants (id, experiment_id, name, description, is_control, traffic_weight, configuration) VALUES
-- Button Color Test variants
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Control (Blue)', 'Original blue button', true, 0.5000, '{"color": "#007bff", "text": "Get Started"}'),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Red Button', 'Red variant button', false, 0.5000, '{"color": "#dc3545", "text": "Get Started"}'),

-- Pricing Page Layout variants
('550e8400-e29b-41d4-a716-446655440303', '550e8400-e29b-41d4-a716-446655440102', 'Current Layout', 'Existing pricing layout', true, 0.3333, '{"layout": "list", "features": "sidebar"}'),
('550e8400-e29b-41d4-a716-446655440304', '550e8400-e29b-41d4-a716-446655440102', 'Grid Layout', 'Grid-based pricing layout', false, 0.3333, '{"layout": "grid", "features": "inline"}'),
('550e8400-e29b-41d4-a716-446655440305', '550e8400-e29b-41d4-a716-446655440102', 'Card Layout', 'Card-based pricing layout', false, 0.3334, '{"layout": "cards", "features": "popup"}'),

-- Checkout Flow variants
('550e8400-e29b-41d4-a716-446655440306', '550e8400-e29b-41d4-a716-446655440103', 'Multi-step Checkout', 'Current multi-step checkout', true, 0.5000, '{"steps": 3, "progress_bar": true}'),
('550e8400-e29b-41d4-a716-446655440307', '550e8400-e29b-41d4-a716-446655440103', 'Single-page Checkout', 'Streamlined single-page checkout', false, 0.5000, '{"steps": 1, "progress_bar": false}'),

-- Email Subject Line variants
('550e8400-e29b-41d4-a716-446655440308', '550e8400-e29b-41d4-a716-446655440104', 'Generic Subject', 'Standard subject line', true, 0.5000, '{"subject": "Weekly Newsletter"}'),
('550e8400-e29b-41d4-a716-446655440309', '550e8400-e29b-41d4-a716-446655440104', 'Personalized Subject', 'Personalized subject line', false, 0.5000, '{"subject": "{{first_name}}, Your Weekly Update"}'),

-- Landing Page Hero variants
('550e8400-e29b-41d4-a716-446655440310', '550e8400-e29b-41d4-a716-446655440201', 'Image Hero', 'Static image hero section', true, 0.5000, '{"type": "image", "src": "hero-image.jpg"}'),
('550e8400-e29b-41d4-a716-446655440311', '550e8400-e29b-41d4-a716-446655440201', 'Video Hero', 'Video background hero section', false, 0.5000, '{"type": "video", "src": "hero-video.mp4"}');

-- Insert sample targeting rules
INSERT INTO targeting_rules (experiment_id, name, attribute_name, operator, value, is_active) VALUES
('550e8400-e29b-41d4-a716-************', 'US Users Only', 'country', 'equals', '"US"', true),
('550e8400-e29b-41d4-a716-************', 'Desktop Users', 'device_type', 'equals', '"desktop"', true),
('550e8400-e29b-41d4-a716-446655440102', 'Premium Users', 'user_tier', 'in', '["premium", "enterprise"]', true),
('550e8400-e29b-41d4-a716-446655440103', 'Returning Users', 'visit_count', 'greater_than', '1', true),
('550e8400-e29b-41d4-a716-446655440201', 'Mobile Users', 'device_type', 'equals', '"mobile"', true);

-- Insert sample user assignments
INSERT INTO user_assignments (experiment_id, variant_id, user_id, session_id, assigned_at, user_attributes) VALUES
-- Button Color Test assignments
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'user_001', 'session_001', '2024-01-02 10:00:00+00', '{"country": "US", "device_type": "desktop"}'),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'user_002', 'session_002', '2024-01-02 11:00:00+00', '{"country": "US", "device_type": "desktop"}'),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'user_003', 'session_003', '2024-01-02 12:00:00+00', '{"country": "US", "device_type": "mobile"}'),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'user_004', 'session_004', '2024-01-02 13:00:00+00', '{"country": "US", "device_type": "desktop"}'),

-- Landing Page Hero assignments
('550e8400-e29b-41d4-a716-446655440201', '550e8400-e29b-41d4-a716-446655440310', 'user_101', 'session_101', '2024-01-11 09:00:00+00', '{"country": "CA", "device_type": "mobile"}'),
('550e8400-e29b-41d4-a716-446655440201', '550e8400-e29b-41d4-a716-446655440311', 'user_102', 'session_102', '2024-01-11 10:00:00+00', '{"country": "CA", "device_type": "mobile"}');

-- Insert sample events
INSERT INTO events (experiment_id, variant_id, user_id, session_id, event_name, event_value, event_properties, timestamp) VALUES
-- Button Color Test events
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'user_001', 'session_001', 'button_click', 1, '{"button_color": "blue", "page": "homepage"}', '2024-01-02 10:05:00+00'),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'user_002', 'session_002', 'button_click', 1, '{"button_color": "red", "page": "homepage"}', '2024-01-02 11:03:00+00'),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'user_004', 'session_004', 'button_click', 1, '{"button_color": "red", "page": "homepage"}', '2024-01-02 13:07:00+00'),

-- Page engagement events
('550e8400-e29b-41d4-a716-446655440201', '550e8400-e29b-41d4-a716-446655440310', 'user_101', 'session_101', 'page_engagement', 45.5, '{"time_on_page": 45.5, "scroll_depth": 0.8}', '2024-01-11 09:02:00+00'),
('550e8400-e29b-41d4-a716-446655440201', '550e8400-e29b-41d4-a716-446655440311', 'user_102', 'session_102', 'page_engagement', 67.2, '{"time_on_page": 67.2, "scroll_depth": 0.95}', '2024-01-11 10:03:00+00'),

-- Additional tracking events
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'user_001', 'session_001', 'page_view', 1, '{"page": "homepage", "referrer": "google"}', '2024-01-02 10:00:00+00'),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'user_002', 'session_002', 'page_view', 1, '{"page": "homepage", "referrer": "direct"}', '2024-01-02 11:00:00+00'),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'user_003', 'session_003', 'page_view', 1, '{"page": "homepage", "referrer": "facebook"}', '2024-01-02 12:00:00+00'),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'user_004', 'session_004', 'page_view', 1, '{"page": "homepage", "referrer": "twitter"}', '2024-01-02 13:00:00+00');

-- Insert sample audit logs
INSERT INTO audit_logs (tenant_id, user_id, action, resource_type, resource_id, new_values, ip_address, user_agent, timestamp) VALUES
('550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440012', 'CREATE', 'experiment', '550e8400-e29b-41d4-a716-************', '{"name": "Button Color Test", "status": "draft"}', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2024-01-01 09:00:00+00'),
('550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440012', 'UPDATE', 'experiment', '550e8400-e29b-41d4-a716-************', '{"status": "active", "start_date": "2024-01-01T00:00:00Z"}', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2024-01-01 10:00:00+00'),
('550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440011', 'UPDATE', 'experiment', '550e8400-e29b-41d4-a716-446655440103', '{"status": "paused"}', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', '2024-01-20 15:30:00+00');

-- Insert sample authorization audit logs
INSERT INTO authorization_audit_log (user_id, tenant_id, resource, action, granted, reason, ip_address, user_agent, request_id, duration, details, timestamp) VALUES
('admin_123', 'tenant_123', 'experiments', 'read', true, 'Permission granted for read on experiments', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'req_001', 15, '{"endpoint": "/api/experiments"}', '2024-01-02 10:00:00+00'),
('viewer_789', 'tenant_123', 'users', 'read', false, 'Missing permission: users:read', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', 'req_002', 8, '{"endpoint": "/api/users"}', '2024-01-02 10:05:00+00'),
('experimenter_456', 'tenant_123', 'experiments', 'create', true, 'Permission granted for create on experiments', '*************', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36', 'req_003', 25, '{"endpoint": "/api/experiments"}', '2024-01-02 10:10:00+00');

-- Insert sample security events
INSERT INTO security_events (user_id, tenant_id, event_type, severity, description, ip_address, user_agent, request_id, metadata, timestamp) VALUES
('viewer_789', 'tenant_123', 'unauthorized_access_attempt', 'medium', 'User attempted to access admin endpoint', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', 'req_002', '{"endpoint": "/api/users", "required_permission": "users:read"}', '2024-01-02 10:05:00+00'),
('unknown_user', 'tenant_123', 'invalid_token', 'high', 'Invalid authentication token used', '*************', 'curl/7.68.0', 'req_004', '{"token_prefix": "invalid_", "endpoint": "/api/experiments"}', '2024-01-02 11:00:00+00');

-- Update statistics for better demo data
UPDATE experiments SET 
    updated_at = CURRENT_TIMESTAMP,
    configuration = configuration || '{"sample_size": 1000, "confidence_level": 0.95}'::jsonb
WHERE status = 'active';

-- Create some additional sample data for better visualization
DO $$
DECLARE
    i INTEGER;
    exp_id UUID;
    var_id UUID;
    user_prefix TEXT;
BEGIN
    -- Add more user assignments and events for the active button color test
    exp_id := '550e8400-e29b-41d4-a716-************';
    
    FOR i IN 5..100 LOOP
        user_prefix := 'user_' || LPAD(i::TEXT, 3, '0');
        
        -- Assign users to variants (roughly 50/50 split)
        IF i % 2 = 0 THEN
            var_id := '550e8400-e29b-41d4-a716-************'; -- Control
        ELSE
            var_id := '550e8400-e29b-41d4-a716-************'; -- Red button
        END IF;
        
        -- Insert user assignment
        INSERT INTO user_assignments (experiment_id, variant_id, user_id, session_id, assigned_at, user_attributes)
        VALUES (
            exp_id,
            var_id,
            user_prefix,
            'session_' || LPAD(i::TEXT, 3, '0'),
            '2024-01-02 00:00:00+00'::timestamp + (i || ' hours')::interval,
            '{"country": "US", "device_type": "desktop"}'::jsonb
        );
        
        -- Insert page view event
        INSERT INTO events (experiment_id, variant_id, user_id, session_id, event_name, event_value, event_properties, timestamp)
        VALUES (
            exp_id,
            var_id,
            user_prefix,
            'session_' || LPAD(i::TEXT, 3, '0'),
            'page_view',
            1,
            '{"page": "homepage"}'::jsonb,
            '2024-01-02 00:00:00+00'::timestamp + (i || ' hours')::interval
        );
        
        -- Insert button click event (conversion) for some users
        -- Red button has slightly higher conversion rate (18% vs 15%)
        IF (var_id = '550e8400-e29b-41d4-a716-************' AND i % 6 = 0) OR 
           (var_id = '550e8400-e29b-41d4-a716-************' AND i % 7 = 0) THEN
            INSERT INTO events (experiment_id, variant_id, user_id, session_id, event_name, event_value, event_properties, timestamp)
            VALUES (
                exp_id,
                var_id,
                user_prefix,
                'session_' || LPAD(i::TEXT, 3, '0'),
                'button_click',
                1,
                CASE 
                    WHEN var_id = '550e8400-e29b-41d4-a716-************' THEN '{"button_color": "blue"}'::jsonb
                    ELSE '{"button_color": "red"}'::jsonb
                END,
                '2024-01-02 00:00:00+00'::timestamp + (i || ' hours')::interval + '5 minutes'::interval
            );
        END IF;
    END LOOP;
END $$;

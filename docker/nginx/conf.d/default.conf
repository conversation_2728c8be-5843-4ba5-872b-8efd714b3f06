# Additional server configuration for A/B Testing Platform

# SSL/HTTPS configuration (uncomment when SSL certificates are available)
# server {
#     listen 443 ssl http2;
#     server_name localhost;
#     
#     ssl_certificate /etc/nginx/ssl/cert.pem;
#     ssl_certificate_key /etc/nginx/ssl/key.pem;
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
#     ssl_prefer_server_ciphers off;
#     ssl_session_cache shared:SSL:10m;
#     ssl_session_timeout 10m;
#     
#     # Include the same location blocks as the HTTP server
#     include /etc/nginx/conf.d/locations.conf;
# }

# Redirect HTTP to HTTPS (uncomment when SSL is enabled)
# server {
#     listen 80;
#     server_name localhost;
#     return 301 https://$server_name$request_uri;
# }

# Development server for frontend assets
server {
    listen 8080;
    server_name localhost;
    
    root /usr/share/nginx/html;
    index index.html;
    
    # Serve static files with long cache for development
    location / {
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }
    
    # Proxy API calls to the main server
    location /api/ {
        proxy_pass http://app_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

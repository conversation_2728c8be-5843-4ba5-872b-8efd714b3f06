{"version": 3, "sources": ["../../src/infiniteQueryObserver.ts"], "sourcesContent": ["import { QueryObserver } from './queryObserver'\nimport {\n  hasNextPage,\n  hasPreviousPage,\n  infiniteQueryBehavior,\n} from './infiniteQueryBehavior'\nimport type { Subscribable } from './subscribable'\nimport type {\n  DefaultError,\n  DefaultedInfiniteQueryObserverOptions,\n  FetchNextPageOptions,\n  FetchPreviousPageOptions,\n  InfiniteData,\n  InfiniteQueryObserverBaseResult,\n  InfiniteQueryObserverOptions,\n  InfiniteQueryObserverResult,\n  QueryKey,\n} from './types'\nimport type { QueryClient } from './queryClient'\nimport type { Query } from './query'\n\ntype InfiniteQueryObserverListener<TData, TError> = (\n  result: InfiniteQueryObserverResult<TData, TError>,\n) => void\n\nexport class InfiniteQueryObserver<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  T<PERSON><PERSON>y<PERSON><PERSON> extends QueryKey = QueryKey,\n  TPageParam = unknown,\n> extends QueryObserver<\n  TQueryFnData,\n  TError,\n  TData,\n  InfiniteData<TQueryFnData, TPageParam>,\n  TQueryKey\n> {\n  // Type override\n  subscribe!: Subscribable<\n    InfiniteQueryObserverListener<TData, TError>\n  >['subscribe']\n\n  // Type override\n  getCurrentResult!: ReplaceReturnType<\n    QueryObserver<\n      TQueryFnData,\n      TError,\n      TData,\n      InfiniteData<TQueryFnData, TPageParam>,\n      TQueryKey\n    >['getCurrentResult'],\n    InfiniteQueryObserverResult<TData, TError>\n  >\n\n  // Type override\n  protected fetch!: ReplaceReturnType<\n    QueryObserver<\n      TQueryFnData,\n      TError,\n      TData,\n      InfiniteData<TQueryFnData, TPageParam>,\n      TQueryKey\n    >['fetch'],\n    Promise<InfiniteQueryObserverResult<TData, TError>>\n  >\n\n  constructor(\n    client: QueryClient,\n    options: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryKey,\n      TPageParam\n    >,\n  ) {\n    super(client, options)\n  }\n\n  protected bindMethods(): void {\n    super.bindMethods()\n    this.fetchNextPage = this.fetchNextPage.bind(this)\n    this.fetchPreviousPage = this.fetchPreviousPage.bind(this)\n  }\n\n  setOptions(\n    options: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryKey,\n      TPageParam\n    >,\n  ): void {\n    super.setOptions({\n      ...options,\n      behavior: infiniteQueryBehavior(),\n    })\n  }\n\n  getOptimisticResult(\n    options: DefaultedInfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryKey,\n      TPageParam\n    >,\n  ): InfiniteQueryObserverResult<TData, TError> {\n    options.behavior = infiniteQueryBehavior()\n    return super.getOptimisticResult(options) as InfiniteQueryObserverResult<\n      TData,\n      TError\n    >\n  }\n\n  fetchNextPage(\n    options?: FetchNextPageOptions,\n  ): Promise<InfiniteQueryObserverResult<TData, TError>> {\n    return this.fetch({\n      ...options,\n      meta: {\n        fetchMore: { direction: 'forward' },\n      },\n    })\n  }\n\n  fetchPreviousPage(\n    options?: FetchPreviousPageOptions,\n  ): Promise<InfiniteQueryObserverResult<TData, TError>> {\n    return this.fetch({\n      ...options,\n      meta: {\n        fetchMore: { direction: 'backward' },\n      },\n    })\n  }\n\n  protected createResult(\n    query: Query<\n      TQueryFnData,\n      TError,\n      InfiniteData<TQueryFnData, TPageParam>,\n      TQueryKey\n    >,\n    options: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryKey,\n      TPageParam\n    >,\n  ): InfiniteQueryObserverResult<TData, TError> {\n    const { state } = query\n    const parentResult = super.createResult(query, options)\n\n    const { isFetching, isRefetching, isError, isRefetchError } = parentResult\n    const fetchDirection = state.fetchMeta?.fetchMore?.direction\n\n    const isFetchNextPageError = isError && fetchDirection === 'forward'\n    const isFetchingNextPage = isFetching && fetchDirection === 'forward'\n\n    const isFetchPreviousPageError = isError && fetchDirection === 'backward'\n    const isFetchingPreviousPage = isFetching && fetchDirection === 'backward'\n\n    const result: InfiniteQueryObserverBaseResult<TData, TError> = {\n      ...parentResult,\n      fetchNextPage: this.fetchNextPage,\n      fetchPreviousPage: this.fetchPreviousPage,\n      hasNextPage: hasNextPage(options, state.data),\n      hasPreviousPage: hasPreviousPage(options, state.data),\n      isFetchNextPageError,\n      isFetchingNextPage,\n      isFetchPreviousPageError,\n      isFetchingPreviousPage,\n      isRefetchError:\n        isRefetchError && !isFetchNextPageError && !isFetchPreviousPageError,\n      isRefetching:\n        isRefetching && !isFetchingNextPage && !isFetchingPreviousPage,\n    }\n\n    return result as InfiniteQueryObserverResult<TData, TError>\n  }\n}\n\ntype ReplaceReturnType<\n  TFunction extends (...args: Array<any>) => unknown,\n  TReturn,\n> = (...args: Parameters<TFunction>) => TReturn\n"], "mappings": ";;;AAAA,SAAS,qBAAqB;AAC9B;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,OACK;AAoBA,IAAM,wBAAN,cAMG,cAMR;AAAA,EA8BA,YACE,QACA,SAOA;AACA,UAAM,QAAQ,OAAO;AAAA,EACvB;AAAA,EAEU,cAAoB;AAC5B,UAAM,YAAY;AAClB,SAAK,gBAAgB,KAAK,cAAc,KAAK,IAAI;AACjD,SAAK,oBAAoB,KAAK,kBAAkB,KAAK,IAAI;AAAA,EAC3D;AAAA,EAEA,WACE,SAOM;AACN,UAAM,WAAW;AAAA,MACf,GAAG;AAAA,MACH,UAAU,sBAAsB;AAAA,IAClC,CAAC;AAAA,EACH;AAAA,EAEA,oBACE,SAO4C;AAC5C,YAAQ,WAAW,sBAAsB;AACzC,WAAO,MAAM,oBAAoB,OAAO;AAAA,EAI1C;AAAA,EAEA,cACE,SACqD;AACrD,WAAO,KAAK,MAAM;AAAA,MAChB,GAAG;AAAA,MACH,MAAM;AAAA,QACJ,WAAW,EAAE,WAAW,UAAU;AAAA,MACpC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,kBACE,SACqD;AACrD,WAAO,KAAK,MAAM;AAAA,MAChB,GAAG;AAAA,MACH,MAAM;AAAA,QACJ,WAAW,EAAE,WAAW,WAAW;AAAA,MACrC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEU,aACR,OAMA,SAO4C;AAzJhD;AA0JI,UAAM,EAAE,MAAM,IAAI;AAClB,UAAM,eAAe,MAAM,aAAa,OAAO,OAAO;AAEtD,UAAM,EAAE,YAAY,cAAc,SAAS,eAAe,IAAI;AAC9D,UAAM,kBAAiB,iBAAM,cAAN,mBAAiB,cAAjB,mBAA4B;AAEnD,UAAM,uBAAuB,WAAW,mBAAmB;AAC3D,UAAM,qBAAqB,cAAc,mBAAmB;AAE5D,UAAM,2BAA2B,WAAW,mBAAmB;AAC/D,UAAM,yBAAyB,cAAc,mBAAmB;AAEhE,UAAM,SAAyD;AAAA,MAC7D,GAAG;AAAA,MACH,eAAe,KAAK;AAAA,MACpB,mBAAmB,KAAK;AAAA,MACxB,aAAa,YAAY,SAAS,MAAM,IAAI;AAAA,MAC5C,iBAAiB,gBAAgB,SAAS,MAAM,IAAI;AAAA,MACpD;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,gBACE,kBAAkB,CAAC,wBAAwB,CAAC;AAAA,MAC9C,cACE,gBAAgB,CAAC,sBAAsB,CAAC;AAAA,IAC5C;AAEA,WAAO;AAAA,EACT;AACF;", "names": []}
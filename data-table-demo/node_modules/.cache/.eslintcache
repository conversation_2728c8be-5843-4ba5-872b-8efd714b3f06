[{"/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/src/index.tsx": "1", "/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/src/reportWebVitals.ts": "2", "/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/src/App.tsx": "3", "/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/src/ExperimentDataTable.tsx": "4"}, {"size": 554, "mtime": 1749631673584, "results": "5", "hashOfConfig": "6"}, {"size": 425, "mtime": 1749631673603, "results": "7", "hashOfConfig": "6"}, {"size": 4873, "mtime": 1749632068402, "results": "8", "hashOfConfig": "6"}, {"size": 30879, "mtime": 1749632108701, "results": "9", "hashOfConfig": "6"}, {"filePath": "10", "messages": "11", "suppressedMessages": "12", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "fnc6dw", {"filePath": "13", "messages": "14", "suppressedMessages": "15", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/src/index.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/src/reportWebVitals.ts", [], [], "/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/src/App.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/src/ExperimentDataTable.tsx", [], []]
[{"/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/src/index.tsx": "1", "/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/src/reportWebVitals.ts": "2", "/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/src/App.tsx": "3", "/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/src/ExperimentDataTable.tsx": "4", "/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/src/contexts/ExperimentContext.tsx": "5", "/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/src/hooks/useBulkOperations.ts": "6", "/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/src/hooks/useExperiments.ts": "7", "/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/src/types/experiment.ts": "8", "/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/src/services/api.ts": "9"}, {"size": 554, "mtime": 1749631673584, "results": "10", "hashOfConfig": "11"}, {"size": 425, "mtime": 1749631673603, "results": "12", "hashOfConfig": "11"}, {"size": 3764, "mtime": 1749648401181, "results": "13", "hashOfConfig": "11"}, {"size": 30161, "mtime": 1749643654970, "results": "14", "hashOfConfig": "11"}, {"size": 7939, "mtime": 1749643559757, "results": "15", "hashOfConfig": "11"}, {"size": 9361, "mtime": 1749648243401, "results": "16", "hashOfConfig": "11"}, {"size": 13210, "mtime": 1749648419949, "results": "17", "hashOfConfig": "11"}, {"size": 3035, "mtime": 1749643135070, "results": "18", "hashOfConfig": "11"}, {"size": 19335, "mtime": 1749648443653, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "fnc6dw", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/src/index.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/src/reportWebVitals.ts", [], [], "/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/src/App.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/src/ExperimentDataTable.tsx", ["47"], [], "/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/src/contexts/ExperimentContext.tsx", ["48"], [], "/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/src/hooks/useBulkOperations.ts", [], [], "/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/src/hooks/useExperiments.ts", [], [], "/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/src/types/experiment.ts", [], [], "/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/src/services/api.ts", [], [], {"ruleId": "49", "severity": 1, "message": "50", "line": 185, "column": 5, "nodeType": "51", "messageId": "52", "endLine": 185, "endColumn": 21}, {"ruleId": "49", "severity": 1, "message": "53", "line": 5, "column": 3, "nodeType": "51", "messageId": "52", "endLine": 5, "endColumn": 13}, "@typescript-eslint/no-unused-vars", "'bulkUpdateStatus' is assigned a value but never used.", "Identifier", "unusedVar", "'Experiment' is defined but never used."]
{"ast": null, "code": "// src/mutation.ts\nimport { notify<PERSON>anager } from \"./notifyManager.js\";\nimport { Removable } from \"./removable.js\";\nimport { createRetryer } from \"./retryer.js\";\nvar Mutation = class extends Removable {\n  #observers;\n  #mutationCache;\n  #retryer;\n  constructor(config) {\n    super();\n    this.mutationId = config.mutationId;\n    this.#mutationCache = config.mutationCache;\n    this.#observers = [];\n    this.state = config.state || getDefaultState();\n    this.setOptions(config.options);\n    this.scheduleGc();\n  }\n  setOptions(options) {\n    this.options = options;\n    this.updateGcTime(this.options.gcTime);\n  }\n  get meta() {\n    return this.options.meta;\n  }\n  addObserver(observer) {\n    if (!this.#observers.includes(observer)) {\n      this.#observers.push(observer);\n      this.clearGcTimeout();\n      this.#mutationCache.notify({\n        type: \"observerAdded\",\n        mutation: this,\n        observer\n      });\n    }\n  }\n  removeObserver(observer) {\n    this.#observers = this.#observers.filter(x => x !== observer);\n    this.scheduleGc();\n    this.#mutationCache.notify({\n      type: \"observerRemoved\",\n      mutation: this,\n      observer\n    });\n  }\n  optionalRemove() {\n    if (!this.#observers.length) {\n      if (this.state.status === \"pending\") {\n        this.scheduleGc();\n      } else {\n        this.#mutationCache.remove(this);\n      }\n    }\n  }\n  continue() {\n    return this.#retryer?.continue() ??\n    // continuing a mutation assumes that variables are set, mutation must have been dehydrated before\n    this.execute(this.state.variables);\n  }\n  async execute(variables) {\n    const onContinue = () => {\n      this.#dispatch({\n        type: \"continue\"\n      });\n    };\n    this.#retryer = createRetryer({\n      fn: () => {\n        if (!this.options.mutationFn) {\n          return Promise.reject(new Error(\"No mutationFn found\"));\n        }\n        return this.options.mutationFn(variables);\n      },\n      onFail: (failureCount, error) => {\n        this.#dispatch({\n          type: \"failed\",\n          failureCount,\n          error\n        });\n      },\n      onPause: () => {\n        this.#dispatch({\n          type: \"pause\"\n        });\n      },\n      onContinue,\n      retry: this.options.retry ?? 0,\n      retryDelay: this.options.retryDelay,\n      networkMode: this.options.networkMode,\n      canRun: () => this.#mutationCache.canRun(this)\n    });\n    const restored = this.state.status === \"pending\";\n    const isPaused = !this.#retryer.canStart();\n    try {\n      if (restored) {\n        onContinue();\n      } else {\n        this.#dispatch({\n          type: \"pending\",\n          variables,\n          isPaused\n        });\n        await this.#mutationCache.config.onMutate?.(variables, this);\n        const context = await this.options.onMutate?.(variables);\n        if (context !== this.state.context) {\n          this.#dispatch({\n            type: \"pending\",\n            context,\n            variables,\n            isPaused\n          });\n        }\n      }\n      const data = await this.#retryer.start();\n      await this.#mutationCache.config.onSuccess?.(data, variables, this.state.context, this);\n      await this.options.onSuccess?.(data, variables, this.state.context);\n      await this.#mutationCache.config.onSettled?.(data, null, this.state.variables, this.state.context, this);\n      await this.options.onSettled?.(data, null, variables, this.state.context);\n      this.#dispatch({\n        type: \"success\",\n        data\n      });\n      return data;\n    } catch (error) {\n      try {\n        await this.#mutationCache.config.onError?.(error, variables, this.state.context, this);\n        await this.options.onError?.(error, variables, this.state.context);\n        await this.#mutationCache.config.onSettled?.(void 0, error, this.state.variables, this.state.context, this);\n        await this.options.onSettled?.(void 0, error, variables, this.state.context);\n        throw error;\n      } finally {\n        this.#dispatch({\n          type: \"error\",\n          error\n        });\n      }\n    } finally {\n      this.#mutationCache.runNext(this);\n    }\n  }\n  #dispatch(action) {\n    const reducer = state => {\n      switch (action.type) {\n        case \"failed\":\n          return {\n            ...state,\n            failureCount: action.failureCount,\n            failureReason: action.error\n          };\n        case \"pause\":\n          return {\n            ...state,\n            isPaused: true\n          };\n        case \"continue\":\n          return {\n            ...state,\n            isPaused: false\n          };\n        case \"pending\":\n          return {\n            ...state,\n            context: action.context,\n            data: void 0,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            isPaused: action.isPaused,\n            status: \"pending\",\n            variables: action.variables,\n            submittedAt: Date.now()\n          };\n        case \"success\":\n          return {\n            ...state,\n            data: action.data,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            status: \"success\",\n            isPaused: false\n          };\n        case \"error\":\n          return {\n            ...state,\n            data: void 0,\n            error: action.error,\n            failureCount: state.failureCount + 1,\n            failureReason: action.error,\n            isPaused: false,\n            status: \"error\"\n          };\n      }\n    };\n    this.state = reducer(this.state);\n    notifyManager.batch(() => {\n      this.#observers.forEach(observer => {\n        observer.onMutationUpdate(action);\n      });\n      this.#mutationCache.notify({\n        mutation: this,\n        type: \"updated\",\n        action\n      });\n    });\n  }\n};\nfunction getDefaultState() {\n  return {\n    context: void 0,\n    data: void 0,\n    error: null,\n    failureCount: 0,\n    failureReason: null,\n    isPaused: false,\n    status: \"idle\",\n    variables: void 0,\n    submittedAt: 0\n  };\n}\nexport { Mutation, getDefaultState };", "map": {"version": 3, "names": ["notify<PERSON><PERSON>ger", "Removable", "createRetryer", "Mutation", "observers", "mutationCache", "retryer", "constructor", "config", "mutationId", "state", "getDefaultState", "setOptions", "options", "scheduleGc", "updateGcTime", "gcTime", "meta", "addObserver", "observer", "includes", "push", "clearGcTimeout", "notify", "type", "mutation", "removeObserver", "filter", "x", "optionalRemove", "length", "status", "remove", "continue", "execute", "variables", "onContinue", "dispatch", "fn", "mutationFn", "Promise", "reject", "Error", "onFail", "failureCount", "error", "onPause", "retry", "retry<PERSON><PERSON><PERSON>", "networkMode", "canRun", "restored", "isPaused", "canStart", "onMutate", "context", "data", "start", "onSuccess", "onSettled", "onError", "runNext", "#dispatch", "action", "reducer", "failureReason", "submittedAt", "Date", "now", "batch", "for<PERSON>ach", "onMutationUpdate"], "sources": ["/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/node_modules/@tanstack/query-core/src/mutation.ts"], "sourcesContent": ["import { notify<PERSON><PERSON><PERSON> } from './notifyManager'\nimport { Removable } from './removable'\nimport { createR<PERSON>ry<PERSON> } from './retryer'\nimport type {\n  DefaultError,\n  MutationMeta,\n  MutationOptions,\n  MutationStatus,\n} from './types'\nimport type { MutationCache } from './mutationCache'\nimport type { MutationObserver } from './mutationObserver'\nimport type { <PERSON><PERSON><PERSON> } from './retryer'\n\n// TYPES\n\ninterface MutationConfig<TData, TError, TVariables, TContext> {\n  mutationId: number\n  mutationCache: MutationCache\n  options: MutationOptions<TData, TError, TVariables, TContext>\n  state?: MutationState<TData, TError, TVariables, TContext>\n}\n\nexport interface MutationState<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = unknown,\n  TContext = unknown,\n> {\n  context: TContext | undefined\n  data: TData | undefined\n  error: TError | null\n  failureCount: number\n  failureReason: TError | null\n  isPaused: boolean\n  status: MutationStatus\n  variables: TVariables | undefined\n  submittedAt: number\n}\n\ninterface FailedAction<TError> {\n  type: 'failed'\n  failureCount: number\n  error: TError | null\n}\n\ninterface PendingAction<TVariables, TContext> {\n  type: 'pending'\n  isPaused: boolean\n  variables?: TVariables\n  context?: TContext\n}\n\ninterface SuccessAction<TData> {\n  type: 'success'\n  data: TData\n}\n\ninterface ErrorAction<TError> {\n  type: 'error'\n  error: TError\n}\n\ninterface PauseAction {\n  type: 'pause'\n}\n\ninterface ContinueAction {\n  type: 'continue'\n}\n\nexport type Action<TData, TError, TVariables, TContext> =\n  | ContinueAction\n  | ErrorAction<TError>\n  | FailedAction<TError>\n  | PendingAction<TVariables, TContext>\n  | PauseAction\n  | SuccessAction<TData>\n\n// CLASS\n\nexport class Mutation<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = unknown,\n  TContext = unknown,\n> extends Removable {\n  state: MutationState<TData, TError, TVariables, TContext>\n  options!: MutationOptions<TData, TError, TVariables, TContext>\n  readonly mutationId: number\n\n  #observers: Array<MutationObserver<TData, TError, TVariables, TContext>>\n  #mutationCache: MutationCache\n  #retryer?: Retryer<TData>\n\n  constructor(config: MutationConfig<TData, TError, TVariables, TContext>) {\n    super()\n\n    this.mutationId = config.mutationId\n    this.#mutationCache = config.mutationCache\n    this.#observers = []\n    this.state = config.state || getDefaultState()\n\n    this.setOptions(config.options)\n    this.scheduleGc()\n  }\n\n  setOptions(\n    options: MutationOptions<TData, TError, TVariables, TContext>,\n  ): void {\n    this.options = options\n\n    this.updateGcTime(this.options.gcTime)\n  }\n\n  get meta(): MutationMeta | undefined {\n    return this.options.meta\n  }\n\n  addObserver(observer: MutationObserver<any, any, any, any>): void {\n    if (!this.#observers.includes(observer)) {\n      this.#observers.push(observer)\n\n      // Stop the mutation from being garbage collected\n      this.clearGcTimeout()\n\n      this.#mutationCache.notify({\n        type: 'observerAdded',\n        mutation: this,\n        observer,\n      })\n    }\n  }\n\n  removeObserver(observer: MutationObserver<any, any, any, any>): void {\n    this.#observers = this.#observers.filter((x) => x !== observer)\n\n    this.scheduleGc()\n\n    this.#mutationCache.notify({\n      type: 'observerRemoved',\n      mutation: this,\n      observer,\n    })\n  }\n\n  protected optionalRemove() {\n    if (!this.#observers.length) {\n      if (this.state.status === 'pending') {\n        this.scheduleGc()\n      } else {\n        this.#mutationCache.remove(this)\n      }\n    }\n  }\n\n  continue(): Promise<unknown> {\n    return (\n      this.#retryer?.continue() ??\n      // continuing a mutation assumes that variables are set, mutation must have been dehydrated before\n      this.execute(this.state.variables!)\n    )\n  }\n\n  async execute(variables: TVariables): Promise<TData> {\n    const onContinue = () => {\n      this.#dispatch({ type: 'continue' })\n    }\n\n    this.#retryer = createRetryer({\n      fn: () => {\n        if (!this.options.mutationFn) {\n          return Promise.reject(new Error('No mutationFn found'))\n        }\n        return this.options.mutationFn(variables)\n      },\n      onFail: (failureCount, error) => {\n        this.#dispatch({ type: 'failed', failureCount, error })\n      },\n      onPause: () => {\n        this.#dispatch({ type: 'pause' })\n      },\n      onContinue,\n      retry: this.options.retry ?? 0,\n      retryDelay: this.options.retryDelay,\n      networkMode: this.options.networkMode,\n      canRun: () => this.#mutationCache.canRun(this),\n    })\n\n    const restored = this.state.status === 'pending'\n    const isPaused = !this.#retryer.canStart()\n\n    try {\n      if (restored) {\n        // Dispatch continue action to unpause restored mutation\n        onContinue()\n      } else {\n        this.#dispatch({ type: 'pending', variables, isPaused })\n        // Notify cache callback\n        await this.#mutationCache.config.onMutate?.(\n          variables,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n        const context = await this.options.onMutate?.(variables)\n        if (context !== this.state.context) {\n          this.#dispatch({\n            type: 'pending',\n            context,\n            variables,\n            isPaused,\n          })\n        }\n      }\n      const data = await this.#retryer.start()\n\n      // Notify cache callback\n      await this.#mutationCache.config.onSuccess?.(\n        data,\n        variables,\n        this.state.context,\n        this as Mutation<unknown, unknown, unknown, unknown>,\n      )\n\n      await this.options.onSuccess?.(data, variables, this.state.context!)\n\n      // Notify cache callback\n      await this.#mutationCache.config.onSettled?.(\n        data,\n        null,\n        this.state.variables,\n        this.state.context,\n        this as Mutation<unknown, unknown, unknown, unknown>,\n      )\n\n      await this.options.onSettled?.(data, null, variables, this.state.context)\n\n      this.#dispatch({ type: 'success', data })\n      return data\n    } catch (error) {\n      try {\n        // Notify cache callback\n        await this.#mutationCache.config.onError?.(\n          error as any,\n          variables,\n          this.state.context,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n\n        await this.options.onError?.(\n          error as TError,\n          variables,\n          this.state.context,\n        )\n\n        // Notify cache callback\n        await this.#mutationCache.config.onSettled?.(\n          undefined,\n          error as any,\n          this.state.variables,\n          this.state.context,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n\n        await this.options.onSettled?.(\n          undefined,\n          error as TError,\n          variables,\n          this.state.context,\n        )\n        throw error\n      } finally {\n        this.#dispatch({ type: 'error', error: error as TError })\n      }\n    } finally {\n      this.#mutationCache.runNext(this)\n    }\n  }\n\n  #dispatch(action: Action<TData, TError, TVariables, TContext>): void {\n    const reducer = (\n      state: MutationState<TData, TError, TVariables, TContext>,\n    ): MutationState<TData, TError, TVariables, TContext> => {\n      switch (action.type) {\n        case 'failed':\n          return {\n            ...state,\n            failureCount: action.failureCount,\n            failureReason: action.error,\n          }\n        case 'pause':\n          return {\n            ...state,\n            isPaused: true,\n          }\n        case 'continue':\n          return {\n            ...state,\n            isPaused: false,\n          }\n        case 'pending':\n          return {\n            ...state,\n            context: action.context,\n            data: undefined,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            isPaused: action.isPaused,\n            status: 'pending',\n            variables: action.variables,\n            submittedAt: Date.now(),\n          }\n        case 'success':\n          return {\n            ...state,\n            data: action.data,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            status: 'success',\n            isPaused: false,\n          }\n        case 'error':\n          return {\n            ...state,\n            data: undefined,\n            error: action.error,\n            failureCount: state.failureCount + 1,\n            failureReason: action.error,\n            isPaused: false,\n            status: 'error',\n          }\n      }\n    }\n    this.state = reducer(this.state)\n\n    notifyManager.batch(() => {\n      this.#observers.forEach((observer) => {\n        observer.onMutationUpdate(action)\n      })\n      this.#mutationCache.notify({\n        mutation: this,\n        type: 'updated',\n        action,\n      })\n    })\n  }\n}\n\nexport function getDefaultState<\n  TData,\n  TError,\n  TVariables,\n  TContext,\n>(): MutationState<TData, TError, TVariables, TContext> {\n  return {\n    context: undefined,\n    data: undefined,\n    error: null,\n    failureCount: 0,\n    failureReason: null,\n    isPaused: false,\n    status: 'idle',\n    variables: undefined,\n    submittedAt: 0,\n  }\n}\n"], "mappings": ";AAAA,SAASA,aAAA,QAAqB;AAC9B,SAASC,SAAA,QAAiB;AAC1B,SAASC,aAAA,QAAqB;AA8EvB,IAAMC,QAAA,GAAN,cAKGF,SAAA,CAAU;EAKlB,CAAAG,SAAA;EACA,CAAAC,aAAA;EACA,CAAAC,OAAA;EAEAC,YAAYC,MAAA,EAA6D;IACvE,MAAM;IAEN,KAAKC,UAAA,GAAaD,MAAA,CAAOC,UAAA;IACzB,KAAK,CAAAJ,aAAA,GAAiBG,MAAA,CAAOH,aAAA;IAC7B,KAAK,CAAAD,SAAA,GAAa,EAAC;IACnB,KAAKM,KAAA,GAAQF,MAAA,CAAOE,KAAA,IAASC,eAAA,CAAgB;IAE7C,KAAKC,UAAA,CAAWJ,MAAA,CAAOK,OAAO;IAC9B,KAAKC,UAAA,CAAW;EAClB;EAEAF,WACEC,OAAA,EACM;IACN,KAAKA,OAAA,GAAUA,OAAA;IAEf,KAAKE,YAAA,CAAa,KAAKF,OAAA,CAAQG,MAAM;EACvC;EAEA,IAAIC,KAAA,EAAiC;IACnC,OAAO,KAAKJ,OAAA,CAAQI,IAAA;EACtB;EAEAC,YAAYC,QAAA,EAAsD;IAChE,IAAI,CAAC,KAAK,CAAAf,SAAA,CAAWgB,QAAA,CAASD,QAAQ,GAAG;MACvC,KAAK,CAAAf,SAAA,CAAWiB,IAAA,CAAKF,QAAQ;MAG7B,KAAKG,cAAA,CAAe;MAEpB,KAAK,CAAAjB,aAAA,CAAekB,MAAA,CAAO;QACzBC,IAAA,EAAM;QACNC,QAAA,EAAU;QACVN;MACF,CAAC;IACH;EACF;EAEAO,eAAeP,QAAA,EAAsD;IACnE,KAAK,CAAAf,SAAA,GAAa,KAAK,CAAAA,SAAA,CAAWuB,MAAA,CAAQC,CAAA,IAAMA,CAAA,KAAMT,QAAQ;IAE9D,KAAKL,UAAA,CAAW;IAEhB,KAAK,CAAAT,aAAA,CAAekB,MAAA,CAAO;MACzBC,IAAA,EAAM;MACNC,QAAA,EAAU;MACVN;IACF,CAAC;EACH;EAEUU,eAAA,EAAiB;IACzB,IAAI,CAAC,KAAK,CAAAzB,SAAA,CAAW0B,MAAA,EAAQ;MAC3B,IAAI,KAAKpB,KAAA,CAAMqB,MAAA,KAAW,WAAW;QACnC,KAAKjB,UAAA,CAAW;MAClB,OAAO;QACL,KAAK,CAAAT,aAAA,CAAe2B,MAAA,CAAO,IAAI;MACjC;IACF;EACF;EAEAC,SAAA,EAA6B;IAC3B,OACE,KAAK,CAAA3B,OAAA,EAAU2B,QAAA,CAAS;IAAA;IAExB,KAAKC,OAAA,CAAQ,KAAKxB,KAAA,CAAMyB,SAAU;EAEtC;EAEA,MAAMD,QAAQC,SAAA,EAAuC;IACnD,MAAMC,UAAA,GAAaA,CAAA,KAAM;MACvB,KAAK,CAAAC,QAAA,CAAU;QAAEb,IAAA,EAAM;MAAW,CAAC;IACrC;IAEA,KAAK,CAAAlB,OAAA,GAAWJ,aAAA,CAAc;MAC5BoC,EAAA,EAAIA,CAAA,KAAM;QACR,IAAI,CAAC,KAAKzB,OAAA,CAAQ0B,UAAA,EAAY;UAC5B,OAAOC,OAAA,CAAQC,MAAA,CAAO,IAAIC,KAAA,CAAM,qBAAqB,CAAC;QACxD;QACA,OAAO,KAAK7B,OAAA,CAAQ0B,UAAA,CAAWJ,SAAS;MAC1C;MACAQ,MAAA,EAAQA,CAACC,YAAA,EAAcC,KAAA,KAAU;QAC/B,KAAK,CAAAR,QAAA,CAAU;UAAEb,IAAA,EAAM;UAAUoB,YAAA;UAAcC;QAAM,CAAC;MACxD;MACAC,OAAA,EAASA,CAAA,KAAM;QACb,KAAK,CAAAT,QAAA,CAAU;UAAEb,IAAA,EAAM;QAAQ,CAAC;MAClC;MACAY,UAAA;MACAW,KAAA,EAAO,KAAKlC,OAAA,CAAQkC,KAAA,IAAS;MAC7BC,UAAA,EAAY,KAAKnC,OAAA,CAAQmC,UAAA;MACzBC,WAAA,EAAa,KAAKpC,OAAA,CAAQoC,WAAA;MAC1BC,MAAA,EAAQA,CAAA,KAAM,KAAK,CAAA7C,aAAA,CAAe6C,MAAA,CAAO,IAAI;IAC/C,CAAC;IAED,MAAMC,QAAA,GAAW,KAAKzC,KAAA,CAAMqB,MAAA,KAAW;IACvC,MAAMqB,QAAA,GAAW,CAAC,KAAK,CAAA9C,OAAA,CAAS+C,QAAA,CAAS;IAEzC,IAAI;MACF,IAAIF,QAAA,EAAU;QAEZf,UAAA,CAAW;MACb,OAAO;QACL,KAAK,CAAAC,QAAA,CAAU;UAAEb,IAAA,EAAM;UAAWW,SAAA;UAAWiB;QAAS,CAAC;QAEvD,MAAM,KAAK,CAAA/C,aAAA,CAAeG,MAAA,CAAO8C,QAAA,GAC/BnB,SAAA,EACA,IACF;QACA,MAAMoB,OAAA,GAAU,MAAM,KAAK1C,OAAA,CAAQyC,QAAA,GAAWnB,SAAS;QACvD,IAAIoB,OAAA,KAAY,KAAK7C,KAAA,CAAM6C,OAAA,EAAS;UAClC,KAAK,CAAAlB,QAAA,CAAU;YACbb,IAAA,EAAM;YACN+B,OAAA;YACApB,SAAA;YACAiB;UACF,CAAC;QACH;MACF;MACA,MAAMI,IAAA,GAAO,MAAM,KAAK,CAAAlD,OAAA,CAASmD,KAAA,CAAM;MAGvC,MAAM,KAAK,CAAApD,aAAA,CAAeG,MAAA,CAAOkD,SAAA,GAC/BF,IAAA,EACArB,SAAA,EACA,KAAKzB,KAAA,CAAM6C,OAAA,EACX,IACF;MAEA,MAAM,KAAK1C,OAAA,CAAQ6C,SAAA,GAAYF,IAAA,EAAMrB,SAAA,EAAW,KAAKzB,KAAA,CAAM6C,OAAQ;MAGnE,MAAM,KAAK,CAAAlD,aAAA,CAAeG,MAAA,CAAOmD,SAAA,GAC/BH,IAAA,EACA,MACA,KAAK9C,KAAA,CAAMyB,SAAA,EACX,KAAKzB,KAAA,CAAM6C,OAAA,EACX,IACF;MAEA,MAAM,KAAK1C,OAAA,CAAQ8C,SAAA,GAAYH,IAAA,EAAM,MAAMrB,SAAA,EAAW,KAAKzB,KAAA,CAAM6C,OAAO;MAExE,KAAK,CAAAlB,QAAA,CAAU;QAAEb,IAAA,EAAM;QAAWgC;MAAK,CAAC;MACxC,OAAOA,IAAA;IACT,SAASX,KAAA,EAAO;MACd,IAAI;QAEF,MAAM,KAAK,CAAAxC,aAAA,CAAeG,MAAA,CAAOoD,OAAA,GAC/Bf,KAAA,EACAV,SAAA,EACA,KAAKzB,KAAA,CAAM6C,OAAA,EACX,IACF;QAEA,MAAM,KAAK1C,OAAA,CAAQ+C,OAAA,GACjBf,KAAA,EACAV,SAAA,EACA,KAAKzB,KAAA,CAAM6C,OACb;QAGA,MAAM,KAAK,CAAAlD,aAAA,CAAeG,MAAA,CAAOmD,SAAA,GAC/B,QACAd,KAAA,EACA,KAAKnC,KAAA,CAAMyB,SAAA,EACX,KAAKzB,KAAA,CAAM6C,OAAA,EACX,IACF;QAEA,MAAM,KAAK1C,OAAA,CAAQ8C,SAAA,GACjB,QACAd,KAAA,EACAV,SAAA,EACA,KAAKzB,KAAA,CAAM6C,OACb;QACA,MAAMV,KAAA;MACR,UAAE;QACA,KAAK,CAAAR,QAAA,CAAU;UAAEb,IAAA,EAAM;UAASqB;QAAuB,CAAC;MAC1D;IACF,UAAE;MACA,KAAK,CAAAxC,aAAA,CAAewD,OAAA,CAAQ,IAAI;IAClC;EACF;EAEA,CAAAxB,QAAAyB,CAAUC,MAAA,EAA2D;IACnE,MAAMC,OAAA,GACJtD,KAAA,IACuD;MACvD,QAAQqD,MAAA,CAAOvC,IAAA;QACb,KAAK;UACH,OAAO;YACL,GAAGd,KAAA;YACHkC,YAAA,EAAcmB,MAAA,CAAOnB,YAAA;YACrBqB,aAAA,EAAeF,MAAA,CAAOlB;UACxB;QACF,KAAK;UACH,OAAO;YACL,GAAGnC,KAAA;YACH0C,QAAA,EAAU;UACZ;QACF,KAAK;UACH,OAAO;YACL,GAAG1C,KAAA;YACH0C,QAAA,EAAU;UACZ;QACF,KAAK;UACH,OAAO;YACL,GAAG1C,KAAA;YACH6C,OAAA,EAASQ,MAAA,CAAOR,OAAA;YAChBC,IAAA,EAAM;YACNZ,YAAA,EAAc;YACdqB,aAAA,EAAe;YACfpB,KAAA,EAAO;YACPO,QAAA,EAAUW,MAAA,CAAOX,QAAA;YACjBrB,MAAA,EAAQ;YACRI,SAAA,EAAW4B,MAAA,CAAO5B,SAAA;YAClB+B,WAAA,EAAaC,IAAA,CAAKC,GAAA,CAAI;UACxB;QACF,KAAK;UACH,OAAO;YACL,GAAG1D,KAAA;YACH8C,IAAA,EAAMO,MAAA,CAAOP,IAAA;YACbZ,YAAA,EAAc;YACdqB,aAAA,EAAe;YACfpB,KAAA,EAAO;YACPd,MAAA,EAAQ;YACRqB,QAAA,EAAU;UACZ;QACF,KAAK;UACH,OAAO;YACL,GAAG1C,KAAA;YACH8C,IAAA,EAAM;YACNX,KAAA,EAAOkB,MAAA,CAAOlB,KAAA;YACdD,YAAA,EAAclC,KAAA,CAAMkC,YAAA,GAAe;YACnCqB,aAAA,EAAeF,MAAA,CAAOlB,KAAA;YACtBO,QAAA,EAAU;YACVrB,MAAA,EAAQ;UACV;MACJ;IACF;IACA,KAAKrB,KAAA,GAAQsD,OAAA,CAAQ,KAAKtD,KAAK;IAE/BV,aAAA,CAAcqE,KAAA,CAAM,MAAM;MACxB,KAAK,CAAAjE,SAAA,CAAWkE,OAAA,CAASnD,QAAA,IAAa;QACpCA,QAAA,CAASoD,gBAAA,CAAiBR,MAAM;MAClC,CAAC;MACD,KAAK,CAAA1D,aAAA,CAAekB,MAAA,CAAO;QACzBE,QAAA,EAAU;QACVD,IAAA,EAAM;QACNuC;MACF,CAAC;IACH,CAAC;EACH;AACF;AAEO,SAASpD,gBAAA,EAKwC;EACtD,OAAO;IACL4C,OAAA,EAAS;IACTC,IAAA,EAAM;IACNX,KAAA,EAAO;IACPD,YAAA,EAAc;IACdqB,aAAA,EAAe;IACfb,QAAA,EAAU;IACVrB,MAAA,EAAQ;IACRI,SAAA,EAAW;IACX+B,WAAA,EAAa;EACf;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
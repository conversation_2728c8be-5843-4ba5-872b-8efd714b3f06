{"ast": null, "code": "// src/usePrefetchInfiniteQuery.tsx\nimport { useQueryClient } from \"./QueryClientProvider.js\";\nfunction usePrefetchInfiniteQuery(options, queryClient) {\n  const client = useQueryClient(queryClient);\n  if (!client.getQueryState(options.queryKey)) {\n    client.prefetchInfiniteQuery(options);\n  }\n}\nexport { usePrefetchInfiniteQuery };", "map": {"version": 3, "names": ["useQueryClient", "usePrefetchInfiniteQuery", "options", "queryClient", "client", "getQueryState", "query<PERSON><PERSON>", "prefetchInfiniteQuery"], "sources": ["/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/node_modules/@tanstack/react-query/src/usePrefetchInfiniteQuery.tsx"], "sourcesContent": ["import { useQueryClient } from './QueryClientProvider'\nimport type {\n  DefaultError,\n  FetchInfiniteQueryOptions,\n  QueryClient,\n  QueryKey,\n} from '@tanstack/query-core'\n\nexport function usePrefetchInfiniteQuery<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n>(\n  options: FetchInfiniteQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryKey,\n    TPageParam\n  >,\n  queryClient?: QueryClient,\n) {\n  const client = useQueryClient(queryClient)\n\n  if (!client.getQueryState(options.queryKey)) {\n    client.prefetchInfiniteQuery(options)\n  }\n}\n"], "mappings": ";AAAA,SAASA,cAAA,QAAsB;AAQxB,SAASC,yBAOdC,OAAA,EAOAC,WAAA,EACA;EACA,MAAMC,MAAA,GAASJ,cAAA,CAAeG,WAAW;EAEzC,IAAI,CAACC,MAAA,CAAOC,aAAA,CAAcH,OAAA,CAAQI,QAAQ,GAAG;IAC3CF,MAAA,CAAOG,qBAAA,CAAsBL,OAAO;EACtC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
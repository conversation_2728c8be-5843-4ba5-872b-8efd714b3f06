{"ast": null, "code": "var _s = $RefreshSig$();\n// Custom hook for bulk experiment operations\nimport { useCallback } from 'react';\nimport { useMutation, useQueryClient } from '@tanstack/react-query';\nimport { toast } from 'react-hot-toast';\nimport { experimentsApi } from '../services/api';\nimport { experimentKeys } from './useExperiments';\nimport { useExperimentContext } from '../contexts/ExperimentContext';\nexport const useBulkOperations = () => {\n  _s();\n  const queryClient = useQueryClient();\n  const {\n    clearSelection\n  } = useExperimentContext();\n\n  // Bulk status update mutation\n  const bulkStatusMutation = useMutation({\n    mutationFn: ({\n      ids,\n      status\n    }) => experimentsApi.bulkUpdateStatus(ids, status),\n    onMutate: async ({\n      ids,\n      status\n    }) => {\n      // Cancel outgoing refetches\n      await queryClient.cancelQueries({\n        queryKey: experimentKeys.lists()\n      });\n\n      // Snapshot previous values\n      const previousQueries = new Map();\n\n      // Get all list queries and store their previous values\n      const queryCache = queryClient.getQueryCache();\n      const listQueries = queryCache.findAll({\n        queryKey: experimentKeys.lists()\n      });\n      listQueries.forEach(query => {\n        if (query.state.data) {\n          previousQueries.set(query.queryKey, query.state.data);\n        }\n      });\n\n      // Optimistically update all list queries\n      listQueries.forEach(query => {\n        queryClient.setQueryData(query.queryKey, old => {\n          if (!old) return old;\n          return {\n            ...old,\n            data: old.data.map(exp => ids.includes(exp.id) ? {\n              ...exp,\n              status,\n              updatedAt: new Date().toISOString()\n            } : exp)\n          };\n        });\n      });\n\n      // Also update individual experiment queries\n      ids.forEach(id => {\n        queryClient.setQueryData(experimentKeys.detail(id), old => {\n          if (!old) return old;\n          return {\n            ...old,\n            data: {\n              ...old.data,\n              status,\n              updatedAt: new Date().toISOString()\n            }\n          };\n        });\n      });\n      return {\n        previousQueries\n      };\n    },\n    onError: (error, {\n      ids,\n      status\n    }, context) => {\n      // Rollback all optimistic updates\n      if (context !== null && context !== void 0 && context.previousQueries) {\n        context.previousQueries.forEach((data, queryKey) => {\n          queryClient.setQueryData(queryKey, data);\n        });\n      }\n      toast.error(`Failed to update ${ids.length} experiments: ${error.message}`);\n    },\n    onSuccess: (response, {\n      ids,\n      status\n    }) => {\n      // Invalidate all list queries\n      queryClient.invalidateQueries({\n        queryKey: experimentKeys.lists()\n      });\n\n      // Invalidate individual experiment queries\n      ids.forEach(id => {\n        queryClient.invalidateQueries({\n          queryKey: experimentKeys.detail(id)\n        });\n      });\n\n      // Clear selection\n      clearSelection();\n      toast.success(`Successfully updated ${ids.length} experiments to ${status.toLowerCase()}`);\n    }\n  });\n\n  // Bulk delete mutation\n  const bulkDeleteMutation = useMutation({\n    mutationFn: ids => experimentsApi.bulkDelete(ids),\n    onMutate: async ids => {\n      // Cancel outgoing refetches\n      await queryClient.cancelQueries({\n        queryKey: experimentKeys.lists()\n      });\n\n      // Snapshot previous values\n      const previousQueries = new Map();\n\n      // Get all list queries and store their previous values\n      const queryCache = queryClient.getQueryCache();\n      const listQueries = queryCache.findAll({\n        queryKey: experimentKeys.lists()\n      });\n      listQueries.forEach(query => {\n        if (query.state.data) {\n          previousQueries.set(query.queryKey, query.state.data);\n        }\n      });\n\n      // Optimistically remove from all list queries\n      listQueries.forEach(query => {\n        queryClient.setQueryData(query.queryKey, old => {\n          if (!old) return old;\n          const filteredData = old.data.filter(exp => !ids.includes(exp.id));\n          return {\n            ...old,\n            data: filteredData,\n            pagination: {\n              ...old.pagination,\n              total: old.pagination.total - (old.data.length - filteredData.length)\n            }\n          };\n        });\n      });\n      return {\n        previousQueries\n      };\n    },\n    onError: (error, ids, context) => {\n      // Rollback all optimistic updates\n      if (context !== null && context !== void 0 && context.previousQueries) {\n        context.previousQueries.forEach((data, queryKey) => {\n          queryClient.setQueryData(queryKey, data);\n        });\n      }\n      toast.error(`Failed to delete ${ids.length} experiments: ${error.message}`);\n    },\n    onSuccess: (response, ids) => {\n      // Invalidate all list queries\n      queryClient.invalidateQueries({\n        queryKey: experimentKeys.lists()\n      });\n\n      // Remove individual experiment queries from cache\n      ids.forEach(id => {\n        queryClient.removeQueries({\n          queryKey: experimentKeys.detail(id)\n        });\n        queryClient.removeQueries({\n          queryKey: experimentKeys.analytics(id)\n        });\n      });\n\n      // Clear selection\n      clearSelection();\n      toast.success(`Successfully deleted ${ids.length} experiments`);\n    }\n  });\n\n  // Exposed methods\n  const bulkUpdateStatus = useCallback(async (ids, status) => {\n    if (ids.length === 0) {\n      throw new Error('No experiments selected');\n    }\n    const result = await bulkStatusMutation.mutateAsync({\n      ids,\n      status\n    });\n    return result.data;\n  }, [bulkStatusMutation]);\n  const bulkDelete = useCallback(async ids => {\n    if (ids.length === 0) {\n      throw new Error('No experiments selected');\n    }\n\n    // Show confirmation for bulk delete\n    const confirmed = window.confirm(`Are you sure you want to delete ${ids.length} experiment${ids.length > 1 ? 's' : ''}? This action cannot be undone.`);\n    if (!confirmed) {\n      return;\n    }\n    await bulkDeleteMutation.mutateAsync(ids);\n  }, [bulkDeleteMutation]);\n\n  // Convenience methods for specific status changes\n  const bulkStart = useCallback(ids => bulkUpdateStatus(ids, 'ACTIVE'), [bulkUpdateStatus]);\n  const bulkPause = useCallback(ids => bulkUpdateStatus(ids, 'PAUSED'), [bulkUpdateStatus]);\n  const bulkComplete = useCallback(ids => bulkUpdateStatus(ids, 'COMPLETED'), [bulkUpdateStatus]);\n  const bulkArchive = useCallback(ids => bulkUpdateStatus(ids, 'ARCHIVED'), [bulkUpdateStatus]);\n\n  // Bulk operation with progress tracking\n  const bulkOperationWithProgress = useCallback(async (ids, operation, operationName) => {\n    const results = {\n      successful: [],\n      failed: []\n    };\n\n    // Show progress toast\n    const progressToast = toast.loading(`${operationName} 0/${ids.length} experiments...`);\n    try {\n      for (let i = 0; i < ids.length; i++) {\n        const id = ids[i];\n        try {\n          await operation(id);\n          results.successful.push(id);\n        } catch (error) {\n          results.failed.push({\n            id,\n            error: error.message || 'Unknown error'\n          });\n        }\n\n        // Update progress\n        toast.loading(`${operationName} ${i + 1}/${ids.length} experiments...`, {\n          id: progressToast\n        });\n      }\n\n      // Show final result\n      toast.dismiss(progressToast);\n      if (results.failed.length === 0) {\n        toast.success(`Successfully ${operationName.toLowerCase()} ${results.successful.length} experiments`);\n      } else if (results.successful.length === 0) {\n        toast.error(`Failed to ${operationName.toLowerCase()} all experiments`);\n      } else {\n        toast.success(`${operationName} completed: ${results.successful.length} successful, ${results.failed.length} failed`);\n      }\n      return results;\n    } catch (error) {\n      toast.dismiss(progressToast);\n      toast.error(`Bulk operation failed: ${error.message}`);\n      throw error;\n    }\n  }, []);\n  return {\n    // Loading states\n    isBulkUpdating: bulkStatusMutation.isLoading,\n    isBulkDeleting: bulkDeleteMutation.isLoading,\n    // Methods\n    bulkUpdateStatus,\n    bulkDelete,\n    bulkOperationWithProgress,\n    // Convenience methods\n    bulkStart,\n    bulkPause,\n    bulkComplete,\n    bulkArchive,\n    // Raw mutations for advanced usage\n    bulkStatusMutation,\n    bulkDeleteMutation\n  };\n};\n_s(useBulkOperations, \"IRFFAHn8avvYLtM0lJyeuO5MQZc=\", false, function () {\n  return [useQueryClient, useExperimentContext, useMutation, useMutation];\n});", "map": {"version": 3, "names": ["useCallback", "useMutation", "useQueryClient", "toast", "experimentsApi", "experimentKeys", "useExperimentContext", "useBulkOperations", "_s", "queryClient", "clearSelection", "bulkStatusMutation", "mutationFn", "ids", "status", "bulkUpdateStatus", "onMutate", "cancelQueries", "query<PERSON><PERSON>", "lists", "previousQueries", "Map", "queryCache", "get<PERSON><PERSON><PERSON><PERSON>ache", "listQueries", "findAll", "for<PERSON>ach", "query", "state", "data", "set", "setQueryData", "old", "map", "exp", "includes", "id", "updatedAt", "Date", "toISOString", "detail", "onError", "error", "context", "length", "message", "onSuccess", "response", "invalidateQueries", "success", "toLowerCase", "bulkDeleteMutation", "bulkDelete", "filteredData", "filter", "pagination", "total", "removeQueries", "analytics", "Error", "result", "mutateAsync", "confirmed", "window", "confirm", "bulkStart", "bulkPause", "bulkComplete", "bulkArchive", "bulkOperationWithProgress", "operation", "operationName", "results", "successful", "failed", "progressToast", "loading", "i", "push", "dismiss", "isBulkUpdating", "isLoading", "isBulkDeleting"], "sources": ["/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/src/hooks/useBulkOperations.ts"], "sourcesContent": ["// Custom hook for bulk experiment operations\nimport { useCallback } from 'react';\nimport { useMutation, useQueryClient } from '@tanstack/react-query';\nimport { toast } from 'react-hot-toast';\nimport {\n  Experiment,\n  ExperimentStatus,\n  PaginatedResponse,\n  ApiError,\n} from '../types/experiment';\nimport { experimentsApi } from '../services/api';\nimport { experimentKeys } from './useExperiments';\nimport { useExperimentContext } from '../contexts/ExperimentContext';\n\nexport const useBulkOperations = () => {\n  const queryClient = useQueryClient();\n  const { clearSelection } = useExperimentContext();\n\n  // Bulk status update mutation\n  const bulkStatusMutation = useMutation({\n    mutationFn: ({ ids, status }: { ids: string[]; status: ExperimentStatus }) =>\n      experimentsApi.bulkUpdateStatus(ids, status),\n    onMutate: async ({ ids, status }) => {\n      // Cancel outgoing refetches\n      await queryClient.cancelQueries({ queryKey: experimentKeys.lists() });\n\n      // Snapshot previous values\n      const previousQueries = new Map();\n      \n      // Get all list queries and store their previous values\n      const queryCache = queryClient.getQueryCache();\n      const listQueries = queryCache.findAll({ queryKey: experimentKeys.lists() });\n      \n      listQueries.forEach(query => {\n        if (query.state.data) {\n          previousQueries.set(query.queryKey, query.state.data);\n        }\n      });\n\n      // Optimistically update all list queries\n      listQueries.forEach(query => {\n        queryClient.setQueryData(\n          query.queryKey,\n          (old: PaginatedResponse<Experiment> | undefined) => {\n            if (!old) return old;\n            return {\n              ...old,\n              data: old.data.map(exp =>\n                ids.includes(exp.id)\n                  ? { ...exp, status, updatedAt: new Date().toISOString() }\n                  : exp\n              ),\n            };\n          }\n        );\n      });\n\n      // Also update individual experiment queries\n      ids.forEach(id => {\n        queryClient.setQueryData(\n          experimentKeys.detail(id),\n          (old: any) => {\n            if (!old) return old;\n            return {\n              ...old,\n              data: {\n                ...old.data,\n                status,\n                updatedAt: new Date().toISOString(),\n              },\n            };\n          }\n        );\n      });\n\n      return { previousQueries };\n    },\n    onError: (error: ApiError, { ids, status }, context) => {\n      // Rollback all optimistic updates\n      if (context?.previousQueries) {\n        context.previousQueries.forEach((data, queryKey) => {\n          queryClient.setQueryData(queryKey, data);\n        });\n      }\n      \n      toast.error(`Failed to update ${ids.length} experiments: ${error.message}`);\n    },\n    onSuccess: (response, { ids, status }) => {\n      // Invalidate all list queries\n      queryClient.invalidateQueries({ queryKey: experimentKeys.lists() });\n      \n      // Invalidate individual experiment queries\n      ids.forEach(id => {\n        queryClient.invalidateQueries({ queryKey: experimentKeys.detail(id) });\n      });\n      \n      // Clear selection\n      clearSelection();\n      \n      toast.success(`Successfully updated ${ids.length} experiments to ${status.toLowerCase()}`);\n    },\n  });\n\n  // Bulk delete mutation\n  const bulkDeleteMutation = useMutation({\n    mutationFn: (ids: string[]) => experimentsApi.bulkDelete(ids),\n    onMutate: async (ids) => {\n      // Cancel outgoing refetches\n      await queryClient.cancelQueries({ queryKey: experimentKeys.lists() });\n\n      // Snapshot previous values\n      const previousQueries = new Map();\n      \n      // Get all list queries and store their previous values\n      const queryCache = queryClient.getQueryCache();\n      const listQueries = queryCache.findAll({ queryKey: experimentKeys.lists() });\n      \n      listQueries.forEach(query => {\n        if (query.state.data) {\n          previousQueries.set(query.queryKey, query.state.data);\n        }\n      });\n\n      // Optimistically remove from all list queries\n      listQueries.forEach(query => {\n        queryClient.setQueryData(\n          query.queryKey,\n          (old: PaginatedResponse<Experiment> | undefined) => {\n            if (!old) return old;\n            const filteredData = old.data.filter(exp => !ids.includes(exp.id));\n            return {\n              ...old,\n              data: filteredData,\n              pagination: {\n                ...old.pagination,\n                total: old.pagination.total - (old.data.length - filteredData.length),\n              },\n            };\n          }\n        );\n      });\n\n      return { previousQueries };\n    },\n    onError: (error: ApiError, ids, context) => {\n      // Rollback all optimistic updates\n      if (context?.previousQueries) {\n        context.previousQueries.forEach((data, queryKey) => {\n          queryClient.setQueryData(queryKey, data);\n        });\n      }\n      \n      toast.error(`Failed to delete ${ids.length} experiments: ${error.message}`);\n    },\n    onSuccess: (response, ids) => {\n      // Invalidate all list queries\n      queryClient.invalidateQueries({ queryKey: experimentKeys.lists() });\n      \n      // Remove individual experiment queries from cache\n      ids.forEach(id => {\n        queryClient.removeQueries({ queryKey: experimentKeys.detail(id) });\n        queryClient.removeQueries({ queryKey: experimentKeys.analytics(id) });\n      });\n      \n      // Clear selection\n      clearSelection();\n      \n      toast.success(`Successfully deleted ${ids.length} experiments`);\n    },\n  });\n\n  // Exposed methods\n  const bulkUpdateStatus = useCallback(\n    async (ids: string[], status: ExperimentStatus): Promise<Experiment[]> => {\n      if (ids.length === 0) {\n        throw new Error('No experiments selected');\n      }\n      \n      const result = await bulkStatusMutation.mutateAsync({ ids, status });\n      return result.data;\n    },\n    [bulkStatusMutation]\n  );\n\n  const bulkDelete = useCallback(\n    async (ids: string[]): Promise<void> => {\n      if (ids.length === 0) {\n        throw new Error('No experiments selected');\n      }\n      \n      // Show confirmation for bulk delete\n      const confirmed = window.confirm(\n        `Are you sure you want to delete ${ids.length} experiment${ids.length > 1 ? 's' : ''}? This action cannot be undone.`\n      );\n      \n      if (!confirmed) {\n        return;\n      }\n      \n      await bulkDeleteMutation.mutateAsync(ids);\n    },\n    [bulkDeleteMutation]\n  );\n\n  // Convenience methods for specific status changes\n  const bulkStart = useCallback(\n    (ids: string[]) => bulkUpdateStatus(ids, 'ACTIVE'),\n    [bulkUpdateStatus]\n  );\n\n  const bulkPause = useCallback(\n    (ids: string[]) => bulkUpdateStatus(ids, 'PAUSED'),\n    [bulkUpdateStatus]\n  );\n\n  const bulkComplete = useCallback(\n    (ids: string[]) => bulkUpdateStatus(ids, 'COMPLETED'),\n    [bulkUpdateStatus]\n  );\n\n  const bulkArchive = useCallback(\n    (ids: string[]) => bulkUpdateStatus(ids, 'ARCHIVED'),\n    [bulkUpdateStatus]\n  );\n\n  // Bulk operation with progress tracking\n  const bulkOperationWithProgress = useCallback(\n    async (\n      ids: string[],\n      operation: (id: string) => Promise<any>,\n      operationName: string\n    ): Promise<{ successful: string[]; failed: { id: string; error: string }[] }> => {\n      const results = {\n        successful: [] as string[],\n        failed: [] as { id: string; error: string }[],\n      };\n\n      // Show progress toast\n      const progressToast = toast.loading(`${operationName} 0/${ids.length} experiments...`);\n\n      try {\n        for (let i = 0; i < ids.length; i++) {\n          const id = ids[i];\n          \n          try {\n            await operation(id);\n            results.successful.push(id);\n          } catch (error: any) {\n            results.failed.push({\n              id,\n              error: error.message || 'Unknown error',\n            });\n          }\n\n          // Update progress\n          toast.loading(\n            `${operationName} ${i + 1}/${ids.length} experiments...`,\n            { id: progressToast }\n          );\n        }\n\n        // Show final result\n        toast.dismiss(progressToast);\n        \n        if (results.failed.length === 0) {\n          toast.success(`Successfully ${operationName.toLowerCase()} ${results.successful.length} experiments`);\n        } else if (results.successful.length === 0) {\n          toast.error(`Failed to ${operationName.toLowerCase()} all experiments`);\n        } else {\n          toast.success(\n            `${operationName} completed: ${results.successful.length} successful, ${results.failed.length} failed`\n          );\n        }\n\n        return results;\n      } catch (error) {\n        toast.dismiss(progressToast);\n        toast.error(`Bulk operation failed: ${error.message}`);\n        throw error;\n      }\n    },\n    []\n  );\n\n  return {\n    // Loading states\n    isBulkUpdating: bulkStatusMutation.isLoading,\n    isBulkDeleting: bulkDeleteMutation.isLoading,\n    \n    // Methods\n    bulkUpdateStatus,\n    bulkDelete,\n    bulkOperationWithProgress,\n    \n    // Convenience methods\n    bulkStart,\n    bulkPause,\n    bulkComplete,\n    bulkArchive,\n    \n    // Raw mutations for advanced usage\n    bulkStatusMutation,\n    bulkDeleteMutation,\n  };\n};\n"], "mappings": ";AAAA;AACA,SAASA,WAAW,QAAQ,OAAO;AACnC,SAASC,WAAW,EAAEC,cAAc,QAAQ,uBAAuB;AACnE,SAASC,KAAK,QAAQ,iBAAiB;AAOvC,SAASC,cAAc,QAAQ,iBAAiB;AAChD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,oBAAoB,QAAQ,+BAA+B;AAEpE,OAAO,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAMC,WAAW,GAAGP,cAAc,CAAC,CAAC;EACpC,MAAM;IAAEQ;EAAe,CAAC,GAAGJ,oBAAoB,CAAC,CAAC;;EAEjD;EACA,MAAMK,kBAAkB,GAAGV,WAAW,CAAC;IACrCW,UAAU,EAAEA,CAAC;MAAEC,GAAG;MAAEC;IAAoD,CAAC,KACvEV,cAAc,CAACW,gBAAgB,CAACF,GAAG,EAAEC,MAAM,CAAC;IAC9CE,QAAQ,EAAE,MAAAA,CAAO;MAAEH,GAAG;MAAEC;IAAO,CAAC,KAAK;MACnC;MACA,MAAML,WAAW,CAACQ,aAAa,CAAC;QAAEC,QAAQ,EAAEb,cAAc,CAACc,KAAK,CAAC;MAAE,CAAC,CAAC;;MAErE;MACA,MAAMC,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;;MAEjC;MACA,MAAMC,UAAU,GAAGb,WAAW,CAACc,aAAa,CAAC,CAAC;MAC9C,MAAMC,WAAW,GAAGF,UAAU,CAACG,OAAO,CAAC;QAAEP,QAAQ,EAAEb,cAAc,CAACc,KAAK,CAAC;MAAE,CAAC,CAAC;MAE5EK,WAAW,CAACE,OAAO,CAACC,KAAK,IAAI;QAC3B,IAAIA,KAAK,CAACC,KAAK,CAACC,IAAI,EAAE;UACpBT,eAAe,CAACU,GAAG,CAACH,KAAK,CAACT,QAAQ,EAAES,KAAK,CAACC,KAAK,CAACC,IAAI,CAAC;QACvD;MACF,CAAC,CAAC;;MAEF;MACAL,WAAW,CAACE,OAAO,CAACC,KAAK,IAAI;QAC3BlB,WAAW,CAACsB,YAAY,CACtBJ,KAAK,CAACT,QAAQ,EACbc,GAA8C,IAAK;UAClD,IAAI,CAACA,GAAG,EAAE,OAAOA,GAAG;UACpB,OAAO;YACL,GAAGA,GAAG;YACNH,IAAI,EAAEG,GAAG,CAACH,IAAI,CAACI,GAAG,CAACC,GAAG,IACpBrB,GAAG,CAACsB,QAAQ,CAACD,GAAG,CAACE,EAAE,CAAC,GAChB;cAAE,GAAGF,GAAG;cAAEpB,MAAM;cAAEuB,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;YAAE,CAAC,GACvDL,GACN;UACF,CAAC;QACH,CACF,CAAC;MACH,CAAC,CAAC;;MAEF;MACArB,GAAG,CAACa,OAAO,CAACU,EAAE,IAAI;QAChB3B,WAAW,CAACsB,YAAY,CACtB1B,cAAc,CAACmC,MAAM,CAACJ,EAAE,CAAC,EACxBJ,GAAQ,IAAK;UACZ,IAAI,CAACA,GAAG,EAAE,OAAOA,GAAG;UACpB,OAAO;YACL,GAAGA,GAAG;YACNH,IAAI,EAAE;cACJ,GAAGG,GAAG,CAACH,IAAI;cACXf,MAAM;cACNuB,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;YACpC;UACF,CAAC;QACH,CACF,CAAC;MACH,CAAC,CAAC;MAEF,OAAO;QAAEnB;MAAgB,CAAC;IAC5B,CAAC;IACDqB,OAAO,EAAEA,CAACC,KAAe,EAAE;MAAE7B,GAAG;MAAEC;IAAO,CAAC,EAAE6B,OAAO,KAAK;MACtD;MACA,IAAIA,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEvB,eAAe,EAAE;QAC5BuB,OAAO,CAACvB,eAAe,CAACM,OAAO,CAAC,CAACG,IAAI,EAAEX,QAAQ,KAAK;UAClDT,WAAW,CAACsB,YAAY,CAACb,QAAQ,EAAEW,IAAI,CAAC;QAC1C,CAAC,CAAC;MACJ;MAEA1B,KAAK,CAACuC,KAAK,CAAC,oBAAoB7B,GAAG,CAAC+B,MAAM,iBAAiBF,KAAK,CAACG,OAAO,EAAE,CAAC;IAC7E,CAAC;IACDC,SAAS,EAAEA,CAACC,QAAQ,EAAE;MAAElC,GAAG;MAAEC;IAAO,CAAC,KAAK;MACxC;MACAL,WAAW,CAACuC,iBAAiB,CAAC;QAAE9B,QAAQ,EAAEb,cAAc,CAACc,KAAK,CAAC;MAAE,CAAC,CAAC;;MAEnE;MACAN,GAAG,CAACa,OAAO,CAACU,EAAE,IAAI;QAChB3B,WAAW,CAACuC,iBAAiB,CAAC;UAAE9B,QAAQ,EAAEb,cAAc,CAACmC,MAAM,CAACJ,EAAE;QAAE,CAAC,CAAC;MACxE,CAAC,CAAC;;MAEF;MACA1B,cAAc,CAAC,CAAC;MAEhBP,KAAK,CAAC8C,OAAO,CAAC,wBAAwBpC,GAAG,CAAC+B,MAAM,mBAAmB9B,MAAM,CAACoC,WAAW,CAAC,CAAC,EAAE,CAAC;IAC5F;EACF,CAAC,CAAC;;EAEF;EACA,MAAMC,kBAAkB,GAAGlD,WAAW,CAAC;IACrCW,UAAU,EAAGC,GAAa,IAAKT,cAAc,CAACgD,UAAU,CAACvC,GAAG,CAAC;IAC7DG,QAAQ,EAAE,MAAOH,GAAG,IAAK;MACvB;MACA,MAAMJ,WAAW,CAACQ,aAAa,CAAC;QAAEC,QAAQ,EAAEb,cAAc,CAACc,KAAK,CAAC;MAAE,CAAC,CAAC;;MAErE;MACA,MAAMC,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;;MAEjC;MACA,MAAMC,UAAU,GAAGb,WAAW,CAACc,aAAa,CAAC,CAAC;MAC9C,MAAMC,WAAW,GAAGF,UAAU,CAACG,OAAO,CAAC;QAAEP,QAAQ,EAAEb,cAAc,CAACc,KAAK,CAAC;MAAE,CAAC,CAAC;MAE5EK,WAAW,CAACE,OAAO,CAACC,KAAK,IAAI;QAC3B,IAAIA,KAAK,CAACC,KAAK,CAACC,IAAI,EAAE;UACpBT,eAAe,CAACU,GAAG,CAACH,KAAK,CAACT,QAAQ,EAAES,KAAK,CAACC,KAAK,CAACC,IAAI,CAAC;QACvD;MACF,CAAC,CAAC;;MAEF;MACAL,WAAW,CAACE,OAAO,CAACC,KAAK,IAAI;QAC3BlB,WAAW,CAACsB,YAAY,CACtBJ,KAAK,CAACT,QAAQ,EACbc,GAA8C,IAAK;UAClD,IAAI,CAACA,GAAG,EAAE,OAAOA,GAAG;UACpB,MAAMqB,YAAY,GAAGrB,GAAG,CAACH,IAAI,CAACyB,MAAM,CAACpB,GAAG,IAAI,CAACrB,GAAG,CAACsB,QAAQ,CAACD,GAAG,CAACE,EAAE,CAAC,CAAC;UAClE,OAAO;YACL,GAAGJ,GAAG;YACNH,IAAI,EAAEwB,YAAY;YAClBE,UAAU,EAAE;cACV,GAAGvB,GAAG,CAACuB,UAAU;cACjBC,KAAK,EAAExB,GAAG,CAACuB,UAAU,CAACC,KAAK,IAAIxB,GAAG,CAACH,IAAI,CAACe,MAAM,GAAGS,YAAY,CAACT,MAAM;YACtE;UACF,CAAC;QACH,CACF,CAAC;MACH,CAAC,CAAC;MAEF,OAAO;QAAExB;MAAgB,CAAC;IAC5B,CAAC;IACDqB,OAAO,EAAEA,CAACC,KAAe,EAAE7B,GAAG,EAAE8B,OAAO,KAAK;MAC1C;MACA,IAAIA,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEvB,eAAe,EAAE;QAC5BuB,OAAO,CAACvB,eAAe,CAACM,OAAO,CAAC,CAACG,IAAI,EAAEX,QAAQ,KAAK;UAClDT,WAAW,CAACsB,YAAY,CAACb,QAAQ,EAAEW,IAAI,CAAC;QAC1C,CAAC,CAAC;MACJ;MAEA1B,KAAK,CAACuC,KAAK,CAAC,oBAAoB7B,GAAG,CAAC+B,MAAM,iBAAiBF,KAAK,CAACG,OAAO,EAAE,CAAC;IAC7E,CAAC;IACDC,SAAS,EAAEA,CAACC,QAAQ,EAAElC,GAAG,KAAK;MAC5B;MACAJ,WAAW,CAACuC,iBAAiB,CAAC;QAAE9B,QAAQ,EAAEb,cAAc,CAACc,KAAK,CAAC;MAAE,CAAC,CAAC;;MAEnE;MACAN,GAAG,CAACa,OAAO,CAACU,EAAE,IAAI;QAChB3B,WAAW,CAACgD,aAAa,CAAC;UAAEvC,QAAQ,EAAEb,cAAc,CAACmC,MAAM,CAACJ,EAAE;QAAE,CAAC,CAAC;QAClE3B,WAAW,CAACgD,aAAa,CAAC;UAAEvC,QAAQ,EAAEb,cAAc,CAACqD,SAAS,CAACtB,EAAE;QAAE,CAAC,CAAC;MACvE,CAAC,CAAC;;MAEF;MACA1B,cAAc,CAAC,CAAC;MAEhBP,KAAK,CAAC8C,OAAO,CAAC,wBAAwBpC,GAAG,CAAC+B,MAAM,cAAc,CAAC;IACjE;EACF,CAAC,CAAC;;EAEF;EACA,MAAM7B,gBAAgB,GAAGf,WAAW,CAClC,OAAOa,GAAa,EAAEC,MAAwB,KAA4B;IACxE,IAAID,GAAG,CAAC+B,MAAM,KAAK,CAAC,EAAE;MACpB,MAAM,IAAIe,KAAK,CAAC,yBAAyB,CAAC;IAC5C;IAEA,MAAMC,MAAM,GAAG,MAAMjD,kBAAkB,CAACkD,WAAW,CAAC;MAAEhD,GAAG;MAAEC;IAAO,CAAC,CAAC;IACpE,OAAO8C,MAAM,CAAC/B,IAAI;EACpB,CAAC,EACD,CAAClB,kBAAkB,CACrB,CAAC;EAED,MAAMyC,UAAU,GAAGpD,WAAW,CAC5B,MAAOa,GAAa,IAAoB;IACtC,IAAIA,GAAG,CAAC+B,MAAM,KAAK,CAAC,EAAE;MACpB,MAAM,IAAIe,KAAK,CAAC,yBAAyB,CAAC;IAC5C;;IAEA;IACA,MAAMG,SAAS,GAAGC,MAAM,CAACC,OAAO,CAC9B,mCAAmCnD,GAAG,CAAC+B,MAAM,cAAc/B,GAAG,CAAC+B,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,iCACtF,CAAC;IAED,IAAI,CAACkB,SAAS,EAAE;MACd;IACF;IAEA,MAAMX,kBAAkB,CAACU,WAAW,CAAChD,GAAG,CAAC;EAC3C,CAAC,EACD,CAACsC,kBAAkB,CACrB,CAAC;;EAED;EACA,MAAMc,SAAS,GAAGjE,WAAW,CAC1Ba,GAAa,IAAKE,gBAAgB,CAACF,GAAG,EAAE,QAAQ,CAAC,EAClD,CAACE,gBAAgB,CACnB,CAAC;EAED,MAAMmD,SAAS,GAAGlE,WAAW,CAC1Ba,GAAa,IAAKE,gBAAgB,CAACF,GAAG,EAAE,QAAQ,CAAC,EAClD,CAACE,gBAAgB,CACnB,CAAC;EAED,MAAMoD,YAAY,GAAGnE,WAAW,CAC7Ba,GAAa,IAAKE,gBAAgB,CAACF,GAAG,EAAE,WAAW,CAAC,EACrD,CAACE,gBAAgB,CACnB,CAAC;EAED,MAAMqD,WAAW,GAAGpE,WAAW,CAC5Ba,GAAa,IAAKE,gBAAgB,CAACF,GAAG,EAAE,UAAU,CAAC,EACpD,CAACE,gBAAgB,CACnB,CAAC;;EAED;EACA,MAAMsD,yBAAyB,GAAGrE,WAAW,CAC3C,OACEa,GAAa,EACbyD,SAAuC,EACvCC,aAAqB,KAC0D;IAC/E,MAAMC,OAAO,GAAG;MACdC,UAAU,EAAE,EAAc;MAC1BC,MAAM,EAAE;IACV,CAAC;;IAED;IACA,MAAMC,aAAa,GAAGxE,KAAK,CAACyE,OAAO,CAAC,GAAGL,aAAa,MAAM1D,GAAG,CAAC+B,MAAM,iBAAiB,CAAC;IAEtF,IAAI;MACF,KAAK,IAAIiC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhE,GAAG,CAAC+B,MAAM,EAAEiC,CAAC,EAAE,EAAE;QACnC,MAAMzC,EAAE,GAAGvB,GAAG,CAACgE,CAAC,CAAC;QAEjB,IAAI;UACF,MAAMP,SAAS,CAAClC,EAAE,CAAC;UACnBoC,OAAO,CAACC,UAAU,CAACK,IAAI,CAAC1C,EAAE,CAAC;QAC7B,CAAC,CAAC,OAAOM,KAAU,EAAE;UACnB8B,OAAO,CAACE,MAAM,CAACI,IAAI,CAAC;YAClB1C,EAAE;YACFM,KAAK,EAAEA,KAAK,CAACG,OAAO,IAAI;UAC1B,CAAC,CAAC;QACJ;;QAEA;QACA1C,KAAK,CAACyE,OAAO,CACX,GAAGL,aAAa,IAAIM,CAAC,GAAG,CAAC,IAAIhE,GAAG,CAAC+B,MAAM,iBAAiB,EACxD;UAAER,EAAE,EAAEuC;QAAc,CACtB,CAAC;MACH;;MAEA;MACAxE,KAAK,CAAC4E,OAAO,CAACJ,aAAa,CAAC;MAE5B,IAAIH,OAAO,CAACE,MAAM,CAAC9B,MAAM,KAAK,CAAC,EAAE;QAC/BzC,KAAK,CAAC8C,OAAO,CAAC,gBAAgBsB,aAAa,CAACrB,WAAW,CAAC,CAAC,IAAIsB,OAAO,CAACC,UAAU,CAAC7B,MAAM,cAAc,CAAC;MACvG,CAAC,MAAM,IAAI4B,OAAO,CAACC,UAAU,CAAC7B,MAAM,KAAK,CAAC,EAAE;QAC1CzC,KAAK,CAACuC,KAAK,CAAC,aAAa6B,aAAa,CAACrB,WAAW,CAAC,CAAC,kBAAkB,CAAC;MACzE,CAAC,MAAM;QACL/C,KAAK,CAAC8C,OAAO,CACX,GAAGsB,aAAa,eAAeC,OAAO,CAACC,UAAU,CAAC7B,MAAM,gBAAgB4B,OAAO,CAACE,MAAM,CAAC9B,MAAM,SAC/F,CAAC;MACH;MAEA,OAAO4B,OAAO;IAChB,CAAC,CAAC,OAAO9B,KAAK,EAAE;MACdvC,KAAK,CAAC4E,OAAO,CAACJ,aAAa,CAAC;MAC5BxE,KAAK,CAACuC,KAAK,CAAC,0BAA0BA,KAAK,CAACG,OAAO,EAAE,CAAC;MACtD,MAAMH,KAAK;IACb;EACF,CAAC,EACD,EACF,CAAC;EAED,OAAO;IACL;IACAsC,cAAc,EAAErE,kBAAkB,CAACsE,SAAS;IAC5CC,cAAc,EAAE/B,kBAAkB,CAAC8B,SAAS;IAE5C;IACAlE,gBAAgB;IAChBqC,UAAU;IACViB,yBAAyB;IAEzB;IACAJ,SAAS;IACTC,SAAS;IACTC,YAAY;IACZC,WAAW;IAEX;IACAzD,kBAAkB;IAClBwC;EACF,CAAC;AACH,CAAC;AAAC3C,EAAA,CAlSWD,iBAAiB;EAAA,QACRL,cAAc,EACPI,oBAAoB,EAGpBL,WAAW,EAqFXA,WAAW;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
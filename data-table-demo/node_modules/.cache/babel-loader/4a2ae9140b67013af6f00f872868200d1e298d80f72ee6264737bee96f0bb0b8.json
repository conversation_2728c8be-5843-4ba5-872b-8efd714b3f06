{"ast": null, "code": "\"use client\";\n\n// src/useBaseQuery.ts\nimport * as React from \"react\";\nimport { isServer, noop, notify<PERSON>anager } from \"@tanstack/query-core\";\nimport { useQueryClient } from \"./QueryClientProvider.js\";\nimport { useQueryErrorResetBoundary } from \"./QueryErrorResetBoundary.js\";\nimport { ensurePreventErrorBoundaryRetry, getHasError, useClearResetErrorBoundary } from \"./errorBoundaryUtils.js\";\nimport { useIsRestoring } from \"./IsRestoringProvider.js\";\nimport { ensureSuspenseTimers, fetchOptimistic, shouldSuspend, willFetch } from \"./suspense.js\";\nfunction useBaseQuery(options, Observer, queryClient) {\n  if (process.env.NODE_ENV !== \"production\") {\n    if (typeof options !== \"object\" || Array.isArray(options)) {\n      throw new Error('Bad argument type. Starting with v5, only the \"Object\" form is allowed when calling query related functions. Please use the error stack to find the culprit call. More info here: https://tanstack.com/query/latest/docs/react/guides/migrating-to-v5#supports-a-single-signature-one-object');\n    }\n  }\n  const isRestoring = useIsRestoring();\n  const errorResetBoundary = useQueryErrorResetBoundary();\n  const client = useQueryClient(queryClient);\n  const defaultedOptions = client.defaultQueryOptions(options);\n  client.getDefaultOptions().queries?._experimental_beforeQuery?.(defaultedOptions);\n  if (process.env.NODE_ENV !== \"production\") {\n    if (!defaultedOptions.queryFn) {\n      console.error(`[${defaultedOptions.queryHash}]: No queryFn was passed as an option, and no default queryFn was found. The queryFn parameter is only optional when using a default queryFn. More info here: https://tanstack.com/query/latest/docs/framework/react/guides/default-query-function`);\n    }\n  }\n  defaultedOptions._optimisticResults = isRestoring ? \"isRestoring\" : \"optimistic\";\n  ensureSuspenseTimers(defaultedOptions);\n  ensurePreventErrorBoundaryRetry(defaultedOptions, errorResetBoundary);\n  useClearResetErrorBoundary(errorResetBoundary);\n  const isNewCacheEntry = !client.getQueryCache().get(defaultedOptions.queryHash);\n  const [observer] = React.useState(() => new Observer(client, defaultedOptions));\n  const result = observer.getOptimisticResult(defaultedOptions);\n  const shouldSubscribe = !isRestoring && options.subscribed !== false;\n  React.useSyncExternalStore(React.useCallback(onStoreChange => {\n    const unsubscribe = shouldSubscribe ? observer.subscribe(notifyManager.batchCalls(onStoreChange)) : noop;\n    observer.updateResult();\n    return unsubscribe;\n  }, [observer, shouldSubscribe]), () => observer.getCurrentResult(), () => observer.getCurrentResult());\n  React.useEffect(() => {\n    observer.setOptions(defaultedOptions);\n  }, [defaultedOptions, observer]);\n  if (shouldSuspend(defaultedOptions, result)) {\n    throw fetchOptimistic(defaultedOptions, observer, errorResetBoundary);\n  }\n  if (getHasError({\n    result,\n    errorResetBoundary,\n    throwOnError: defaultedOptions.throwOnError,\n    query: client.getQueryCache().get(defaultedOptions.queryHash),\n    suspense: defaultedOptions.suspense\n  })) {\n    throw result.error;\n  }\n  ;\n  client.getDefaultOptions().queries?._experimental_afterQuery?.(defaultedOptions, result);\n  if (defaultedOptions.experimental_prefetchInRender && !isServer && willFetch(result, isRestoring)) {\n    const promise = isNewCacheEntry ?\n    // Fetch immediately on render in order to ensure `.promise` is resolved even if the component is unmounted\n    fetchOptimistic(defaultedOptions, observer, errorResetBoundary) :\n    // subscribe to the \"cache promise\" so that we can finalize the currentThenable once data comes in\n    client.getQueryCache().get(defaultedOptions.queryHash)?.promise;\n    promise?.catch(noop).finally(() => {\n      observer.updateResult();\n    });\n  }\n  return !defaultedOptions.notifyOnChangeProps ? observer.trackResult(result) : result;\n}\nexport { useBaseQuery };", "map": {"version": 3, "names": ["React", "isServer", "noop", "notify<PERSON><PERSON>ger", "useQueryClient", "useQueryErrorResetBoundary", "ensurePreventErrorBoundaryRetry", "getHasError", "useClearResetErrorBoundary", "useIsRestoring", "ensureSuspenseTimers", "fetchOptimistic", "shouldSuspend", "<PERSON><PERSON><PERSON><PERSON>", "useBaseQuery", "options", "Observer", "queryClient", "process", "env", "NODE_ENV", "Array", "isArray", "Error", "isRestoring", "errorResetBoundary", "client", "defaultedOptions", "defaultQueryOptions", "getDefaultOptions", "queries", "_experimental_beforeQ<PERSON>y", "queryFn", "console", "error", "queryHash", "_optimisticResults", "isNewCacheEntry", "get<PERSON><PERSON><PERSON><PERSON>ache", "get", "observer", "useState", "result", "getOptimisticResult", "shouldSubscribe", "subscribed", "useSyncExternalStore", "useCallback", "onStoreChange", "unsubscribe", "subscribe", "batchCalls", "updateResult", "getCurrentResult", "useEffect", "setOptions", "throwOnError", "query", "suspense", "_experimental_afterQuery", "experimental_prefetchInRender", "promise", "catch", "finally", "notifyOnChangeProps", "trackResult"], "sources": ["/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/node_modules/@tanstack/react-query/src/useBaseQuery.ts"], "sourcesContent": ["'use client'\nimport * as React from 'react'\n\nimport { isServer, noop, notifyManager } from '@tanstack/query-core'\nimport { useQueryClient } from './QueryClientProvider'\nimport { useQueryErrorResetBoundary } from './QueryErrorResetBoundary'\nimport {\n  ensurePreventErrorBoundaryRetry,\n  getHasError,\n  useClearResetErrorBoundary,\n} from './errorBoundaryUtils'\nimport { useIsRestoring } from './IsRestoringProvider'\nimport {\n  ensureSuspenseTimers,\n  fetchOptimistic,\n  shouldSuspend,\n  willFetch,\n} from './suspense'\nimport type {\n  QueryClient,\n  QueryKey,\n  QueryObserver,\n  QueryObserverResult,\n} from '@tanstack/query-core'\nimport type { UseBaseQueryOptions } from './types'\n\nexport function useBaseQuery<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryData,\n  <PERSON><PERSON><PERSON>y<PERSON>ey extends QueryKey,\n>(\n  options: UseBaseQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n  Observer: typeof QueryObserver,\n  queryClient?: QueryClient,\n): QueryObserverResult<TData, TError> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (typeof options !== 'object' || Array.isArray(options)) {\n      throw new Error(\n        'Bad argument type. Starting with v5, only the \"Object\" form is allowed when calling query related functions. Please use the error stack to find the culprit call. More info here: https://tanstack.com/query/latest/docs/react/guides/migrating-to-v5#supports-a-single-signature-one-object',\n      )\n    }\n  }\n\n  const isRestoring = useIsRestoring()\n  const errorResetBoundary = useQueryErrorResetBoundary()\n  const client = useQueryClient(queryClient)\n  const defaultedOptions = client.defaultQueryOptions(options)\n\n  ;(client.getDefaultOptions().queries as any)?._experimental_beforeQuery?.(\n    defaultedOptions,\n  )\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (!defaultedOptions.queryFn) {\n      console.error(\n        `[${defaultedOptions.queryHash}]: No queryFn was passed as an option, and no default queryFn was found. The queryFn parameter is only optional when using a default queryFn. More info here: https://tanstack.com/query/latest/docs/framework/react/guides/default-query-function`,\n      )\n    }\n  }\n\n  // Make sure results are optimistically set in fetching state before subscribing or updating options\n  defaultedOptions._optimisticResults = isRestoring\n    ? 'isRestoring'\n    : 'optimistic'\n\n  ensureSuspenseTimers(defaultedOptions)\n  ensurePreventErrorBoundaryRetry(defaultedOptions, errorResetBoundary)\n\n  useClearResetErrorBoundary(errorResetBoundary)\n\n  // this needs to be invoked before creating the Observer because that can create a cache entry\n  const isNewCacheEntry = !client\n    .getQueryCache()\n    .get(defaultedOptions.queryHash)\n\n  const [observer] = React.useState(\n    () =>\n      new Observer<TQueryFnData, TError, TData, TQueryData, TQueryKey>(\n        client,\n        defaultedOptions,\n      ),\n  )\n\n  // note: this must be called before useSyncExternalStore\n  const result = observer.getOptimisticResult(defaultedOptions)\n\n  const shouldSubscribe = !isRestoring && options.subscribed !== false\n  React.useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) => {\n        const unsubscribe = shouldSubscribe\n          ? observer.subscribe(notifyManager.batchCalls(onStoreChange))\n          : noop\n\n        // Update result to make sure we did not miss any query updates\n        // between creating the observer and subscribing to it.\n        observer.updateResult()\n\n        return unsubscribe\n      },\n      [observer, shouldSubscribe],\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult(),\n  )\n\n  React.useEffect(() => {\n    observer.setOptions(defaultedOptions)\n  }, [defaultedOptions, observer])\n\n  // Handle suspense\n  if (shouldSuspend(defaultedOptions, result)) {\n    throw fetchOptimistic(defaultedOptions, observer, errorResetBoundary)\n  }\n\n  // Handle error boundary\n  if (\n    getHasError({\n      result,\n      errorResetBoundary,\n      throwOnError: defaultedOptions.throwOnError,\n      query: client\n        .getQueryCache()\n        .get<\n          TQueryFnData,\n          TError,\n          TQueryData,\n          TQueryKey\n        >(defaultedOptions.queryHash),\n      suspense: defaultedOptions.suspense,\n    })\n  ) {\n    throw result.error\n  }\n\n  ;(client.getDefaultOptions().queries as any)?._experimental_afterQuery?.(\n    defaultedOptions,\n    result,\n  )\n\n  if (\n    defaultedOptions.experimental_prefetchInRender &&\n    !isServer &&\n    willFetch(result, isRestoring)\n  ) {\n    const promise = isNewCacheEntry\n      ? // Fetch immediately on render in order to ensure `.promise` is resolved even if the component is unmounted\n        fetchOptimistic(defaultedOptions, observer, errorResetBoundary)\n      : // subscribe to the \"cache promise\" so that we can finalize the currentThenable once data comes in\n        client.getQueryCache().get(defaultedOptions.queryHash)?.promise\n\n    promise?.catch(noop).finally(() => {\n      // `.updateResult()` will trigger `.#currentThenable` to finalize\n      observer.updateResult()\n    })\n  }\n\n  // Handle result property usage tracking\n  return !defaultedOptions.notifyOnChangeProps\n    ? observer.trackResult(result)\n    : result\n}\n"], "mappings": ";;;AACA,YAAYA,KAAA,MAAW;AAEvB,SAASC,QAAA,EAAUC,IAAA,EAAMC,aAAA,QAAqB;AAC9C,SAASC,cAAA,QAAsB;AAC/B,SAASC,0BAAA,QAAkC;AAC3C,SACEC,+BAAA,EACAC,WAAA,EACAC,0BAAA,QACK;AACP,SAASC,cAAA,QAAsB;AAC/B,SACEC,oBAAA,EACAC,eAAA,EACAC,aAAA,EACAC,SAAA,QACK;AASA,SAASC,aAOdC,OAAA,EAOAC,QAAA,EACAC,WAAA,EACoC;EACpC,IAAIC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;IACzC,IAAI,OAAOL,OAAA,KAAY,YAAYM,KAAA,CAAMC,OAAA,CAAQP,OAAO,GAAG;MACzD,MAAM,IAAIQ,KAAA,CACR,8RACF;IACF;EACF;EAEA,MAAMC,WAAA,GAAcf,cAAA,CAAe;EACnC,MAAMgB,kBAAA,GAAqBpB,0BAAA,CAA2B;EACtD,MAAMqB,MAAA,GAAStB,cAAA,CAAea,WAAW;EACzC,MAAMU,gBAAA,GAAmBD,MAAA,CAAOE,mBAAA,CAAoBb,OAAO;EAEzDW,MAAA,CAAOG,iBAAA,CAAkB,EAAEC,OAAA,EAAiBC,yBAAA,GAC5CJ,gBACF;EAEA,IAAIT,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;IACzC,IAAI,CAACO,gBAAA,CAAiBK,OAAA,EAAS;MAC7BC,OAAA,CAAQC,KAAA,CACN,IAAIP,gBAAA,CAAiBQ,SAAS,oPAChC;IACF;EACF;EAGAR,gBAAA,CAAiBS,kBAAA,GAAqBZ,WAAA,GAClC,gBACA;EAEJd,oBAAA,CAAqBiB,gBAAgB;EACrCrB,+BAAA,CAAgCqB,gBAAA,EAAkBF,kBAAkB;EAEpEjB,0BAAA,CAA2BiB,kBAAkB;EAG7C,MAAMY,eAAA,GAAkB,CAACX,MAAA,CACtBY,aAAA,CAAc,EACdC,GAAA,CAAIZ,gBAAA,CAAiBQ,SAAS;EAEjC,MAAM,CAACK,QAAQ,IAAUxC,KAAA,CAAAyC,QAAA,CACvB,MACE,IAAIzB,QAAA,CACFU,MAAA,EACAC,gBACF,CACJ;EAGA,MAAMe,MAAA,GAASF,QAAA,CAASG,mBAAA,CAAoBhB,gBAAgB;EAE5D,MAAMiB,eAAA,GAAkB,CAACpB,WAAA,IAAeT,OAAA,CAAQ8B,UAAA,KAAe;EACzD7C,KAAA,CAAA8C,oBAAA,CACE9C,KAAA,CAAA+C,WAAA,CACHC,aAAA,IAAkB;IACjB,MAAMC,WAAA,GAAcL,eAAA,GAChBJ,QAAA,CAASU,SAAA,CAAU/C,aAAA,CAAcgD,UAAA,CAAWH,aAAa,CAAC,IAC1D9C,IAAA;IAIJsC,QAAA,CAASY,YAAA,CAAa;IAEtB,OAAOH,WAAA;EACT,GACA,CAACT,QAAA,EAAUI,eAAe,CAC5B,GACA,MAAMJ,QAAA,CAASa,gBAAA,CAAiB,GAChC,MAAMb,QAAA,CAASa,gBAAA,CAAiB,CAClC;EAEMrD,KAAA,CAAAsD,SAAA,CAAU,MAAM;IACpBd,QAAA,CAASe,UAAA,CAAW5B,gBAAgB;EACtC,GAAG,CAACA,gBAAA,EAAkBa,QAAQ,CAAC;EAG/B,IAAI5B,aAAA,CAAce,gBAAA,EAAkBe,MAAM,GAAG;IAC3C,MAAM/B,eAAA,CAAgBgB,gBAAA,EAAkBa,QAAA,EAAUf,kBAAkB;EACtE;EAGA,IACElB,WAAA,CAAY;IACVmC,MAAA;IACAjB,kBAAA;IACA+B,YAAA,EAAc7B,gBAAA,CAAiB6B,YAAA;IAC/BC,KAAA,EAAO/B,MAAA,CACJY,aAAA,CAAc,EACdC,GAAA,CAKCZ,gBAAA,CAAiBQ,SAAS;IAC9BuB,QAAA,EAAU/B,gBAAA,CAAiB+B;EAC7B,CAAC,GACD;IACA,MAAMhB,MAAA,CAAOR,KAAA;EACf;EAEA;EAAER,MAAA,CAAOG,iBAAA,CAAkB,EAAEC,OAAA,EAAiB6B,wBAAA,GAC5ChC,gBAAA,EACAe,MACF;EAEA,IACEf,gBAAA,CAAiBiC,6BAAA,IACjB,CAAC3D,QAAA,IACDY,SAAA,CAAU6B,MAAA,EAAQlB,WAAW,GAC7B;IACA,MAAMqC,OAAA,GAAUxB,eAAA;IAAA;IAEZ1B,eAAA,CAAgBgB,gBAAA,EAAkBa,QAAA,EAAUf,kBAAkB;IAAA;IAE9DC,MAAA,CAAOY,aAAA,CAAc,EAAEC,GAAA,CAAIZ,gBAAA,CAAiBQ,SAAS,GAAG0B,OAAA;IAE5DA,OAAA,EAASC,KAAA,CAAM5D,IAAI,EAAE6D,OAAA,CAAQ,MAAM;MAEjCvB,QAAA,CAASY,YAAA,CAAa;IACxB,CAAC;EACH;EAGA,OAAO,CAACzB,gBAAA,CAAiBqC,mBAAA,GACrBxB,QAAA,CAASyB,WAAA,CAAYvB,MAAM,IAC3BA,MAAA;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
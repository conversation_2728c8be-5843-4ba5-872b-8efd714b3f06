{"ast": null, "code": "// src/focusManager.ts\nimport { Subscribable } from \"./subscribable.js\";\nimport { isServer } from \"./utils.js\";\nvar FocusManager = class extends Subscribable {\n  #focused;\n  #cleanup;\n  #setup;\n  constructor() {\n    super();\n    this.#setup = onFocus => {\n      if (!isServer && window.addEventListener) {\n        const listener = () => onFocus();\n        window.addEventListener(\"visibilitychange\", listener, false);\n        return () => {\n          window.removeEventListener(\"visibilitychange\", listener);\n        };\n      }\n      return;\n    };\n  }\n  onSubscribe() {\n    if (!this.#cleanup) {\n      this.setEventListener(this.#setup);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#cleanup?.();\n      this.#cleanup = void 0;\n    }\n  }\n  setEventListener(setup) {\n    this.#setup = setup;\n    this.#cleanup?.();\n    this.#cleanup = setup(focused => {\n      if (typeof focused === \"boolean\") {\n        this.setFocused(focused);\n      } else {\n        this.onFocus();\n      }\n    });\n  }\n  setFocused(focused) {\n    const changed = this.#focused !== focused;\n    if (changed) {\n      this.#focused = focused;\n      this.onFocus();\n    }\n  }\n  onFocus() {\n    const isFocused = this.isFocused();\n    this.listeners.forEach(listener => {\n      listener(isFocused);\n    });\n  }\n  isFocused() {\n    if (typeof this.#focused === \"boolean\") {\n      return this.#focused;\n    }\n    return globalThis.document?.visibilityState !== \"hidden\";\n  }\n};\nvar focusManager = new FocusManager();\nexport { FocusManager, focusManager };", "map": {"version": 3, "names": ["Subscribable", "isServer", "FocusManager", "focused", "cleanup", "setup", "constructor", "onFocus", "window", "addEventListener", "listener", "removeEventListener", "onSubscribe", "setEventListener", "onUnsubscribe", "hasListeners", "setFocused", "changed", "isFocused", "listeners", "for<PERSON>ach", "globalThis", "document", "visibilityState", "focusManager"], "sources": ["/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/node_modules/@tanstack/query-core/src/focusManager.ts"], "sourcesContent": ["import { Subscribable } from './subscribable'\nimport { isServer } from './utils'\n\ntype Listener = (focused: boolean) => void\n\ntype SetupFn = (\n  setFocused: (focused?: boolean) => void,\n) => (() => void) | undefined\n\nexport class FocusManager extends Subscribable<Listener> {\n  #focused?: boolean\n  #cleanup?: () => void\n\n  #setup: SetupFn\n\n  constructor() {\n    super()\n    this.#setup = (onFocus) => {\n      // addEventListener does not exist in React Native, but window does\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      if (!isServer && window.addEventListener) {\n        const listener = () => onFocus()\n        // Listen to visibilitychange\n        window.addEventListener('visibilitychange', listener, false)\n\n        return () => {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('visibilitychange', listener)\n        }\n      }\n      return\n    }\n  }\n\n  protected onSubscribe(): void {\n    if (!this.#cleanup) {\n      this.setEventListener(this.#setup)\n    }\n  }\n\n  protected onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#cleanup?.()\n      this.#cleanup = undefined\n    }\n  }\n\n  setEventListener(setup: SetupFn): void {\n    this.#setup = setup\n    this.#cleanup?.()\n    this.#cleanup = setup((focused) => {\n      if (typeof focused === 'boolean') {\n        this.setFocused(focused)\n      } else {\n        this.onFocus()\n      }\n    })\n  }\n\n  setFocused(focused?: boolean): void {\n    const changed = this.#focused !== focused\n    if (changed) {\n      this.#focused = focused\n      this.onFocus()\n    }\n  }\n\n  onFocus(): void {\n    const isFocused = this.isFocused()\n    this.listeners.forEach((listener) => {\n      listener(isFocused)\n    })\n  }\n\n  isFocused(): boolean {\n    if (typeof this.#focused === 'boolean') {\n      return this.#focused\n    }\n\n    // document global can be unavailable in react native\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    return globalThis.document?.visibilityState !== 'hidden'\n  }\n}\n\nexport const focusManager = new FocusManager()\n"], "mappings": ";AAAA,SAASA,YAAA,QAAoB;AAC7B,SAASC,QAAA,QAAgB;AAQlB,IAAMC,YAAA,GAAN,cAA2BF,YAAA,CAAuB;EACvD,CAAAG,OAAA;EACA,CAAAC,OAAA;EAEA,CAAAC,KAAA;EAEAC,YAAA,EAAc;IACZ,MAAM;IACN,KAAK,CAAAD,KAAA,GAAUE,OAAA,IAAY;MAGzB,IAAI,CAACN,QAAA,IAAYO,MAAA,CAAOC,gBAAA,EAAkB;QACxC,MAAMC,QAAA,GAAWA,CAAA,KAAMH,OAAA,CAAQ;QAE/BC,MAAA,CAAOC,gBAAA,CAAiB,oBAAoBC,QAAA,EAAU,KAAK;QAE3D,OAAO,MAAM;UAEXF,MAAA,CAAOG,mBAAA,CAAoB,oBAAoBD,QAAQ;QACzD;MACF;MACA;IACF;EACF;EAEUE,YAAA,EAAoB;IAC5B,IAAI,CAAC,KAAK,CAAAR,OAAA,EAAU;MAClB,KAAKS,gBAAA,CAAiB,KAAK,CAAAR,KAAM;IACnC;EACF;EAEUS,cAAA,EAAgB;IACxB,IAAI,CAAC,KAAKC,YAAA,CAAa,GAAG;MACxB,KAAK,CAAAX,OAAA,GAAW;MAChB,KAAK,CAAAA,OAAA,GAAW;IAClB;EACF;EAEAS,iBAAiBR,KAAA,EAAsB;IACrC,KAAK,CAAAA,KAAA,GAASA,KAAA;IACd,KAAK,CAAAD,OAAA,GAAW;IAChB,KAAK,CAAAA,OAAA,GAAWC,KAAA,CAAOF,OAAA,IAAY;MACjC,IAAI,OAAOA,OAAA,KAAY,WAAW;QAChC,KAAKa,UAAA,CAAWb,OAAO;MACzB,OAAO;QACL,KAAKI,OAAA,CAAQ;MACf;IACF,CAAC;EACH;EAEAS,WAAWb,OAAA,EAAyB;IAClC,MAAMc,OAAA,GAAU,KAAK,CAAAd,OAAA,KAAaA,OAAA;IAClC,IAAIc,OAAA,EAAS;MACX,KAAK,CAAAd,OAAA,GAAWA,OAAA;MAChB,KAAKI,OAAA,CAAQ;IACf;EACF;EAEAA,QAAA,EAAgB;IACd,MAAMW,SAAA,GAAY,KAAKA,SAAA,CAAU;IACjC,KAAKC,SAAA,CAAUC,OAAA,CAASV,QAAA,IAAa;MACnCA,QAAA,CAASQ,SAAS;IACpB,CAAC;EACH;EAEAA,UAAA,EAAqB;IACnB,IAAI,OAAO,KAAK,CAAAf,OAAA,KAAa,WAAW;MACtC,OAAO,KAAK,CAAAA,OAAA;IACd;IAIA,OAAOkB,UAAA,CAAWC,QAAA,EAAUC,eAAA,KAAoB;EAClD;AACF;AAEO,IAAMC,YAAA,GAAe,IAAItB,YAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "var _s = $RefreshSig$();\n// Custom hook for experiment management with API communication, caching, and error handling\nimport { useCallback, useMemo, useEffect } from 'react';\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\nimport { toast } from 'react-hot-toast';\nimport { experimentsApi } from '../services/api';\nimport { useExperimentContext } from '../contexts/ExperimentContext';\n\n// Query keys factory\nexport const experimentKeys = {\n  all: ['experiments'],\n  lists: () => [...experimentKeys.all, 'list'],\n  list: (filters, pagination) => [...experimentKeys.lists(), {\n    filters,\n    pagination\n  }],\n  details: () => [...experimentKeys.all, 'detail'],\n  detail: id => [...experimentKeys.details(), id],\n  analytics: id => [...experimentKeys.all, 'analytics', id]\n};\n\n// Main experiments hook\nexport const useExperiments = (filters = {}, pagination = {\n  page: 1,\n  limit: 20\n}) => {\n  _s();\n  const queryClient = useQueryClient();\n  const {\n    state,\n    setGlobalLoading,\n    addOptimisticUpdate,\n    removeOptimisticUpdate\n  } = useExperimentContext();\n\n  // Fetch experiments query\n  const {\n    data: experimentsResponse,\n    isLoading,\n    isError,\n    error,\n    refetch,\n    isFetching,\n    isPlaceholderData\n  } = useQuery({\n    queryKey: experimentKeys.list(filters, pagination),\n    queryFn: () => experimentsApi.getExperiments(filters, pagination),\n    placeholderData: previousData => previousData,\n    staleTime: state.cacheConfig.staleTime,\n    gcTime: state.cacheConfig.gcTime,\n    refetchOnWindowFocus: state.cacheConfig.refetchOnWindowFocus,\n    refetchOnMount: state.cacheConfig.refetchOnMount\n  });\n\n  // Handle loading states and errors\n  useEffect(() => {\n    if (isError && error) {\n      setGlobalLoading({\n        isLoading: false,\n        isError: true,\n        error: error.message\n      });\n      toast.error(`Failed to load experiments: ${error.message}`);\n    } else if (isLoading) {\n      setGlobalLoading({\n        isLoading: true,\n        isError: false,\n        error: null\n      });\n    } else {\n      setGlobalLoading({\n        isLoading: false,\n        isError: false,\n        error: null\n      });\n    }\n  }, [isLoading, isError, error, setGlobalLoading]);\n\n  // Extract data with fallbacks\n  const experiments = useMemo(() => (experimentsResponse === null || experimentsResponse === void 0 ? void 0 : experimentsResponse.data) || [], [experimentsResponse]);\n  const pagination_info = useMemo(() => experimentsResponse === null || experimentsResponse === void 0 ? void 0 : experimentsResponse.pagination, [experimentsResponse]);\n\n  // Create experiment mutation\n  const createMutation = useMutation({\n    mutationFn: data => experimentsApi.createExperiment(data),\n    onMutate: async newExperiment => {\n      // Cancel outgoing refetches\n      await queryClient.cancelQueries({\n        queryKey: experimentKeys.lists()\n      });\n\n      // Snapshot previous value\n      const previousExperiments = queryClient.getQueryData(experimentKeys.list(filters, pagination));\n\n      // Optimistically update\n      const optimisticExperiment = {\n        id: `temp-${Date.now()}`,\n        tenantId: 'current-tenant',\n        name: newExperiment.name,\n        description: newExperiment.description,\n        status: 'DRAFT',\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString(),\n        createdBy: 'current-user',\n        variants: newExperiment.variants.map((v, i) => ({\n          ...v,\n          id: `temp-variant-${i}`\n        })),\n        tags: newExperiment.tags || [],\n        _count: {\n          userAssignments: 0,\n          events: 0\n        }\n      };\n      queryClient.setQueryData(experimentKeys.list(filters, pagination), old => {\n        if (!old) return old;\n        return {\n          ...old,\n          data: [optimisticExperiment, ...old.data],\n          pagination: {\n            ...old.pagination,\n            total: old.pagination.total + 1\n          }\n        };\n      });\n\n      // Store rollback function\n      addOptimisticUpdate(`create-${optimisticExperiment.id}`, {\n        type: 'create',\n        data: optimisticExperiment,\n        rollback: () => {\n          queryClient.setQueryData(experimentKeys.list(filters, pagination), previousExperiments);\n        }\n      });\n      return {\n        previousExperiments,\n        optimisticExperiment\n      };\n    },\n    onError: (error, newExperiment, context) => {\n      // Rollback optimistic update\n      if (context !== null && context !== void 0 && context.optimisticExperiment) {\n        removeOptimisticUpdate(`create-${context.optimisticExperiment.id}`);\n        queryClient.setQueryData(experimentKeys.list(filters, pagination), context.previousExperiments);\n      }\n      toast.error(`Failed to create experiment: ${error.message}`);\n    },\n    onSuccess: (response, variables, context) => {\n      // Remove optimistic update\n      if (context !== null && context !== void 0 && context.optimisticExperiment) {\n        removeOptimisticUpdate(`create-${context.optimisticExperiment.id}`);\n      }\n\n      // Invalidate and refetch\n      queryClient.invalidateQueries({\n        queryKey: experimentKeys.lists()\n      });\n      toast.success('Experiment created successfully');\n    }\n  });\n\n  // Update experiment mutation\n  const updateMutation = useMutation({\n    mutationFn: ({\n      id,\n      data\n    }) => experimentsApi.updateExperiment(id, data),\n    onMutate: async ({\n      id,\n      data\n    }) => {\n      await queryClient.cancelQueries({\n        queryKey: experimentKeys.detail(id)\n      });\n      await queryClient.cancelQueries({\n        queryKey: experimentKeys.lists()\n      });\n      const previousExperiment = queryClient.getQueryData(experimentKeys.detail(id));\n      const previousList = queryClient.getQueryData(experimentKeys.list(filters, pagination));\n\n      // Optimistically update detail\n      queryClient.setQueryData(experimentKeys.detail(id), old => {\n        if (!old) return old;\n        return {\n          ...old,\n          data: {\n            ...old.data,\n            ...data,\n            updatedAt: new Date().toISOString()\n          }\n        };\n      });\n\n      // Optimistically update list\n      queryClient.setQueryData(experimentKeys.list(filters, pagination), old => {\n        if (!old) return old;\n        return {\n          ...old,\n          data: old.data.map(exp => exp.id === id ? {\n            ...exp,\n            ...data,\n            updatedAt: new Date().toISOString()\n          } : exp)\n        };\n      });\n      return {\n        previousExperiment,\n        previousList\n      };\n    },\n    onError: (error, {\n      id\n    }, context) => {\n      // Rollback\n      if (context !== null && context !== void 0 && context.previousExperiment) {\n        queryClient.setQueryData(experimentKeys.detail(id), context.previousExperiment);\n      }\n      if (context !== null && context !== void 0 && context.previousList) {\n        queryClient.setQueryData(experimentKeys.list(filters, pagination), context.previousList);\n      }\n      toast.error(`Failed to update experiment: ${error.message}`);\n    },\n    onSuccess: (response, {\n      id\n    }) => {\n      queryClient.invalidateQueries({\n        queryKey: experimentKeys.detail(id)\n      });\n      queryClient.invalidateQueries({\n        queryKey: experimentKeys.lists()\n      });\n      toast.success('Experiment updated successfully');\n    }\n  });\n\n  // Delete experiment mutation\n  const deleteMutation = useMutation({\n    mutationFn: id => experimentsApi.deleteExperiment(id),\n    onMutate: async id => {\n      await queryClient.cancelQueries({\n        queryKey: experimentKeys.lists()\n      });\n      const previousList = queryClient.getQueryData(experimentKeys.list(filters, pagination));\n\n      // Optimistically remove from list\n      queryClient.setQueryData(experimentKeys.list(filters, pagination), old => {\n        if (!old) return old;\n        return {\n          ...old,\n          data: old.data.filter(exp => exp.id !== id),\n          pagination: {\n            ...old.pagination,\n            total: old.pagination.total - 1\n          }\n        };\n      });\n      return {\n        previousList\n      };\n    },\n    onError: (error, id, context) => {\n      // Rollback\n      if (context !== null && context !== void 0 && context.previousList) {\n        queryClient.setQueryData(experimentKeys.list(filters, pagination), context.previousList);\n      }\n      toast.error(`Failed to delete experiment: ${error.message}`);\n    },\n    onSuccess: () => {\n      queryClient.invalidateQueries({\n        queryKey: experimentKeys.lists()\n      });\n      toast.success('Experiment deleted successfully');\n    }\n  });\n\n  // Status change mutation\n  const statusMutation = useMutation({\n    mutationFn: ({\n      id,\n      status,\n      data\n    }) => experimentsApi.updateExperimentStatus(id, status, data),\n    onMutate: async ({\n      id,\n      status\n    }) => {\n      await queryClient.cancelQueries({\n        queryKey: experimentKeys.lists()\n      });\n      await queryClient.cancelQueries({\n        queryKey: experimentKeys.detail(id)\n      });\n      const previousList = queryClient.getQueryData(experimentKeys.list(filters, pagination));\n      const previousDetail = queryClient.getQueryData(experimentKeys.detail(id));\n\n      // Optimistically update status\n      const updateStatus = exp => ({\n        ...exp,\n        status,\n        updatedAt: new Date().toISOString()\n      });\n      queryClient.setQueryData(experimentKeys.list(filters, pagination), old => {\n        if (!old) return old;\n        return {\n          ...old,\n          data: old.data.map(exp => exp.id === id ? updateStatus(exp) : exp)\n        };\n      });\n      queryClient.setQueryData(experimentKeys.detail(id), old => {\n        if (!old) return old;\n        return {\n          ...old,\n          data: updateStatus(old.data)\n        };\n      });\n      return {\n        previousList,\n        previousDetail\n      };\n    },\n    onError: (error, {\n      id\n    }, context) => {\n      // Rollback\n      if (context !== null && context !== void 0 && context.previousList) {\n        queryClient.setQueryData(experimentKeys.list(filters, pagination), context.previousList);\n      }\n      if (context !== null && context !== void 0 && context.previousDetail) {\n        queryClient.setQueryData(experimentKeys.detail(id), context.previousDetail);\n      }\n      toast.error(`Failed to update status: ${error.message}`);\n    },\n    onSuccess: (response, {\n      status\n    }) => {\n      queryClient.invalidateQueries({\n        queryKey: experimentKeys.lists()\n      });\n      toast.success(`Experiment ${status.toLowerCase()} successfully`);\n    }\n  });\n\n  // Duplicate experiment mutation\n  const duplicateMutation = useMutation({\n    mutationFn: id => experimentsApi.duplicateExperiment(id),\n    onSuccess: () => {\n      queryClient.invalidateQueries({\n        queryKey: experimentKeys.lists()\n      });\n      toast.success('Experiment duplicated successfully');\n    },\n    onError: error => {\n      toast.error(`Failed to duplicate experiment: ${error.message}`);\n    }\n  });\n\n  // Exposed methods\n  const createExperiment = useCallback(async data => {\n    const result = await createMutation.mutateAsync(data);\n    return result.data;\n  }, [createMutation]);\n  const updateExperiment = useCallback(async (id, data) => {\n    const result = await updateMutation.mutateAsync({\n      id,\n      data\n    });\n    return result.data;\n  }, [updateMutation]);\n  const deleteExperiment = useCallback(async id => {\n    await deleteMutation.mutateAsync(id);\n  }, [deleteMutation]);\n  const changeStatus = useCallback(async (id, status, data) => {\n    const result = await statusMutation.mutateAsync({\n      id,\n      status,\n      data\n    });\n    return result.data;\n  }, [statusMutation]);\n  const duplicateExperiment = useCallback(async id => {\n    const result = await duplicateMutation.mutateAsync(id);\n    return result.data;\n  }, [duplicateMutation]);\n  return {\n    // Data\n    experiments,\n    pagination: pagination_info,\n    // Loading states\n    isLoading,\n    isFetching,\n    isError,\n    error: error === null || error === void 0 ? void 0 : error.message,\n    isPreviousData,\n    // Mutation states\n    isCreating: createMutation.isLoading,\n    isUpdating: updateMutation.isLoading,\n    isDeleting: deleteMutation.isLoading,\n    isChangingStatus: statusMutation.isLoading,\n    isDuplicating: duplicateMutation.isLoading,\n    // Methods\n    createExperiment,\n    updateExperiment,\n    deleteExperiment,\n    changeStatus,\n    duplicateExperiment,\n    refetch,\n    // Raw mutations for advanced usage\n    createMutation,\n    updateMutation,\n    deleteMutation,\n    statusMutation,\n    duplicateMutation\n  };\n};\n_s(useExperiments, \"XN1WJtyBuT5rd/EFJLmBLqMKYg0=\", false, function () {\n  return [useQueryClient, useExperimentContext, useQuery, useMutation, useMutation, useMutation, useMutation, useMutation];\n});", "map": {"version": 3, "names": ["useCallback", "useMemo", "useEffect", "useQuery", "useMutation", "useQueryClient", "toast", "experimentsApi", "useExperimentContext", "experimentKeys", "all", "lists", "list", "filters", "pagination", "details", "detail", "id", "analytics", "useExperiments", "page", "limit", "_s", "queryClient", "state", "setGlobalLoading", "addOptimisticUpdate", "removeOptimisticUpdate", "data", "experimentsResponse", "isLoading", "isError", "error", "refetch", "isFetching", "isPlaceholderData", "query<PERSON><PERSON>", "queryFn", "getExperiments", "placeholderData", "previousData", "staleTime", "cacheConfig", "gcTime", "refetchOnWindowFocus", "refetchOnMount", "message", "experiments", "pagination_info", "createMutation", "mutationFn", "createExperiment", "onMutate", "newExperiment", "cancelQueries", "previousExperiments", "getQueryData", "optimisticExperiment", "Date", "now", "tenantId", "name", "description", "status", "createdAt", "toISOString", "updatedAt", "created<PERSON>y", "variants", "map", "v", "i", "tags", "_count", "userAssignments", "events", "setQueryData", "old", "total", "type", "rollback", "onError", "context", "onSuccess", "response", "variables", "invalidateQueries", "success", "updateMutation", "updateExperiment", "previousExperiment", "previousList", "exp", "deleteMutation", "deleteExperiment", "filter", "statusMutation", "updateExperimentStatus", "previousDetail", "updateStatus", "toLowerCase", "duplicateMutation", "duplicateExperiment", "result", "mutateAsync", "changeStatus", "isPreviousData", "isCreating", "isUpdating", "isDeleting", "isChangingStatus", "isDuplicating"], "sources": ["/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/src/hooks/useExperiments.ts"], "sourcesContent": ["// Custom hook for experiment management with API communication, caching, and error handling\nimport { useCallback, useMemo, useEffect } from 'react';\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\nimport { toast } from 'react-hot-toast';\nimport {\n  Experiment,\n  ExperimentFilters,\n  PaginationParams,\n  CreateExperimentForm,\n  UpdateExperimentForm,\n  ExperimentStatus,\n  PaginatedResponse,\n  ApiResponse,\n  ApiError,\n} from '../types/experiment';\nimport { experimentsApi } from '../services/api';\nimport { useExperimentContext } from '../contexts/ExperimentContext';\n\n// Query keys factory\nexport const experimentKeys = {\n  all: ['experiments'] as const,\n  lists: () => [...experimentKeys.all, 'list'] as const,\n  list: (filters: ExperimentFilters, pagination: PaginationParams) =>\n    [...experimentKeys.lists(), { filters, pagination }] as const,\n  details: () => [...experimentKeys.all, 'detail'] as const,\n  detail: (id: string) => [...experimentKeys.details(), id] as const,\n  analytics: (id: string) => [...experimentKeys.all, 'analytics', id] as const,\n};\n\n// Main experiments hook\nexport const useExperiments = (\n  filters: ExperimentFilters = {},\n  pagination: PaginationParams = { page: 1, limit: 20 }\n) => {\n  const queryClient = useQueryClient();\n  const { state, setGlobalLoading, addOptimisticUpdate, removeOptimisticUpdate } = useExperimentContext();\n\n  // Fetch experiments query\n  const {\n    data: experimentsResponse,\n    isLoading,\n    isError,\n    error,\n    refetch,\n    isFetching,\n    isPlaceholderData,\n  } = useQuery({\n    queryKey: experimentKeys.list(filters, pagination),\n    queryFn: () => experimentsApi.getExperiments(filters, pagination),\n    placeholderData: (previousData) => previousData,\n    staleTime: state.cacheConfig.staleTime,\n    gcTime: state.cacheConfig.gcTime,\n    refetchOnWindowFocus: state.cacheConfig.refetchOnWindowFocus,\n    refetchOnMount: state.cacheConfig.refetchOnMount,\n  });\n\n  // Handle loading states and errors\n  useEffect(() => {\n    if (isError && error) {\n      setGlobalLoading({\n        isLoading: false,\n        isError: true,\n        error: error.message,\n      });\n      toast.error(`Failed to load experiments: ${error.message}`);\n    } else if (isLoading) {\n      setGlobalLoading({\n        isLoading: true,\n        isError: false,\n        error: null,\n      });\n    } else {\n      setGlobalLoading({\n        isLoading: false,\n        isError: false,\n        error: null,\n      });\n    }\n  }, [isLoading, isError, error, setGlobalLoading]);\n\n  // Extract data with fallbacks\n  const experiments = useMemo(() => experimentsResponse?.data || [], [experimentsResponse]);\n  const pagination_info = useMemo(() => experimentsResponse?.pagination, [experimentsResponse]);\n\n  // Create experiment mutation\n  const createMutation = useMutation({\n    mutationFn: (data: CreateExperimentForm) => experimentsApi.createExperiment(data),\n    onMutate: async (newExperiment) => {\n      // Cancel outgoing refetches\n      await queryClient.cancelQueries({ queryKey: experimentKeys.lists() });\n\n      // Snapshot previous value\n      const previousExperiments = queryClient.getQueryData(\n        experimentKeys.list(filters, pagination)\n      );\n\n      // Optimistically update\n      const optimisticExperiment: Experiment = {\n        id: `temp-${Date.now()}`,\n        tenantId: 'current-tenant',\n        name: newExperiment.name,\n        description: newExperiment.description,\n        status: 'DRAFT',\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString(),\n        createdBy: 'current-user',\n        variants: newExperiment.variants.map((v, i) => ({ ...v, id: `temp-variant-${i}` })),\n        tags: newExperiment.tags || [],\n        _count: { userAssignments: 0, events: 0 },\n      };\n\n      queryClient.setQueryData(\n        experimentKeys.list(filters, pagination),\n        (old: PaginatedResponse<Experiment> | undefined) => {\n          if (!old) return old;\n          return {\n            ...old,\n            data: [optimisticExperiment, ...old.data],\n            pagination: {\n              ...old.pagination,\n              total: old.pagination.total + 1,\n            },\n          };\n        }\n      );\n\n      // Store rollback function\n      addOptimisticUpdate(`create-${optimisticExperiment.id}`, {\n        type: 'create',\n        data: optimisticExperiment,\n        rollback: () => {\n          queryClient.setQueryData(\n            experimentKeys.list(filters, pagination),\n            previousExperiments\n          );\n        },\n      });\n\n      return { previousExperiments, optimisticExperiment };\n    },\n    onError: (error: ApiError, newExperiment, context) => {\n      // Rollback optimistic update\n      if (context?.optimisticExperiment) {\n        removeOptimisticUpdate(`create-${context.optimisticExperiment.id}`);\n        queryClient.setQueryData(\n          experimentKeys.list(filters, pagination),\n          context.previousExperiments\n        );\n      }\n      toast.error(`Failed to create experiment: ${error.message}`);\n    },\n    onSuccess: (response, variables, context) => {\n      // Remove optimistic update\n      if (context?.optimisticExperiment) {\n        removeOptimisticUpdate(`create-${context.optimisticExperiment.id}`);\n      }\n      \n      // Invalidate and refetch\n      queryClient.invalidateQueries({ queryKey: experimentKeys.lists() });\n      toast.success('Experiment created successfully');\n    },\n  });\n\n  // Update experiment mutation\n  const updateMutation = useMutation({\n    mutationFn: ({ id, data }: { id: string; data: UpdateExperimentForm }) =>\n      experimentsApi.updateExperiment(id, data),\n    onMutate: async ({ id, data }) => {\n      await queryClient.cancelQueries({ queryKey: experimentKeys.detail(id) });\n      await queryClient.cancelQueries({ queryKey: experimentKeys.lists() });\n\n      const previousExperiment = queryClient.getQueryData(experimentKeys.detail(id));\n      const previousList = queryClient.getQueryData(experimentKeys.list(filters, pagination));\n\n      // Optimistically update detail\n      queryClient.setQueryData(experimentKeys.detail(id), (old: ApiResponse<Experiment> | undefined) => {\n        if (!old) return old;\n        return {\n          ...old,\n          data: { ...old.data, ...data, updatedAt: new Date().toISOString() },\n        };\n      });\n\n      // Optimistically update list\n      queryClient.setQueryData(\n        experimentKeys.list(filters, pagination),\n        (old: PaginatedResponse<Experiment> | undefined) => {\n          if (!old) return old;\n          return {\n            ...old,\n            data: old.data.map(exp =>\n              exp.id === id\n                ? { ...exp, ...data, updatedAt: new Date().toISOString() }\n                : exp\n            ),\n          };\n        }\n      );\n\n      return { previousExperiment, previousList };\n    },\n    onError: (error: ApiError, { id }, context) => {\n      // Rollback\n      if (context?.previousExperiment) {\n        queryClient.setQueryData(experimentKeys.detail(id), context.previousExperiment);\n      }\n      if (context?.previousList) {\n        queryClient.setQueryData(experimentKeys.list(filters, pagination), context.previousList);\n      }\n      toast.error(`Failed to update experiment: ${error.message}`);\n    },\n    onSuccess: (response, { id }) => {\n      queryClient.invalidateQueries({ queryKey: experimentKeys.detail(id) });\n      queryClient.invalidateQueries({ queryKey: experimentKeys.lists() });\n      toast.success('Experiment updated successfully');\n    },\n  });\n\n  // Delete experiment mutation\n  const deleteMutation = useMutation({\n    mutationFn: (id: string) => experimentsApi.deleteExperiment(id),\n    onMutate: async (id) => {\n      await queryClient.cancelQueries({ queryKey: experimentKeys.lists() });\n\n      const previousList = queryClient.getQueryData(experimentKeys.list(filters, pagination));\n\n      // Optimistically remove from list\n      queryClient.setQueryData(\n        experimentKeys.list(filters, pagination),\n        (old: PaginatedResponse<Experiment> | undefined) => {\n          if (!old) return old;\n          return {\n            ...old,\n            data: old.data.filter(exp => exp.id !== id),\n            pagination: {\n              ...old.pagination,\n              total: old.pagination.total - 1,\n            },\n          };\n        }\n      );\n\n      return { previousList };\n    },\n    onError: (error: ApiError, id, context) => {\n      // Rollback\n      if (context?.previousList) {\n        queryClient.setQueryData(experimentKeys.list(filters, pagination), context.previousList);\n      }\n      toast.error(`Failed to delete experiment: ${error.message}`);\n    },\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: experimentKeys.lists() });\n      toast.success('Experiment deleted successfully');\n    },\n  });\n\n  // Status change mutation\n  const statusMutation = useMutation({\n    mutationFn: ({ id, status, data }: { id: string; status: ExperimentStatus; data?: any }) =>\n      experimentsApi.updateExperimentStatus(id, status, data),\n    onMutate: async ({ id, status }) => {\n      await queryClient.cancelQueries({ queryKey: experimentKeys.lists() });\n      await queryClient.cancelQueries({ queryKey: experimentKeys.detail(id) });\n\n      const previousList = queryClient.getQueryData(experimentKeys.list(filters, pagination));\n      const previousDetail = queryClient.getQueryData(experimentKeys.detail(id));\n\n      // Optimistically update status\n      const updateStatus = (exp: Experiment) => ({\n        ...exp,\n        status,\n        updatedAt: new Date().toISOString(),\n      });\n\n      queryClient.setQueryData(\n        experimentKeys.list(filters, pagination),\n        (old: PaginatedResponse<Experiment> | undefined) => {\n          if (!old) return old;\n          return {\n            ...old,\n            data: old.data.map(exp => exp.id === id ? updateStatus(exp) : exp),\n          };\n        }\n      );\n\n      queryClient.setQueryData(experimentKeys.detail(id), (old: ApiResponse<Experiment> | undefined) => {\n        if (!old) return old;\n        return {\n          ...old,\n          data: updateStatus(old.data),\n        };\n      });\n\n      return { previousList, previousDetail };\n    },\n    onError: (error: ApiError, { id }, context) => {\n      // Rollback\n      if (context?.previousList) {\n        queryClient.setQueryData(experimentKeys.list(filters, pagination), context.previousList);\n      }\n      if (context?.previousDetail) {\n        queryClient.setQueryData(experimentKeys.detail(id), context.previousDetail);\n      }\n      toast.error(`Failed to update status: ${error.message}`);\n    },\n    onSuccess: (response, { status }) => {\n      queryClient.invalidateQueries({ queryKey: experimentKeys.lists() });\n      toast.success(`Experiment ${status.toLowerCase()} successfully`);\n    },\n  });\n\n  // Duplicate experiment mutation\n  const duplicateMutation = useMutation({\n    mutationFn: (id: string) => experimentsApi.duplicateExperiment(id),\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: experimentKeys.lists() });\n      toast.success('Experiment duplicated successfully');\n    },\n    onError: (error: ApiError) => {\n      toast.error(`Failed to duplicate experiment: ${error.message}`);\n    },\n  });\n\n  // Exposed methods\n  const createExperiment = useCallback(\n    async (data: CreateExperimentForm): Promise<Experiment> => {\n      const result = await createMutation.mutateAsync(data);\n      return result.data;\n    },\n    [createMutation]\n  );\n\n  const updateExperiment = useCallback(\n    async (id: string, data: UpdateExperimentForm): Promise<Experiment> => {\n      const result = await updateMutation.mutateAsync({ id, data });\n      return result.data;\n    },\n    [updateMutation]\n  );\n\n  const deleteExperiment = useCallback(\n    async (id: string): Promise<void> => {\n      await deleteMutation.mutateAsync(id);\n    },\n    [deleteMutation]\n  );\n\n  const changeStatus = useCallback(\n    async (id: string, status: ExperimentStatus, data?: any): Promise<Experiment> => {\n      const result = await statusMutation.mutateAsync({ id, status, data });\n      return result.data;\n    },\n    [statusMutation]\n  );\n\n  const duplicateExperiment = useCallback(\n    async (id: string): Promise<Experiment> => {\n      const result = await duplicateMutation.mutateAsync(id);\n      return result.data;\n    },\n    [duplicateMutation]\n  );\n\n  return {\n    // Data\n    experiments,\n    pagination: pagination_info,\n    \n    // Loading states\n    isLoading,\n    isFetching,\n    isError,\n    error: error?.message,\n    isPreviousData,\n    \n    // Mutation states\n    isCreating: createMutation.isLoading,\n    isUpdating: updateMutation.isLoading,\n    isDeleting: deleteMutation.isLoading,\n    isChangingStatus: statusMutation.isLoading,\n    isDuplicating: duplicateMutation.isLoading,\n    \n    // Methods\n    createExperiment,\n    updateExperiment,\n    deleteExperiment,\n    changeStatus,\n    duplicateExperiment,\n    refetch,\n    \n    // Raw mutations for advanced usage\n    createMutation,\n    updateMutation,\n    deleteMutation,\n    statusMutation,\n    duplicateMutation,\n  };\n};\n"], "mappings": ";AAAA;AACA,SAASA,WAAW,EAAEC,OAAO,EAAEC,SAAS,QAAQ,OAAO;AACvD,SAASC,QAAQ,EAAEC,WAAW,EAAEC,cAAc,QAAQ,uBAAuB;AAC7E,SAASC,KAAK,QAAQ,iBAAiB;AAYvC,SAASC,cAAc,QAAQ,iBAAiB;AAChD,SAASC,oBAAoB,QAAQ,+BAA+B;;AAEpE;AACA,OAAO,MAAMC,cAAc,GAAG;EAC5BC,GAAG,EAAE,CAAC,aAAa,CAAU;EAC7BC,KAAK,EAAEA,CAAA,KAAM,CAAC,GAAGF,cAAc,CAACC,GAAG,EAAE,MAAM,CAAU;EACrDE,IAAI,EAAEA,CAACC,OAA0B,EAAEC,UAA4B,KAC7D,CAAC,GAAGL,cAAc,CAACE,KAAK,CAAC,CAAC,EAAE;IAAEE,OAAO;IAAEC;EAAW,CAAC,CAAU;EAC/DC,OAAO,EAAEA,CAAA,KAAM,CAAC,GAAGN,cAAc,CAACC,GAAG,EAAE,QAAQ,CAAU;EACzDM,MAAM,EAAGC,EAAU,IAAK,CAAC,GAAGR,cAAc,CAACM,OAAO,CAAC,CAAC,EAAEE,EAAE,CAAU;EAClEC,SAAS,EAAGD,EAAU,IAAK,CAAC,GAAGR,cAAc,CAACC,GAAG,EAAE,WAAW,EAAEO,EAAE;AACpE,CAAC;;AAED;AACA,OAAO,MAAME,cAAc,GAAGA,CAC5BN,OAA0B,GAAG,CAAC,CAAC,EAC/BC,UAA4B,GAAG;EAAEM,IAAI,EAAE,CAAC;EAAEC,KAAK,EAAE;AAAG,CAAC,KAClD;EAAAC,EAAA;EACH,MAAMC,WAAW,GAAGlB,cAAc,CAAC,CAAC;EACpC,MAAM;IAAEmB,KAAK;IAAEC,gBAAgB;IAAEC,mBAAmB;IAAEC;EAAuB,CAAC,GAAGnB,oBAAoB,CAAC,CAAC;;EAEvG;EACA,MAAM;IACJoB,IAAI,EAAEC,mBAAmB;IACzBC,SAAS;IACTC,OAAO;IACPC,KAAK;IACLC,OAAO;IACPC,UAAU;IACVC;EACF,CAAC,GAAGhC,QAAQ,CAAC;IACXiC,QAAQ,EAAE3B,cAAc,CAACG,IAAI,CAACC,OAAO,EAAEC,UAAU,CAAC;IAClDuB,OAAO,EAAEA,CAAA,KAAM9B,cAAc,CAAC+B,cAAc,CAACzB,OAAO,EAAEC,UAAU,CAAC;IACjEyB,eAAe,EAAGC,YAAY,IAAKA,YAAY;IAC/CC,SAAS,EAAEjB,KAAK,CAACkB,WAAW,CAACD,SAAS;IACtCE,MAAM,EAAEnB,KAAK,CAACkB,WAAW,CAACC,MAAM;IAChCC,oBAAoB,EAAEpB,KAAK,CAACkB,WAAW,CAACE,oBAAoB;IAC5DC,cAAc,EAAErB,KAAK,CAACkB,WAAW,CAACG;EACpC,CAAC,CAAC;;EAEF;EACA3C,SAAS,CAAC,MAAM;IACd,IAAI6B,OAAO,IAAIC,KAAK,EAAE;MACpBP,gBAAgB,CAAC;QACfK,SAAS,EAAE,KAAK;QAChBC,OAAO,EAAE,IAAI;QACbC,KAAK,EAAEA,KAAK,CAACc;MACf,CAAC,CAAC;MACFxC,KAAK,CAAC0B,KAAK,CAAC,+BAA+BA,KAAK,CAACc,OAAO,EAAE,CAAC;IAC7D,CAAC,MAAM,IAAIhB,SAAS,EAAE;MACpBL,gBAAgB,CAAC;QACfK,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE,KAAK;QACdC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,CAAC,MAAM;MACLP,gBAAgB,CAAC;QACfK,SAAS,EAAE,KAAK;QAChBC,OAAO,EAAE,KAAK;QACdC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACF,SAAS,EAAEC,OAAO,EAAEC,KAAK,EAAEP,gBAAgB,CAAC,CAAC;;EAEjD;EACA,MAAMsB,WAAW,GAAG9C,OAAO,CAAC,MAAM,CAAA4B,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAED,IAAI,KAAI,EAAE,EAAE,CAACC,mBAAmB,CAAC,CAAC;EACzF,MAAMmB,eAAe,GAAG/C,OAAO,CAAC,MAAM4B,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEf,UAAU,EAAE,CAACe,mBAAmB,CAAC,CAAC;;EAE7F;EACA,MAAMoB,cAAc,GAAG7C,WAAW,CAAC;IACjC8C,UAAU,EAAGtB,IAA0B,IAAKrB,cAAc,CAAC4C,gBAAgB,CAACvB,IAAI,CAAC;IACjFwB,QAAQ,EAAE,MAAOC,aAAa,IAAK;MACjC;MACA,MAAM9B,WAAW,CAAC+B,aAAa,CAAC;QAAElB,QAAQ,EAAE3B,cAAc,CAACE,KAAK,CAAC;MAAE,CAAC,CAAC;;MAErE;MACA,MAAM4C,mBAAmB,GAAGhC,WAAW,CAACiC,YAAY,CAClD/C,cAAc,CAACG,IAAI,CAACC,OAAO,EAAEC,UAAU,CACzC,CAAC;;MAED;MACA,MAAM2C,oBAAgC,GAAG;QACvCxC,EAAE,EAAE,QAAQyC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;QACxBC,QAAQ,EAAE,gBAAgB;QAC1BC,IAAI,EAAER,aAAa,CAACQ,IAAI;QACxBC,WAAW,EAAET,aAAa,CAACS,WAAW;QACtCC,MAAM,EAAE,OAAO;QACfC,SAAS,EAAE,IAAIN,IAAI,CAAC,CAAC,CAACO,WAAW,CAAC,CAAC;QACnCC,SAAS,EAAE,IAAIR,IAAI,CAAC,CAAC,CAACO,WAAW,CAAC,CAAC;QACnCE,SAAS,EAAE,cAAc;QACzBC,QAAQ,EAAEf,aAAa,CAACe,QAAQ,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,MAAM;UAAE,GAAGD,CAAC;UAAErD,EAAE,EAAE,gBAAgBsD,CAAC;QAAG,CAAC,CAAC,CAAC;QACnFC,IAAI,EAAEnB,aAAa,CAACmB,IAAI,IAAI,EAAE;QAC9BC,MAAM,EAAE;UAAEC,eAAe,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAE;MAC1C,CAAC;MAEDpD,WAAW,CAACqD,YAAY,CACtBnE,cAAc,CAACG,IAAI,CAACC,OAAO,EAAEC,UAAU,CAAC,EACvC+D,GAA8C,IAAK;QAClD,IAAI,CAACA,GAAG,EAAE,OAAOA,GAAG;QACpB,OAAO;UACL,GAAGA,GAAG;UACNjD,IAAI,EAAE,CAAC6B,oBAAoB,EAAE,GAAGoB,GAAG,CAACjD,IAAI,CAAC;UACzCd,UAAU,EAAE;YACV,GAAG+D,GAAG,CAAC/D,UAAU;YACjBgE,KAAK,EAAED,GAAG,CAAC/D,UAAU,CAACgE,KAAK,GAAG;UAChC;QACF,CAAC;MACH,CACF,CAAC;;MAED;MACApD,mBAAmB,CAAC,UAAU+B,oBAAoB,CAACxC,EAAE,EAAE,EAAE;QACvD8D,IAAI,EAAE,QAAQ;QACdnD,IAAI,EAAE6B,oBAAoB;QAC1BuB,QAAQ,EAAEA,CAAA,KAAM;UACdzD,WAAW,CAACqD,YAAY,CACtBnE,cAAc,CAACG,IAAI,CAACC,OAAO,EAAEC,UAAU,CAAC,EACxCyC,mBACF,CAAC;QACH;MACF,CAAC,CAAC;MAEF,OAAO;QAAEA,mBAAmB;QAAEE;MAAqB,CAAC;IACtD,CAAC;IACDwB,OAAO,EAAEA,CAACjD,KAAe,EAAEqB,aAAa,EAAE6B,OAAO,KAAK;MACpD;MACA,IAAIA,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEzB,oBAAoB,EAAE;QACjC9B,sBAAsB,CAAC,UAAUuD,OAAO,CAACzB,oBAAoB,CAACxC,EAAE,EAAE,CAAC;QACnEM,WAAW,CAACqD,YAAY,CACtBnE,cAAc,CAACG,IAAI,CAACC,OAAO,EAAEC,UAAU,CAAC,EACxCoE,OAAO,CAAC3B,mBACV,CAAC;MACH;MACAjD,KAAK,CAAC0B,KAAK,CAAC,gCAAgCA,KAAK,CAACc,OAAO,EAAE,CAAC;IAC9D,CAAC;IACDqC,SAAS,EAAEA,CAACC,QAAQ,EAAEC,SAAS,EAAEH,OAAO,KAAK;MAC3C;MACA,IAAIA,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEzB,oBAAoB,EAAE;QACjC9B,sBAAsB,CAAC,UAAUuD,OAAO,CAACzB,oBAAoB,CAACxC,EAAE,EAAE,CAAC;MACrE;;MAEA;MACAM,WAAW,CAAC+D,iBAAiB,CAAC;QAAElD,QAAQ,EAAE3B,cAAc,CAACE,KAAK,CAAC;MAAE,CAAC,CAAC;MACnEL,KAAK,CAACiF,OAAO,CAAC,iCAAiC,CAAC;IAClD;EACF,CAAC,CAAC;;EAEF;EACA,MAAMC,cAAc,GAAGpF,WAAW,CAAC;IACjC8C,UAAU,EAAEA,CAAC;MAAEjC,EAAE;MAAEW;IAAiD,CAAC,KACnErB,cAAc,CAACkF,gBAAgB,CAACxE,EAAE,EAAEW,IAAI,CAAC;IAC3CwB,QAAQ,EAAE,MAAAA,CAAO;MAAEnC,EAAE;MAAEW;IAAK,CAAC,KAAK;MAChC,MAAML,WAAW,CAAC+B,aAAa,CAAC;QAAElB,QAAQ,EAAE3B,cAAc,CAACO,MAAM,CAACC,EAAE;MAAE,CAAC,CAAC;MACxE,MAAMM,WAAW,CAAC+B,aAAa,CAAC;QAAElB,QAAQ,EAAE3B,cAAc,CAACE,KAAK,CAAC;MAAE,CAAC,CAAC;MAErE,MAAM+E,kBAAkB,GAAGnE,WAAW,CAACiC,YAAY,CAAC/C,cAAc,CAACO,MAAM,CAACC,EAAE,CAAC,CAAC;MAC9E,MAAM0E,YAAY,GAAGpE,WAAW,CAACiC,YAAY,CAAC/C,cAAc,CAACG,IAAI,CAACC,OAAO,EAAEC,UAAU,CAAC,CAAC;;MAEvF;MACAS,WAAW,CAACqD,YAAY,CAACnE,cAAc,CAACO,MAAM,CAACC,EAAE,CAAC,EAAG4D,GAAwC,IAAK;QAChG,IAAI,CAACA,GAAG,EAAE,OAAOA,GAAG;QACpB,OAAO;UACL,GAAGA,GAAG;UACNjD,IAAI,EAAE;YAAE,GAAGiD,GAAG,CAACjD,IAAI;YAAE,GAAGA,IAAI;YAAEsC,SAAS,EAAE,IAAIR,IAAI,CAAC,CAAC,CAACO,WAAW,CAAC;UAAE;QACpE,CAAC;MACH,CAAC,CAAC;;MAEF;MACA1C,WAAW,CAACqD,YAAY,CACtBnE,cAAc,CAACG,IAAI,CAACC,OAAO,EAAEC,UAAU,CAAC,EACvC+D,GAA8C,IAAK;QAClD,IAAI,CAACA,GAAG,EAAE,OAAOA,GAAG;QACpB,OAAO;UACL,GAAGA,GAAG;UACNjD,IAAI,EAAEiD,GAAG,CAACjD,IAAI,CAACyC,GAAG,CAACuB,GAAG,IACpBA,GAAG,CAAC3E,EAAE,KAAKA,EAAE,GACT;YAAE,GAAG2E,GAAG;YAAE,GAAGhE,IAAI;YAAEsC,SAAS,EAAE,IAAIR,IAAI,CAAC,CAAC,CAACO,WAAW,CAAC;UAAE,CAAC,GACxD2B,GACN;QACF,CAAC;MACH,CACF,CAAC;MAED,OAAO;QAAEF,kBAAkB;QAAEC;MAAa,CAAC;IAC7C,CAAC;IACDV,OAAO,EAAEA,CAACjD,KAAe,EAAE;MAAEf;IAAG,CAAC,EAAEiE,OAAO,KAAK;MAC7C;MACA,IAAIA,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEQ,kBAAkB,EAAE;QAC/BnE,WAAW,CAACqD,YAAY,CAACnE,cAAc,CAACO,MAAM,CAACC,EAAE,CAAC,EAAEiE,OAAO,CAACQ,kBAAkB,CAAC;MACjF;MACA,IAAIR,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAES,YAAY,EAAE;QACzBpE,WAAW,CAACqD,YAAY,CAACnE,cAAc,CAACG,IAAI,CAACC,OAAO,EAAEC,UAAU,CAAC,EAAEoE,OAAO,CAACS,YAAY,CAAC;MAC1F;MACArF,KAAK,CAAC0B,KAAK,CAAC,gCAAgCA,KAAK,CAACc,OAAO,EAAE,CAAC;IAC9D,CAAC;IACDqC,SAAS,EAAEA,CAACC,QAAQ,EAAE;MAAEnE;IAAG,CAAC,KAAK;MAC/BM,WAAW,CAAC+D,iBAAiB,CAAC;QAAElD,QAAQ,EAAE3B,cAAc,CAACO,MAAM,CAACC,EAAE;MAAE,CAAC,CAAC;MACtEM,WAAW,CAAC+D,iBAAiB,CAAC;QAAElD,QAAQ,EAAE3B,cAAc,CAACE,KAAK,CAAC;MAAE,CAAC,CAAC;MACnEL,KAAK,CAACiF,OAAO,CAAC,iCAAiC,CAAC;IAClD;EACF,CAAC,CAAC;;EAEF;EACA,MAAMM,cAAc,GAAGzF,WAAW,CAAC;IACjC8C,UAAU,EAAGjC,EAAU,IAAKV,cAAc,CAACuF,gBAAgB,CAAC7E,EAAE,CAAC;IAC/DmC,QAAQ,EAAE,MAAOnC,EAAE,IAAK;MACtB,MAAMM,WAAW,CAAC+B,aAAa,CAAC;QAAElB,QAAQ,EAAE3B,cAAc,CAACE,KAAK,CAAC;MAAE,CAAC,CAAC;MAErE,MAAMgF,YAAY,GAAGpE,WAAW,CAACiC,YAAY,CAAC/C,cAAc,CAACG,IAAI,CAACC,OAAO,EAAEC,UAAU,CAAC,CAAC;;MAEvF;MACAS,WAAW,CAACqD,YAAY,CACtBnE,cAAc,CAACG,IAAI,CAACC,OAAO,EAAEC,UAAU,CAAC,EACvC+D,GAA8C,IAAK;QAClD,IAAI,CAACA,GAAG,EAAE,OAAOA,GAAG;QACpB,OAAO;UACL,GAAGA,GAAG;UACNjD,IAAI,EAAEiD,GAAG,CAACjD,IAAI,CAACmE,MAAM,CAACH,GAAG,IAAIA,GAAG,CAAC3E,EAAE,KAAKA,EAAE,CAAC;UAC3CH,UAAU,EAAE;YACV,GAAG+D,GAAG,CAAC/D,UAAU;YACjBgE,KAAK,EAAED,GAAG,CAAC/D,UAAU,CAACgE,KAAK,GAAG;UAChC;QACF,CAAC;MACH,CACF,CAAC;MAED,OAAO;QAAEa;MAAa,CAAC;IACzB,CAAC;IACDV,OAAO,EAAEA,CAACjD,KAAe,EAAEf,EAAE,EAAEiE,OAAO,KAAK;MACzC;MACA,IAAIA,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAES,YAAY,EAAE;QACzBpE,WAAW,CAACqD,YAAY,CAACnE,cAAc,CAACG,IAAI,CAACC,OAAO,EAAEC,UAAU,CAAC,EAAEoE,OAAO,CAACS,YAAY,CAAC;MAC1F;MACArF,KAAK,CAAC0B,KAAK,CAAC,gCAAgCA,KAAK,CAACc,OAAO,EAAE,CAAC;IAC9D,CAAC;IACDqC,SAAS,EAAEA,CAAA,KAAM;MACf5D,WAAW,CAAC+D,iBAAiB,CAAC;QAAElD,QAAQ,EAAE3B,cAAc,CAACE,KAAK,CAAC;MAAE,CAAC,CAAC;MACnEL,KAAK,CAACiF,OAAO,CAAC,iCAAiC,CAAC;IAClD;EACF,CAAC,CAAC;;EAEF;EACA,MAAMS,cAAc,GAAG5F,WAAW,CAAC;IACjC8C,UAAU,EAAEA,CAAC;MAAEjC,EAAE;MAAE8C,MAAM;MAAEnC;IAA2D,CAAC,KACrFrB,cAAc,CAAC0F,sBAAsB,CAAChF,EAAE,EAAE8C,MAAM,EAAEnC,IAAI,CAAC;IACzDwB,QAAQ,EAAE,MAAAA,CAAO;MAAEnC,EAAE;MAAE8C;IAAO,CAAC,KAAK;MAClC,MAAMxC,WAAW,CAAC+B,aAAa,CAAC;QAAElB,QAAQ,EAAE3B,cAAc,CAACE,KAAK,CAAC;MAAE,CAAC,CAAC;MACrE,MAAMY,WAAW,CAAC+B,aAAa,CAAC;QAAElB,QAAQ,EAAE3B,cAAc,CAACO,MAAM,CAACC,EAAE;MAAE,CAAC,CAAC;MAExE,MAAM0E,YAAY,GAAGpE,WAAW,CAACiC,YAAY,CAAC/C,cAAc,CAACG,IAAI,CAACC,OAAO,EAAEC,UAAU,CAAC,CAAC;MACvF,MAAMoF,cAAc,GAAG3E,WAAW,CAACiC,YAAY,CAAC/C,cAAc,CAACO,MAAM,CAACC,EAAE,CAAC,CAAC;;MAE1E;MACA,MAAMkF,YAAY,GAAIP,GAAe,KAAM;QACzC,GAAGA,GAAG;QACN7B,MAAM;QACNG,SAAS,EAAE,IAAIR,IAAI,CAAC,CAAC,CAACO,WAAW,CAAC;MACpC,CAAC,CAAC;MAEF1C,WAAW,CAACqD,YAAY,CACtBnE,cAAc,CAACG,IAAI,CAACC,OAAO,EAAEC,UAAU,CAAC,EACvC+D,GAA8C,IAAK;QAClD,IAAI,CAACA,GAAG,EAAE,OAAOA,GAAG;QACpB,OAAO;UACL,GAAGA,GAAG;UACNjD,IAAI,EAAEiD,GAAG,CAACjD,IAAI,CAACyC,GAAG,CAACuB,GAAG,IAAIA,GAAG,CAAC3E,EAAE,KAAKA,EAAE,GAAGkF,YAAY,CAACP,GAAG,CAAC,GAAGA,GAAG;QACnE,CAAC;MACH,CACF,CAAC;MAEDrE,WAAW,CAACqD,YAAY,CAACnE,cAAc,CAACO,MAAM,CAACC,EAAE,CAAC,EAAG4D,GAAwC,IAAK;QAChG,IAAI,CAACA,GAAG,EAAE,OAAOA,GAAG;QACpB,OAAO;UACL,GAAGA,GAAG;UACNjD,IAAI,EAAEuE,YAAY,CAACtB,GAAG,CAACjD,IAAI;QAC7B,CAAC;MACH,CAAC,CAAC;MAEF,OAAO;QAAE+D,YAAY;QAAEO;MAAe,CAAC;IACzC,CAAC;IACDjB,OAAO,EAAEA,CAACjD,KAAe,EAAE;MAAEf;IAAG,CAAC,EAAEiE,OAAO,KAAK;MAC7C;MACA,IAAIA,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAES,YAAY,EAAE;QACzBpE,WAAW,CAACqD,YAAY,CAACnE,cAAc,CAACG,IAAI,CAACC,OAAO,EAAEC,UAAU,CAAC,EAAEoE,OAAO,CAACS,YAAY,CAAC;MAC1F;MACA,IAAIT,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEgB,cAAc,EAAE;QAC3B3E,WAAW,CAACqD,YAAY,CAACnE,cAAc,CAACO,MAAM,CAACC,EAAE,CAAC,EAAEiE,OAAO,CAACgB,cAAc,CAAC;MAC7E;MACA5F,KAAK,CAAC0B,KAAK,CAAC,4BAA4BA,KAAK,CAACc,OAAO,EAAE,CAAC;IAC1D,CAAC;IACDqC,SAAS,EAAEA,CAACC,QAAQ,EAAE;MAAErB;IAAO,CAAC,KAAK;MACnCxC,WAAW,CAAC+D,iBAAiB,CAAC;QAAElD,QAAQ,EAAE3B,cAAc,CAACE,KAAK,CAAC;MAAE,CAAC,CAAC;MACnEL,KAAK,CAACiF,OAAO,CAAC,cAAcxB,MAAM,CAACqC,WAAW,CAAC,CAAC,eAAe,CAAC;IAClE;EACF,CAAC,CAAC;;EAEF;EACA,MAAMC,iBAAiB,GAAGjG,WAAW,CAAC;IACpC8C,UAAU,EAAGjC,EAAU,IAAKV,cAAc,CAAC+F,mBAAmB,CAACrF,EAAE,CAAC;IAClEkE,SAAS,EAAEA,CAAA,KAAM;MACf5D,WAAW,CAAC+D,iBAAiB,CAAC;QAAElD,QAAQ,EAAE3B,cAAc,CAACE,KAAK,CAAC;MAAE,CAAC,CAAC;MACnEL,KAAK,CAACiF,OAAO,CAAC,oCAAoC,CAAC;IACrD,CAAC;IACDN,OAAO,EAAGjD,KAAe,IAAK;MAC5B1B,KAAK,CAAC0B,KAAK,CAAC,mCAAmCA,KAAK,CAACc,OAAO,EAAE,CAAC;IACjE;EACF,CAAC,CAAC;;EAEF;EACA,MAAMK,gBAAgB,GAAGnD,WAAW,CAClC,MAAO4B,IAA0B,IAA0B;IACzD,MAAM2E,MAAM,GAAG,MAAMtD,cAAc,CAACuD,WAAW,CAAC5E,IAAI,CAAC;IACrD,OAAO2E,MAAM,CAAC3E,IAAI;EACpB,CAAC,EACD,CAACqB,cAAc,CACjB,CAAC;EAED,MAAMwC,gBAAgB,GAAGzF,WAAW,CAClC,OAAOiB,EAAU,EAAEW,IAA0B,KAA0B;IACrE,MAAM2E,MAAM,GAAG,MAAMf,cAAc,CAACgB,WAAW,CAAC;MAAEvF,EAAE;MAAEW;IAAK,CAAC,CAAC;IAC7D,OAAO2E,MAAM,CAAC3E,IAAI;EACpB,CAAC,EACD,CAAC4D,cAAc,CACjB,CAAC;EAED,MAAMM,gBAAgB,GAAG9F,WAAW,CAClC,MAAOiB,EAAU,IAAoB;IACnC,MAAM4E,cAAc,CAACW,WAAW,CAACvF,EAAE,CAAC;EACtC,CAAC,EACD,CAAC4E,cAAc,CACjB,CAAC;EAED,MAAMY,YAAY,GAAGzG,WAAW,CAC9B,OAAOiB,EAAU,EAAE8C,MAAwB,EAAEnC,IAAU,KAA0B;IAC/E,MAAM2E,MAAM,GAAG,MAAMP,cAAc,CAACQ,WAAW,CAAC;MAAEvF,EAAE;MAAE8C,MAAM;MAAEnC;IAAK,CAAC,CAAC;IACrE,OAAO2E,MAAM,CAAC3E,IAAI;EACpB,CAAC,EACD,CAACoE,cAAc,CACjB,CAAC;EAED,MAAMM,mBAAmB,GAAGtG,WAAW,CACrC,MAAOiB,EAAU,IAA0B;IACzC,MAAMsF,MAAM,GAAG,MAAMF,iBAAiB,CAACG,WAAW,CAACvF,EAAE,CAAC;IACtD,OAAOsF,MAAM,CAAC3E,IAAI;EACpB,CAAC,EACD,CAACyE,iBAAiB,CACpB,CAAC;EAED,OAAO;IACL;IACAtD,WAAW;IACXjC,UAAU,EAAEkC,eAAe;IAE3B;IACAlB,SAAS;IACTI,UAAU;IACVH,OAAO;IACPC,KAAK,EAAEA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEc,OAAO;IACrB4D,cAAc;IAEd;IACAC,UAAU,EAAE1D,cAAc,CAACnB,SAAS;IACpC8E,UAAU,EAAEpB,cAAc,CAAC1D,SAAS;IACpC+E,UAAU,EAAEhB,cAAc,CAAC/D,SAAS;IACpCgF,gBAAgB,EAAEd,cAAc,CAAClE,SAAS;IAC1CiF,aAAa,EAAEV,iBAAiB,CAACvE,SAAS;IAE1C;IACAqB,gBAAgB;IAChBsC,gBAAgB;IAChBK,gBAAgB;IAChBW,YAAY;IACZH,mBAAmB;IACnBrE,OAAO;IAEP;IACAgB,cAAc;IACduC,cAAc;IACdK,cAAc;IACdG,cAAc;IACdK;EACF,CAAC;AACH,CAAC;AAAC/E,EAAA,CAhXWH,cAAc;EAAA,QAILd,cAAc,EAC+CG,oBAAoB,EAWjGL,QAAQ,EAuCWC,WAAW,EA+EXA,WAAW,EAuDXA,WAAW,EAuCXA,WAAW,EAuDRA,WAAW;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/src/types/experiment.ts"], "sourcesContent": ["// Experiment types and interfaces\nexport interface Experiment {\n  id: string;\n  tenantId: string;\n  name: string;\n  description?: string;\n  status: ExperimentStatus;\n  startDate?: string;\n  endDate?: string;\n  createdAt: string;\n  updatedAt: string;\n  createdBy: string;\n  variants: ExperimentVariant[];\n  tags: string[];\n  targetingRules?: TargetingRule[];\n  _count: {\n    userAssignments: number;\n    events: number;\n  };\n}\n\nexport interface ExperimentVariant {\n  id: string;\n  name: string;\n  trafficWeight: number;\n  config?: Record<string, any>;\n}\n\nexport interface TargetingRule {\n  id: string;\n  field: string;\n  operator: string;\n  value: any;\n  type: 'include' | 'exclude';\n}\n\nexport type ExperimentStatus = 'DRAFT' | 'ACTIVE' | 'PAUSED' | 'COMPLETED' | 'ARCHIVED';\n\nexport interface ExperimentFilters {\n  status?: ExperimentStatus[];\n  tags?: string[];\n  search?: string;\n  createdBy?: string[];\n  dateRange?: {\n    start?: string;\n    end?: string;\n  };\n}\n\nexport interface PaginationParams {\n  page: number;\n  limit: number;\n  sortBy?: string;\n  sortOrder?: 'asc' | 'desc';\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[];\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n  };\n}\n\nexport interface ApiResponse<T> {\n  data: T;\n  message?: string;\n  success: boolean;\n}\n\nexport interface CreateExperimentForm {\n  name: string;\n  description?: string;\n  variants: Omit<ExperimentVariant, 'id'>[];\n  tags?: string[];\n  targetingRules?: Omit<TargetingRule, 'id'>[];\n}\n\nexport interface UpdateExperimentForm extends Partial<CreateExperimentForm> {\n  id: string;\n}\n\nexport interface ExperimentAnalytics {\n  experimentId: string;\n  totalAssignments: number;\n  totalEvents: number;\n  conversionRate: number;\n  variantPerformance: VariantPerformance[];\n  timeSeriesData: TimeSeriesPoint[];\n  statisticalSignificance?: StatisticalSignificance;\n}\n\nexport interface VariantPerformance {\n  variantId: string;\n  variantName: string;\n  assignments: number;\n  events: number;\n  conversionRate: number;\n  confidence?: number;\n}\n\nexport interface TimeSeriesPoint {\n  date: string;\n  assignments: number;\n  events: number;\n  conversionRate: number;\n}\n\nexport interface StatisticalSignificance {\n  isSignificant: boolean;\n  confidence: number;\n  pValue: number;\n  winningVariant?: string;\n}\n\n// Error types\nexport interface ApiError {\n  message: string;\n  code: string;\n  statusCode: number;\n  details?: any;\n  fieldErrors?: FieldError[];\n}\n\nexport interface FieldError {\n  field: string;\n  message: string;\n  code: string;\n}\n\n// Loading states\nexport interface LoadingState {\n  isLoading: boolean;\n  isError: boolean;\n  error?: string | null;\n}\n\n// Cache configuration\nexport interface CacheConfig {\n  staleTime: number;\n  cacheTime: number;\n  refetchOnWindowFocus: boolean;\n  refetchOnMount: boolean;\n}\n\n// Optimistic update types\nexport interface OptimisticUpdate<T> {\n  type: 'create' | 'update' | 'delete';\n  data: T;\n  rollback: () => void;\n}\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
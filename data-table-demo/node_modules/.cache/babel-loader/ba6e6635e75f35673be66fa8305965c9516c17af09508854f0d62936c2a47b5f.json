{"ast": null, "code": "\"use client\";\n\n// src/useQueries.ts\nimport * as React from \"react\";\nimport { QueriesObserver, QueryObserver, noop, notifyManager } from \"@tanstack/query-core\";\nimport { useQueryClient } from \"./QueryClientProvider.js\";\nimport { useIsRestoring } from \"./IsRestoringProvider.js\";\nimport { useQueryErrorResetBoundary } from \"./QueryErrorResetBoundary.js\";\nimport { ensurePreventErrorBoundaryRetry, getHasError, useClearResetErrorBoundary } from \"./errorBoundaryUtils.js\";\nimport { ensureSuspenseTimers, fetchOptimistic, shouldSuspend, willFetch } from \"./suspense.js\";\nfunction useQueries({\n  queries,\n  ...options\n}, queryClient) {\n  const client = useQueryClient(queryClient);\n  const isRestoring = useIsRestoring();\n  const errorResetBoundary = useQueryErrorResetBoundary();\n  const defaultedQueries = React.useMemo(() => queries.map(opts => {\n    const defaultedOptions = client.defaultQueryOptions(opts);\n    defaultedOptions._optimisticResults = isRestoring ? \"isRestoring\" : \"optimistic\";\n    return defaultedOptions;\n  }), [queries, client, isRestoring]);\n  defaultedQueries.forEach(query => {\n    ensureSuspenseTimers(query);\n    ensurePreventErrorBoundaryRetry(query, errorResetBoundary);\n  });\n  useClearResetErrorBoundary(errorResetBoundary);\n  const [observer] = React.useState(() => new QueriesObserver(client, defaultedQueries, options));\n  const [optimisticResult, getCombinedResult, trackResult] = observer.getOptimisticResult(defaultedQueries, options.combine);\n  const shouldSubscribe = !isRestoring && options.subscribed !== false;\n  React.useSyncExternalStore(React.useCallback(onStoreChange => shouldSubscribe ? observer.subscribe(notifyManager.batchCalls(onStoreChange)) : noop, [observer, shouldSubscribe]), () => observer.getCurrentResult(), () => observer.getCurrentResult());\n  React.useEffect(() => {\n    observer.setQueries(defaultedQueries, options);\n  }, [defaultedQueries, options, observer]);\n  const shouldAtLeastOneSuspend = optimisticResult.some((result, index) => shouldSuspend(defaultedQueries[index], result));\n  const suspensePromises = shouldAtLeastOneSuspend ? optimisticResult.flatMap((result, index) => {\n    const opts = defaultedQueries[index];\n    if (opts) {\n      const queryObserver = new QueryObserver(client, opts);\n      if (shouldSuspend(opts, result)) {\n        return fetchOptimistic(opts, queryObserver, errorResetBoundary);\n      } else if (willFetch(result, isRestoring)) {\n        void fetchOptimistic(opts, queryObserver, errorResetBoundary);\n      }\n    }\n    return [];\n  }) : [];\n  if (suspensePromises.length > 0) {\n    throw Promise.all(suspensePromises);\n  }\n  const firstSingleResultWhichShouldThrow = optimisticResult.find((result, index) => {\n    const query = defaultedQueries[index];\n    return query && getHasError({\n      result,\n      errorResetBoundary,\n      throwOnError: query.throwOnError,\n      query: client.getQueryCache().get(query.queryHash),\n      suspense: query.suspense\n    });\n  });\n  if (firstSingleResultWhichShouldThrow?.error) {\n    throw firstSingleResultWhichShouldThrow.error;\n  }\n  return getCombinedResult(trackResult());\n}\nexport { useQueries };", "map": {"version": 3, "names": ["React", "QueriesObserver", "QueryObserver", "noop", "notify<PERSON><PERSON>ger", "useQueryClient", "useIsRestoring", "useQueryErrorResetBoundary", "ensurePreventErrorBoundaryRetry", "getHasError", "useClearResetErrorBoundary", "ensureSuspenseTimers", "fetchOptimistic", "shouldSuspend", "<PERSON><PERSON><PERSON><PERSON>", "useQueries", "queries", "options", "queryClient", "client", "isRestoring", "errorResetBoundary", "defaultedQueries", "useMemo", "map", "opts", "defaultedOptions", "defaultQueryOptions", "_optimisticResults", "for<PERSON>ach", "query", "observer", "useState", "optimisticResult", "getCombinedResult", "trackResult", "getOptimisticResult", "combine", "shouldSubscribe", "subscribed", "useSyncExternalStore", "useCallback", "onStoreChange", "subscribe", "batchCalls", "getCurrentResult", "useEffect", "setQueries", "shouldAtLeastOneSuspend", "some", "result", "index", "suspensePromises", "flatMap", "queryObserver", "length", "Promise", "all", "firstSingleResultWhichShouldThrow", "find", "throwOnError", "get<PERSON><PERSON><PERSON><PERSON>ache", "get", "queryHash", "suspense", "error"], "sources": ["/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/node_modules/@tanstack/react-query/src/useQueries.ts"], "sourcesContent": ["'use client'\nimport * as React from 'react'\n\nimport {\n  QueriesObserver,\n  QueryObserver,\n  noop,\n  notifyManager,\n} from '@tanstack/query-core'\nimport { useQueryClient } from './QueryClientProvider'\nimport { useIsRestoring } from './IsRestoringProvider'\nimport { useQueryErrorResetBoundary } from './QueryErrorResetBoundary'\nimport {\n  ensurePreventErrorBoundaryRetry,\n  getHasError,\n  useClearResetErrorBoundary,\n} from './errorBoundaryUtils'\nimport {\n  ensureSuspenseTimers,\n  fetchOptimistic,\n  shouldSuspend,\n  willFetch,\n} from './suspense'\nimport type {\n  DefinedUseQueryResult,\n  UseQueryOptions,\n  UseQueryResult,\n} from './types'\nimport type {\n  DefaultError,\n  OmitKeyof,\n  QueriesObserverOptions,\n  QueriesPlaceholderDataFunction,\n  QueryClient,\n  QueryFunction,\n  QueryKey,\n  QueryObserverOptions,\n  ThrowOnError,\n} from '@tanstack/query-core'\n\n// This defines the `UseQueryOptions` that are accepted in `QueriesOptions` & `GetOptions`.\n// `placeholderData` function always gets undefined passed\ntype UseQueryOptionsForUseQueries<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> = OmitKeyof<\n  UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  'placeholderData' | 'subscribed'\n> & {\n  placeholderData?: TQueryFnData | QueriesPlaceholderDataFunction<TQueryFnData>\n}\n\n// Avoid TS depth-limit error in case of large array literal\ntype MAXIMUM_DEPTH = 20\n\n// Widen the type of the symbol to enable type inference even if skipToken is not immutable.\ntype SkipTokenForUseQueries = symbol\n\ntype GetUseQueryOptionsForUseQueries<T> =\n  // Part 1: responsible for applying explicit type parameter to function arguments, if object { queryFnData: TQueryFnData, error: TError, data: TData }\n  T extends {\n    queryFnData: infer TQueryFnData\n    error?: infer TError\n    data: infer TData\n  }\n    ? UseQueryOptionsForUseQueries<TQueryFnData, TError, TData>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n      ? UseQueryOptionsForUseQueries<TQueryFnData, TError>\n      : T extends { data: infer TData; error?: infer TError }\n        ? UseQueryOptionsForUseQueries<unknown, TError, TData>\n        : // Part 2: responsible for applying explicit type parameter to function arguments, if tuple [TQueryFnData, TError, TData]\n          T extends [infer TQueryFnData, infer TError, infer TData]\n          ? UseQueryOptionsForUseQueries<TQueryFnData, TError, TData>\n          : T extends [infer TQueryFnData, infer TError]\n            ? UseQueryOptionsForUseQueries<TQueryFnData, TError>\n            : T extends [infer TQueryFnData]\n              ? UseQueryOptionsForUseQueries<TQueryFnData>\n              : // Part 3: responsible for inferring and enforcing type if no explicit parameter was provided\n                T extends {\n                    queryFn?:\n                      | QueryFunction<infer TQueryFnData, infer TQueryKey>\n                      | SkipTokenForUseQueries\n                    select?: (data: any) => infer TData\n                    throwOnError?: ThrowOnError<any, infer TError, any, any>\n                  }\n                ? UseQueryOptionsForUseQueries<\n                    TQueryFnData,\n                    unknown extends TError ? DefaultError : TError,\n                    unknown extends TData ? TQueryFnData : TData,\n                    TQueryKey\n                  >\n                : // Fallback\n                  UseQueryOptionsForUseQueries\n\n// A defined initialData setting should return a DefinedUseQueryResult rather than UseQueryResult\ntype GetDefinedOrUndefinedQueryResult<T, TData, TError = unknown> = T extends {\n  initialData?: infer TInitialData\n}\n  ? unknown extends TInitialData\n    ? UseQueryResult<TData, TError>\n    : TInitialData extends TData\n      ? DefinedUseQueryResult<TData, TError>\n      : TInitialData extends () => infer TInitialDataResult\n        ? unknown extends TInitialDataResult\n          ? UseQueryResult<TData, TError>\n          : TInitialDataResult extends TData\n            ? DefinedUseQueryResult<TData, TError>\n            : UseQueryResult<TData, TError>\n        : UseQueryResult<TData, TError>\n  : UseQueryResult<TData, TError>\n\ntype GetUseQueryResult<T> =\n  // Part 1: responsible for mapping explicit type parameter to function result, if object\n  T extends { queryFnData: any; error?: infer TError; data: infer TData }\n    ? GetDefinedOrUndefinedQueryResult<T, TData, TError>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n      ? GetDefinedOrUndefinedQueryResult<T, TQueryFnData, TError>\n      : T extends { data: infer TData; error?: infer TError }\n        ? GetDefinedOrUndefinedQueryResult<T, TData, TError>\n        : // Part 2: responsible for mapping explicit type parameter to function result, if tuple\n          T extends [any, infer TError, infer TData]\n          ? GetDefinedOrUndefinedQueryResult<T, TData, TError>\n          : T extends [infer TQueryFnData, infer TError]\n            ? GetDefinedOrUndefinedQueryResult<T, TQueryFnData, TError>\n            : T extends [infer TQueryFnData]\n              ? GetDefinedOrUndefinedQueryResult<T, TQueryFnData>\n              : // Part 3: responsible for mapping inferred type to results, if no explicit parameter was provided\n                T extends {\n                    queryFn?:\n                      | QueryFunction<infer TQueryFnData, any>\n                      | SkipTokenForUseQueries\n                    select?: (data: any) => infer TData\n                    throwOnError?: ThrowOnError<any, infer TError, any, any>\n                  }\n                ? GetDefinedOrUndefinedQueryResult<\n                    T,\n                    unknown extends TData ? TQueryFnData : TData,\n                    unknown extends TError ? DefaultError : TError\n                  >\n                : // Fallback\n                  UseQueryResult\n\n/**\n * QueriesOptions reducer recursively unwraps function arguments to infer/enforce type param\n */\nexport type QueriesOptions<\n  T extends Array<any>,\n  TResults extends Array<any> = [],\n  TDepth extends ReadonlyArray<number> = [],\n> = TDepth['length'] extends MAXIMUM_DEPTH\n  ? Array<UseQueryOptionsForUseQueries>\n  : T extends []\n    ? []\n    : T extends [infer Head]\n      ? [...TResults, GetUseQueryOptionsForUseQueries<Head>]\n      : T extends [infer Head, ...infer Tails]\n        ? QueriesOptions<\n            [...Tails],\n            [...TResults, GetUseQueryOptionsForUseQueries<Head>],\n            [...TDepth, 1]\n          >\n        : ReadonlyArray<unknown> extends T\n          ? T\n          : // If T is *some* array but we couldn't assign unknown[] to it, then it must hold some known/homogenous type!\n            // use this to infer the param types in the case of Array.map() argument\n            T extends Array<\n                UseQueryOptionsForUseQueries<\n                  infer TQueryFnData,\n                  infer TError,\n                  infer TData,\n                  infer TQueryKey\n                >\n              >\n            ? Array<\n                UseQueryOptionsForUseQueries<\n                  TQueryFnData,\n                  TError,\n                  TData,\n                  TQueryKey\n                >\n              >\n            : // Fallback\n              Array<UseQueryOptionsForUseQueries>\n\n/**\n * QueriesResults reducer recursively maps type param to results\n */\nexport type QueriesResults<\n  T extends Array<any>,\n  TResults extends Array<any> = [],\n  TDepth extends ReadonlyArray<number> = [],\n> = TDepth['length'] extends MAXIMUM_DEPTH\n  ? Array<UseQueryResult>\n  : T extends []\n    ? []\n    : T extends [infer Head]\n      ? [...TResults, GetUseQueryResult<Head>]\n      : T extends [infer Head, ...infer Tails]\n        ? QueriesResults<\n            [...Tails],\n            [...TResults, GetUseQueryResult<Head>],\n            [...TDepth, 1]\n          >\n        : { [K in keyof T]: GetUseQueryResult<T[K]> }\n\nexport function useQueries<\n  T extends Array<any>,\n  TCombinedResult = QueriesResults<T>,\n>(\n  {\n    queries,\n    ...options\n  }: {\n    queries:\n      | readonly [...QueriesOptions<T>]\n      | readonly [...{ [K in keyof T]: GetUseQueryOptionsForUseQueries<T[K]> }]\n    combine?: (result: QueriesResults<T>) => TCombinedResult\n    subscribed?: boolean\n  },\n  queryClient?: QueryClient,\n): TCombinedResult {\n  const client = useQueryClient(queryClient)\n  const isRestoring = useIsRestoring()\n  const errorResetBoundary = useQueryErrorResetBoundary()\n\n  const defaultedQueries = React.useMemo(\n    () =>\n      queries.map((opts) => {\n        const defaultedOptions = client.defaultQueryOptions(\n          opts as QueryObserverOptions,\n        )\n\n        // Make sure the results are already in fetching state before subscribing or updating options\n        defaultedOptions._optimisticResults = isRestoring\n          ? 'isRestoring'\n          : 'optimistic'\n\n        return defaultedOptions\n      }),\n    [queries, client, isRestoring],\n  )\n\n  defaultedQueries.forEach((query) => {\n    ensureSuspenseTimers(query)\n    ensurePreventErrorBoundaryRetry(query, errorResetBoundary)\n  })\n\n  useClearResetErrorBoundary(errorResetBoundary)\n\n  const [observer] = React.useState(\n    () =>\n      new QueriesObserver<TCombinedResult>(\n        client,\n        defaultedQueries,\n        options as QueriesObserverOptions<TCombinedResult>,\n      ),\n  )\n\n  // note: this must be called before useSyncExternalStore\n  const [optimisticResult, getCombinedResult, trackResult] =\n    observer.getOptimisticResult(\n      defaultedQueries,\n      (options as QueriesObserverOptions<TCombinedResult>).combine,\n    )\n\n  const shouldSubscribe = !isRestoring && options.subscribed !== false\n  React.useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) =>\n        shouldSubscribe\n          ? observer.subscribe(notifyManager.batchCalls(onStoreChange))\n          : noop,\n      [observer, shouldSubscribe],\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult(),\n  )\n\n  React.useEffect(() => {\n    observer.setQueries(\n      defaultedQueries,\n      options as QueriesObserverOptions<TCombinedResult>,\n    )\n  }, [defaultedQueries, options, observer])\n\n  const shouldAtLeastOneSuspend = optimisticResult.some((result, index) =>\n    shouldSuspend(defaultedQueries[index], result),\n  )\n\n  const suspensePromises = shouldAtLeastOneSuspend\n    ? optimisticResult.flatMap((result, index) => {\n        const opts = defaultedQueries[index]\n\n        if (opts) {\n          const queryObserver = new QueryObserver(client, opts)\n          if (shouldSuspend(opts, result)) {\n            return fetchOptimistic(opts, queryObserver, errorResetBoundary)\n          } else if (willFetch(result, isRestoring)) {\n            void fetchOptimistic(opts, queryObserver, errorResetBoundary)\n          }\n        }\n        return []\n      })\n    : []\n\n  if (suspensePromises.length > 0) {\n    throw Promise.all(suspensePromises)\n  }\n  const firstSingleResultWhichShouldThrow = optimisticResult.find(\n    (result, index) => {\n      const query = defaultedQueries[index]\n      return (\n        query &&\n        getHasError({\n          result,\n          errorResetBoundary,\n          throwOnError: query.throwOnError,\n          query: client.getQueryCache().get(query.queryHash),\n          suspense: query.suspense,\n        })\n      )\n    },\n  )\n\n  if (firstSingleResultWhichShouldThrow?.error) {\n    throw firstSingleResultWhichShouldThrow.error\n  }\n\n  return getCombinedResult(trackResult())\n}\n"], "mappings": ";;;AACA,YAAYA,KAAA,MAAW;AAEvB,SACEC,eAAA,EACAC,aAAA,EACAC,IAAA,EACAC,aAAA,QACK;AACP,SAASC,cAAA,QAAsB;AAC/B,SAASC,cAAA,QAAsB;AAC/B,SAASC,0BAAA,QAAkC;AAC3C,SACEC,+BAAA,EACAC,WAAA,EACAC,0BAAA,QACK;AACP,SACEC,oBAAA,EACAC,eAAA,EACAC,aAAA,EACAC,SAAA,QACK;AAyLA,SAASC,WAId;EACEC,OAAA;EACA,GAAGC;AACL,GAOAC,WAAA,EACiB;EACjB,MAAMC,MAAA,GAASd,cAAA,CAAea,WAAW;EACzC,MAAME,WAAA,GAAcd,cAAA,CAAe;EACnC,MAAMe,kBAAA,GAAqBd,0BAAA,CAA2B;EAEtD,MAAMe,gBAAA,GAAyBtB,KAAA,CAAAuB,OAAA,CAC7B,MACEP,OAAA,CAAQQ,GAAA,CAAKC,IAAA,IAAS;IACpB,MAAMC,gBAAA,GAAmBP,MAAA,CAAOQ,mBAAA,CAC9BF,IACF;IAGAC,gBAAA,CAAiBE,kBAAA,GAAqBR,WAAA,GAClC,gBACA;IAEJ,OAAOM,gBAAA;EACT,CAAC,GACH,CAACV,OAAA,EAASG,MAAA,EAAQC,WAAW,CAC/B;EAEAE,gBAAA,CAAiBO,OAAA,CAASC,KAAA,IAAU;IAClCnB,oBAAA,CAAqBmB,KAAK;IAC1BtB,+BAAA,CAAgCsB,KAAA,EAAOT,kBAAkB;EAC3D,CAAC;EAEDX,0BAAA,CAA2BW,kBAAkB;EAE7C,MAAM,CAACU,QAAQ,IAAU/B,KAAA,CAAAgC,QAAA,CACvB,MACE,IAAI/B,eAAA,CACFkB,MAAA,EACAG,gBAAA,EACAL,OACF,CACJ;EAGA,MAAM,CAACgB,gBAAA,EAAkBC,iBAAA,EAAmBC,WAAW,IACrDJ,QAAA,CAASK,mBAAA,CACPd,gBAAA,EACCL,OAAA,CAAoDoB,OACvD;EAEF,MAAMC,eAAA,GAAkB,CAAClB,WAAA,IAAeH,OAAA,CAAQsB,UAAA,KAAe;EACzDvC,KAAA,CAAAwC,oBAAA,CACExC,KAAA,CAAAyC,WAAA,CACHC,aAAA,IACCJ,eAAA,GACIP,QAAA,CAASY,SAAA,CAAUvC,aAAA,CAAcwC,UAAA,CAAWF,aAAa,CAAC,IAC1DvC,IAAA,EACN,CAAC4B,QAAA,EAAUO,eAAe,CAC5B,GACA,MAAMP,QAAA,CAASc,gBAAA,CAAiB,GAChC,MAAMd,QAAA,CAASc,gBAAA,CAAiB,CAClC;EAEM7C,KAAA,CAAA8C,SAAA,CAAU,MAAM;IACpBf,QAAA,CAASgB,UAAA,CACPzB,gBAAA,EACAL,OACF;EACF,GAAG,CAACK,gBAAA,EAAkBL,OAAA,EAASc,QAAQ,CAAC;EAExC,MAAMiB,uBAAA,GAA0Bf,gBAAA,CAAiBgB,IAAA,CAAK,CAACC,MAAA,EAAQC,KAAA,KAC7DtC,aAAA,CAAcS,gBAAA,CAAiB6B,KAAK,GAAGD,MAAM,CAC/C;EAEA,MAAME,gBAAA,GAAmBJ,uBAAA,GACrBf,gBAAA,CAAiBoB,OAAA,CAAQ,CAACH,MAAA,EAAQC,KAAA,KAAU;IAC1C,MAAM1B,IAAA,GAAOH,gBAAA,CAAiB6B,KAAK;IAEnC,IAAI1B,IAAA,EAAM;MACR,MAAM6B,aAAA,GAAgB,IAAIpD,aAAA,CAAciB,MAAA,EAAQM,IAAI;MACpD,IAAIZ,aAAA,CAAcY,IAAA,EAAMyB,MAAM,GAAG;QAC/B,OAAOtC,eAAA,CAAgBa,IAAA,EAAM6B,aAAA,EAAejC,kBAAkB;MAChE,WAAWP,SAAA,CAAUoC,MAAA,EAAQ9B,WAAW,GAAG;QACzC,KAAKR,eAAA,CAAgBa,IAAA,EAAM6B,aAAA,EAAejC,kBAAkB;MAC9D;IACF;IACA,OAAO,EAAC;EACV,CAAC,IACD,EAAC;EAEL,IAAI+B,gBAAA,CAAiBG,MAAA,GAAS,GAAG;IAC/B,MAAMC,OAAA,CAAQC,GAAA,CAAIL,gBAAgB;EACpC;EACA,MAAMM,iCAAA,GAAoCzB,gBAAA,CAAiB0B,IAAA,CACzD,CAACT,MAAA,EAAQC,KAAA,KAAU;IACjB,MAAMrB,KAAA,GAAQR,gBAAA,CAAiB6B,KAAK;IACpC,OACErB,KAAA,IACArB,WAAA,CAAY;MACVyC,MAAA;MACA7B,kBAAA;MACAuC,YAAA,EAAc9B,KAAA,CAAM8B,YAAA;MACpB9B,KAAA,EAAOX,MAAA,CAAO0C,aAAA,CAAc,EAAEC,GAAA,CAAIhC,KAAA,CAAMiC,SAAS;MACjDC,QAAA,EAAUlC,KAAA,CAAMkC;IAClB,CAAC;EAEL,CACF;EAEA,IAAIN,iCAAA,EAAmCO,KAAA,EAAO;IAC5C,MAAMP,iCAAA,CAAkCO,KAAA;EAC1C;EAEA,OAAO/B,iBAAA,CAAkBC,WAAA,CAAY,CAAC;AACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "// src/streamedQuery.ts\nimport { addToEnd } from \"./utils.js\";\nfunction streamedQuery({\n  queryFn,\n  refetchMode = \"reset\",\n  maxChunks\n}) {\n  return async context => {\n    const query = context.client.getQueryCache().find({\n      queryKey: context.queryKey,\n      exact: true\n    });\n    const isRefetch = !!query && query.state.data !== void 0;\n    if (isRefetch && refetchMode === \"reset\") {\n      query.setState({\n        status: \"pending\",\n        data: void 0,\n        error: null,\n        fetchStatus: \"fetching\"\n      });\n    }\n    let result = [];\n    const stream = await queryFn(context);\n    for await (const chunk of stream) {\n      if (context.signal.aborted) {\n        break;\n      }\n      if (!isRefetch || refetchMode !== \"replace\") {\n        context.client.setQueryData(context.queryKey, (prev = []) => {\n          return addToEnd(prev, chunk, maxChunks);\n        });\n      }\n      result = addToEnd(result, chunk, maxChunks);\n    }\n    if (isRefetch && refetchMode === \"replace\" && !context.signal.aborted) {\n      context.client.setQueryData(context.queryKey, result);\n    }\n    return context.client.getQueryData(context.queryKey);\n  };\n}\nexport { streamedQuery };", "map": {"version": 3, "names": ["addToEnd", "streamedQuery", "queryFn", "refetchMode", "max<PERSON><PERSON><PERSON>", "context", "query", "client", "get<PERSON><PERSON><PERSON><PERSON>ache", "find", "query<PERSON><PERSON>", "exact", "isRefetch", "state", "data", "setState", "status", "error", "fetchStatus", "result", "stream", "chunk", "signal", "aborted", "setQueryData", "prev", "getQueryData"], "sources": ["/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/node_modules/@tanstack/query-core/src/streamedQuery.ts"], "sourcesContent": ["import { addToEnd } from './utils'\nimport type { QueryFunction, QueryFunctionContext, QueryKey } from './types'\n\n/**\n * This is a helper function to create a query function that streams data from an AsyncIterable.\n * Data will be an Array of all the chunks received.\n * The query will be in a 'pending' state until the first chunk of data is received, but will go to 'success' after that.\n * The query will stay in fetchStatus 'fetching' until the stream ends.\n * @param queryFn - The function that returns an AsyncIterable to stream data from.\n * @param refetchMode - Defines how re-fetches are handled.\n * Defaults to `'reset'`, erases all data and puts the query back into `pending` state.\n * Set to `'append'` to append new data to the existing data.\n * Set to `'replace'` to write all data to the cache once the stream ends.\n * @param maxChunks - The maximum number of chunks to keep in the cache.\n * Defaults to `undefined`, meaning all chunks will be kept.\n * If `undefined` or `0`, the number of chunks is unlimited.\n * If the number of chunks exceeds this number, the oldest chunk will be removed.\n */\nexport function streamedQuery<\n  TQueryFnData = unknown,\n  TQueryKey extends QueryKey = QueryKey,\n>({\n  queryFn,\n  refetchMode = 'reset',\n  maxChunks,\n}: {\n  queryFn: (\n    context: QueryFunctionContext<TQueryKey>,\n  ) => AsyncIterable<TQueryFnData> | Promise<AsyncIterable<TQueryFnData>>\n  refetchMode?: 'append' | 'reset' | 'replace'\n  maxChunks?: number\n}): QueryFunction<Array<TQueryFnData>, TQueryKey> {\n  return async (context) => {\n    const query = context.client\n      .getQueryCache()\n      .find({ queryKey: context.queryKey, exact: true })\n    const isRefetch = !!query && query.state.data !== undefined\n\n    if (isRefetch && refetchMode === 'reset') {\n      query.setState({\n        status: 'pending',\n        data: undefined,\n        error: null,\n        fetchStatus: 'fetching',\n      })\n    }\n\n    let result: Array<TQueryFnData> = []\n    const stream = await queryFn(context)\n\n    for await (const chunk of stream) {\n      if (context.signal.aborted) {\n        break\n      }\n\n      // don't append to the cache directly when replace-refetching\n      if (!isRefetch || refetchMode !== 'replace') {\n        context.client.setQueryData<Array<TQueryFnData>>(\n          context.queryKey,\n          (prev = []) => {\n            return addToEnd(prev, chunk, maxChunks)\n          },\n        )\n      }\n      result = addToEnd(result, chunk, maxChunks)\n    }\n\n    // finalize result: replace-refetching needs to write to the cache\n    if (isRefetch && refetchMode === 'replace' && !context.signal.aborted) {\n      context.client.setQueryData<Array<TQueryFnData>>(context.queryKey, result)\n    }\n\n    return context.client.getQueryData(context.queryKey)!\n  }\n}\n"], "mappings": ";AAAA,SAASA,QAAA,QAAgB;AAkBlB,SAASC,cAGd;EACAC,OAAA;EACAC,WAAA,GAAc;EACdC;AACF,GAMkD;EAChD,OAAO,MAAOC,OAAA,IAAY;IACxB,MAAMC,KAAA,GAAQD,OAAA,CAAQE,MAAA,CACnBC,aAAA,CAAc,EACdC,IAAA,CAAK;MAAEC,QAAA,EAAUL,OAAA,CAAQK,QAAA;MAAUC,KAAA,EAAO;IAAK,CAAC;IACnD,MAAMC,SAAA,GAAY,CAAC,CAACN,KAAA,IAASA,KAAA,CAAMO,KAAA,CAAMC,IAAA,KAAS;IAElD,IAAIF,SAAA,IAAaT,WAAA,KAAgB,SAAS;MACxCG,KAAA,CAAMS,QAAA,CAAS;QACbC,MAAA,EAAQ;QACRF,IAAA,EAAM;QACNG,KAAA,EAAO;QACPC,WAAA,EAAa;MACf,CAAC;IACH;IAEA,IAAIC,MAAA,GAA8B,EAAC;IACnC,MAAMC,MAAA,GAAS,MAAMlB,OAAA,CAAQG,OAAO;IAEpC,iBAAiBgB,KAAA,IAASD,MAAA,EAAQ;MAChC,IAAIf,OAAA,CAAQiB,MAAA,CAAOC,OAAA,EAAS;QAC1B;MACF;MAGA,IAAI,CAACX,SAAA,IAAaT,WAAA,KAAgB,WAAW;QAC3CE,OAAA,CAAQE,MAAA,CAAOiB,YAAA,CACbnB,OAAA,CAAQK,QAAA,EACR,CAACe,IAAA,GAAO,EAAC,KAAM;UACb,OAAOzB,QAAA,CAASyB,IAAA,EAAMJ,KAAA,EAAOjB,SAAS;QACxC,CACF;MACF;MACAe,MAAA,GAASnB,QAAA,CAASmB,MAAA,EAAQE,KAAA,EAAOjB,SAAS;IAC5C;IAGA,IAAIQ,SAAA,IAAaT,WAAA,KAAgB,aAAa,CAACE,OAAA,CAAQiB,MAAA,CAAOC,OAAA,EAAS;MACrElB,OAAA,CAAQE,MAAA,CAAOiB,YAAA,CAAkCnB,OAAA,CAAQK,QAAA,EAAUS,MAAM;IAC3E;IAEA,OAAOd,OAAA,CAAQE,MAAA,CAAOmB,YAAA,CAAarB,OAAA,CAAQK,QAAQ;EACrD;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
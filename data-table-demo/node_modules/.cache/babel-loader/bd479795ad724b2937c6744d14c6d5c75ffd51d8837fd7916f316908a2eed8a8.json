{"ast": null, "code": "// src/queryObserver.ts\nimport { focusManager } from \"./focusManager.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { fetchState } from \"./query.js\";\nimport { Subscribable } from \"./subscribable.js\";\nimport { pendingThenable } from \"./thenable.js\";\nimport { isServer, isValidTimeout, noop, replaceData, resolveEnabled, resolveStaleTime, shallowEqualObjects, timeUntilStale } from \"./utils.js\";\nvar QueryObserver = class extends Subscribable {\n  constructor(client, options) {\n    super();\n    this.options = options;\n    this.#client = client;\n    this.#selectError = null;\n    this.#currentThenable = pendingThenable();\n    if (!this.options.experimental_prefetchInRender) {\n      this.#currentThenable.reject(new Error(\"experimental_prefetchInRender feature flag is not enabled\"));\n    }\n    this.bindMethods();\n    this.setOptions(options);\n  }\n  #client;\n  #currentQuery = void 0;\n  #currentQueryInitialState = void 0;\n  #currentResult = void 0;\n  #currentResultState;\n  #currentResultOptions;\n  #currentThenable;\n  #selectError;\n  #selectFn;\n  #selectResult;\n  // This property keeps track of the last query with defined data.\n  // It will be used to pass the previous data and query to the placeholder function between renders.\n  #lastQueryWithDefinedData;\n  #staleTimeoutId;\n  #refetchIntervalId;\n  #currentRefetchInterval;\n  #trackedProps = /* @__PURE__ */new Set();\n  bindMethods() {\n    this.refetch = this.refetch.bind(this);\n  }\n  onSubscribe() {\n    if (this.listeners.size === 1) {\n      this.#currentQuery.addObserver(this);\n      if (shouldFetchOnMount(this.#currentQuery, this.options)) {\n        this.#executeFetch();\n      } else {\n        this.updateResult();\n      }\n      this.#updateTimers();\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.destroy();\n    }\n  }\n  shouldFetchOnReconnect() {\n    return shouldFetchOn(this.#currentQuery, this.options, this.options.refetchOnReconnect);\n  }\n  shouldFetchOnWindowFocus() {\n    return shouldFetchOn(this.#currentQuery, this.options, this.options.refetchOnWindowFocus);\n  }\n  destroy() {\n    this.listeners = /* @__PURE__ */new Set();\n    this.#clearStaleTimeout();\n    this.#clearRefetchInterval();\n    this.#currentQuery.removeObserver(this);\n  }\n  setOptions(options) {\n    const prevOptions = this.options;\n    const prevQuery = this.#currentQuery;\n    this.options = this.#client.defaultQueryOptions(options);\n    if (this.options.enabled !== void 0 && typeof this.options.enabled !== \"boolean\" && typeof this.options.enabled !== \"function\" && typeof resolveEnabled(this.options.enabled, this.#currentQuery) !== \"boolean\") {\n      throw new Error(\"Expected enabled to be a boolean or a callback that returns a boolean\");\n    }\n    this.#updateQuery();\n    this.#currentQuery.setOptions(this.options);\n    if (prevOptions._defaulted && !shallowEqualObjects(this.options, prevOptions)) {\n      this.#client.getQueryCache().notify({\n        type: \"observerOptionsUpdated\",\n        query: this.#currentQuery,\n        observer: this\n      });\n    }\n    const mounted = this.hasListeners();\n    if (mounted && shouldFetchOptionally(this.#currentQuery, prevQuery, this.options, prevOptions)) {\n      this.#executeFetch();\n    }\n    this.updateResult();\n    if (mounted && (this.#currentQuery !== prevQuery || resolveEnabled(this.options.enabled, this.#currentQuery) !== resolveEnabled(prevOptions.enabled, this.#currentQuery) || resolveStaleTime(this.options.staleTime, this.#currentQuery) !== resolveStaleTime(prevOptions.staleTime, this.#currentQuery))) {\n      this.#updateStaleTimeout();\n    }\n    const nextRefetchInterval = this.#computeRefetchInterval();\n    if (mounted && (this.#currentQuery !== prevQuery || resolveEnabled(this.options.enabled, this.#currentQuery) !== resolveEnabled(prevOptions.enabled, this.#currentQuery) || nextRefetchInterval !== this.#currentRefetchInterval)) {\n      this.#updateRefetchInterval(nextRefetchInterval);\n    }\n  }\n  getOptimisticResult(options) {\n    const query = this.#client.getQueryCache().build(this.#client, options);\n    const result = this.createResult(query, options);\n    if (shouldAssignObserverCurrentProperties(this, result)) {\n      this.#currentResult = result;\n      this.#currentResultOptions = this.options;\n      this.#currentResultState = this.#currentQuery.state;\n    }\n    return result;\n  }\n  getCurrentResult() {\n    return this.#currentResult;\n  }\n  trackResult(result, onPropTracked) {\n    return new Proxy(result, {\n      get: (target, key) => {\n        this.trackProp(key);\n        onPropTracked?.(key);\n        return Reflect.get(target, key);\n      }\n    });\n  }\n  trackProp(key) {\n    this.#trackedProps.add(key);\n  }\n  getCurrentQuery() {\n    return this.#currentQuery;\n  }\n  refetch({\n    ...options\n  } = {}) {\n    return this.fetch({\n      ...options\n    });\n  }\n  fetchOptimistic(options) {\n    const defaultedOptions = this.#client.defaultQueryOptions(options);\n    const query = this.#client.getQueryCache().build(this.#client, defaultedOptions);\n    return query.fetch().then(() => this.createResult(query, defaultedOptions));\n  }\n  fetch(fetchOptions) {\n    return this.#executeFetch({\n      ...fetchOptions,\n      cancelRefetch: fetchOptions.cancelRefetch ?? true\n    }).then(() => {\n      this.updateResult();\n      return this.#currentResult;\n    });\n  }\n  #executeFetch(fetchOptions) {\n    this.#updateQuery();\n    let promise = this.#currentQuery.fetch(this.options, fetchOptions);\n    if (!fetchOptions?.throwOnError) {\n      promise = promise.catch(noop);\n    }\n    return promise;\n  }\n  #updateStaleTimeout() {\n    this.#clearStaleTimeout();\n    const staleTime = resolveStaleTime(this.options.staleTime, this.#currentQuery);\n    if (isServer || this.#currentResult.isStale || !isValidTimeout(staleTime)) {\n      return;\n    }\n    const time = timeUntilStale(this.#currentResult.dataUpdatedAt, staleTime);\n    const timeout = time + 1;\n    this.#staleTimeoutId = setTimeout(() => {\n      if (!this.#currentResult.isStale) {\n        this.updateResult();\n      }\n    }, timeout);\n  }\n  #computeRefetchInterval() {\n    return (typeof this.options.refetchInterval === \"function\" ? this.options.refetchInterval(this.#currentQuery) : this.options.refetchInterval) ?? false;\n  }\n  #updateRefetchInterval(nextInterval) {\n    this.#clearRefetchInterval();\n    this.#currentRefetchInterval = nextInterval;\n    if (isServer || resolveEnabled(this.options.enabled, this.#currentQuery) === false || !isValidTimeout(this.#currentRefetchInterval) || this.#currentRefetchInterval === 0) {\n      return;\n    }\n    this.#refetchIntervalId = setInterval(() => {\n      if (this.options.refetchIntervalInBackground || focusManager.isFocused()) {\n        this.#executeFetch();\n      }\n    }, this.#currentRefetchInterval);\n  }\n  #updateTimers() {\n    this.#updateStaleTimeout();\n    this.#updateRefetchInterval(this.#computeRefetchInterval());\n  }\n  #clearStaleTimeout() {\n    if (this.#staleTimeoutId) {\n      clearTimeout(this.#staleTimeoutId);\n      this.#staleTimeoutId = void 0;\n    }\n  }\n  #clearRefetchInterval() {\n    if (this.#refetchIntervalId) {\n      clearInterval(this.#refetchIntervalId);\n      this.#refetchIntervalId = void 0;\n    }\n  }\n  createResult(query, options) {\n    const prevQuery = this.#currentQuery;\n    const prevOptions = this.options;\n    const prevResult = this.#currentResult;\n    const prevResultState = this.#currentResultState;\n    const prevResultOptions = this.#currentResultOptions;\n    const queryChange = query !== prevQuery;\n    const queryInitialState = queryChange ? query.state : this.#currentQueryInitialState;\n    const {\n      state\n    } = query;\n    let newState = {\n      ...state\n    };\n    let isPlaceholderData = false;\n    let data;\n    if (options._optimisticResults) {\n      const mounted = this.hasListeners();\n      const fetchOnMount = !mounted && shouldFetchOnMount(query, options);\n      const fetchOptionally = mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions);\n      if (fetchOnMount || fetchOptionally) {\n        newState = {\n          ...newState,\n          ...fetchState(state.data, query.options)\n        };\n      }\n      if (options._optimisticResults === \"isRestoring\") {\n        newState.fetchStatus = \"idle\";\n      }\n    }\n    let {\n      error,\n      errorUpdatedAt,\n      status\n    } = newState;\n    data = newState.data;\n    let skipSelect = false;\n    if (options.placeholderData !== void 0 && data === void 0 && status === \"pending\") {\n      let placeholderData;\n      if (prevResult?.isPlaceholderData && options.placeholderData === prevResultOptions?.placeholderData) {\n        placeholderData = prevResult.data;\n        skipSelect = true;\n      } else {\n        placeholderData = typeof options.placeholderData === \"function\" ? options.placeholderData(this.#lastQueryWithDefinedData?.state.data, this.#lastQueryWithDefinedData) : options.placeholderData;\n      }\n      if (placeholderData !== void 0) {\n        status = \"success\";\n        data = replaceData(prevResult?.data, placeholderData, options);\n        isPlaceholderData = true;\n      }\n    }\n    if (options.select && data !== void 0 && !skipSelect) {\n      if (prevResult && data === prevResultState?.data && options.select === this.#selectFn) {\n        data = this.#selectResult;\n      } else {\n        try {\n          this.#selectFn = options.select;\n          data = options.select(data);\n          data = replaceData(prevResult?.data, data, options);\n          this.#selectResult = data;\n          this.#selectError = null;\n        } catch (selectError) {\n          this.#selectError = selectError;\n        }\n      }\n    }\n    if (this.#selectError) {\n      error = this.#selectError;\n      data = this.#selectResult;\n      errorUpdatedAt = Date.now();\n      status = \"error\";\n    }\n    const isFetching = newState.fetchStatus === \"fetching\";\n    const isPending = status === \"pending\";\n    const isError = status === \"error\";\n    const isLoading = isPending && isFetching;\n    const hasData = data !== void 0;\n    const result = {\n      status,\n      fetchStatus: newState.fetchStatus,\n      isPending,\n      isSuccess: status === \"success\",\n      isError,\n      isInitialLoading: isLoading,\n      isLoading,\n      data,\n      dataUpdatedAt: newState.dataUpdatedAt,\n      error,\n      errorUpdatedAt,\n      failureCount: newState.fetchFailureCount,\n      failureReason: newState.fetchFailureReason,\n      errorUpdateCount: newState.errorUpdateCount,\n      isFetched: newState.dataUpdateCount > 0 || newState.errorUpdateCount > 0,\n      isFetchedAfterMount: newState.dataUpdateCount > queryInitialState.dataUpdateCount || newState.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching,\n      isRefetching: isFetching && !isPending,\n      isLoadingError: isError && !hasData,\n      isPaused: newState.fetchStatus === \"paused\",\n      isPlaceholderData,\n      isRefetchError: isError && hasData,\n      isStale: isStale(query, options),\n      refetch: this.refetch,\n      promise: this.#currentThenable\n    };\n    const nextResult = result;\n    if (this.options.experimental_prefetchInRender) {\n      const finalizeThenableIfPossible = thenable => {\n        if (nextResult.status === \"error\") {\n          thenable.reject(nextResult.error);\n        } else if (nextResult.data !== void 0) {\n          thenable.resolve(nextResult.data);\n        }\n      };\n      const recreateThenable = () => {\n        const pending = this.#currentThenable = nextResult.promise = pendingThenable();\n        finalizeThenableIfPossible(pending);\n      };\n      const prevThenable = this.#currentThenable;\n      switch (prevThenable.status) {\n        case \"pending\":\n          if (query.queryHash === prevQuery.queryHash) {\n            finalizeThenableIfPossible(prevThenable);\n          }\n          break;\n        case \"fulfilled\":\n          if (nextResult.status === \"error\" || nextResult.data !== prevThenable.value) {\n            recreateThenable();\n          }\n          break;\n        case \"rejected\":\n          if (nextResult.status !== \"error\" || nextResult.error !== prevThenable.reason) {\n            recreateThenable();\n          }\n          break;\n      }\n    }\n    return nextResult;\n  }\n  updateResult() {\n    const prevResult = this.#currentResult;\n    const nextResult = this.createResult(this.#currentQuery, this.options);\n    this.#currentResultState = this.#currentQuery.state;\n    this.#currentResultOptions = this.options;\n    if (this.#currentResultState.data !== void 0) {\n      this.#lastQueryWithDefinedData = this.#currentQuery;\n    }\n    if (shallowEqualObjects(nextResult, prevResult)) {\n      return;\n    }\n    this.#currentResult = nextResult;\n    const shouldNotifyListeners = () => {\n      if (!prevResult) {\n        return true;\n      }\n      const {\n        notifyOnChangeProps\n      } = this.options;\n      const notifyOnChangePropsValue = typeof notifyOnChangeProps === \"function\" ? notifyOnChangeProps() : notifyOnChangeProps;\n      if (notifyOnChangePropsValue === \"all\" || !notifyOnChangePropsValue && !this.#trackedProps.size) {\n        return true;\n      }\n      const includedProps = new Set(notifyOnChangePropsValue ?? this.#trackedProps);\n      if (this.options.throwOnError) {\n        includedProps.add(\"error\");\n      }\n      return Object.keys(this.#currentResult).some(key => {\n        const typedKey = key;\n        const changed = this.#currentResult[typedKey] !== prevResult[typedKey];\n        return changed && includedProps.has(typedKey);\n      });\n    };\n    this.#notify({\n      listeners: shouldNotifyListeners()\n    });\n  }\n  #updateQuery() {\n    const query = this.#client.getQueryCache().build(this.#client, this.options);\n    if (query === this.#currentQuery) {\n      return;\n    }\n    const prevQuery = this.#currentQuery;\n    this.#currentQuery = query;\n    this.#currentQueryInitialState = query.state;\n    if (this.hasListeners()) {\n      prevQuery?.removeObserver(this);\n      query.addObserver(this);\n    }\n  }\n  onQueryUpdate() {\n    this.updateResult();\n    if (this.hasListeners()) {\n      this.#updateTimers();\n    }\n  }\n  #notify(notifyOptions) {\n    notifyManager.batch(() => {\n      if (notifyOptions.listeners) {\n        this.listeners.forEach(listener => {\n          listener(this.#currentResult);\n        });\n      }\n      this.#client.getQueryCache().notify({\n        query: this.#currentQuery,\n        type: \"observerResultsUpdated\"\n      });\n    });\n  }\n};\nfunction shouldLoadOnMount(query, options) {\n  return resolveEnabled(options.enabled, query) !== false && query.state.data === void 0 && !(query.state.status === \"error\" && options.retryOnMount === false);\n}\nfunction shouldFetchOnMount(query, options) {\n  return shouldLoadOnMount(query, options) || query.state.data !== void 0 && shouldFetchOn(query, options, options.refetchOnMount);\n}\nfunction shouldFetchOn(query, options, field) {\n  if (resolveEnabled(options.enabled, query) !== false && resolveStaleTime(options.staleTime, query) !== \"static\") {\n    const value = typeof field === \"function\" ? field(query) : field;\n    return value === \"always\" || value !== false && isStale(query, options);\n  }\n  return false;\n}\nfunction shouldFetchOptionally(query, prevQuery, options, prevOptions) {\n  return (query !== prevQuery || resolveEnabled(prevOptions.enabled, query) === false) && (!options.suspense || query.state.status !== \"error\") && isStale(query, options);\n}\nfunction isStale(query, options) {\n  return resolveEnabled(options.enabled, query) !== false && query.isStaleByTime(resolveStaleTime(options.staleTime, query));\n}\nfunction shouldAssignObserverCurrentProperties(observer, optimisticResult) {\n  if (!shallowEqualObjects(observer.getCurrentResult(), optimisticResult)) {\n    return true;\n  }\n  return false;\n}\nexport { QueryObserver };", "map": {"version": 3, "names": ["focusManager", "notify<PERSON><PERSON>ger", "fetchState", "Subscribable", "pendingThenable", "isServer", "isValidTimeout", "noop", "replaceData", "resolveEnabled", "resolveStaleTime", "shallowEqualObjects", "timeUntilStale", "QueryObserver", "constructor", "client", "options", "selectError", "currentThenable", "experimental_prefetchInRender", "reject", "Error", "bindMethods", "setOptions", "<PERSON><PERSON><PERSON><PERSON>", "currentQueryInitialState", "currentResult", "currentResultState", "currentResultOptions", "selectFn", "selectResult", "lastQueryWithDefinedData", "staleTimeoutId", "refetchIntervalId", "currentRefetchInterval", "trackedProps", "Set", "refetch", "bind", "onSubscribe", "listeners", "size", "addObserver", "shouldFetchOnMount", "executeFetch", "updateResult", "updateTimers", "onUnsubscribe", "hasListeners", "destroy", "shouldFetchOnReconnect", "shouldFetchOn", "refetchOnReconnect", "shouldFetchOnWindowFocus", "refetchOnWindowFocus", "clearStaleTimeout", "clearRefetchInterval", "removeObserver", "prevOptions", "prev<PERSON><PERSON><PERSON>", "defaultQueryOptions", "enabled", "updateQuery", "_defaulted", "get<PERSON><PERSON><PERSON><PERSON>ache", "notify", "type", "query", "observer", "mounted", "shouldFetchOptionally", "staleTime", "updateStaleTimeout", "nextRefetchInterval", "computeRefetchInterval", "updateRefetchInterval", "getOptimisticResult", "build", "result", "createResult", "shouldAssignObserverCurrentProperties", "state", "getCurrentResult", "trackResult", "onPropTracked", "Proxy", "get", "target", "key", "trackProp", "Reflect", "add", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fetch", "fetchOptimistic", "defaultedOptions", "then", "fetchOptions", "cancelRefetch", "#executeFetch", "promise", "throwOnError", "catch", "#updateStaleTimeout", "isStale", "time", "dataUpdatedAt", "timeout", "setTimeout", "#computeRefetchInterval", "refetchInterval", "#updateRefetchInterval", "nextInterval", "setInterval", "refetchIntervalInBackground", "isFocused", "#updateTimers", "#clearStaleTimeout", "clearTimeout", "#clearRefetchInterval", "clearInterval", "prevResult", "prevResultState", "prevResultOptions", "query<PERSON>hange", "queryInitialState", "newState", "isPlaceholderData", "data", "_optimisticResults", "fetchOnMount", "fetchOptionally", "fetchStatus", "error", "errorUpdatedAt", "status", "skipSelect", "placeholderData", "select", "Date", "now", "isFetching", "isPending", "isError", "isLoading", "hasData", "isSuccess", "isInitialLoading", "failureCount", "fetchFailureCount", "failureReason", "fetchFailureReason", "errorUpdateCount", "isFetched", "dataUpdateCount", "isFetchedAfterMount", "isRefetching", "isLoadingError", "isPaused", "isRefetchError", "nextResult", "finalizeThenableIfPossible", "thenable", "resolve", "recreateThenable", "pending", "prevThenable", "queryHash", "value", "reason", "shouldNotifyListeners", "notifyOnChangeProps", "notifyOnChangePropsValue", "includedProps", "Object", "keys", "some", "<PERSON><PERSON><PERSON>", "changed", "has", "#updateQuery", "onQueryUpdate", "#notify", "notifyOptions", "batch", "for<PERSON>ach", "listener", "shouldLoadOnMount", "retryOnMount", "refetchOnMount", "field", "suspense", "isStaleByTime", "optimisticResult"], "sources": ["/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/node_modules/@tanstack/query-core/src/queryObserver.ts"], "sourcesContent": ["import { focusManager } from './focusManager'\nimport { notifyManager } from './notifyManager'\nimport { fetchState } from './query'\nimport { Subscribable } from './subscribable'\nimport { pendingThenable } from './thenable'\nimport {\n  isServer,\n  isValidTimeout,\n  noop,\n  replaceData,\n  resolveEnabled,\n  resolveStaleTime,\n  shallowEqualObjects,\n  timeUntilStale,\n} from './utils'\nimport type { FetchOptions, Query, QueryState } from './query'\nimport type { QueryClient } from './queryClient'\nimport type { PendingThenable, Thenable } from './thenable'\nimport type {\n  DefaultError,\n  DefaultedQueryObserverOptions,\n  PlaceholderDataFunction,\n  QueryKey,\n  QueryObserverBaseResult,\n  QueryObserverOptions,\n  QueryObserverResult,\n  QueryOptions,\n  RefetchOptions,\n} from './types'\n\ntype QueryObserverListener<TData, TError> = (\n  result: QueryObserverResult<TData, TError>,\n) => void\n\ninterface ObserverFetchOptions extends FetchOptions {\n  throwOnError?: boolean\n}\n\nexport class QueryObserver<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> extends Subscribable<QueryObserverListener<TData, TError>> {\n  #client: QueryClient\n  #currentQuery: Query<TQueryFnData, TError, TQueryData, TQueryKey> = undefined!\n  #currentQueryInitialState: QueryState<TQueryData, TError> = undefined!\n  #currentResult: QueryObserverResult<TData, TError> = undefined!\n  #currentResultState?: QueryState<TQueryData, TError>\n  #currentResultOptions?: QueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >\n  #currentThenable: Thenable<TData>\n  #selectError: TError | null\n  #selectFn?: (data: TQueryData) => TData\n  #selectResult?: TData\n  // This property keeps track of the last query with defined data.\n  // It will be used to pass the previous data and query to the placeholder function between renders.\n  #lastQueryWithDefinedData?: Query<TQueryFnData, TError, TQueryData, TQueryKey>\n  #staleTimeoutId?: ReturnType<typeof setTimeout>\n  #refetchIntervalId?: ReturnType<typeof setInterval>\n  #currentRefetchInterval?: number | false\n  #trackedProps = new Set<keyof QueryObserverResult>()\n\n  constructor(\n    client: QueryClient,\n    public options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ) {\n    super()\n\n    this.#client = client\n    this.#selectError = null\n    this.#currentThenable = pendingThenable()\n    if (!this.options.experimental_prefetchInRender) {\n      this.#currentThenable.reject(\n        new Error('experimental_prefetchInRender feature flag is not enabled'),\n      )\n    }\n\n    this.bindMethods()\n    this.setOptions(options)\n  }\n\n  protected bindMethods(): void {\n    this.refetch = this.refetch.bind(this)\n  }\n\n  protected onSubscribe(): void {\n    if (this.listeners.size === 1) {\n      this.#currentQuery.addObserver(this)\n\n      if (shouldFetchOnMount(this.#currentQuery, this.options)) {\n        this.#executeFetch()\n      } else {\n        this.updateResult()\n      }\n\n      this.#updateTimers()\n    }\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.hasListeners()) {\n      this.destroy()\n    }\n  }\n\n  shouldFetchOnReconnect(): boolean {\n    return shouldFetchOn(\n      this.#currentQuery,\n      this.options,\n      this.options.refetchOnReconnect,\n    )\n  }\n\n  shouldFetchOnWindowFocus(): boolean {\n    return shouldFetchOn(\n      this.#currentQuery,\n      this.options,\n      this.options.refetchOnWindowFocus,\n    )\n  }\n\n  destroy(): void {\n    this.listeners = new Set()\n    this.#clearStaleTimeout()\n    this.#clearRefetchInterval()\n    this.#currentQuery.removeObserver(this)\n  }\n\n  setOptions(\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): void {\n    const prevOptions = this.options\n    const prevQuery = this.#currentQuery\n\n    this.options = this.#client.defaultQueryOptions(options)\n\n    if (\n      this.options.enabled !== undefined &&\n      typeof this.options.enabled !== 'boolean' &&\n      typeof this.options.enabled !== 'function' &&\n      typeof resolveEnabled(this.options.enabled, this.#currentQuery) !==\n        'boolean'\n    ) {\n      throw new Error(\n        'Expected enabled to be a boolean or a callback that returns a boolean',\n      )\n    }\n\n    this.#updateQuery()\n    this.#currentQuery.setOptions(this.options)\n\n    if (\n      prevOptions._defaulted &&\n      !shallowEqualObjects(this.options, prevOptions)\n    ) {\n      this.#client.getQueryCache().notify({\n        type: 'observerOptionsUpdated',\n        query: this.#currentQuery,\n        observer: this,\n      })\n    }\n\n    const mounted = this.hasListeners()\n\n    // Fetch if there are subscribers\n    if (\n      mounted &&\n      shouldFetchOptionally(\n        this.#currentQuery,\n        prevQuery,\n        this.options,\n        prevOptions,\n      )\n    ) {\n      this.#executeFetch()\n    }\n\n    // Update result\n    this.updateResult()\n\n    // Update stale interval if needed\n    if (\n      mounted &&\n      (this.#currentQuery !== prevQuery ||\n        resolveEnabled(this.options.enabled, this.#currentQuery) !==\n          resolveEnabled(prevOptions.enabled, this.#currentQuery) ||\n        resolveStaleTime(this.options.staleTime, this.#currentQuery) !==\n          resolveStaleTime(prevOptions.staleTime, this.#currentQuery))\n    ) {\n      this.#updateStaleTimeout()\n    }\n\n    const nextRefetchInterval = this.#computeRefetchInterval()\n\n    // Update refetch interval if needed\n    if (\n      mounted &&\n      (this.#currentQuery !== prevQuery ||\n        resolveEnabled(this.options.enabled, this.#currentQuery) !==\n          resolveEnabled(prevOptions.enabled, this.#currentQuery) ||\n        nextRefetchInterval !== this.#currentRefetchInterval)\n    ) {\n      this.#updateRefetchInterval(nextRefetchInterval)\n    }\n  }\n\n  getOptimisticResult(\n    options: DefaultedQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): QueryObserverResult<TData, TError> {\n    const query = this.#client.getQueryCache().build(this.#client, options)\n\n    const result = this.createResult(query, options)\n\n    if (shouldAssignObserverCurrentProperties(this, result)) {\n      // this assigns the optimistic result to the current Observer\n      // because if the query function changes, useQuery will be performing\n      // an effect where it would fetch again.\n      // When the fetch finishes, we perform a deep data cloning in order\n      // to reuse objects references. This deep data clone is performed against\n      // the `observer.currentResult.data` property\n      // When QueryKey changes, we refresh the query and get new `optimistic`\n      // result, while we leave the `observer.currentResult`, so when new data\n      // arrives, it finds the old `observer.currentResult` which is related\n      // to the old QueryKey. Which means that currentResult and selectData are\n      // out of sync already.\n      // To solve this, we move the cursor of the currentResult every time\n      // an observer reads an optimistic value.\n\n      // When keeping the previous data, the result doesn't change until new\n      // data arrives.\n      this.#currentResult = result\n      this.#currentResultOptions = this.options\n      this.#currentResultState = this.#currentQuery.state\n    }\n    return result\n  }\n\n  getCurrentResult(): QueryObserverResult<TData, TError> {\n    return this.#currentResult\n  }\n\n  trackResult(\n    result: QueryObserverResult<TData, TError>,\n    onPropTracked?: (key: keyof QueryObserverResult) => void,\n  ): QueryObserverResult<TData, TError> {\n    return new Proxy(result, {\n      get: (target, key) => {\n        this.trackProp(key as keyof QueryObserverResult)\n        onPropTracked?.(key as keyof QueryObserverResult)\n        return Reflect.get(target, key)\n      },\n    })\n  }\n\n  trackProp(key: keyof QueryObserverResult) {\n    this.#trackedProps.add(key)\n  }\n\n  getCurrentQuery(): Query<TQueryFnData, TError, TQueryData, TQueryKey> {\n    return this.#currentQuery\n  }\n\n  refetch({ ...options }: RefetchOptions = {}): Promise<\n    QueryObserverResult<TData, TError>\n  > {\n    return this.fetch({\n      ...options,\n    })\n  }\n\n  fetchOptimistic(\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): Promise<QueryObserverResult<TData, TError>> {\n    const defaultedOptions = this.#client.defaultQueryOptions(options)\n\n    const query = this.#client\n      .getQueryCache()\n      .build(this.#client, defaultedOptions)\n\n    return query.fetch().then(() => this.createResult(query, defaultedOptions))\n  }\n\n  protected fetch(\n    fetchOptions: ObserverFetchOptions,\n  ): Promise<QueryObserverResult<TData, TError>> {\n    return this.#executeFetch({\n      ...fetchOptions,\n      cancelRefetch: fetchOptions.cancelRefetch ?? true,\n    }).then(() => {\n      this.updateResult()\n      return this.#currentResult\n    })\n  }\n\n  #executeFetch(\n    fetchOptions?: Omit<ObserverFetchOptions, 'initialPromise'>,\n  ): Promise<TQueryData | undefined> {\n    // Make sure we reference the latest query as the current one might have been removed\n    this.#updateQuery()\n\n    // Fetch\n    let promise: Promise<TQueryData | undefined> = this.#currentQuery.fetch(\n      this.options as QueryOptions<TQueryFnData, TError, TQueryData, TQueryKey>,\n      fetchOptions,\n    )\n\n    if (!fetchOptions?.throwOnError) {\n      promise = promise.catch(noop)\n    }\n\n    return promise\n  }\n\n  #updateStaleTimeout(): void {\n    this.#clearStaleTimeout()\n    const staleTime = resolveStaleTime(\n      this.options.staleTime,\n      this.#currentQuery,\n    )\n\n    if (isServer || this.#currentResult.isStale || !isValidTimeout(staleTime)) {\n      return\n    }\n\n    const time = timeUntilStale(this.#currentResult.dataUpdatedAt, staleTime)\n\n    // The timeout is sometimes triggered 1 ms before the stale time expiration.\n    // To mitigate this issue we always add 1 ms to the timeout.\n    const timeout = time + 1\n\n    this.#staleTimeoutId = setTimeout(() => {\n      if (!this.#currentResult.isStale) {\n        this.updateResult()\n      }\n    }, timeout)\n  }\n\n  #computeRefetchInterval() {\n    return (\n      (typeof this.options.refetchInterval === 'function'\n        ? this.options.refetchInterval(this.#currentQuery)\n        : this.options.refetchInterval) ?? false\n    )\n  }\n\n  #updateRefetchInterval(nextInterval: number | false): void {\n    this.#clearRefetchInterval()\n\n    this.#currentRefetchInterval = nextInterval\n\n    if (\n      isServer ||\n      resolveEnabled(this.options.enabled, this.#currentQuery) === false ||\n      !isValidTimeout(this.#currentRefetchInterval) ||\n      this.#currentRefetchInterval === 0\n    ) {\n      return\n    }\n\n    this.#refetchIntervalId = setInterval(() => {\n      if (\n        this.options.refetchIntervalInBackground ||\n        focusManager.isFocused()\n      ) {\n        this.#executeFetch()\n      }\n    }, this.#currentRefetchInterval)\n  }\n\n  #updateTimers(): void {\n    this.#updateStaleTimeout()\n    this.#updateRefetchInterval(this.#computeRefetchInterval())\n  }\n\n  #clearStaleTimeout(): void {\n    if (this.#staleTimeoutId) {\n      clearTimeout(this.#staleTimeoutId)\n      this.#staleTimeoutId = undefined\n    }\n  }\n\n  #clearRefetchInterval(): void {\n    if (this.#refetchIntervalId) {\n      clearInterval(this.#refetchIntervalId)\n      this.#refetchIntervalId = undefined\n    }\n  }\n\n  protected createResult(\n    query: Query<TQueryFnData, TError, TQueryData, TQueryKey>,\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): QueryObserverResult<TData, TError> {\n    const prevQuery = this.#currentQuery\n    const prevOptions = this.options\n    const prevResult = this.#currentResult as\n      | QueryObserverResult<TData, TError>\n      | undefined\n    const prevResultState = this.#currentResultState\n    const prevResultOptions = this.#currentResultOptions\n    const queryChange = query !== prevQuery\n    const queryInitialState = queryChange\n      ? query.state\n      : this.#currentQueryInitialState\n\n    const { state } = query\n    let newState = { ...state }\n    let isPlaceholderData = false\n    let data: TData | undefined\n\n    // Optimistically set result in fetching state if needed\n    if (options._optimisticResults) {\n      const mounted = this.hasListeners()\n\n      const fetchOnMount = !mounted && shouldFetchOnMount(query, options)\n\n      const fetchOptionally =\n        mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions)\n\n      if (fetchOnMount || fetchOptionally) {\n        newState = {\n          ...newState,\n          ...fetchState(state.data, query.options),\n        }\n      }\n      if (options._optimisticResults === 'isRestoring') {\n        newState.fetchStatus = 'idle'\n      }\n    }\n\n    let { error, errorUpdatedAt, status } = newState\n\n    // Per default, use query data\n    data = newState.data as unknown as TData\n    let skipSelect = false\n\n    // use placeholderData if needed\n    if (\n      options.placeholderData !== undefined &&\n      data === undefined &&\n      status === 'pending'\n    ) {\n      let placeholderData\n\n      // Memoize placeholder data\n      if (\n        prevResult?.isPlaceholderData &&\n        options.placeholderData === prevResultOptions?.placeholderData\n      ) {\n        placeholderData = prevResult.data\n        // we have to skip select when reading this memoization\n        // because prevResult.data is already \"selected\"\n        skipSelect = true\n      } else {\n        // compute placeholderData\n        placeholderData =\n          typeof options.placeholderData === 'function'\n            ? (\n                options.placeholderData as unknown as PlaceholderDataFunction<TQueryData>\n              )(\n                this.#lastQueryWithDefinedData?.state.data,\n                this.#lastQueryWithDefinedData as any,\n              )\n            : options.placeholderData\n      }\n\n      if (placeholderData !== undefined) {\n        status = 'success'\n        data = replaceData(\n          prevResult?.data,\n          placeholderData as unknown,\n          options,\n        ) as TData\n        isPlaceholderData = true\n      }\n    }\n\n    // Select data if needed\n    // this also runs placeholderData through the select function\n    if (options.select && data !== undefined && !skipSelect) {\n      // Memoize select result\n      if (\n        prevResult &&\n        data === prevResultState?.data &&\n        options.select === this.#selectFn\n      ) {\n        data = this.#selectResult\n      } else {\n        try {\n          this.#selectFn = options.select\n          data = options.select(data as any)\n          data = replaceData(prevResult?.data, data, options)\n          this.#selectResult = data\n          this.#selectError = null\n        } catch (selectError) {\n          this.#selectError = selectError as TError\n        }\n      }\n    }\n\n    if (this.#selectError) {\n      error = this.#selectError as any\n      data = this.#selectResult\n      errorUpdatedAt = Date.now()\n      status = 'error'\n    }\n\n    const isFetching = newState.fetchStatus === 'fetching'\n    const isPending = status === 'pending'\n    const isError = status === 'error'\n\n    const isLoading = isPending && isFetching\n    const hasData = data !== undefined\n\n    const result: QueryObserverBaseResult<TData, TError> = {\n      status,\n      fetchStatus: newState.fetchStatus,\n      isPending,\n      isSuccess: status === 'success',\n      isError,\n      isInitialLoading: isLoading,\n      isLoading,\n      data,\n      dataUpdatedAt: newState.dataUpdatedAt,\n      error,\n      errorUpdatedAt,\n      failureCount: newState.fetchFailureCount,\n      failureReason: newState.fetchFailureReason,\n      errorUpdateCount: newState.errorUpdateCount,\n      isFetched: newState.dataUpdateCount > 0 || newState.errorUpdateCount > 0,\n      isFetchedAfterMount:\n        newState.dataUpdateCount > queryInitialState.dataUpdateCount ||\n        newState.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching,\n      isRefetching: isFetching && !isPending,\n      isLoadingError: isError && !hasData,\n      isPaused: newState.fetchStatus === 'paused',\n      isPlaceholderData,\n      isRefetchError: isError && hasData,\n      isStale: isStale(query, options),\n      refetch: this.refetch,\n      promise: this.#currentThenable,\n    }\n\n    const nextResult = result as QueryObserverResult<TData, TError>\n\n    if (this.options.experimental_prefetchInRender) {\n      const finalizeThenableIfPossible = (thenable: PendingThenable<TData>) => {\n        if (nextResult.status === 'error') {\n          thenable.reject(nextResult.error)\n        } else if (nextResult.data !== undefined) {\n          thenable.resolve(nextResult.data)\n        }\n      }\n\n      /**\n       * Create a new thenable and result promise when the results have changed\n       */\n      const recreateThenable = () => {\n        const pending =\n          (this.#currentThenable =\n          nextResult.promise =\n            pendingThenable())\n\n        finalizeThenableIfPossible(pending)\n      }\n\n      const prevThenable = this.#currentThenable\n      switch (prevThenable.status) {\n        case 'pending':\n          // Finalize the previous thenable if it was pending\n          // and we are still observing the same query\n          if (query.queryHash === prevQuery.queryHash) {\n            finalizeThenableIfPossible(prevThenable)\n          }\n          break\n        case 'fulfilled':\n          if (\n            nextResult.status === 'error' ||\n            nextResult.data !== prevThenable.value\n          ) {\n            recreateThenable()\n          }\n          break\n        case 'rejected':\n          if (\n            nextResult.status !== 'error' ||\n            nextResult.error !== prevThenable.reason\n          ) {\n            recreateThenable()\n          }\n          break\n      }\n    }\n\n    return nextResult\n  }\n\n  updateResult(): void {\n    const prevResult = this.#currentResult as\n      | QueryObserverResult<TData, TError>\n      | undefined\n\n    const nextResult = this.createResult(this.#currentQuery, this.options)\n\n    this.#currentResultState = this.#currentQuery.state\n    this.#currentResultOptions = this.options\n\n    if (this.#currentResultState.data !== undefined) {\n      this.#lastQueryWithDefinedData = this.#currentQuery\n    }\n\n    // Only notify and update result if something has changed\n    if (shallowEqualObjects(nextResult, prevResult)) {\n      return\n    }\n\n    this.#currentResult = nextResult\n\n    const shouldNotifyListeners = (): boolean => {\n      if (!prevResult) {\n        return true\n      }\n\n      const { notifyOnChangeProps } = this.options\n      const notifyOnChangePropsValue =\n        typeof notifyOnChangeProps === 'function'\n          ? notifyOnChangeProps()\n          : notifyOnChangeProps\n\n      if (\n        notifyOnChangePropsValue === 'all' ||\n        (!notifyOnChangePropsValue && !this.#trackedProps.size)\n      ) {\n        return true\n      }\n\n      const includedProps = new Set(\n        notifyOnChangePropsValue ?? this.#trackedProps,\n      )\n\n      if (this.options.throwOnError) {\n        includedProps.add('error')\n      }\n\n      return Object.keys(this.#currentResult).some((key) => {\n        const typedKey = key as keyof QueryObserverResult\n        const changed = this.#currentResult[typedKey] !== prevResult[typedKey]\n\n        return changed && includedProps.has(typedKey)\n      })\n    }\n\n    this.#notify({ listeners: shouldNotifyListeners() })\n  }\n\n  #updateQuery(): void {\n    const query = this.#client.getQueryCache().build(this.#client, this.options)\n\n    if (query === this.#currentQuery) {\n      return\n    }\n\n    const prevQuery = this.#currentQuery as\n      | Query<TQueryFnData, TError, TQueryData, TQueryKey>\n      | undefined\n    this.#currentQuery = query\n    this.#currentQueryInitialState = query.state\n\n    if (this.hasListeners()) {\n      prevQuery?.removeObserver(this)\n      query.addObserver(this)\n    }\n  }\n\n  onQueryUpdate(): void {\n    this.updateResult()\n\n    if (this.hasListeners()) {\n      this.#updateTimers()\n    }\n  }\n\n  #notify(notifyOptions: { listeners: boolean }): void {\n    notifyManager.batch(() => {\n      // First, trigger the listeners\n      if (notifyOptions.listeners) {\n        this.listeners.forEach((listener) => {\n          listener(this.#currentResult)\n        })\n      }\n\n      // Then the cache listeners\n      this.#client.getQueryCache().notify({\n        query: this.#currentQuery,\n        type: 'observerResultsUpdated',\n      })\n    })\n  }\n}\n\nfunction shouldLoadOnMount(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any>,\n): boolean {\n  return (\n    resolveEnabled(options.enabled, query) !== false &&\n    query.state.data === undefined &&\n    !(query.state.status === 'error' && options.retryOnMount === false)\n  )\n}\n\nfunction shouldFetchOnMount(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return (\n    shouldLoadOnMount(query, options) ||\n    (query.state.data !== undefined &&\n      shouldFetchOn(query, options, options.refetchOnMount))\n  )\n}\n\nfunction shouldFetchOn(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n  field: (typeof options)['refetchOnMount'] &\n    (typeof options)['refetchOnWindowFocus'] &\n    (typeof options)['refetchOnReconnect'],\n) {\n  if (\n    resolveEnabled(options.enabled, query) !== false &&\n    resolveStaleTime(options.staleTime, query) !== 'static'\n  ) {\n    const value = typeof field === 'function' ? field(query) : field\n\n    return value === 'always' || (value !== false && isStale(query, options))\n  }\n  return false\n}\n\nfunction shouldFetchOptionally(\n  query: Query<any, any, any, any>,\n  prevQuery: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n  prevOptions: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return (\n    (query !== prevQuery ||\n      resolveEnabled(prevOptions.enabled, query) === false) &&\n    (!options.suspense || query.state.status !== 'error') &&\n    isStale(query, options)\n  )\n}\n\nfunction isStale(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return (\n    resolveEnabled(options.enabled, query) !== false &&\n    query.isStaleByTime(resolveStaleTime(options.staleTime, query))\n  )\n}\n\n// this function would decide if we will update the observer's 'current'\n// properties after an optimistic reading via getOptimisticResult\nfunction shouldAssignObserverCurrentProperties<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  observer: QueryObserver<TQueryFnData, TError, TData, TQueryData, TQueryKey>,\n  optimisticResult: QueryObserverResult<TData, TError>,\n) {\n  // if the newly created result isn't what the observer is holding as current,\n  // then we'll need to update the properties as well\n  if (!shallowEqualObjects(observer.getCurrentResult(), optimisticResult)) {\n    return true\n  }\n\n  // basically, just keep previous properties if nothing changed\n  return false\n}\n"], "mappings": ";AAAA,SAASA,YAAA,QAAoB;AAC7B,SAASC,aAAA,QAAqB;AAC9B,SAASC,UAAA,QAAkB;AAC3B,SAASC,YAAA,QAAoB;AAC7B,SAASC,eAAA,QAAuB;AAChC,SACEC,QAAA,EACAC,cAAA,EACAC,IAAA,EACAC,WAAA,EACAC,cAAA,EACAC,gBAAA,EACAC,mBAAA,EACAC,cAAA,QACK;AAwBA,IAAMC,aAAA,GAAN,cAMGV,YAAA,CAAmD;EAyB3DW,YACEC,MAAA,EACOC,OAAA,EAOP;IACA,MAAM;IARC,KAAAA,OAAA,GAAAA,OAAA;IAUP,KAAK,CAAAD,MAAA,GAAUA,MAAA;IACf,KAAK,CAAAE,WAAA,GAAe;IACpB,KAAK,CAAAC,eAAA,GAAmBd,eAAA,CAAgB;IACxC,IAAI,CAAC,KAAKY,OAAA,CAAQG,6BAAA,EAA+B;MAC/C,KAAK,CAAAD,eAAA,CAAiBE,MAAA,CACpB,IAAIC,KAAA,CAAM,2DAA2D,CACvE;IACF;IAEA,KAAKC,WAAA,CAAY;IACjB,KAAKC,UAAA,CAAWP,OAAO;EACzB;EA/CA,CAAAD,MAAA;EACA,CAAAS,YAAA,GAAoE;EACpE,CAAAC,wBAAA,GAA4D;EAC5D,CAAAC,aAAA,GAAqD;EACrD,CAAAC,kBAAA;EACA,CAAAC,oBAAA;EAOA,CAAAV,eAAA;EACA,CAAAD,WAAA;EACA,CAAAY,QAAA;EACA,CAAAC,YAAA;EAAA;EAAA;EAGA,CAAAC,wBAAA;EACA,CAAAC,cAAA;EACA,CAAAC,iBAAA;EACA,CAAAC,sBAAA;EACA,CAAAC,YAAA,GAAgB,mBAAIC,GAAA,CAA+B;EA2BzCd,YAAA,EAAoB;IAC5B,KAAKe,OAAA,GAAU,KAAKA,OAAA,CAAQC,IAAA,CAAK,IAAI;EACvC;EAEUC,YAAA,EAAoB;IAC5B,IAAI,KAAKC,SAAA,CAAUC,IAAA,KAAS,GAAG;MAC7B,KAAK,CAAAjB,YAAA,CAAckB,WAAA,CAAY,IAAI;MAEnC,IAAIC,kBAAA,CAAmB,KAAK,CAAAnB,YAAA,EAAe,KAAKR,OAAO,GAAG;QACxD,KAAK,CAAA4B,YAAA,CAAc;MACrB,OAAO;QACL,KAAKC,YAAA,CAAa;MACpB;MAEA,KAAK,CAAAC,YAAA,CAAc;IACrB;EACF;EAEUC,cAAA,EAAsB;IAC9B,IAAI,CAAC,KAAKC,YAAA,CAAa,GAAG;MACxB,KAAKC,OAAA,CAAQ;IACf;EACF;EAEAC,uBAAA,EAAkC;IAChC,OAAOC,aAAA,CACL,KAAK,CAAA3B,YAAA,EACL,KAAKR,OAAA,EACL,KAAKA,OAAA,CAAQoC,kBACf;EACF;EAEAC,yBAAA,EAAoC;IAClC,OAAOF,aAAA,CACL,KAAK,CAAA3B,YAAA,EACL,KAAKR,OAAA,EACL,KAAKA,OAAA,CAAQsC,oBACf;EACF;EAEAL,QAAA,EAAgB;IACd,KAAKT,SAAA,GAAY,mBAAIJ,GAAA,CAAI;IACzB,KAAK,CAAAmB,iBAAA,CAAmB;IACxB,KAAK,CAAAC,oBAAA,CAAsB;IAC3B,KAAK,CAAAhC,YAAA,CAAciC,cAAA,CAAe,IAAI;EACxC;EAEAlC,WACEP,OAAA,EAOM;IACN,MAAM0C,WAAA,GAAc,KAAK1C,OAAA;IACzB,MAAM2C,SAAA,GAAY,KAAK,CAAAnC,YAAA;IAEvB,KAAKR,OAAA,GAAU,KAAK,CAAAD,MAAA,CAAQ6C,mBAAA,CAAoB5C,OAAO;IAEvD,IACE,KAAKA,OAAA,CAAQ6C,OAAA,KAAY,UACzB,OAAO,KAAK7C,OAAA,CAAQ6C,OAAA,KAAY,aAChC,OAAO,KAAK7C,OAAA,CAAQ6C,OAAA,KAAY,cAChC,OAAOpD,cAAA,CAAe,KAAKO,OAAA,CAAQ6C,OAAA,EAAS,KAAK,CAAArC,YAAa,MAC5D,WACF;MACA,MAAM,IAAIH,KAAA,CACR,uEACF;IACF;IAEA,KAAK,CAAAyC,WAAA,CAAa;IAClB,KAAK,CAAAtC,YAAA,CAAcD,UAAA,CAAW,KAAKP,OAAO;IAE1C,IACE0C,WAAA,CAAYK,UAAA,IACZ,CAACpD,mBAAA,CAAoB,KAAKK,OAAA,EAAS0C,WAAW,GAC9C;MACA,KAAK,CAAA3C,MAAA,CAAQiD,aAAA,CAAc,EAAEC,MAAA,CAAO;QAClCC,IAAA,EAAM;QACNC,KAAA,EAAO,KAAK,CAAA3C,YAAA;QACZ4C,QAAA,EAAU;MACZ,CAAC;IACH;IAEA,MAAMC,OAAA,GAAU,KAAKrB,YAAA,CAAa;IAGlC,IACEqB,OAAA,IACAC,qBAAA,CACE,KAAK,CAAA9C,YAAA,EACLmC,SAAA,EACA,KAAK3C,OAAA,EACL0C,WACF,GACA;MACA,KAAK,CAAAd,YAAA,CAAc;IACrB;IAGA,KAAKC,YAAA,CAAa;IAGlB,IACEwB,OAAA,KACC,KAAK,CAAA7C,YAAA,KAAkBmC,SAAA,IACtBlD,cAAA,CAAe,KAAKO,OAAA,CAAQ6C,OAAA,EAAS,KAAK,CAAArC,YAAa,MACrDf,cAAA,CAAeiD,WAAA,CAAYG,OAAA,EAAS,KAAK,CAAArC,YAAa,KACxDd,gBAAA,CAAiB,KAAKM,OAAA,CAAQuD,SAAA,EAAW,KAAK,CAAA/C,YAAa,MACzDd,gBAAA,CAAiBgD,WAAA,CAAYa,SAAA,EAAW,KAAK,CAAA/C,YAAa,IAC9D;MACA,KAAK,CAAAgD,kBAAA,CAAoB;IAC3B;IAEA,MAAMC,mBAAA,GAAsB,KAAK,CAAAC,sBAAA,CAAwB;IAGzD,IACEL,OAAA,KACC,KAAK,CAAA7C,YAAA,KAAkBmC,SAAA,IACtBlD,cAAA,CAAe,KAAKO,OAAA,CAAQ6C,OAAA,EAAS,KAAK,CAAArC,YAAa,MACrDf,cAAA,CAAeiD,WAAA,CAAYG,OAAA,EAAS,KAAK,CAAArC,YAAa,KACxDiD,mBAAA,KAAwB,KAAK,CAAAvC,sBAAA,GAC/B;MACA,KAAK,CAAAyC,qBAAA,CAAuBF,mBAAmB;IACjD;EACF;EAEAG,oBACE5D,OAAA,EAOoC;IACpC,MAAMmD,KAAA,GAAQ,KAAK,CAAApD,MAAA,CAAQiD,aAAA,CAAc,EAAEa,KAAA,CAAM,KAAK,CAAA9D,MAAA,EAASC,OAAO;IAEtE,MAAM8D,MAAA,GAAS,KAAKC,YAAA,CAAaZ,KAAA,EAAOnD,OAAO;IAE/C,IAAIgE,qCAAA,CAAsC,MAAMF,MAAM,GAAG;MAiBvD,KAAK,CAAApD,aAAA,GAAiBoD,MAAA;MACtB,KAAK,CAAAlD,oBAAA,GAAwB,KAAKZ,OAAA;MAClC,KAAK,CAAAW,kBAAA,GAAsB,KAAK,CAAAH,YAAA,CAAcyD,KAAA;IAChD;IACA,OAAOH,MAAA;EACT;EAEAI,iBAAA,EAAuD;IACrD,OAAO,KAAK,CAAAxD,aAAA;EACd;EAEAyD,YACEL,MAAA,EACAM,aAAA,EACoC;IACpC,OAAO,IAAIC,KAAA,CAAMP,MAAA,EAAQ;MACvBQ,GAAA,EAAKA,CAACC,MAAA,EAAQC,GAAA,KAAQ;QACpB,KAAKC,SAAA,CAAUD,GAAgC;QAC/CJ,aAAA,GAAgBI,GAAgC;QAChD,OAAOE,OAAA,CAAQJ,GAAA,CAAIC,MAAA,EAAQC,GAAG;MAChC;IACF,CAAC;EACH;EAEAC,UAAUD,GAAA,EAAgC;IACxC,KAAK,CAAArD,YAAA,CAAcwD,GAAA,CAAIH,GAAG;EAC5B;EAEAI,gBAAA,EAAsE;IACpE,OAAO,KAAK,CAAApE,YAAA;EACd;EAEAa,QAAQ;IAAE,GAAGrB;EAAQ,IAAoB,CAAC,GAExC;IACA,OAAO,KAAK6E,KAAA,CAAM;MAChB,GAAG7E;IACL,CAAC;EACH;EAEA8E,gBACE9E,OAAA,EAO6C;IAC7C,MAAM+E,gBAAA,GAAmB,KAAK,CAAAhF,MAAA,CAAQ6C,mBAAA,CAAoB5C,OAAO;IAEjE,MAAMmD,KAAA,GAAQ,KAAK,CAAApD,MAAA,CAChBiD,aAAA,CAAc,EACda,KAAA,CAAM,KAAK,CAAA9D,MAAA,EAASgF,gBAAgB;IAEvC,OAAO5B,KAAA,CAAM0B,KAAA,CAAM,EAAEG,IAAA,CAAK,MAAM,KAAKjB,YAAA,CAAaZ,KAAA,EAAO4B,gBAAgB,CAAC;EAC5E;EAEUF,MACRI,YAAA,EAC6C;IAC7C,OAAO,KAAK,CAAArD,YAAA,CAAc;MACxB,GAAGqD,YAAA;MACHC,aAAA,EAAeD,YAAA,CAAaC,aAAA,IAAiB;IAC/C,CAAC,EAAEF,IAAA,CAAK,MAAM;MACZ,KAAKnD,YAAA,CAAa;MAClB,OAAO,KAAK,CAAAnB,aAAA;IACd,CAAC;EACH;EAEA,CAAAkB,YAAAuD,CACEF,YAAA,EACiC;IAEjC,KAAK,CAAAnC,WAAA,CAAa;IAGlB,IAAIsC,OAAA,GAA2C,KAAK,CAAA5E,YAAA,CAAcqE,KAAA,CAChE,KAAK7E,OAAA,EACLiF,YACF;IAEA,IAAI,CAACA,YAAA,EAAcI,YAAA,EAAc;MAC/BD,OAAA,GAAUA,OAAA,CAAQE,KAAA,CAAM/F,IAAI;IAC9B;IAEA,OAAO6F,OAAA;EACT;EAEA,CAAA5B,kBAAA+B,CAAA,EAA4B;IAC1B,KAAK,CAAAhD,iBAAA,CAAmB;IACxB,MAAMgB,SAAA,GAAY7D,gBAAA,CAChB,KAAKM,OAAA,CAAQuD,SAAA,EACb,KAAK,CAAA/C,YACP;IAEA,IAAInB,QAAA,IAAY,KAAK,CAAAqB,aAAA,CAAe8E,OAAA,IAAW,CAAClG,cAAA,CAAeiE,SAAS,GAAG;MACzE;IACF;IAEA,MAAMkC,IAAA,GAAO7F,cAAA,CAAe,KAAK,CAAAc,aAAA,CAAegF,aAAA,EAAenC,SAAS;IAIxE,MAAMoC,OAAA,GAAUF,IAAA,GAAO;IAEvB,KAAK,CAAAzE,cAAA,GAAkB4E,UAAA,CAAW,MAAM;MACtC,IAAI,CAAC,KAAK,CAAAlF,aAAA,CAAe8E,OAAA,EAAS;QAChC,KAAK3D,YAAA,CAAa;MACpB;IACF,GAAG8D,OAAO;EACZ;EAEA,CAAAjC,sBAAAmC,CAAA,EAA0B;IACxB,QACG,OAAO,KAAK7F,OAAA,CAAQ8F,eAAA,KAAoB,aACrC,KAAK9F,OAAA,CAAQ8F,eAAA,CAAgB,KAAK,CAAAtF,YAAa,IAC/C,KAAKR,OAAA,CAAQ8F,eAAA,KAAoB;EAEzC;EAEA,CAAAnC,qBAAAoC,CAAuBC,YAAA,EAAoC;IACzD,KAAK,CAAAxD,oBAAA,CAAsB;IAE3B,KAAK,CAAAtB,sBAAA,GAA0B8E,YAAA;IAE/B,IACE3G,QAAA,IACAI,cAAA,CAAe,KAAKO,OAAA,CAAQ6C,OAAA,EAAS,KAAK,CAAArC,YAAa,MAAM,SAC7D,CAAClB,cAAA,CAAe,KAAK,CAAA4B,sBAAuB,KAC5C,KAAK,CAAAA,sBAAA,KAA4B,GACjC;MACA;IACF;IAEA,KAAK,CAAAD,iBAAA,GAAqBgF,WAAA,CAAY,MAAM;MAC1C,IACE,KAAKjG,OAAA,CAAQkG,2BAAA,IACblH,YAAA,CAAamH,SAAA,CAAU,GACvB;QACA,KAAK,CAAAvE,YAAA,CAAc;MACrB;IACF,GAAG,KAAK,CAAAV,sBAAuB;EACjC;EAEA,CAAAY,YAAAsE,CAAA,EAAsB;IACpB,KAAK,CAAA5C,kBAAA,CAAoB;IACzB,KAAK,CAAAG,qBAAA,CAAuB,KAAK,CAAAD,sBAAA,CAAwB,CAAC;EAC5D;EAEA,CAAAnB,iBAAA8D,CAAA,EAA2B;IACzB,IAAI,KAAK,CAAArF,cAAA,EAAiB;MACxBsF,YAAA,CAAa,KAAK,CAAAtF,cAAe;MACjC,KAAK,CAAAA,cAAA,GAAkB;IACzB;EACF;EAEA,CAAAwB,oBAAA+D,CAAA,EAA8B;IAC5B,IAAI,KAAK,CAAAtF,iBAAA,EAAoB;MAC3BuF,aAAA,CAAc,KAAK,CAAAvF,iBAAkB;MACrC,KAAK,CAAAA,iBAAA,GAAqB;IAC5B;EACF;EAEU8C,aACRZ,KAAA,EACAnD,OAAA,EAOoC;IACpC,MAAM2C,SAAA,GAAY,KAAK,CAAAnC,YAAA;IACvB,MAAMkC,WAAA,GAAc,KAAK1C,OAAA;IACzB,MAAMyG,UAAA,GAAa,KAAK,CAAA/F,aAAA;IAGxB,MAAMgG,eAAA,GAAkB,KAAK,CAAA/F,kBAAA;IAC7B,MAAMgG,iBAAA,GAAoB,KAAK,CAAA/F,oBAAA;IAC/B,MAAMgG,WAAA,GAAczD,KAAA,KAAUR,SAAA;IAC9B,MAAMkE,iBAAA,GAAoBD,WAAA,GACtBzD,KAAA,CAAMc,KAAA,GACN,KAAK,CAAAxD,wBAAA;IAET,MAAM;MAAEwD;IAAM,IAAId,KAAA;IAClB,IAAI2D,QAAA,GAAW;MAAE,GAAG7C;IAAM;IAC1B,IAAI8C,iBAAA,GAAoB;IACxB,IAAIC,IAAA;IAGJ,IAAIhH,OAAA,CAAQiH,kBAAA,EAAoB;MAC9B,MAAM5D,OAAA,GAAU,KAAKrB,YAAA,CAAa;MAElC,MAAMkF,YAAA,GAAe,CAAC7D,OAAA,IAAW1B,kBAAA,CAAmBwB,KAAA,EAAOnD,OAAO;MAElE,MAAMmH,eAAA,GACJ9D,OAAA,IAAWC,qBAAA,CAAsBH,KAAA,EAAOR,SAAA,EAAW3C,OAAA,EAAS0C,WAAW;MAEzE,IAAIwE,YAAA,IAAgBC,eAAA,EAAiB;QACnCL,QAAA,GAAW;UACT,GAAGA,QAAA;UACH,GAAG5H,UAAA,CAAW+E,KAAA,CAAM+C,IAAA,EAAM7D,KAAA,CAAMnD,OAAO;QACzC;MACF;MACA,IAAIA,OAAA,CAAQiH,kBAAA,KAAuB,eAAe;QAChDH,QAAA,CAASM,WAAA,GAAc;MACzB;IACF;IAEA,IAAI;MAAEC,KAAA;MAAOC,cAAA;MAAgBC;IAAO,IAAIT,QAAA;IAGxCE,IAAA,GAAOF,QAAA,CAASE,IAAA;IAChB,IAAIQ,UAAA,GAAa;IAGjB,IACExH,OAAA,CAAQyH,eAAA,KAAoB,UAC5BT,IAAA,KAAS,UACTO,MAAA,KAAW,WACX;MACA,IAAIE,eAAA;MAGJ,IACEhB,UAAA,EAAYM,iBAAA,IACZ/G,OAAA,CAAQyH,eAAA,KAAoBd,iBAAA,EAAmBc,eAAA,EAC/C;QACAA,eAAA,GAAkBhB,UAAA,CAAWO,IAAA;QAG7BQ,UAAA,GAAa;MACf,OAAO;QAELC,eAAA,GACE,OAAOzH,OAAA,CAAQyH,eAAA,KAAoB,aAE7BzH,OAAA,CAAQyH,eAAA,CAER,KAAK,CAAA1G,wBAAA,EAA2BkD,KAAA,CAAM+C,IAAA,EACtC,KAAK,CAAAjG,wBACP,IACAf,OAAA,CAAQyH,eAAA;MAChB;MAEA,IAAIA,eAAA,KAAoB,QAAW;QACjCF,MAAA,GAAS;QACTP,IAAA,GAAOxH,WAAA,CACLiH,UAAA,EAAYO,IAAA,EACZS,eAAA,EACAzH,OACF;QACA+G,iBAAA,GAAoB;MACtB;IACF;IAIA,IAAI/G,OAAA,CAAQ0H,MAAA,IAAUV,IAAA,KAAS,UAAa,CAACQ,UAAA,EAAY;MAEvD,IACEf,UAAA,IACAO,IAAA,KAASN,eAAA,EAAiBM,IAAA,IAC1BhH,OAAA,CAAQ0H,MAAA,KAAW,KAAK,CAAA7G,QAAA,EACxB;QACAmG,IAAA,GAAO,KAAK,CAAAlG,YAAA;MACd,OAAO;QACL,IAAI;UACF,KAAK,CAAAD,QAAA,GAAYb,OAAA,CAAQ0H,MAAA;UACzBV,IAAA,GAAOhH,OAAA,CAAQ0H,MAAA,CAAOV,IAAW;UACjCA,IAAA,GAAOxH,WAAA,CAAYiH,UAAA,EAAYO,IAAA,EAAMA,IAAA,EAAMhH,OAAO;UAClD,KAAK,CAAAc,YAAA,GAAgBkG,IAAA;UACrB,KAAK,CAAA/G,WAAA,GAAe;QACtB,SAASA,WAAA,EAAa;UACpB,KAAK,CAAAA,WAAA,GAAeA,WAAA;QACtB;MACF;IACF;IAEA,IAAI,KAAK,CAAAA,WAAA,EAAc;MACrBoH,KAAA,GAAQ,KAAK,CAAApH,WAAA;MACb+G,IAAA,GAAO,KAAK,CAAAlG,YAAA;MACZwG,cAAA,GAAiBK,IAAA,CAAKC,GAAA,CAAI;MAC1BL,MAAA,GAAS;IACX;IAEA,MAAMM,UAAA,GAAaf,QAAA,CAASM,WAAA,KAAgB;IAC5C,MAAMU,SAAA,GAAYP,MAAA,KAAW;IAC7B,MAAMQ,OAAA,GAAUR,MAAA,KAAW;IAE3B,MAAMS,SAAA,GAAYF,SAAA,IAAaD,UAAA;IAC/B,MAAMI,OAAA,GAAUjB,IAAA,KAAS;IAEzB,MAAMlD,MAAA,GAAiD;MACrDyD,MAAA;MACAH,WAAA,EAAaN,QAAA,CAASM,WAAA;MACtBU,SAAA;MACAI,SAAA,EAAWX,MAAA,KAAW;MACtBQ,OAAA;MACAI,gBAAA,EAAkBH,SAAA;MAClBA,SAAA;MACAhB,IAAA;MACAtB,aAAA,EAAeoB,QAAA,CAASpB,aAAA;MACxB2B,KAAA;MACAC,cAAA;MACAc,YAAA,EAActB,QAAA,CAASuB,iBAAA;MACvBC,aAAA,EAAexB,QAAA,CAASyB,kBAAA;MACxBC,gBAAA,EAAkB1B,QAAA,CAAS0B,gBAAA;MAC3BC,SAAA,EAAW3B,QAAA,CAAS4B,eAAA,GAAkB,KAAK5B,QAAA,CAAS0B,gBAAA,GAAmB;MACvEG,mBAAA,EACE7B,QAAA,CAAS4B,eAAA,GAAkB7B,iBAAA,CAAkB6B,eAAA,IAC7C5B,QAAA,CAAS0B,gBAAA,GAAmB3B,iBAAA,CAAkB2B,gBAAA;MAChDX,UAAA;MACAe,YAAA,EAAcf,UAAA,IAAc,CAACC,SAAA;MAC7Be,cAAA,EAAgBd,OAAA,IAAW,CAACE,OAAA;MAC5Ba,QAAA,EAAUhC,QAAA,CAASM,WAAA,KAAgB;MACnCL,iBAAA;MACAgC,cAAA,EAAgBhB,OAAA,IAAWE,OAAA;MAC3BzC,OAAA,EAASA,OAAA,CAAQrC,KAAA,EAAOnD,OAAO;MAC/BqB,OAAA,EAAS,KAAKA,OAAA;MACd+D,OAAA,EAAS,KAAK,CAAAlF;IAChB;IAEA,MAAM8I,UAAA,GAAalF,MAAA;IAEnB,IAAI,KAAK9D,OAAA,CAAQG,6BAAA,EAA+B;MAC9C,MAAM8I,0BAAA,GAA8BC,QAAA,IAAqC;QACvE,IAAIF,UAAA,CAAWzB,MAAA,KAAW,SAAS;UACjC2B,QAAA,CAAS9I,MAAA,CAAO4I,UAAA,CAAW3B,KAAK;QAClC,WAAW2B,UAAA,CAAWhC,IAAA,KAAS,QAAW;UACxCkC,QAAA,CAASC,OAAA,CAAQH,UAAA,CAAWhC,IAAI;QAClC;MACF;MAKA,MAAMoC,gBAAA,GAAmBA,CAAA,KAAM;QAC7B,MAAMC,OAAA,GACH,KAAK,CAAAnJ,eAAA,GACN8I,UAAA,CAAW5D,OAAA,GACThG,eAAA,CAAgB;QAEpB6J,0BAAA,CAA2BI,OAAO;MACpC;MAEA,MAAMC,YAAA,GAAe,KAAK,CAAApJ,eAAA;MAC1B,QAAQoJ,YAAA,CAAa/B,MAAA;QACnB,KAAK;UAGH,IAAIpE,KAAA,CAAMoG,SAAA,KAAc5G,SAAA,CAAU4G,SAAA,EAAW;YAC3CN,0BAAA,CAA2BK,YAAY;UACzC;UACA;QACF,KAAK;UACH,IACEN,UAAA,CAAWzB,MAAA,KAAW,WACtByB,UAAA,CAAWhC,IAAA,KAASsC,YAAA,CAAaE,KAAA,EACjC;YACAJ,gBAAA,CAAiB;UACnB;UACA;QACF,KAAK;UACH,IACEJ,UAAA,CAAWzB,MAAA,KAAW,WACtByB,UAAA,CAAW3B,KAAA,KAAUiC,YAAA,CAAaG,MAAA,EAClC;YACAL,gBAAA,CAAiB;UACnB;UACA;MACJ;IACF;IAEA,OAAOJ,UAAA;EACT;EAEAnH,aAAA,EAAqB;IACnB,MAAM4E,UAAA,GAAa,KAAK,CAAA/F,aAAA;IAIxB,MAAMsI,UAAA,GAAa,KAAKjF,YAAA,CAAa,KAAK,CAAAvD,YAAA,EAAe,KAAKR,OAAO;IAErE,KAAK,CAAAW,kBAAA,GAAsB,KAAK,CAAAH,YAAA,CAAcyD,KAAA;IAC9C,KAAK,CAAArD,oBAAA,GAAwB,KAAKZ,OAAA;IAElC,IAAI,KAAK,CAAAW,kBAAA,CAAoBqG,IAAA,KAAS,QAAW;MAC/C,KAAK,CAAAjG,wBAAA,GAA4B,KAAK,CAAAP,YAAA;IACxC;IAGA,IAAIb,mBAAA,CAAoBqJ,UAAA,EAAYvC,UAAU,GAAG;MAC/C;IACF;IAEA,KAAK,CAAA/F,aAAA,GAAiBsI,UAAA;IAEtB,MAAMU,qBAAA,GAAwBA,CAAA,KAAe;MAC3C,IAAI,CAACjD,UAAA,EAAY;QACf,OAAO;MACT;MAEA,MAAM;QAAEkD;MAAoB,IAAI,KAAK3J,OAAA;MACrC,MAAM4J,wBAAA,GACJ,OAAOD,mBAAA,KAAwB,aAC3BA,mBAAA,CAAoB,IACpBA,mBAAA;MAEN,IACEC,wBAAA,KAA6B,SAC5B,CAACA,wBAAA,IAA4B,CAAC,KAAK,CAAAzI,YAAA,CAAcM,IAAA,EAClD;QACA,OAAO;MACT;MAEA,MAAMoI,aAAA,GAAgB,IAAIzI,GAAA,CACxBwI,wBAAA,IAA4B,KAAK,CAAAzI,YACnC;MAEA,IAAI,KAAKnB,OAAA,CAAQqF,YAAA,EAAc;QAC7BwE,aAAA,CAAclF,GAAA,CAAI,OAAO;MAC3B;MAEA,OAAOmF,MAAA,CAAOC,IAAA,CAAK,KAAK,CAAArJ,aAAc,EAAEsJ,IAAA,CAAMxF,GAAA,IAAQ;QACpD,MAAMyF,QAAA,GAAWzF,GAAA;QACjB,MAAM0F,OAAA,GAAU,KAAK,CAAAxJ,aAAA,CAAeuJ,QAAQ,MAAMxD,UAAA,CAAWwD,QAAQ;QAErE,OAAOC,OAAA,IAAWL,aAAA,CAAcM,GAAA,CAAIF,QAAQ;MAC9C,CAAC;IACH;IAEA,KAAK,CAAAhH,MAAA,CAAQ;MAAEzB,SAAA,EAAWkI,qBAAA,CAAsB;IAAE,CAAC;EACrD;EAEA,CAAA5G,WAAAsH,CAAA,EAAqB;IACnB,MAAMjH,KAAA,GAAQ,KAAK,CAAApD,MAAA,CAAQiD,aAAA,CAAc,EAAEa,KAAA,CAAM,KAAK,CAAA9D,MAAA,EAAS,KAAKC,OAAO;IAE3E,IAAImD,KAAA,KAAU,KAAK,CAAA3C,YAAA,EAAe;MAChC;IACF;IAEA,MAAMmC,SAAA,GAAY,KAAK,CAAAnC,YAAA;IAGvB,KAAK,CAAAA,YAAA,GAAgB2C,KAAA;IACrB,KAAK,CAAA1C,wBAAA,GAA4B0C,KAAA,CAAMc,KAAA;IAEvC,IAAI,KAAKjC,YAAA,CAAa,GAAG;MACvBW,SAAA,EAAWF,cAAA,CAAe,IAAI;MAC9BU,KAAA,CAAMzB,WAAA,CAAY,IAAI;IACxB;EACF;EAEA2I,cAAA,EAAsB;IACpB,KAAKxI,YAAA,CAAa;IAElB,IAAI,KAAKG,YAAA,CAAa,GAAG;MACvB,KAAK,CAAAF,YAAA,CAAc;IACrB;EACF;EAEA,CAAAmB,MAAAqH,CAAQC,aAAA,EAA6C;IACnDtL,aAAA,CAAcuL,KAAA,CAAM,MAAM;MAExB,IAAID,aAAA,CAAc/I,SAAA,EAAW;QAC3B,KAAKA,SAAA,CAAUiJ,OAAA,CAASC,QAAA,IAAa;UACnCA,QAAA,CAAS,KAAK,CAAAhK,aAAc;QAC9B,CAAC;MACH;MAGA,KAAK,CAAAX,MAAA,CAAQiD,aAAA,CAAc,EAAEC,MAAA,CAAO;QAClCE,KAAA,EAAO,KAAK,CAAA3C,YAAA;QACZ0C,IAAA,EAAM;MACR,CAAC;IACH,CAAC;EACH;AACF;AAEA,SAASyH,kBACPxH,KAAA,EACAnD,OAAA,EACS;EACT,OACEP,cAAA,CAAeO,OAAA,CAAQ6C,OAAA,EAASM,KAAK,MAAM,SAC3CA,KAAA,CAAMc,KAAA,CAAM+C,IAAA,KAAS,UACrB,EAAE7D,KAAA,CAAMc,KAAA,CAAMsD,MAAA,KAAW,WAAWvH,OAAA,CAAQ4K,YAAA,KAAiB;AAEjE;AAEA,SAASjJ,mBACPwB,KAAA,EACAnD,OAAA,EACS;EACT,OACE2K,iBAAA,CAAkBxH,KAAA,EAAOnD,OAAO,KAC/BmD,KAAA,CAAMc,KAAA,CAAM+C,IAAA,KAAS,UACpB7E,aAAA,CAAcgB,KAAA,EAAOnD,OAAA,EAASA,OAAA,CAAQ6K,cAAc;AAE1D;AAEA,SAAS1I,cACPgB,KAAA,EACAnD,OAAA,EACA8K,KAAA,EAGA;EACA,IACErL,cAAA,CAAeO,OAAA,CAAQ6C,OAAA,EAASM,KAAK,MAAM,SAC3CzD,gBAAA,CAAiBM,OAAA,CAAQuD,SAAA,EAAWJ,KAAK,MAAM,UAC/C;IACA,MAAMqG,KAAA,GAAQ,OAAOsB,KAAA,KAAU,aAAaA,KAAA,CAAM3H,KAAK,IAAI2H,KAAA;IAE3D,OAAOtB,KAAA,KAAU,YAAaA,KAAA,KAAU,SAAShE,OAAA,CAAQrC,KAAA,EAAOnD,OAAO;EACzE;EACA,OAAO;AACT;AAEA,SAASsD,sBACPH,KAAA,EACAR,SAAA,EACA3C,OAAA,EACA0C,WAAA,EACS;EACT,QACGS,KAAA,KAAUR,SAAA,IACTlD,cAAA,CAAeiD,WAAA,CAAYG,OAAA,EAASM,KAAK,MAAM,WAChD,CAACnD,OAAA,CAAQ+K,QAAA,IAAY5H,KAAA,CAAMc,KAAA,CAAMsD,MAAA,KAAW,YAC7C/B,OAAA,CAAQrC,KAAA,EAAOnD,OAAO;AAE1B;AAEA,SAASwF,QACPrC,KAAA,EACAnD,OAAA,EACS;EACT,OACEP,cAAA,CAAeO,OAAA,CAAQ6C,OAAA,EAASM,KAAK,MAAM,SAC3CA,KAAA,CAAM6H,aAAA,CAActL,gBAAA,CAAiBM,OAAA,CAAQuD,SAAA,EAAWJ,KAAK,CAAC;AAElE;AAIA,SAASa,sCAOPZ,QAAA,EACA6H,gBAAA,EACA;EAGA,IAAI,CAACtL,mBAAA,CAAoByD,QAAA,CAASc,gBAAA,CAAiB,GAAG+G,gBAAgB,GAAG;IACvE,OAAO;EACT;EAGA,OAAO;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
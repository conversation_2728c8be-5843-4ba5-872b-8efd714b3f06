{"ast": null, "code": "\"use client\";\n\n// src/useInfiniteQuery.ts\nimport { InfiniteQueryObserver } from \"@tanstack/query-core\";\nimport { useBaseQuery } from \"./useBaseQuery.js\";\nfunction useInfiniteQuery(options, queryClient) {\n  return useBaseQuery(options, InfiniteQueryObserver, queryClient);\n}\nexport { useInfiniteQuery };", "map": {"version": 3, "names": ["InfiniteQueryObserver", "useBaseQuery", "useInfiniteQuery", "options", "queryClient"], "sources": ["/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/node_modules/@tanstack/react-query/src/useInfiniteQuery.ts"], "sourcesContent": ["'use client'\nimport { InfiniteQueryObserver } from '@tanstack/query-core'\nimport { useBaseQuery } from './useBaseQuery'\nimport type {\n  DefaultError,\n  InfiniteData,\n  QueryClient,\n  QueryKey,\n  QueryObserver,\n} from '@tanstack/query-core'\nimport type {\n  DefinedUseInfiniteQueryResult,\n  UseInfiniteQueryOptions,\n  UseInfiniteQueryResult,\n} from './types'\nimport type {\n  DefinedInitialDataInfiniteOptions,\n  UndefinedInitialDataInfiniteOptions,\n} from './infiniteQueryOptions'\n\nexport function useInfiniteQuery<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n>(\n  options: DefinedInitialDataInfiniteOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryKey,\n    TPageParam\n  >,\n  queryClient?: QueryClient,\n): DefinedUseInfiniteQueryResult<TData, TError>\n\nexport function useInfiniteQuery<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n>(\n  options: UndefinedInitialDataInfiniteOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryKey,\n    TPageParam\n  >,\n  queryClient?: QueryClient,\n): UseInfiniteQueryResult<TData, TError>\n\nexport function useInfiniteQuery<\n  TQueryFnData,\n  TError = DefaultError,\n  TData = InfiniteData<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n>(\n  options: UseInfiniteQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryKey,\n    TPageParam\n  >,\n  queryClient?: QueryClient,\n): UseInfiniteQueryResult<TData, TError>\n\nexport function useInfiniteQuery(\n  options: UseInfiniteQueryOptions,\n  queryClient?: QueryClient,\n) {\n  return useBaseQuery(\n    options,\n    InfiniteQueryObserver as typeof QueryObserver,\n    queryClient,\n  )\n}\n"], "mappings": ";;;AACA,SAASA,qBAAA,QAA6B;AACtC,SAASC,YAAA,QAAoB;AAqEtB,SAASC,iBACdC,OAAA,EACAC,WAAA,EACA;EACA,OAAOH,YAAA,CACLE,OAAA,EACAH,qBAAA,EACAI,WACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
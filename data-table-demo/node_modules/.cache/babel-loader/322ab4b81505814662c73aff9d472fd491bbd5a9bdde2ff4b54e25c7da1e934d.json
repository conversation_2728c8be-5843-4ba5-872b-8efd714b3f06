{"ast": null, "code": "// ../../node_modules/.pnpm/solid-js@1.9.5/node_modules/solid-js/dist/solid.js\nvar sharedConfig = {\n  context: void 0,\n  registry: void 0,\n  effects: void 0,\n  done: false,\n  getContextId() {\n    return getContextId(this.context.count);\n  },\n  getNextContextId() {\n    return getContextId(this.context.count++);\n  }\n};\nfunction getContextId(count) {\n  const num = String(count),\n    len = num.length - 1;\n  return sharedConfig.context.id + (len ? String.fromCharCode(96 + len) : \"\") + num;\n}\nfunction setHydrateContext(context) {\n  sharedConfig.context = context;\n}\nfunction nextHydrateContext() {\n  return {\n    ...sharedConfig.context,\n    id: sharedConfig.getNextContextId(),\n    count: 0\n  };\n}\nvar IS_DEV = false;\nvar equalFn = (a, b) => a === b;\nvar $PROXY = Symbol(\"solid-proxy\");\nvar SUPPORTS_PROXY = typeof Proxy === \"function\";\nvar $TRACK = Symbol(\"solid-track\");\nvar signalOptions = {\n  equals: equalFn\n};\nvar ERROR = null;\nvar runEffects = runQueue;\nvar STALE = 1;\nvar PENDING = 2;\nvar UNOWNED = {\n  owned: null,\n  cleanups: null,\n  context: null,\n  owner: null\n};\nvar NO_INIT = {};\nvar Owner = null;\nvar Transition = null;\nvar Scheduler = null;\nvar ExternalSourceConfig = null;\nvar Listener = null;\nvar Updates = null;\nvar Effects = null;\nvar ExecCount = 0;\nfunction createRoot(fn, detachedOwner) {\n  const listener = Listener,\n    owner = Owner,\n    unowned = fn.length === 0,\n    current = detachedOwner === void 0 ? owner : detachedOwner,\n    root = unowned ? UNOWNED : {\n      owned: null,\n      cleanups: null,\n      context: current ? current.context : null,\n      owner: current\n    },\n    updateFn = unowned ? fn : () => fn(() => untrack(() => cleanNode(root)));\n  Owner = root;\n  Listener = null;\n  try {\n    return runUpdates(updateFn, true);\n  } finally {\n    Listener = listener;\n    Owner = owner;\n  }\n}\nfunction createSignal(value, options) {\n  options = options ? Object.assign({}, signalOptions, options) : signalOptions;\n  const s = {\n    value,\n    observers: null,\n    observerSlots: null,\n    comparator: options.equals || void 0\n  };\n  const setter = value2 => {\n    if (typeof value2 === \"function\") {\n      if (Transition && Transition.running && Transition.sources.has(s)) value2 = value2(s.tValue);else value2 = value2(s.value);\n    }\n    return writeSignal(s, value2);\n  };\n  return [readSignal.bind(s), setter];\n}\nfunction createComputed(fn, value, options) {\n  const c = createComputation(fn, value, true, STALE);\n  if (Scheduler && Transition && Transition.running) Updates.push(c);else updateComputation(c);\n}\nfunction createRenderEffect(fn, value, options) {\n  const c = createComputation(fn, value, false, STALE);\n  if (Scheduler && Transition && Transition.running) Updates.push(c);else updateComputation(c);\n}\nfunction createEffect(fn, value, options) {\n  runEffects = runUserEffects;\n  const c = createComputation(fn, value, false, STALE),\n    s = SuspenseContext && useContext(SuspenseContext);\n  if (s) c.suspense = s;\n  if (!options || !options.render) c.user = true;\n  Effects ? Effects.push(c) : updateComputation(c);\n}\nfunction createMemo(fn, value, options) {\n  options = options ? Object.assign({}, signalOptions, options) : signalOptions;\n  const c = createComputation(fn, value, true, 0);\n  c.observers = null;\n  c.observerSlots = null;\n  c.comparator = options.equals || void 0;\n  if (Scheduler && Transition && Transition.running) {\n    c.tState = STALE;\n    Updates.push(c);\n  } else updateComputation(c);\n  return readSignal.bind(c);\n}\nfunction isPromise(v) {\n  return v && typeof v === \"object\" && \"then\" in v;\n}\nfunction createResource(pSource, pFetcher, pOptions) {\n  let source;\n  let fetcher;\n  let options;\n  {\n    source = true;\n    fetcher = pSource;\n    options = {};\n  }\n  let pr = null,\n    initP = NO_INIT,\n    id = null,\n    loadedUnderTransition = false,\n    scheduled = false,\n    resolved = \"initialValue\" in options,\n    dynamic = typeof source === \"function\" && createMemo(source);\n  const contexts = /* @__PURE__ */new Set(),\n    [value, setValue] = (options.storage || createSignal)(options.initialValue),\n    [error, setError] = createSignal(void 0),\n    [track, trigger] = createSignal(void 0, {\n      equals: false\n    }),\n    [state, setState] = createSignal(resolved ? \"ready\" : \"unresolved\");\n  if (sharedConfig.context) {\n    id = sharedConfig.getNextContextId();\n    if (options.ssrLoadFrom === \"initial\") initP = options.initialValue;else if (sharedConfig.load && sharedConfig.has(id)) initP = sharedConfig.load(id);\n  }\n  function loadEnd(p, v, error2, key) {\n    if (pr === p) {\n      pr = null;\n      key !== void 0 && (resolved = true);\n      if ((p === initP || v === initP) && options.onHydrated) queueMicrotask(() => options.onHydrated(key, {\n        value: v\n      }));\n      initP = NO_INIT;\n      if (Transition && p && loadedUnderTransition) {\n        Transition.promises.delete(p);\n        loadedUnderTransition = false;\n        runUpdates(() => {\n          Transition.running = true;\n          completeLoad(v, error2);\n        }, false);\n      } else completeLoad(v, error2);\n    }\n    return v;\n  }\n  function completeLoad(v, err) {\n    runUpdates(() => {\n      if (err === void 0) setValue(() => v);\n      setState(err !== void 0 ? \"errored\" : resolved ? \"ready\" : \"unresolved\");\n      setError(err);\n      for (const c of contexts.keys()) c.decrement();\n      contexts.clear();\n    }, false);\n  }\n  function read() {\n    const c = SuspenseContext && useContext(SuspenseContext),\n      v = value(),\n      err = error();\n    if (err !== void 0 && !pr) throw err;\n    if (Listener && !Listener.user && c) {\n      createComputed(() => {\n        track();\n        if (pr) {\n          if (c.resolved && Transition && loadedUnderTransition) Transition.promises.add(pr);else if (!contexts.has(c)) {\n            c.increment();\n            contexts.add(c);\n          }\n        }\n      });\n    }\n    return v;\n  }\n  function load(refetching = true) {\n    if (refetching !== false && scheduled) return;\n    scheduled = false;\n    const lookup = dynamic ? dynamic() : source;\n    loadedUnderTransition = Transition && Transition.running;\n    if (lookup == null || lookup === false) {\n      loadEnd(pr, untrack(value));\n      return;\n    }\n    if (Transition && pr) Transition.promises.delete(pr);\n    const p = initP !== NO_INIT ? initP : untrack(() => fetcher(lookup, {\n      value: value(),\n      refetching\n    }));\n    if (!isPromise(p)) {\n      loadEnd(pr, p, void 0, lookup);\n      return p;\n    }\n    pr = p;\n    if (\"value\" in p) {\n      if (p.status === \"success\") loadEnd(pr, p.value, void 0, lookup);else loadEnd(pr, void 0, castError(p.value), lookup);\n      return p;\n    }\n    scheduled = true;\n    queueMicrotask(() => scheduled = false);\n    runUpdates(() => {\n      setState(resolved ? \"refreshing\" : \"pending\");\n      trigger();\n    }, false);\n    return p.then(v => loadEnd(p, v, void 0, lookup), e => loadEnd(p, void 0, castError(e), lookup));\n  }\n  Object.defineProperties(read, {\n    state: {\n      get: () => state()\n    },\n    error: {\n      get: () => error()\n    },\n    loading: {\n      get() {\n        const s = state();\n        return s === \"pending\" || s === \"refreshing\";\n      }\n    },\n    latest: {\n      get() {\n        if (!resolved) return read();\n        const err = error();\n        if (err && !pr) throw err;\n        return value();\n      }\n    }\n  });\n  if (dynamic) createComputed(() => load(false));else load(false);\n  return [read, {\n    refetch: load,\n    mutate: setValue\n  }];\n}\nfunction batch(fn) {\n  return runUpdates(fn, false);\n}\nfunction untrack(fn) {\n  if (!ExternalSourceConfig && Listener === null) return fn();\n  const listener = Listener;\n  Listener = null;\n  try {\n    if (ExternalSourceConfig) return ExternalSourceConfig.untrack(fn);\n    return fn();\n  } finally {\n    Listener = listener;\n  }\n}\nfunction on(deps, fn, options) {\n  const isArray3 = Array.isArray(deps);\n  let prevInput;\n  let defer = options && options.defer;\n  return prevValue => {\n    let input;\n    if (isArray3) {\n      input = Array(deps.length);\n      for (let i = 0; i < deps.length; i++) input[i] = deps[i]();\n    } else input = deps();\n    if (defer) {\n      defer = false;\n      return prevValue;\n    }\n    const result = untrack(() => fn(input, prevInput, prevValue));\n    prevInput = input;\n    return result;\n  };\n}\nfunction onMount(fn) {\n  createEffect(() => untrack(fn));\n}\nfunction onCleanup(fn) {\n  if (Owner === null) ;else if (Owner.cleanups === null) Owner.cleanups = [fn];else Owner.cleanups.push(fn);\n  return fn;\n}\nfunction getOwner() {\n  return Owner;\n}\nfunction runWithOwner(o, fn) {\n  const prev = Owner;\n  const prevListener = Listener;\n  Owner = o;\n  Listener = null;\n  try {\n    return runUpdates(fn, true);\n  } catch (err) {\n    handleError(err);\n  } finally {\n    Owner = prev;\n    Listener = prevListener;\n  }\n}\nfunction startTransition(fn) {\n  if (Transition && Transition.running) {\n    fn();\n    return Transition.done;\n  }\n  const l = Listener;\n  const o = Owner;\n  return Promise.resolve().then(() => {\n    Listener = l;\n    Owner = o;\n    let t;\n    if (Scheduler || SuspenseContext) {\n      t = Transition || (Transition = {\n        sources: /* @__PURE__ */new Set(),\n        effects: [],\n        promises: /* @__PURE__ */new Set(),\n        disposed: /* @__PURE__ */new Set(),\n        queue: /* @__PURE__ */new Set(),\n        running: true\n      });\n      t.done || (t.done = new Promise(res => t.resolve = res));\n      t.running = true;\n    }\n    runUpdates(fn, false);\n    Listener = Owner = null;\n    return t ? t.done : void 0;\n  });\n}\nvar [transPending, setTransPending] = /* @__PURE__ */createSignal(false);\nfunction useTransition() {\n  return [transPending, startTransition];\n}\nfunction createContext(defaultValue, options) {\n  const id = Symbol(\"context\");\n  return {\n    id,\n    Provider: createProvider(id),\n    defaultValue\n  };\n}\nfunction useContext(context) {\n  let value;\n  return Owner && Owner.context && (value = Owner.context[context.id]) !== void 0 ? value : context.defaultValue;\n}\nfunction children(fn) {\n  const children2 = createMemo(fn);\n  const memo = createMemo(() => resolveChildren(children2()));\n  memo.toArray = () => {\n    const c = memo();\n    return Array.isArray(c) ? c : c != null ? [c] : [];\n  };\n  return memo;\n}\nvar SuspenseContext;\nfunction readSignal() {\n  const runningTransition = Transition && Transition.running;\n  if (this.sources && (runningTransition ? this.tState : this.state)) {\n    if ((runningTransition ? this.tState : this.state) === STALE) updateComputation(this);else {\n      const updates = Updates;\n      Updates = null;\n      runUpdates(() => lookUpstream(this), false);\n      Updates = updates;\n    }\n  }\n  if (Listener) {\n    const sSlot = this.observers ? this.observers.length : 0;\n    if (!Listener.sources) {\n      Listener.sources = [this];\n      Listener.sourceSlots = [sSlot];\n    } else {\n      Listener.sources.push(this);\n      Listener.sourceSlots.push(sSlot);\n    }\n    if (!this.observers) {\n      this.observers = [Listener];\n      this.observerSlots = [Listener.sources.length - 1];\n    } else {\n      this.observers.push(Listener);\n      this.observerSlots.push(Listener.sources.length - 1);\n    }\n  }\n  if (runningTransition && Transition.sources.has(this)) return this.tValue;\n  return this.value;\n}\nfunction writeSignal(node, value, isComp) {\n  let current = Transition && Transition.running && Transition.sources.has(node) ? node.tValue : node.value;\n  if (!node.comparator || !node.comparator(current, value)) {\n    if (Transition) {\n      const TransitionRunning = Transition.running;\n      if (TransitionRunning || !isComp && Transition.sources.has(node)) {\n        Transition.sources.add(node);\n        node.tValue = value;\n      }\n      if (!TransitionRunning) node.value = value;\n    } else node.value = value;\n    if (node.observers && node.observers.length) {\n      runUpdates(() => {\n        for (let i = 0; i < node.observers.length; i += 1) {\n          const o = node.observers[i];\n          const TransitionRunning = Transition && Transition.running;\n          if (TransitionRunning && Transition.disposed.has(o)) continue;\n          if (TransitionRunning ? !o.tState : !o.state) {\n            if (o.pure) Updates.push(o);else Effects.push(o);\n            if (o.observers) markDownstream(o);\n          }\n          if (!TransitionRunning) o.state = STALE;else o.tState = STALE;\n        }\n        if (Updates.length > 1e6) {\n          Updates = [];\n          if (IS_DEV) ;\n          throw new Error();\n        }\n      }, false);\n    }\n  }\n  return value;\n}\nfunction updateComputation(node) {\n  if (!node.fn) return;\n  cleanNode(node);\n  const time = ExecCount;\n  runComputation(node, Transition && Transition.running && Transition.sources.has(node) ? node.tValue : node.value, time);\n  if (Transition && !Transition.running && Transition.sources.has(node)) {\n    queueMicrotask(() => {\n      runUpdates(() => {\n        Transition && (Transition.running = true);\n        Listener = Owner = node;\n        runComputation(node, node.tValue, time);\n        Listener = Owner = null;\n      }, false);\n    });\n  }\n}\nfunction runComputation(node, value, time) {\n  let nextValue;\n  const owner = Owner,\n    listener = Listener;\n  Listener = Owner = node;\n  try {\n    nextValue = node.fn(value);\n  } catch (err) {\n    if (node.pure) {\n      if (Transition && Transition.running) {\n        node.tState = STALE;\n        node.tOwned && node.tOwned.forEach(cleanNode);\n        node.tOwned = void 0;\n      } else {\n        node.state = STALE;\n        node.owned && node.owned.forEach(cleanNode);\n        node.owned = null;\n      }\n    }\n    node.updatedAt = time + 1;\n    return handleError(err);\n  } finally {\n    Listener = listener;\n    Owner = owner;\n  }\n  if (!node.updatedAt || node.updatedAt <= time) {\n    if (node.updatedAt != null && \"observers\" in node) {\n      writeSignal(node, nextValue, true);\n    } else if (Transition && Transition.running && node.pure) {\n      Transition.sources.add(node);\n      node.tValue = nextValue;\n    } else node.value = nextValue;\n    node.updatedAt = time;\n  }\n}\nfunction createComputation(fn, init, pure, state = STALE, options) {\n  const c = {\n    fn,\n    state,\n    updatedAt: null,\n    owned: null,\n    sources: null,\n    sourceSlots: null,\n    cleanups: null,\n    value: init,\n    owner: Owner,\n    context: Owner ? Owner.context : null,\n    pure\n  };\n  if (Transition && Transition.running) {\n    c.state = 0;\n    c.tState = state;\n  }\n  if (Owner === null) ;else if (Owner !== UNOWNED) {\n    if (Transition && Transition.running && Owner.pure) {\n      if (!Owner.tOwned) Owner.tOwned = [c];else Owner.tOwned.push(c);\n    } else {\n      if (!Owner.owned) Owner.owned = [c];else Owner.owned.push(c);\n    }\n  }\n  if (ExternalSourceConfig && c.fn) {\n    const [track, trigger] = createSignal(void 0, {\n      equals: false\n    });\n    const ordinary = ExternalSourceConfig.factory(c.fn, trigger);\n    onCleanup(() => ordinary.dispose());\n    const triggerInTransition = () => startTransition(trigger).then(() => inTransition.dispose());\n    const inTransition = ExternalSourceConfig.factory(c.fn, triggerInTransition);\n    c.fn = x => {\n      track();\n      return Transition && Transition.running ? inTransition.track(x) : ordinary.track(x);\n    };\n  }\n  return c;\n}\nfunction runTop(node) {\n  const runningTransition = Transition && Transition.running;\n  if ((runningTransition ? node.tState : node.state) === 0) return;\n  if ((runningTransition ? node.tState : node.state) === PENDING) return lookUpstream(node);\n  if (node.suspense && untrack(node.suspense.inFallback)) return node.suspense.effects.push(node);\n  const ancestors = [node];\n  while ((node = node.owner) && (!node.updatedAt || node.updatedAt < ExecCount)) {\n    if (runningTransition && Transition.disposed.has(node)) return;\n    if (runningTransition ? node.tState : node.state) ancestors.push(node);\n  }\n  for (let i = ancestors.length - 1; i >= 0; i--) {\n    node = ancestors[i];\n    if (runningTransition) {\n      let top = node,\n        prev = ancestors[i + 1];\n      while ((top = top.owner) && top !== prev) {\n        if (Transition.disposed.has(top)) return;\n      }\n    }\n    if ((runningTransition ? node.tState : node.state) === STALE) {\n      updateComputation(node);\n    } else if ((runningTransition ? node.tState : node.state) === PENDING) {\n      const updates = Updates;\n      Updates = null;\n      runUpdates(() => lookUpstream(node, ancestors[0]), false);\n      Updates = updates;\n    }\n  }\n}\nfunction runUpdates(fn, init) {\n  if (Updates) return fn();\n  let wait = false;\n  if (!init) Updates = [];\n  if (Effects) wait = true;else Effects = [];\n  ExecCount++;\n  try {\n    const res = fn();\n    completeUpdates(wait);\n    return res;\n  } catch (err) {\n    if (!wait) Effects = null;\n    Updates = null;\n    handleError(err);\n  }\n}\nfunction completeUpdates(wait) {\n  if (Updates) {\n    if (Scheduler && Transition && Transition.running) scheduleQueue(Updates);else runQueue(Updates);\n    Updates = null;\n  }\n  if (wait) return;\n  let res;\n  if (Transition) {\n    if (!Transition.promises.size && !Transition.queue.size) {\n      const sources = Transition.sources;\n      const disposed = Transition.disposed;\n      Effects.push.apply(Effects, Transition.effects);\n      res = Transition.resolve;\n      for (const e2 of Effects) {\n        \"tState\" in e2 && (e2.state = e2.tState);\n        delete e2.tState;\n      }\n      Transition = null;\n      runUpdates(() => {\n        for (const d of disposed) cleanNode(d);\n        for (const v of sources) {\n          v.value = v.tValue;\n          if (v.owned) {\n            for (let i = 0, len = v.owned.length; i < len; i++) cleanNode(v.owned[i]);\n          }\n          if (v.tOwned) v.owned = v.tOwned;\n          delete v.tValue;\n          delete v.tOwned;\n          v.tState = 0;\n        }\n        setTransPending(false);\n      }, false);\n    } else if (Transition.running) {\n      Transition.running = false;\n      Transition.effects.push.apply(Transition.effects, Effects);\n      Effects = null;\n      setTransPending(true);\n      return;\n    }\n  }\n  const e = Effects;\n  Effects = null;\n  if (e.length) runUpdates(() => runEffects(e), false);\n  if (res) res();\n}\nfunction runQueue(queue) {\n  for (let i = 0; i < queue.length; i++) runTop(queue[i]);\n}\nfunction scheduleQueue(queue) {\n  for (let i = 0; i < queue.length; i++) {\n    const item = queue[i];\n    const tasks = Transition.queue;\n    if (!tasks.has(item)) {\n      tasks.add(item);\n      Scheduler(() => {\n        tasks.delete(item);\n        runUpdates(() => {\n          Transition.running = true;\n          runTop(item);\n        }, false);\n        Transition && (Transition.running = false);\n      });\n    }\n  }\n}\nfunction runUserEffects(queue) {\n  let i,\n    userLength = 0;\n  for (i = 0; i < queue.length; i++) {\n    const e = queue[i];\n    if (!e.user) runTop(e);else queue[userLength++] = e;\n  }\n  if (sharedConfig.context) {\n    if (sharedConfig.count) {\n      sharedConfig.effects || (sharedConfig.effects = []);\n      sharedConfig.effects.push(...queue.slice(0, userLength));\n      return;\n    }\n    setHydrateContext();\n  }\n  if (sharedConfig.effects && (sharedConfig.done || !sharedConfig.count)) {\n    queue = [...sharedConfig.effects, ...queue];\n    userLength += sharedConfig.effects.length;\n    delete sharedConfig.effects;\n  }\n  for (i = 0; i < userLength; i++) runTop(queue[i]);\n}\nfunction lookUpstream(node, ignore) {\n  const runningTransition = Transition && Transition.running;\n  if (runningTransition) node.tState = 0;else node.state = 0;\n  for (let i = 0; i < node.sources.length; i += 1) {\n    const source = node.sources[i];\n    if (source.sources) {\n      const state = runningTransition ? source.tState : source.state;\n      if (state === STALE) {\n        if (source !== ignore && (!source.updatedAt || source.updatedAt < ExecCount)) runTop(source);\n      } else if (state === PENDING) lookUpstream(source, ignore);\n    }\n  }\n}\nfunction markDownstream(node) {\n  const runningTransition = Transition && Transition.running;\n  for (let i = 0; i < node.observers.length; i += 1) {\n    const o = node.observers[i];\n    if (runningTransition ? !o.tState : !o.state) {\n      if (runningTransition) o.tState = PENDING;else o.state = PENDING;\n      if (o.pure) Updates.push(o);else Effects.push(o);\n      o.observers && markDownstream(o);\n    }\n  }\n}\nfunction cleanNode(node) {\n  let i;\n  if (node.sources) {\n    while (node.sources.length) {\n      const source = node.sources.pop(),\n        index = node.sourceSlots.pop(),\n        obs = source.observers;\n      if (obs && obs.length) {\n        const n = obs.pop(),\n          s = source.observerSlots.pop();\n        if (index < obs.length) {\n          n.sourceSlots[s] = index;\n          obs[index] = n;\n          source.observerSlots[index] = s;\n        }\n      }\n    }\n  }\n  if (node.tOwned) {\n    for (i = node.tOwned.length - 1; i >= 0; i--) cleanNode(node.tOwned[i]);\n    delete node.tOwned;\n  }\n  if (Transition && Transition.running && node.pure) {\n    reset(node, true);\n  } else if (node.owned) {\n    for (i = node.owned.length - 1; i >= 0; i--) cleanNode(node.owned[i]);\n    node.owned = null;\n  }\n  if (node.cleanups) {\n    for (i = node.cleanups.length - 1; i >= 0; i--) node.cleanups[i]();\n    node.cleanups = null;\n  }\n  if (Transition && Transition.running) node.tState = 0;else node.state = 0;\n}\nfunction reset(node, top) {\n  if (!top) {\n    node.tState = 0;\n    Transition.disposed.add(node);\n  }\n  if (node.owned) {\n    for (let i = 0; i < node.owned.length; i++) reset(node.owned[i]);\n  }\n}\nfunction castError(err) {\n  if (err instanceof Error) return err;\n  return new Error(typeof err === \"string\" ? err : \"Unknown error\", {\n    cause: err\n  });\n}\nfunction runErrors(err, fns, owner) {\n  try {\n    for (const f of fns) f(err);\n  } catch (e) {\n    handleError(e, owner && owner.owner || null);\n  }\n}\nfunction handleError(err, owner = Owner) {\n  const fns = ERROR && owner && owner.context && owner.context[ERROR];\n  const error = castError(err);\n  if (!fns) throw error;\n  if (Effects) Effects.push({\n    fn() {\n      runErrors(error, fns, owner);\n    },\n    state: STALE\n  });else runErrors(error, fns, owner);\n}\nfunction resolveChildren(children2) {\n  if (typeof children2 === \"function\" && !children2.length) return resolveChildren(children2());\n  if (Array.isArray(children2)) {\n    const results = [];\n    for (let i = 0; i < children2.length; i++) {\n      const result = resolveChildren(children2[i]);\n      Array.isArray(result) ? results.push.apply(results, result) : results.push(result);\n    }\n    return results;\n  }\n  return children2;\n}\nfunction createProvider(id, options) {\n  return function provider(props) {\n    let res;\n    createRenderEffect(() => res = untrack(() => {\n      Owner.context = {\n        ...Owner.context,\n        [id]: props.value\n      };\n      return children(() => props.children);\n    }), void 0);\n    return res;\n  };\n}\nvar FALLBACK = Symbol(\"fallback\");\nfunction dispose(d) {\n  for (let i = 0; i < d.length; i++) d[i]();\n}\nfunction mapArray(list, mapFn, options = {}) {\n  let items = [],\n    mapped = [],\n    disposers = [],\n    len = 0,\n    indexes = mapFn.length > 1 ? [] : null;\n  onCleanup(() => dispose(disposers));\n  return () => {\n    let newItems = list() || [],\n      newLen = newItems.length,\n      i,\n      j;\n    newItems[$TRACK];\n    return untrack(() => {\n      let newIndices, newIndicesNext, temp, tempdisposers, tempIndexes, start, end, newEnd, item;\n      if (newLen === 0) {\n        if (len !== 0) {\n          dispose(disposers);\n          disposers = [];\n          items = [];\n          mapped = [];\n          len = 0;\n          indexes && (indexes = []);\n        }\n        if (options.fallback) {\n          items = [FALLBACK];\n          mapped[0] = createRoot(disposer => {\n            disposers[0] = disposer;\n            return options.fallback();\n          });\n          len = 1;\n        }\n      } else if (len === 0) {\n        mapped = new Array(newLen);\n        for (j = 0; j < newLen; j++) {\n          items[j] = newItems[j];\n          mapped[j] = createRoot(mapper);\n        }\n        len = newLen;\n      } else {\n        temp = new Array(newLen);\n        tempdisposers = new Array(newLen);\n        indexes && (tempIndexes = new Array(newLen));\n        for (start = 0, end = Math.min(len, newLen); start < end && items[start] === newItems[start]; start++);\n        for (end = len - 1, newEnd = newLen - 1; end >= start && newEnd >= start && items[end] === newItems[newEnd]; end--, newEnd--) {\n          temp[newEnd] = mapped[end];\n          tempdisposers[newEnd] = disposers[end];\n          indexes && (tempIndexes[newEnd] = indexes[end]);\n        }\n        newIndices = /* @__PURE__ */new Map();\n        newIndicesNext = new Array(newEnd + 1);\n        for (j = newEnd; j >= start; j--) {\n          item = newItems[j];\n          i = newIndices.get(item);\n          newIndicesNext[j] = i === void 0 ? -1 : i;\n          newIndices.set(item, j);\n        }\n        for (i = start; i <= end; i++) {\n          item = items[i];\n          j = newIndices.get(item);\n          if (j !== void 0 && j !== -1) {\n            temp[j] = mapped[i];\n            tempdisposers[j] = disposers[i];\n            indexes && (tempIndexes[j] = indexes[i]);\n            j = newIndicesNext[j];\n            newIndices.set(item, j);\n          } else disposers[i]();\n        }\n        for (j = start; j < newLen; j++) {\n          if (j in temp) {\n            mapped[j] = temp[j];\n            disposers[j] = tempdisposers[j];\n            if (indexes) {\n              indexes[j] = tempIndexes[j];\n              indexes[j](j);\n            }\n          } else mapped[j] = createRoot(mapper);\n        }\n        mapped = mapped.slice(0, len = newLen);\n        items = newItems.slice(0);\n      }\n      return mapped;\n    });\n    function mapper(disposer) {\n      disposers[j] = disposer;\n      if (indexes) {\n        const [s, set] = createSignal(j);\n        indexes[j] = set;\n        return mapFn(newItems[j], s);\n      }\n      return mapFn(newItems[j]);\n    }\n  };\n}\nfunction indexArray(list, mapFn, options = {}) {\n  let items = [],\n    mapped = [],\n    disposers = [],\n    signals = [],\n    len = 0,\n    i;\n  onCleanup(() => dispose(disposers));\n  return () => {\n    const newItems = list() || [],\n      newLen = newItems.length;\n    newItems[$TRACK];\n    return untrack(() => {\n      if (newLen === 0) {\n        if (len !== 0) {\n          dispose(disposers);\n          disposers = [];\n          items = [];\n          mapped = [];\n          len = 0;\n          signals = [];\n        }\n        if (options.fallback) {\n          items = [FALLBACK];\n          mapped[0] = createRoot(disposer => {\n            disposers[0] = disposer;\n            return options.fallback();\n          });\n          len = 1;\n        }\n        return mapped;\n      }\n      if (items[0] === FALLBACK) {\n        disposers[0]();\n        disposers = [];\n        items = [];\n        mapped = [];\n        len = 0;\n      }\n      for (i = 0; i < newLen; i++) {\n        if (i < items.length && items[i] !== newItems[i]) {\n          signals[i](() => newItems[i]);\n        } else if (i >= items.length) {\n          mapped[i] = createRoot(mapper);\n        }\n      }\n      for (; i < items.length; i++) {\n        disposers[i]();\n      }\n      len = signals.length = disposers.length = newLen;\n      items = newItems.slice(0);\n      return mapped = mapped.slice(0, len);\n    });\n    function mapper(disposer) {\n      disposers[i] = disposer;\n      const [s, set] = createSignal(newItems[i]);\n      signals[i] = set;\n      return mapFn(s, i);\n    }\n  };\n}\nvar hydrationEnabled = false;\nfunction createComponent(Comp, props) {\n  if (hydrationEnabled) {\n    if (sharedConfig.context) {\n      const c = sharedConfig.context;\n      setHydrateContext(nextHydrateContext());\n      const r = untrack(() => Comp(props || {}));\n      setHydrateContext(c);\n      return r;\n    }\n  }\n  return untrack(() => Comp(props || {}));\n}\nfunction trueFn() {\n  return true;\n}\nvar propTraps = {\n  get(_, property, receiver) {\n    if (property === $PROXY) return receiver;\n    return _.get(property);\n  },\n  has(_, property) {\n    if (property === $PROXY) return true;\n    return _.has(property);\n  },\n  set: trueFn,\n  deleteProperty: trueFn,\n  getOwnPropertyDescriptor(_, property) {\n    return {\n      configurable: true,\n      enumerable: true,\n      get() {\n        return _.get(property);\n      },\n      set: trueFn,\n      deleteProperty: trueFn\n    };\n  },\n  ownKeys(_) {\n    return _.keys();\n  }\n};\nfunction resolveSource(s) {\n  return !(s = typeof s === \"function\" ? s() : s) ? {} : s;\n}\nfunction resolveSources() {\n  for (let i = 0, length = this.length; i < length; ++i) {\n    const v = this[i]();\n    if (v !== void 0) return v;\n  }\n}\nfunction mergeProps(...sources) {\n  let proxy = false;\n  for (let i = 0; i < sources.length; i++) {\n    const s = sources[i];\n    proxy = proxy || !!s && $PROXY in s;\n    sources[i] = typeof s === \"function\" ? (proxy = true, createMemo(s)) : s;\n  }\n  if (SUPPORTS_PROXY && proxy) {\n    return new Proxy({\n      get(property) {\n        for (let i = sources.length - 1; i >= 0; i--) {\n          const v = resolveSource(sources[i])[property];\n          if (v !== void 0) return v;\n        }\n      },\n      has(property) {\n        for (let i = sources.length - 1; i >= 0; i--) {\n          if (property in resolveSource(sources[i])) return true;\n        }\n        return false;\n      },\n      keys() {\n        const keys = [];\n        for (let i = 0; i < sources.length; i++) keys.push(...Object.keys(resolveSource(sources[i])));\n        return [...new Set(keys)];\n      }\n    }, propTraps);\n  }\n  const sourcesMap = {};\n  const defined = /* @__PURE__ */Object.create(null);\n  for (let i = sources.length - 1; i >= 0; i--) {\n    const source = sources[i];\n    if (!source) continue;\n    const sourceKeys = Object.getOwnPropertyNames(source);\n    for (let i2 = sourceKeys.length - 1; i2 >= 0; i2--) {\n      const key = sourceKeys[i2];\n      if (key === \"__proto__\" || key === \"constructor\") continue;\n      const desc = Object.getOwnPropertyDescriptor(source, key);\n      if (!defined[key]) {\n        defined[key] = desc.get ? {\n          enumerable: true,\n          configurable: true,\n          get: resolveSources.bind(sourcesMap[key] = [desc.get.bind(source)])\n        } : desc.value !== void 0 ? desc : void 0;\n      } else {\n        const sources2 = sourcesMap[key];\n        if (sources2) {\n          if (desc.get) sources2.push(desc.get.bind(source));else if (desc.value !== void 0) sources2.push(() => desc.value);\n        }\n      }\n    }\n  }\n  const target = {};\n  const definedKeys = Object.keys(defined);\n  for (let i = definedKeys.length - 1; i >= 0; i--) {\n    const key = definedKeys[i],\n      desc = defined[key];\n    if (desc && desc.get) Object.defineProperty(target, key, desc);else target[key] = desc ? desc.value : void 0;\n  }\n  return target;\n}\nfunction splitProps(props, ...keys) {\n  if (SUPPORTS_PROXY && $PROXY in props) {\n    const blocked = new Set(keys.length > 1 ? keys.flat() : keys[0]);\n    const res = keys.map(k => {\n      return new Proxy({\n        get(property) {\n          return k.includes(property) ? props[property] : void 0;\n        },\n        has(property) {\n          return k.includes(property) && property in props;\n        },\n        keys() {\n          return k.filter(property => property in props);\n        }\n      }, propTraps);\n    });\n    res.push(new Proxy({\n      get(property) {\n        return blocked.has(property) ? void 0 : props[property];\n      },\n      has(property) {\n        return blocked.has(property) ? false : property in props;\n      },\n      keys() {\n        return Object.keys(props).filter(k => !blocked.has(k));\n      }\n    }, propTraps));\n    return res;\n  }\n  const otherObject = {};\n  const objects = keys.map(() => ({}));\n  for (const propName of Object.getOwnPropertyNames(props)) {\n    const desc = Object.getOwnPropertyDescriptor(props, propName);\n    const isDefaultDesc = !desc.get && !desc.set && desc.enumerable && desc.writable && desc.configurable;\n    let blocked = false;\n    let objectIndex = 0;\n    for (const k of keys) {\n      if (k.includes(propName)) {\n        blocked = true;\n        isDefaultDesc ? objects[objectIndex][propName] = desc.value : Object.defineProperty(objects[objectIndex], propName, desc);\n      }\n      ++objectIndex;\n    }\n    if (!blocked) {\n      isDefaultDesc ? otherObject[propName] = desc.value : Object.defineProperty(otherObject, propName, desc);\n    }\n  }\n  return [...objects, otherObject];\n}\nfunction lazy(fn) {\n  let comp;\n  let p;\n  const wrap = props => {\n    const ctx = sharedConfig.context;\n    if (ctx) {\n      const [s, set] = createSignal();\n      sharedConfig.count || (sharedConfig.count = 0);\n      sharedConfig.count++;\n      (p || (p = fn())).then(mod => {\n        !sharedConfig.done && setHydrateContext(ctx);\n        sharedConfig.count--;\n        set(() => mod.default);\n        setHydrateContext();\n      });\n      comp = s;\n    } else if (!comp) {\n      const [s] = createResource(() => (p || (p = fn())).then(mod => mod.default));\n      comp = s;\n    }\n    let Comp;\n    return createMemo(() => (Comp = comp()) ? untrack(() => {\n      if (IS_DEV) ;\n      if (!ctx || sharedConfig.done) return Comp(props);\n      const c = sharedConfig.context;\n      setHydrateContext(ctx);\n      const r = Comp(props);\n      setHydrateContext(c);\n      return r;\n    }) : \"\");\n  };\n  wrap.preload = () => p || ((p = fn()).then(mod => comp = () => mod.default), p);\n  return wrap;\n}\nvar counter = 0;\nfunction createUniqueId() {\n  const ctx = sharedConfig.context;\n  return ctx ? sharedConfig.getNextContextId() : `cl-${counter++}`;\n}\nvar narrowedError = name => `Stale read from <${name}>.`;\nfunction For(props) {\n  const fallback = \"fallback\" in props && {\n    fallback: () => props.fallback\n  };\n  return createMemo(mapArray(() => props.each, props.children, fallback || void 0));\n}\nfunction Index(props) {\n  const fallback = \"fallback\" in props && {\n    fallback: () => props.fallback\n  };\n  return createMemo(indexArray(() => props.each, props.children, fallback || void 0));\n}\nfunction Show(props) {\n  const keyed = props.keyed;\n  const conditionValue = createMemo(() => props.when, void 0, void 0);\n  const condition = keyed ? conditionValue : createMemo(conditionValue, void 0, {\n    equals: (a, b) => !a === !b\n  });\n  return createMemo(() => {\n    const c = condition();\n    if (c) {\n      const child = props.children;\n      const fn = typeof child === \"function\" && child.length > 0;\n      return fn ? untrack(() => child(keyed ? c : () => {\n        if (!untrack(condition)) throw narrowedError(\"Show\");\n        return conditionValue();\n      })) : child;\n    }\n    return props.fallback;\n  }, void 0, void 0);\n}\nfunction Switch(props) {\n  const chs = children(() => props.children);\n  const switchFunc = createMemo(() => {\n    const ch = chs();\n    const mps = Array.isArray(ch) ? ch : [ch];\n    let func = () => void 0;\n    for (let i = 0; i < mps.length; i++) {\n      const index = i;\n      const mp = mps[i];\n      const prevFunc = func;\n      const conditionValue = createMemo(() => prevFunc() ? void 0 : mp.when, void 0, void 0);\n      const condition = mp.keyed ? conditionValue : createMemo(conditionValue, void 0, {\n        equals: (a, b) => !a === !b\n      });\n      func = () => prevFunc() || (condition() ? [index, conditionValue, mp] : void 0);\n    }\n    return func;\n  });\n  return createMemo(() => {\n    const sel = switchFunc()();\n    if (!sel) return props.fallback;\n    const [index, conditionValue, mp] = sel;\n    const child = mp.children;\n    const fn = typeof child === \"function\" && child.length > 0;\n    return fn ? untrack(() => child(mp.keyed ? conditionValue() : () => {\n      if (untrack(switchFunc)()?.[0] !== index) throw narrowedError(\"Match\");\n      return conditionValue();\n    })) : child;\n  }, void 0, void 0);\n}\nfunction Match(props) {\n  return props;\n}\nvar DEV = void 0;\n\n// ../../node_modules/.pnpm/solid-js@1.9.5/node_modules/solid-js/web/dist/web.js\nvar booleans = [\"allowfullscreen\", \"async\", \"autofocus\", \"autoplay\", \"checked\", \"controls\", \"default\", \"disabled\", \"formnovalidate\", \"hidden\", \"indeterminate\", \"inert\", \"ismap\", \"loop\", \"multiple\", \"muted\", \"nomodule\", \"novalidate\", \"open\", \"playsinline\", \"readonly\", \"required\", \"reversed\", \"seamless\", \"selected\"];\nvar Properties = /* @__PURE__ */new Set([\"className\", \"value\", \"readOnly\", \"formNoValidate\", \"isMap\", \"noModule\", \"playsInline\", ...booleans]);\nvar ChildProperties = /* @__PURE__ */new Set([\"innerHTML\", \"textContent\", \"innerText\", \"children\"]);\nvar Aliases = /* @__PURE__ */Object.assign(/* @__PURE__ */Object.create(null), {\n  className: \"class\",\n  htmlFor: \"for\"\n});\nvar PropAliases = /* @__PURE__ */Object.assign(/* @__PURE__ */Object.create(null), {\n  class: \"className\",\n  formnovalidate: {\n    $: \"formNoValidate\",\n    BUTTON: 1,\n    INPUT: 1\n  },\n  ismap: {\n    $: \"isMap\",\n    IMG: 1\n  },\n  nomodule: {\n    $: \"noModule\",\n    SCRIPT: 1\n  },\n  playsinline: {\n    $: \"playsInline\",\n    VIDEO: 1\n  },\n  readonly: {\n    $: \"readOnly\",\n    INPUT: 1,\n    TEXTAREA: 1\n  }\n});\nfunction getPropAlias(prop, tagName) {\n  const a = PropAliases[prop];\n  return typeof a === \"object\" ? a[tagName] ? a[\"$\"] : void 0 : a;\n}\nvar DelegatedEvents = /* @__PURE__ */new Set([\"beforeinput\", \"click\", \"dblclick\", \"contextmenu\", \"focusin\", \"focusout\", \"input\", \"keydown\", \"keyup\", \"mousedown\", \"mousemove\", \"mouseout\", \"mouseover\", \"mouseup\", \"pointerdown\", \"pointermove\", \"pointerout\", \"pointerover\", \"pointerup\", \"touchend\", \"touchmove\", \"touchstart\"]);\nvar SVGElements = /* @__PURE__ */new Set([\"altGlyph\", \"altGlyphDef\", \"altGlyphItem\", \"animate\", \"animateColor\", \"animateMotion\", \"animateTransform\", \"circle\", \"clipPath\", \"color-profile\", \"cursor\", \"defs\", \"desc\", \"ellipse\", \"feBlend\", \"feColorMatrix\", \"feComponentTransfer\", \"feComposite\", \"feConvolveMatrix\", \"feDiffuseLighting\", \"feDisplacementMap\", \"feDistantLight\", \"feDropShadow\", \"feFlood\", \"feFuncA\", \"feFuncB\", \"feFuncG\", \"feFuncR\", \"feGaussianBlur\", \"feImage\", \"feMerge\", \"feMergeNode\", \"feMorphology\", \"feOffset\", \"fePointLight\", \"feSpecularLighting\", \"feSpotLight\", \"feTile\", \"feTurbulence\", \"filter\", \"font\", \"font-face\", \"font-face-format\", \"font-face-name\", \"font-face-src\", \"font-face-uri\", \"foreignObject\", \"g\", \"glyph\", \"glyphRef\", \"hkern\", \"image\", \"line\", \"linearGradient\", \"marker\", \"mask\", \"metadata\", \"missing-glyph\", \"mpath\", \"path\", \"pattern\", \"polygon\", \"polyline\", \"radialGradient\", \"rect\", \"set\", \"stop\", \"svg\", \"switch\", \"symbol\", \"text\", \"textPath\", \"tref\", \"tspan\", \"use\", \"view\", \"vkern\"]);\nvar SVGNamespace = {\n  xlink: \"http://www.w3.org/1999/xlink\",\n  xml: \"http://www.w3.org/XML/1998/namespace\"\n};\nfunction reconcileArrays(parentNode, a, b) {\n  let bLength = b.length,\n    aEnd = a.length,\n    bEnd = bLength,\n    aStart = 0,\n    bStart = 0,\n    after = a[aEnd - 1].nextSibling,\n    map = null;\n  while (aStart < aEnd || bStart < bEnd) {\n    if (a[aStart] === b[bStart]) {\n      aStart++;\n      bStart++;\n      continue;\n    }\n    while (a[aEnd - 1] === b[bEnd - 1]) {\n      aEnd--;\n      bEnd--;\n    }\n    if (aEnd === aStart) {\n      const node = bEnd < bLength ? bStart ? b[bStart - 1].nextSibling : b[bEnd - bStart] : after;\n      while (bStart < bEnd) parentNode.insertBefore(b[bStart++], node);\n    } else if (bEnd === bStart) {\n      while (aStart < aEnd) {\n        if (!map || !map.has(a[aStart])) a[aStart].remove();\n        aStart++;\n      }\n    } else if (a[aStart] === b[bEnd - 1] && b[bStart] === a[aEnd - 1]) {\n      const node = a[--aEnd].nextSibling;\n      parentNode.insertBefore(b[bStart++], a[aStart++].nextSibling);\n      parentNode.insertBefore(b[--bEnd], node);\n      a[aEnd] = b[bEnd];\n    } else {\n      if (!map) {\n        map = /* @__PURE__ */new Map();\n        let i = bStart;\n        while (i < bEnd) map.set(b[i], i++);\n      }\n      const index = map.get(a[aStart]);\n      if (index != null) {\n        if (bStart < index && index < bEnd) {\n          let i = aStart,\n            sequence = 1,\n            t;\n          while (++i < aEnd && i < bEnd) {\n            if ((t = map.get(a[i])) == null || t !== index + sequence) break;\n            sequence++;\n          }\n          if (sequence > index - bStart) {\n            const node = a[aStart];\n            while (bStart < index) parentNode.insertBefore(b[bStart++], node);\n          } else parentNode.replaceChild(b[bStart++], a[aStart++]);\n        } else aStart++;\n      } else a[aStart++].remove();\n    }\n  }\n}\nvar $$EVENTS = \"_$DX_DELEGATE\";\nfunction render(code, element, init, options = {}) {\n  let disposer;\n  createRoot(dispose2 => {\n    disposer = dispose2;\n    element === document ? code() : insert(element, code(), element.firstChild ? null : void 0, init);\n  }, options.owner);\n  return () => {\n    disposer();\n    element.textContent = \"\";\n  };\n}\nfunction template(html, isImportNode, isSVG, isMathML) {\n  let node;\n  const create = () => {\n    const t = isMathML ? document.createElementNS(\"http://www.w3.org/1998/Math/MathML\", \"template\") : document.createElement(\"template\");\n    t.innerHTML = html;\n    return isSVG ? t.content.firstChild.firstChild : isMathML ? t.firstChild : t.content.firstChild;\n  };\n  const fn = isImportNode ? () => untrack(() => document.importNode(node || (node = create()), true)) : () => (node || (node = create())).cloneNode(true);\n  fn.cloneNode = fn;\n  return fn;\n}\nfunction delegateEvents(eventNames, document2 = window.document) {\n  const e = document2[$$EVENTS] || (document2[$$EVENTS] = /* @__PURE__ */new Set());\n  for (let i = 0, l = eventNames.length; i < l; i++) {\n    const name = eventNames[i];\n    if (!e.has(name)) {\n      e.add(name);\n      document2.addEventListener(name, eventHandler);\n    }\n  }\n}\nfunction clearDelegatedEvents(document2 = window.document) {\n  if (document2[$$EVENTS]) {\n    for (let name of document2[$$EVENTS].keys()) document2.removeEventListener(name, eventHandler);\n    delete document2[$$EVENTS];\n  }\n}\nfunction setAttribute(node, name, value) {\n  if (isHydrating(node)) return;\n  if (value == null) node.removeAttribute(name);else node.setAttribute(name, value);\n}\nfunction setAttributeNS(node, namespace, name, value) {\n  if (isHydrating(node)) return;\n  if (value == null) node.removeAttributeNS(namespace, name);else node.setAttributeNS(namespace, name, value);\n}\nfunction setBoolAttribute(node, name, value) {\n  if (isHydrating(node)) return;\n  value ? node.setAttribute(name, \"\") : node.removeAttribute(name);\n}\nfunction className(node, value) {\n  if (isHydrating(node)) return;\n  if (value == null) node.removeAttribute(\"class\");else node.className = value;\n}\nfunction addEventListener(node, name, handler, delegate) {\n  if (delegate) {\n    if (Array.isArray(handler)) {\n      node[`$$${name}`] = handler[0];\n      node[`$$${name}Data`] = handler[1];\n    } else node[`$$${name}`] = handler;\n  } else if (Array.isArray(handler)) {\n    const handlerFn = handler[0];\n    node.addEventListener(name, handler[0] = e => handlerFn.call(node, handler[1], e));\n  } else node.addEventListener(name, handler, typeof handler !== \"function\" && handler);\n}\nfunction classList(node, value, prev = {}) {\n  const classKeys = Object.keys(value || {}),\n    prevKeys = Object.keys(prev);\n  let i, len;\n  for (i = 0, len = prevKeys.length; i < len; i++) {\n    const key = prevKeys[i];\n    if (!key || key === \"undefined\" || value[key]) continue;\n    toggleClassKey(node, key, false);\n    delete prev[key];\n  }\n  for (i = 0, len = classKeys.length; i < len; i++) {\n    const key = classKeys[i],\n      classValue = !!value[key];\n    if (!key || key === \"undefined\" || prev[key] === classValue || !classValue) continue;\n    toggleClassKey(node, key, true);\n    prev[key] = classValue;\n  }\n  return prev;\n}\nfunction style(node, value, prev) {\n  if (!value) return prev ? setAttribute(node, \"style\") : value;\n  const nodeStyle = node.style;\n  if (typeof value === \"string\") return nodeStyle.cssText = value;\n  typeof prev === \"string\" && (nodeStyle.cssText = prev = void 0);\n  prev || (prev = {});\n  value || (value = {});\n  let v, s;\n  for (s in prev) {\n    value[s] == null && nodeStyle.removeProperty(s);\n    delete prev[s];\n  }\n  for (s in value) {\n    v = value[s];\n    if (v !== prev[s]) {\n      nodeStyle.setProperty(s, v);\n      prev[s] = v;\n    }\n  }\n  return prev;\n}\nfunction spread(node, props = {}, isSVG, skipChildren) {\n  const prevProps = {};\n  if (!skipChildren) {\n    createRenderEffect(() => prevProps.children = insertExpression(node, props.children, prevProps.children));\n  }\n  createRenderEffect(() => typeof props.ref === \"function\" && use(props.ref, node));\n  createRenderEffect(() => assign(node, props, isSVG, true, prevProps, true));\n  return prevProps;\n}\nfunction use(fn, element, arg) {\n  return untrack(() => fn(element, arg));\n}\nfunction insert(parent, accessor, marker, initial) {\n  if (marker !== void 0 && !initial) initial = [];\n  if (typeof accessor !== \"function\") return insertExpression(parent, accessor, initial, marker);\n  createRenderEffect(current => insertExpression(parent, accessor(), current, marker), initial);\n}\nfunction assign(node, props, isSVG, skipChildren, prevProps = {}, skipRef = false) {\n  props || (props = {});\n  for (const prop in prevProps) {\n    if (!(prop in props)) {\n      if (prop === \"children\") continue;\n      prevProps[prop] = assignProp(node, prop, null, prevProps[prop], isSVG, skipRef, props);\n    }\n  }\n  for (const prop in props) {\n    if (prop === \"children\") {\n      continue;\n    }\n    const value = props[prop];\n    prevProps[prop] = assignProp(node, prop, value, prevProps[prop], isSVG, skipRef, props);\n  }\n}\nfunction getNextElement(template2) {\n  let node,\n    key,\n    hydrating = isHydrating();\n  if (!hydrating || !(node = sharedConfig.registry.get(key = getHydrationKey()))) {\n    return template2();\n  }\n  if (sharedConfig.completed) sharedConfig.completed.add(node);\n  sharedConfig.registry.delete(key);\n  return node;\n}\nfunction isHydrating(node) {\n  return !!sharedConfig.context && !sharedConfig.done && (!node || node.isConnected);\n}\nfunction toPropertyName(name) {\n  return name.toLowerCase().replace(/-([a-z])/g, (_, w) => w.toUpperCase());\n}\nfunction toggleClassKey(node, key, value) {\n  const classNames = key.trim().split(/\\s+/);\n  for (let i = 0, nameLen = classNames.length; i < nameLen; i++) node.classList.toggle(classNames[i], value);\n}\nfunction assignProp(node, prop, value, prev, isSVG, skipRef, props) {\n  let isCE, isProp, isChildProp, propAlias, forceProp;\n  if (prop === \"style\") return style(node, value, prev);\n  if (prop === \"classList\") return classList(node, value, prev);\n  if (value === prev) return prev;\n  if (prop === \"ref\") {\n    if (!skipRef) value(node);\n  } else if (prop.slice(0, 3) === \"on:\") {\n    const e = prop.slice(3);\n    prev && node.removeEventListener(e, prev, typeof prev !== \"function\" && prev);\n    value && node.addEventListener(e, value, typeof value !== \"function\" && value);\n  } else if (prop.slice(0, 10) === \"oncapture:\") {\n    const e = prop.slice(10);\n    prev && node.removeEventListener(e, prev, true);\n    value && node.addEventListener(e, value, true);\n  } else if (prop.slice(0, 2) === \"on\") {\n    const name = prop.slice(2).toLowerCase();\n    const delegate = DelegatedEvents.has(name);\n    if (!delegate && prev) {\n      const h = Array.isArray(prev) ? prev[0] : prev;\n      node.removeEventListener(name, h);\n    }\n    if (delegate || value) {\n      addEventListener(node, name, value, delegate);\n      delegate && delegateEvents([name]);\n    }\n  } else if (prop.slice(0, 5) === \"attr:\") {\n    setAttribute(node, prop.slice(5), value);\n  } else if (prop.slice(0, 5) === \"bool:\") {\n    setBoolAttribute(node, prop.slice(5), value);\n  } else if ((forceProp = prop.slice(0, 5) === \"prop:\") || (isChildProp = ChildProperties.has(prop)) || !isSVG && ((propAlias = getPropAlias(prop, node.tagName)) || (isProp = Properties.has(prop))) || (isCE = node.nodeName.includes(\"-\") || \"is\" in props)) {\n    if (forceProp) {\n      prop = prop.slice(5);\n      isProp = true;\n    } else if (isHydrating(node)) return value;\n    if (prop === \"class\" || prop === \"className\") className(node, value);else if (isCE && !isProp && !isChildProp) node[toPropertyName(prop)] = value;else node[propAlias || prop] = value;\n  } else {\n    const ns = isSVG && prop.indexOf(\":\") > -1 && SVGNamespace[prop.split(\":\")[0]];\n    if (ns) setAttributeNS(node, ns, prop, value);else setAttribute(node, Aliases[prop] || prop, value);\n  }\n  return value;\n}\nfunction eventHandler(e) {\n  if (sharedConfig.registry && sharedConfig.events) {\n    if (sharedConfig.events.find(([el, ev]) => ev === e)) return;\n  }\n  let node = e.target;\n  const key = `$$${e.type}`;\n  const oriTarget = e.target;\n  const oriCurrentTarget = e.currentTarget;\n  const retarget = value => Object.defineProperty(e, \"target\", {\n    configurable: true,\n    value\n  });\n  const handleNode = () => {\n    const handler = node[key];\n    if (handler && !node.disabled) {\n      const data = node[`${key}Data`];\n      data !== void 0 ? handler.call(node, data, e) : handler.call(node, e);\n      if (e.cancelBubble) return;\n    }\n    node.host && typeof node.host !== \"string\" && !node.host._$host && node.contains(e.target) && retarget(node.host);\n    return true;\n  };\n  const walkUpTree = () => {\n    while (handleNode() && (node = node._$host || node.parentNode || node.host));\n  };\n  Object.defineProperty(e, \"currentTarget\", {\n    configurable: true,\n    get() {\n      return node || document;\n    }\n  });\n  if (sharedConfig.registry && !sharedConfig.done) sharedConfig.done = _$HY.done = true;\n  if (e.composedPath) {\n    const path = e.composedPath();\n    retarget(path[0]);\n    for (let i = 0; i < path.length - 2; i++) {\n      node = path[i];\n      if (!handleNode()) break;\n      if (node._$host) {\n        node = node._$host;\n        walkUpTree();\n        break;\n      }\n      if (node.parentNode === oriCurrentTarget) {\n        break;\n      }\n    }\n  } else walkUpTree();\n  retarget(oriTarget);\n}\nfunction insertExpression(parent, value, current, marker, unwrapArray) {\n  const hydrating = isHydrating(parent);\n  if (hydrating) {\n    !current && (current = [...parent.childNodes]);\n    let cleaned = [];\n    for (let i = 0; i < current.length; i++) {\n      const node = current[i];\n      if (node.nodeType === 8 && node.data.slice(0, 2) === \"!$\") node.remove();else cleaned.push(node);\n    }\n    current = cleaned;\n  }\n  while (typeof current === \"function\") current = current();\n  if (value === current) return current;\n  const t = typeof value,\n    multi = marker !== void 0;\n  parent = multi && current[0] && current[0].parentNode || parent;\n  if (t === \"string\" || t === \"number\") {\n    if (hydrating) return current;\n    if (t === \"number\") {\n      value = value.toString();\n      if (value === current) return current;\n    }\n    if (multi) {\n      let node = current[0];\n      if (node && node.nodeType === 3) {\n        node.data !== value && (node.data = value);\n      } else node = document.createTextNode(value);\n      current = cleanChildren(parent, current, marker, node);\n    } else {\n      if (current !== \"\" && typeof current === \"string\") {\n        current = parent.firstChild.data = value;\n      } else current = parent.textContent = value;\n    }\n  } else if (value == null || t === \"boolean\") {\n    if (hydrating) return current;\n    current = cleanChildren(parent, current, marker);\n  } else if (t === \"function\") {\n    createRenderEffect(() => {\n      let v = value();\n      while (typeof v === \"function\") v = v();\n      current = insertExpression(parent, v, current, marker);\n    });\n    return () => current;\n  } else if (Array.isArray(value)) {\n    const array = [];\n    const currentArray = current && Array.isArray(current);\n    if (normalizeIncomingArray(array, value, current, unwrapArray)) {\n      createRenderEffect(() => current = insertExpression(parent, array, current, marker, true));\n      return () => current;\n    }\n    if (hydrating) {\n      if (!array.length) return current;\n      if (marker === void 0) return current = [...parent.childNodes];\n      let node = array[0];\n      if (node.parentNode !== parent) return current;\n      const nodes = [node];\n      while ((node = node.nextSibling) !== marker) nodes.push(node);\n      return current = nodes;\n    }\n    if (array.length === 0) {\n      current = cleanChildren(parent, current, marker);\n      if (multi) return current;\n    } else if (currentArray) {\n      if (current.length === 0) {\n        appendNodes(parent, array, marker);\n      } else reconcileArrays(parent, current, array);\n    } else {\n      current && cleanChildren(parent);\n      appendNodes(parent, array);\n    }\n    current = array;\n  } else if (value.nodeType) {\n    if (hydrating && value.parentNode) return current = multi ? [value] : value;\n    if (Array.isArray(current)) {\n      if (multi) return current = cleanChildren(parent, current, marker, value);\n      cleanChildren(parent, current, null, value);\n    } else if (current == null || current === \"\" || !parent.firstChild) {\n      parent.appendChild(value);\n    } else parent.replaceChild(value, parent.firstChild);\n    current = value;\n  } else ;\n  return current;\n}\nfunction normalizeIncomingArray(normalized, array, current, unwrap) {\n  let dynamic = false;\n  for (let i = 0, len = array.length; i < len; i++) {\n    let item = array[i],\n      prev = current && current[normalized.length],\n      t;\n    if (item == null || item === true || item === false) ;else if ((t = typeof item) === \"object\" && item.nodeType) {\n      normalized.push(item);\n    } else if (Array.isArray(item)) {\n      dynamic = normalizeIncomingArray(normalized, item, prev) || dynamic;\n    } else if (t === \"function\") {\n      if (unwrap) {\n        while (typeof item === \"function\") item = item();\n        dynamic = normalizeIncomingArray(normalized, Array.isArray(item) ? item : [item], Array.isArray(prev) ? prev : [prev]) || dynamic;\n      } else {\n        normalized.push(item);\n        dynamic = true;\n      }\n    } else {\n      const value = String(item);\n      if (prev && prev.nodeType === 3 && prev.data === value) normalized.push(prev);else normalized.push(document.createTextNode(value));\n    }\n  }\n  return dynamic;\n}\nfunction appendNodes(parent, array, marker = null) {\n  for (let i = 0, len = array.length; i < len; i++) parent.insertBefore(array[i], marker);\n}\nfunction cleanChildren(parent, current, marker, replacement) {\n  if (marker === void 0) return parent.textContent = \"\";\n  const node = replacement || document.createTextNode(\"\");\n  if (current.length) {\n    let inserted = false;\n    for (let i = current.length - 1; i >= 0; i--) {\n      const el = current[i];\n      if (node !== el) {\n        const isParent = el.parentNode === parent;\n        if (!inserted && !i) isParent ? parent.replaceChild(node, el) : parent.insertBefore(node, marker);else isParent && el.remove();\n      } else inserted = true;\n    }\n  } else parent.insertBefore(node, marker);\n  return [node];\n}\nfunction getHydrationKey() {\n  return sharedConfig.getNextContextId();\n}\nvar isServer = false;\nvar SVG_NAMESPACE = \"http://www.w3.org/2000/svg\";\nfunction createElement(tagName, isSVG = false) {\n  return isSVG ? document.createElementNS(SVG_NAMESPACE, tagName) : document.createElement(tagName);\n}\nfunction Portal(props) {\n  const {\n      useShadow\n    } = props,\n    marker = document.createTextNode(\"\"),\n    mount = () => props.mount || document.body,\n    owner = getOwner();\n  let content;\n  let hydrating = !!sharedConfig.context;\n  createEffect(() => {\n    if (hydrating) getOwner().user = hydrating = false;\n    content || (content = runWithOwner(owner, () => createMemo(() => props.children)));\n    const el = mount();\n    if (el instanceof HTMLHeadElement) {\n      const [clean, setClean] = createSignal(false);\n      const cleanup = () => setClean(true);\n      createRoot(dispose2 => insert(el, () => !clean() ? content() : dispose2(), null));\n      onCleanup(cleanup);\n    } else {\n      const container = createElement(props.isSVG ? \"g\" : \"div\", props.isSVG),\n        renderRoot = useShadow && container.attachShadow ? container.attachShadow({\n          mode: \"open\"\n        }) : container;\n      Object.defineProperty(container, \"_$host\", {\n        get() {\n          return marker.parentNode;\n        },\n        configurable: true\n      });\n      insert(renderRoot, content);\n      el.appendChild(container);\n      props.ref && props.ref(container);\n      onCleanup(() => el.removeChild(container));\n    }\n  }, void 0, {\n    render: !hydrating\n  });\n  return marker;\n}\nfunction createDynamic(component, props) {\n  const cached = createMemo(component);\n  return createMemo(() => {\n    const component2 = cached();\n    switch (typeof component2) {\n      case \"function\":\n        return untrack(() => component2(props));\n      case \"string\":\n        const isSvg = SVGElements.has(component2);\n        const el = sharedConfig.context ? getNextElement() : createElement(component2, isSvg);\n        spread(el, props, isSvg);\n        return el;\n    }\n  });\n}\nfunction Dynamic(props) {\n  const [, others] = splitProps(props, [\"component\"]);\n  return createDynamic(() => props.component, others);\n}\n\n// ../../node_modules/.pnpm/superjson@2.2.1/node_modules/superjson/dist/double-indexed-kv.js\nvar DoubleIndexedKV = class {\n  constructor() {\n    this.keyToValue = /* @__PURE__ */new Map();\n    this.valueToKey = /* @__PURE__ */new Map();\n  }\n  set(key, value) {\n    this.keyToValue.set(key, value);\n    this.valueToKey.set(value, key);\n  }\n  getByKey(key) {\n    return this.keyToValue.get(key);\n  }\n  getByValue(value) {\n    return this.valueToKey.get(value);\n  }\n  clear() {\n    this.keyToValue.clear();\n    this.valueToKey.clear();\n  }\n};\n\n// ../../node_modules/.pnpm/superjson@2.2.1/node_modules/superjson/dist/registry.js\nvar Registry = class {\n  constructor(generateIdentifier) {\n    this.generateIdentifier = generateIdentifier;\n    this.kv = new DoubleIndexedKV();\n  }\n  register(value, identifier) {\n    if (this.kv.getByValue(value)) {\n      return;\n    }\n    if (!identifier) {\n      identifier = this.generateIdentifier(value);\n    }\n    this.kv.set(identifier, value);\n  }\n  clear() {\n    this.kv.clear();\n  }\n  getIdentifier(value) {\n    return this.kv.getByValue(value);\n  }\n  getValue(identifier) {\n    return this.kv.getByKey(identifier);\n  }\n};\n\n// ../../node_modules/.pnpm/superjson@2.2.1/node_modules/superjson/dist/class-registry.js\nvar ClassRegistry = class extends Registry {\n  constructor() {\n    super(c => c.name);\n    this.classToAllowedProps = /* @__PURE__ */new Map();\n  }\n  register(value, options) {\n    if (typeof options === \"object\") {\n      if (options.allowProps) {\n        this.classToAllowedProps.set(value, options.allowProps);\n      }\n      super.register(value, options.identifier);\n    } else {\n      super.register(value, options);\n    }\n  }\n  getAllowedProps(value) {\n    return this.classToAllowedProps.get(value);\n  }\n};\n\n// ../../node_modules/.pnpm/superjson@2.2.1/node_modules/superjson/dist/util.js\nfunction valuesOfObj(record) {\n  if (\"values\" in Object) {\n    return Object.values(record);\n  }\n  const values = [];\n  for (const key in record) {\n    if (record.hasOwnProperty(key)) {\n      values.push(record[key]);\n    }\n  }\n  return values;\n}\nfunction find(record, predicate) {\n  const values = valuesOfObj(record);\n  if (\"find\" in values) {\n    return values.find(predicate);\n  }\n  const valuesNotNever = values;\n  for (let i = 0; i < valuesNotNever.length; i++) {\n    const value = valuesNotNever[i];\n    if (predicate(value)) {\n      return value;\n    }\n  }\n  return void 0;\n}\nfunction forEach(record, run) {\n  Object.entries(record).forEach(([key, value]) => run(value, key));\n}\nfunction includes(arr, value) {\n  return arr.indexOf(value) !== -1;\n}\nfunction findArr(record, predicate) {\n  for (let i = 0; i < record.length; i++) {\n    const value = record[i];\n    if (predicate(value)) {\n      return value;\n    }\n  }\n  return void 0;\n}\n\n// ../../node_modules/.pnpm/superjson@2.2.1/node_modules/superjson/dist/custom-transformer-registry.js\nvar CustomTransformerRegistry = class {\n  constructor() {\n    this.transfomers = {};\n  }\n  register(transformer) {\n    this.transfomers[transformer.name] = transformer;\n  }\n  findApplicable(v) {\n    return find(this.transfomers, transformer => transformer.isApplicable(v));\n  }\n  findByName(name) {\n    return this.transfomers[name];\n  }\n};\n\n// ../../node_modules/.pnpm/superjson@2.2.1/node_modules/superjson/dist/is.js\nvar getType = payload => Object.prototype.toString.call(payload).slice(8, -1);\nvar isUndefined = payload => typeof payload === \"undefined\";\nvar isNull = payload => payload === null;\nvar isPlainObject = payload => {\n  if (typeof payload !== \"object\" || payload === null) return false;\n  if (payload === Object.prototype) return false;\n  if (Object.getPrototypeOf(payload) === null) return true;\n  return Object.getPrototypeOf(payload) === Object.prototype;\n};\nvar isEmptyObject = payload => isPlainObject(payload) && Object.keys(payload).length === 0;\nvar isArray = payload => Array.isArray(payload);\nvar isString = payload => typeof payload === \"string\";\nvar isNumber = payload => typeof payload === \"number\" && !isNaN(payload);\nvar isBoolean = payload => typeof payload === \"boolean\";\nvar isRegExp = payload => payload instanceof RegExp;\nvar isMap = payload => payload instanceof Map;\nvar isSet = payload => payload instanceof Set;\nvar isSymbol = payload => getType(payload) === \"Symbol\";\nvar isDate = payload => payload instanceof Date && !isNaN(payload.valueOf());\nvar isError = payload => payload instanceof Error;\nvar isNaNValue = payload => typeof payload === \"number\" && isNaN(payload);\nvar isPrimitive = payload => isBoolean(payload) || isNull(payload) || isUndefined(payload) || isNumber(payload) || isString(payload) || isSymbol(payload);\nvar isBigint = payload => typeof payload === \"bigint\";\nvar isInfinite = payload => payload === Infinity || payload === -Infinity;\nvar isTypedArray = payload => ArrayBuffer.isView(payload) && !(payload instanceof DataView);\nvar isURL = payload => payload instanceof URL;\n\n// ../../node_modules/.pnpm/superjson@2.2.1/node_modules/superjson/dist/pathstringifier.js\nvar escapeKey = key => key.replace(/\\./g, \"\\\\.\");\nvar stringifyPath = path => path.map(String).map(escapeKey).join(\".\");\nvar parsePath = string => {\n  const result = [];\n  let segment = \"\";\n  for (let i = 0; i < string.length; i++) {\n    let char = string.charAt(i);\n    const isEscapedDot = char === \"\\\\\" && string.charAt(i + 1) === \".\";\n    if (isEscapedDot) {\n      segment += \".\";\n      i++;\n      continue;\n    }\n    const isEndOfSegment = char === \".\";\n    if (isEndOfSegment) {\n      result.push(segment);\n      segment = \"\";\n      continue;\n    }\n    segment += char;\n  }\n  const lastSegment = segment;\n  result.push(lastSegment);\n  return result;\n};\n\n// ../../node_modules/.pnpm/superjson@2.2.1/node_modules/superjson/dist/transformer.js\nfunction simpleTransformation(isApplicable, annotation, transform, untransform) {\n  return {\n    isApplicable,\n    annotation,\n    transform,\n    untransform\n  };\n}\nvar simpleRules = [simpleTransformation(isUndefined, \"undefined\", () => null, () => void 0), simpleTransformation(isBigint, \"bigint\", v => v.toString(), v => {\n  if (typeof BigInt !== \"undefined\") {\n    return BigInt(v);\n  }\n  console.error(\"Please add a BigInt polyfill.\");\n  return v;\n}), simpleTransformation(isDate, \"Date\", v => v.toISOString(), v => new Date(v)), simpleTransformation(isError, \"Error\", (v, superJson) => {\n  const baseError = {\n    name: v.name,\n    message: v.message\n  };\n  superJson.allowedErrorProps.forEach(prop => {\n    baseError[prop] = v[prop];\n  });\n  return baseError;\n}, (v, superJson) => {\n  const e = new Error(v.message);\n  e.name = v.name;\n  e.stack = v.stack;\n  superJson.allowedErrorProps.forEach(prop => {\n    e[prop] = v[prop];\n  });\n  return e;\n}), simpleTransformation(isRegExp, \"regexp\", v => \"\" + v, regex => {\n  const body = regex.slice(1, regex.lastIndexOf(\"/\"));\n  const flags = regex.slice(regex.lastIndexOf(\"/\") + 1);\n  return new RegExp(body, flags);\n}), simpleTransformation(isSet, \"set\",\n// (sets only exist in es6+)\n// eslint-disable-next-line es5/no-es6-methods\nv => [...v.values()], v => new Set(v)), simpleTransformation(isMap, \"map\", v => [...v.entries()], v => new Map(v)), simpleTransformation(v => isNaNValue(v) || isInfinite(v), \"number\", v => {\n  if (isNaNValue(v)) {\n    return \"NaN\";\n  }\n  if (v > 0) {\n    return \"Infinity\";\n  } else {\n    return \"-Infinity\";\n  }\n}, Number), simpleTransformation(v => v === 0 && 1 / v === -Infinity, \"number\", () => {\n  return \"-0\";\n}, Number), simpleTransformation(isURL, \"URL\", v => v.toString(), v => new URL(v))];\nfunction compositeTransformation(isApplicable, annotation, transform, untransform) {\n  return {\n    isApplicable,\n    annotation,\n    transform,\n    untransform\n  };\n}\nvar symbolRule = compositeTransformation((s, superJson) => {\n  if (isSymbol(s)) {\n    const isRegistered = !!superJson.symbolRegistry.getIdentifier(s);\n    return isRegistered;\n  }\n  return false;\n}, (s, superJson) => {\n  const identifier = superJson.symbolRegistry.getIdentifier(s);\n  return [\"symbol\", identifier];\n}, v => v.description, (_, a, superJson) => {\n  const value = superJson.symbolRegistry.getValue(a[1]);\n  if (!value) {\n    throw new Error(\"Trying to deserialize unknown symbol\");\n  }\n  return value;\n});\nvar constructorToName = [Int8Array, Uint8Array, Int16Array, Uint16Array, Int32Array, Uint32Array, Float32Array, Float64Array, Uint8ClampedArray].reduce((obj, ctor) => {\n  obj[ctor.name] = ctor;\n  return obj;\n}, {});\nvar typedArrayRule = compositeTransformation(isTypedArray, v => [\"typed-array\", v.constructor.name], v => [...v], (v, a) => {\n  const ctor = constructorToName[a[1]];\n  if (!ctor) {\n    throw new Error(\"Trying to deserialize unknown typed array\");\n  }\n  return new ctor(v);\n});\nfunction isInstanceOfRegisteredClass(potentialClass, superJson) {\n  if (potentialClass?.constructor) {\n    const isRegistered = !!superJson.classRegistry.getIdentifier(potentialClass.constructor);\n    return isRegistered;\n  }\n  return false;\n}\nvar classRule = compositeTransformation(isInstanceOfRegisteredClass, (clazz, superJson) => {\n  const identifier = superJson.classRegistry.getIdentifier(clazz.constructor);\n  return [\"class\", identifier];\n}, (clazz, superJson) => {\n  const allowedProps = superJson.classRegistry.getAllowedProps(clazz.constructor);\n  if (!allowedProps) {\n    return {\n      ...clazz\n    };\n  }\n  const result = {};\n  allowedProps.forEach(prop => {\n    result[prop] = clazz[prop];\n  });\n  return result;\n}, (v, a, superJson) => {\n  const clazz = superJson.classRegistry.getValue(a[1]);\n  if (!clazz) {\n    throw new Error(\"Trying to deserialize unknown class - check https://github.com/blitz-js/superjson/issues/116#issuecomment-773996564\");\n  }\n  return Object.assign(Object.create(clazz.prototype), v);\n});\nvar customRule = compositeTransformation((value, superJson) => {\n  return !!superJson.customTransformerRegistry.findApplicable(value);\n}, (value, superJson) => {\n  const transformer = superJson.customTransformerRegistry.findApplicable(value);\n  return [\"custom\", transformer.name];\n}, (value, superJson) => {\n  const transformer = superJson.customTransformerRegistry.findApplicable(value);\n  return transformer.serialize(value);\n}, (v, a, superJson) => {\n  const transformer = superJson.customTransformerRegistry.findByName(a[1]);\n  if (!transformer) {\n    throw new Error(\"Trying to deserialize unknown custom value\");\n  }\n  return transformer.deserialize(v);\n});\nvar compositeRules = [classRule, symbolRule, customRule, typedArrayRule];\nvar transformValue = (value, superJson) => {\n  const applicableCompositeRule = findArr(compositeRules, rule => rule.isApplicable(value, superJson));\n  if (applicableCompositeRule) {\n    return {\n      value: applicableCompositeRule.transform(value, superJson),\n      type: applicableCompositeRule.annotation(value, superJson)\n    };\n  }\n  const applicableSimpleRule = findArr(simpleRules, rule => rule.isApplicable(value, superJson));\n  if (applicableSimpleRule) {\n    return {\n      value: applicableSimpleRule.transform(value, superJson),\n      type: applicableSimpleRule.annotation\n    };\n  }\n  return void 0;\n};\nvar simpleRulesByAnnotation = {};\nsimpleRules.forEach(rule => {\n  simpleRulesByAnnotation[rule.annotation] = rule;\n});\nvar untransformValue = (json, type, superJson) => {\n  if (isArray(type)) {\n    switch (type[0]) {\n      case \"symbol\":\n        return symbolRule.untransform(json, type, superJson);\n      case \"class\":\n        return classRule.untransform(json, type, superJson);\n      case \"custom\":\n        return customRule.untransform(json, type, superJson);\n      case \"typed-array\":\n        return typedArrayRule.untransform(json, type, superJson);\n      default:\n        throw new Error(\"Unknown transformation: \" + type);\n    }\n  } else {\n    const transformation = simpleRulesByAnnotation[type];\n    if (!transformation) {\n      throw new Error(\"Unknown transformation: \" + type);\n    }\n    return transformation.untransform(json, superJson);\n  }\n};\n\n// ../../node_modules/.pnpm/superjson@2.2.1/node_modules/superjson/dist/accessDeep.js\nvar getNthKey = (value, n) => {\n  const keys = value.keys();\n  while (n > 0) {\n    keys.next();\n    n--;\n  }\n  return keys.next().value;\n};\nfunction validatePath(path) {\n  if (includes(path, \"__proto__\")) {\n    throw new Error(\"__proto__ is not allowed as a property\");\n  }\n  if (includes(path, \"prototype\")) {\n    throw new Error(\"prototype is not allowed as a property\");\n  }\n  if (includes(path, \"constructor\")) {\n    throw new Error(\"constructor is not allowed as a property\");\n  }\n}\nvar getDeep = (object, path) => {\n  validatePath(path);\n  for (let i = 0; i < path.length; i++) {\n    const key = path[i];\n    if (isSet(object)) {\n      object = getNthKey(object, +key);\n    } else if (isMap(object)) {\n      const row = +key;\n      const type = +path[++i] === 0 ? \"key\" : \"value\";\n      const keyOfRow = getNthKey(object, row);\n      switch (type) {\n        case \"key\":\n          object = keyOfRow;\n          break;\n        case \"value\":\n          object = object.get(keyOfRow);\n          break;\n      }\n    } else {\n      object = object[key];\n    }\n  }\n  return object;\n};\nvar setDeep = (object, path, mapper) => {\n  validatePath(path);\n  if (path.length === 0) {\n    return mapper(object);\n  }\n  let parent = object;\n  for (let i = 0; i < path.length - 1; i++) {\n    const key = path[i];\n    if (isArray(parent)) {\n      const index = +key;\n      parent = parent[index];\n    } else if (isPlainObject(parent)) {\n      parent = parent[key];\n    } else if (isSet(parent)) {\n      const row = +key;\n      parent = getNthKey(parent, row);\n    } else if (isMap(parent)) {\n      const isEnd = i === path.length - 2;\n      if (isEnd) {\n        break;\n      }\n      const row = +key;\n      const type = +path[++i] === 0 ? \"key\" : \"value\";\n      const keyOfRow = getNthKey(parent, row);\n      switch (type) {\n        case \"key\":\n          parent = keyOfRow;\n          break;\n        case \"value\":\n          parent = parent.get(keyOfRow);\n          break;\n      }\n    }\n  }\n  const lastKey = path[path.length - 1];\n  if (isArray(parent)) {\n    parent[+lastKey] = mapper(parent[+lastKey]);\n  } else if (isPlainObject(parent)) {\n    parent[lastKey] = mapper(parent[lastKey]);\n  }\n  if (isSet(parent)) {\n    const oldValue = getNthKey(parent, +lastKey);\n    const newValue = mapper(oldValue);\n    if (oldValue !== newValue) {\n      parent.delete(oldValue);\n      parent.add(newValue);\n    }\n  }\n  if (isMap(parent)) {\n    const row = +path[path.length - 2];\n    const keyToRow = getNthKey(parent, row);\n    const type = +lastKey === 0 ? \"key\" : \"value\";\n    switch (type) {\n      case \"key\":\n        {\n          const newKey = mapper(keyToRow);\n          parent.set(newKey, parent.get(keyToRow));\n          if (newKey !== keyToRow) {\n            parent.delete(keyToRow);\n          }\n          break;\n        }\n      case \"value\":\n        {\n          parent.set(keyToRow, mapper(parent.get(keyToRow)));\n          break;\n        }\n    }\n  }\n  return object;\n};\n\n// ../../node_modules/.pnpm/superjson@2.2.1/node_modules/superjson/dist/plainer.js\nfunction traverse(tree, walker2, origin = []) {\n  if (!tree) {\n    return;\n  }\n  if (!isArray(tree)) {\n    forEach(tree, (subtree, key) => traverse(subtree, walker2, [...origin, ...parsePath(key)]));\n    return;\n  }\n  const [nodeValue, children2] = tree;\n  if (children2) {\n    forEach(children2, (child, key) => {\n      traverse(child, walker2, [...origin, ...parsePath(key)]);\n    });\n  }\n  walker2(nodeValue, origin);\n}\nfunction applyValueAnnotations(plain, annotations, superJson) {\n  traverse(annotations, (type, path) => {\n    plain = setDeep(plain, path, v => untransformValue(v, type, superJson));\n  });\n  return plain;\n}\nfunction applyReferentialEqualityAnnotations(plain, annotations) {\n  function apply(identicalPaths, path) {\n    const object = getDeep(plain, parsePath(path));\n    identicalPaths.map(parsePath).forEach(identicalObjectPath => {\n      plain = setDeep(plain, identicalObjectPath, () => object);\n    });\n  }\n  if (isArray(annotations)) {\n    const [root, other] = annotations;\n    root.forEach(identicalPath => {\n      plain = setDeep(plain, parsePath(identicalPath), () => plain);\n    });\n    if (other) {\n      forEach(other, apply);\n    }\n  } else {\n    forEach(annotations, apply);\n  }\n  return plain;\n}\nvar isDeep = (object, superJson) => isPlainObject(object) || isArray(object) || isMap(object) || isSet(object) || isInstanceOfRegisteredClass(object, superJson);\nfunction addIdentity(object, path, identities) {\n  const existingSet = identities.get(object);\n  if (existingSet) {\n    existingSet.push(path);\n  } else {\n    identities.set(object, [path]);\n  }\n}\nfunction generateReferentialEqualityAnnotations(identitites, dedupe) {\n  const result = {};\n  let rootEqualityPaths = void 0;\n  identitites.forEach(paths => {\n    if (paths.length <= 1) {\n      return;\n    }\n    if (!dedupe) {\n      paths = paths.map(path => path.map(String)).sort((a, b) => a.length - b.length);\n    }\n    const [representativePath, ...identicalPaths] = paths;\n    if (representativePath.length === 0) {\n      rootEqualityPaths = identicalPaths.map(stringifyPath);\n    } else {\n      result[stringifyPath(representativePath)] = identicalPaths.map(stringifyPath);\n    }\n  });\n  if (rootEqualityPaths) {\n    if (isEmptyObject(result)) {\n      return [rootEqualityPaths];\n    } else {\n      return [rootEqualityPaths, result];\n    }\n  } else {\n    return isEmptyObject(result) ? void 0 : result;\n  }\n}\nvar walker = (object, identities, superJson, dedupe, path = [], objectsInThisPath = [], seenObjects = /* @__PURE__ */new Map()) => {\n  const primitive = isPrimitive(object);\n  if (!primitive) {\n    addIdentity(object, path, identities);\n    const seen = seenObjects.get(object);\n    if (seen) {\n      return dedupe ? {\n        transformedValue: null\n      } : seen;\n    }\n  }\n  if (!isDeep(object, superJson)) {\n    const transformed2 = transformValue(object, superJson);\n    const result2 = transformed2 ? {\n      transformedValue: transformed2.value,\n      annotations: [transformed2.type]\n    } : {\n      transformedValue: object\n    };\n    if (!primitive) {\n      seenObjects.set(object, result2);\n    }\n    return result2;\n  }\n  if (includes(objectsInThisPath, object)) {\n    return {\n      transformedValue: null\n    };\n  }\n  const transformationResult = transformValue(object, superJson);\n  const transformed = transformationResult?.value ?? object;\n  const transformedValue = isArray(transformed) ? [] : {};\n  const innerAnnotations = {};\n  forEach(transformed, (value, index) => {\n    if (index === \"__proto__\" || index === \"constructor\" || index === \"prototype\") {\n      throw new Error(`Detected property ${index}. This is a prototype pollution risk, please remove it from your object.`);\n    }\n    const recursiveResult = walker(value, identities, superJson, dedupe, [...path, index], [...objectsInThisPath, object], seenObjects);\n    transformedValue[index] = recursiveResult.transformedValue;\n    if (isArray(recursiveResult.annotations)) {\n      innerAnnotations[index] = recursiveResult.annotations;\n    } else if (isPlainObject(recursiveResult.annotations)) {\n      forEach(recursiveResult.annotations, (tree, key) => {\n        innerAnnotations[escapeKey(index) + \".\" + key] = tree;\n      });\n    }\n  });\n  const result = isEmptyObject(innerAnnotations) ? {\n    transformedValue,\n    annotations: !!transformationResult ? [transformationResult.type] : void 0\n  } : {\n    transformedValue,\n    annotations: !!transformationResult ? [transformationResult.type, innerAnnotations] : innerAnnotations\n  };\n  if (!primitive) {\n    seenObjects.set(object, result);\n  }\n  return result;\n};\n\n// ../../node_modules/.pnpm/is-what@4.1.16/node_modules/is-what/dist/index.js\nfunction getType2(payload) {\n  return Object.prototype.toString.call(payload).slice(8, -1);\n}\nfunction isArray2(payload) {\n  return getType2(payload) === \"Array\";\n}\nfunction isPlainObject2(payload) {\n  if (getType2(payload) !== \"Object\") return false;\n  const prototype = Object.getPrototypeOf(payload);\n  return !!prototype && prototype.constructor === Object && prototype === Object.prototype;\n}\n\n// ../../node_modules/.pnpm/copy-anything@3.0.5/node_modules/copy-anything/dist/index.js\nfunction assignProp2(carry, key, newVal, originalObject, includeNonenumerable) {\n  const propType = {}.propertyIsEnumerable.call(originalObject, key) ? \"enumerable\" : \"nonenumerable\";\n  if (propType === \"enumerable\") carry[key] = newVal;\n  if (includeNonenumerable && propType === \"nonenumerable\") {\n    Object.defineProperty(carry, key, {\n      value: newVal,\n      enumerable: false,\n      writable: true,\n      configurable: true\n    });\n  }\n}\nfunction copy(target, options = {}) {\n  if (isArray2(target)) {\n    return target.map(item => copy(item, options));\n  }\n  if (!isPlainObject2(target)) {\n    return target;\n  }\n  const props = Object.getOwnPropertyNames(target);\n  const symbols = Object.getOwnPropertySymbols(target);\n  return [...props, ...symbols].reduce((carry, key) => {\n    if (isArray2(options.props) && !options.props.includes(key)) {\n      return carry;\n    }\n    const val = target[key];\n    const newVal = copy(val, options);\n    assignProp2(carry, key, newVal, target, options.nonenumerable);\n    return carry;\n  }, {});\n}\n\n// ../../node_modules/.pnpm/superjson@2.2.1/node_modules/superjson/dist/index.js\nvar SuperJSON = class {\n  /**\n   * @param dedupeReferentialEqualities  If true, SuperJSON will make sure only one instance of referentially equal objects are serialized and the rest are replaced with `null`.\n   */\n  constructor({\n    dedupe = false\n  } = {}) {\n    this.classRegistry = new ClassRegistry();\n    this.symbolRegistry = new Registry(s => s.description ?? \"\");\n    this.customTransformerRegistry = new CustomTransformerRegistry();\n    this.allowedErrorProps = [];\n    this.dedupe = dedupe;\n  }\n  serialize(object) {\n    const identities = /* @__PURE__ */new Map();\n    const output = walker(object, identities, this, this.dedupe);\n    const res = {\n      json: output.transformedValue\n    };\n    if (output.annotations) {\n      res.meta = {\n        ...res.meta,\n        values: output.annotations\n      };\n    }\n    const equalityAnnotations = generateReferentialEqualityAnnotations(identities, this.dedupe);\n    if (equalityAnnotations) {\n      res.meta = {\n        ...res.meta,\n        referentialEqualities: equalityAnnotations\n      };\n    }\n    return res;\n  }\n  deserialize(payload) {\n    const {\n      json,\n      meta\n    } = payload;\n    let result = copy(json);\n    if (meta?.values) {\n      result = applyValueAnnotations(result, meta.values, this);\n    }\n    if (meta?.referentialEqualities) {\n      result = applyReferentialEqualityAnnotations(result, meta.referentialEqualities);\n    }\n    return result;\n  }\n  stringify(object) {\n    return JSON.stringify(this.serialize(object));\n  }\n  parse(string) {\n    return this.deserialize(JSON.parse(string));\n  }\n  registerClass(v, options) {\n    this.classRegistry.register(v, options);\n  }\n  registerSymbol(v, identifier) {\n    this.symbolRegistry.register(v, identifier);\n  }\n  registerCustom(transformer, name) {\n    this.customTransformerRegistry.register({\n      name,\n      ...transformer\n    });\n  }\n  allowErrorProps(...props) {\n    this.allowedErrorProps.push(...props);\n  }\n};\nSuperJSON.defaultInstance = new SuperJSON();\nSuperJSON.serialize = SuperJSON.defaultInstance.serialize.bind(SuperJSON.defaultInstance);\nSuperJSON.deserialize = SuperJSON.defaultInstance.deserialize.bind(SuperJSON.defaultInstance);\nSuperJSON.stringify = SuperJSON.defaultInstance.stringify.bind(SuperJSON.defaultInstance);\nSuperJSON.parse = SuperJSON.defaultInstance.parse.bind(SuperJSON.defaultInstance);\nSuperJSON.registerClass = SuperJSON.defaultInstance.registerClass.bind(SuperJSON.defaultInstance);\nSuperJSON.registerSymbol = SuperJSON.defaultInstance.registerSymbol.bind(SuperJSON.defaultInstance);\nSuperJSON.registerCustom = SuperJSON.defaultInstance.registerCustom.bind(SuperJSON.defaultInstance);\nSuperJSON.allowErrorProps = SuperJSON.defaultInstance.allowErrorProps.bind(SuperJSON.defaultInstance);\nvar serialize = SuperJSON.serialize;\nSuperJSON.deserialize;\nvar stringify = SuperJSON.stringify;\nSuperJSON.parse;\nSuperJSON.registerClass;\nSuperJSON.registerCustom;\nSuperJSON.registerSymbol;\nSuperJSON.allowErrorProps;\n\n// src/utils.tsx\nfunction getQueryStatusLabel(query) {\n  return query.state.fetchStatus === \"fetching\" ? \"fetching\" : !query.getObserversCount() ? \"inactive\" : query.state.fetchStatus === \"paused\" ? \"paused\" : query.isStale() ? \"stale\" : \"fresh\";\n}\nfunction getSidedProp(prop, side) {\n  return `${prop}${side.charAt(0).toUpperCase() + side.slice(1)}`;\n}\nfunction getQueryStatusColor({\n  queryState,\n  observerCount,\n  isStale\n}) {\n  return queryState.fetchStatus === \"fetching\" ? \"blue\" : !observerCount ? \"gray\" : queryState.fetchStatus === \"paused\" ? \"purple\" : isStale ? \"yellow\" : \"green\";\n}\nfunction getMutationStatusColor({\n  status,\n  isPaused\n}) {\n  return isPaused ? \"purple\" : status === \"error\" ? \"red\" : status === \"pending\" ? \"yellow\" : status === \"success\" ? \"green\" : \"gray\";\n}\nfunction getQueryStatusColorByLabel(label) {\n  return label === \"fresh\" ? \"green\" : label === \"stale\" ? \"yellow\" : label === \"paused\" ? \"purple\" : label === \"inactive\" ? \"gray\" : \"blue\";\n}\nvar displayValue = (value, beautify = false) => {\n  const {\n    json\n  } = serialize(value);\n  return JSON.stringify(json, null, beautify ? 2 : void 0);\n};\nvar getStatusRank = q => q.state.fetchStatus !== \"idle\" ? 0 : !q.getObserversCount() ? 3 : q.isStale() ? 2 : 1;\nvar queryHashSort = (a, b) => a.queryHash.localeCompare(b.queryHash);\nvar dateSort = (a, b) => a.state.dataUpdatedAt < b.state.dataUpdatedAt ? 1 : -1;\nvar statusAndDateSort = (a, b) => {\n  if (getStatusRank(a) === getStatusRank(b)) {\n    return dateSort(a, b);\n  }\n  return getStatusRank(a) > getStatusRank(b) ? 1 : -1;\n};\nvar sortFns = {\n  status: statusAndDateSort,\n  \"query hash\": queryHashSort,\n  \"last updated\": dateSort\n};\nvar getMutationStatusRank = m => m.state.isPaused ? 0 : m.state.status === \"error\" ? 2 : m.state.status === \"pending\" ? 1 : 3;\nvar mutationDateSort = (a, b) => a.state.submittedAt < b.state.submittedAt ? 1 : -1;\nvar mutationStatusSort = (a, b) => {\n  if (getMutationStatusRank(a) === getMutationStatusRank(b)) {\n    return mutationDateSort(a, b);\n  }\n  return getMutationStatusRank(a) > getMutationStatusRank(b) ? 1 : -1;\n};\nvar mutationSortFns = {\n  status: mutationStatusSort,\n  \"last updated\": mutationDateSort\n};\nvar convertRemToPixels = rem => {\n  return rem * parseFloat(getComputedStyle(document.documentElement).fontSize);\n};\nvar getPreferredColorScheme = () => {\n  const [colorScheme, setColorScheme] = createSignal(\"dark\");\n  onMount(() => {\n    const query = window.matchMedia(\"(prefers-color-scheme: dark)\");\n    setColorScheme(query.matches ? \"dark\" : \"light\");\n    const listener = e => {\n      setColorScheme(e.matches ? \"dark\" : \"light\");\n    };\n    query.addEventListener(\"change\", listener);\n    onCleanup(() => query.removeEventListener(\"change\", listener));\n  });\n  return colorScheme;\n};\nvar updateNestedDataByPath = (oldData, updatePath, value) => {\n  if (updatePath.length === 0) {\n    return value;\n  }\n  if (oldData instanceof Map) {\n    const newData = new Map(oldData);\n    if (updatePath.length === 1) {\n      newData.set(updatePath[0], value);\n      return newData;\n    }\n    const [head, ...tail] = updatePath;\n    newData.set(head, updateNestedDataByPath(newData.get(head), tail, value));\n    return newData;\n  }\n  if (oldData instanceof Set) {\n    const setAsArray = updateNestedDataByPath(Array.from(oldData), updatePath, value);\n    return new Set(setAsArray);\n  }\n  if (Array.isArray(oldData)) {\n    const newData = [...oldData];\n    if (updatePath.length === 1) {\n      newData[updatePath[0]] = value;\n      return newData;\n    }\n    const [head, ...tail] = updatePath;\n    newData[head] = updateNestedDataByPath(newData[head], tail, value);\n    return newData;\n  }\n  if (oldData instanceof Object) {\n    const newData = {\n      ...oldData\n    };\n    if (updatePath.length === 1) {\n      newData[updatePath[0]] = value;\n      return newData;\n    }\n    const [head, ...tail] = updatePath;\n    newData[head] = updateNestedDataByPath(newData[head], tail, value);\n    return newData;\n  }\n  return oldData;\n};\nvar deleteNestedDataByPath = (oldData, deletePath) => {\n  if (oldData instanceof Map) {\n    const newData = new Map(oldData);\n    if (deletePath.length === 1) {\n      newData.delete(deletePath[0]);\n      return newData;\n    }\n    const [head, ...tail] = deletePath;\n    newData.set(head, deleteNestedDataByPath(newData.get(head), tail));\n    return newData;\n  }\n  if (oldData instanceof Set) {\n    const setAsArray = deleteNestedDataByPath(Array.from(oldData), deletePath);\n    return new Set(setAsArray);\n  }\n  if (Array.isArray(oldData)) {\n    const newData = [...oldData];\n    if (deletePath.length === 1) {\n      return newData.filter((_, idx) => idx.toString() !== deletePath[0]);\n    }\n    const [head, ...tail] = deletePath;\n    newData[head] = deleteNestedDataByPath(newData[head], tail);\n    return newData;\n  }\n  if (oldData instanceof Object) {\n    const newData = {\n      ...oldData\n    };\n    if (deletePath.length === 1) {\n      delete newData[deletePath[0]];\n      return newData;\n    }\n    const [head, ...tail] = deletePath;\n    newData[head] = deleteNestedDataByPath(newData[head], tail);\n    return newData;\n  }\n  return oldData;\n};\nvar setupStyleSheet = (nonce, target) => {\n  if (!nonce) return;\n  const styleExists = document.querySelector(\"#_goober\") || target?.querySelector(\"#_goober\");\n  if (styleExists) return;\n  const styleTag = document.createElement(\"style\");\n  const textNode = document.createTextNode(\"\");\n  styleTag.appendChild(textNode);\n  styleTag.id = \"_goober\";\n  styleTag.setAttribute(\"nonce\", nonce);\n  if (target) {\n    target.appendChild(styleTag);\n  } else {\n    document.head.appendChild(styleTag);\n  }\n};\nexport { $TRACK, DEV, Dynamic, For, Index, Match, Portal, Show, Switch, addEventListener, batch, className, clearDelegatedEvents, convertRemToPixels, createComponent, createComputed, createContext, createEffect, createMemo, createRenderEffect, createRoot, createSignal, createUniqueId, delegateEvents, deleteNestedDataByPath, displayValue, getMutationStatusColor, getOwner, getPreferredColorScheme, getQueryStatusColor, getQueryStatusColorByLabel, getQueryStatusLabel, getSidedProp, insert, isServer, lazy, mergeProps, mutationSortFns, on, onCleanup, onMount, render, serialize, setAttribute, setupStyleSheet, sortFns, splitProps, spread, stringify, template, untrack, updateNestedDataByPath, use, useContext, useTransition };", "map": {"version": 3, "names": ["sharedConfig", "context", "registry", "effects", "done", "getContextId", "count", "getNextContextId", "num", "String", "len", "length", "id", "fromCharCode", "setHydrateContext", "nextHydrateContext", "IS_DEV", "equalFn", "a", "b", "$PROXY", "Symbol", "SUPPORTS_PROXY", "Proxy", "$TRACK", "signalOptions", "equals", "ERROR", "runEffects", "runQueue", "STALE", "PENDING", "UNOWNED", "owned", "cleanups", "owner", "NO_INIT", "Owner", "Transition", "Scheduler", "ExternalSourceConfig", "Listener", "Updates", "Effects", "ExecCount", "createRoot", "fn", "detachedOwner", "listener", "unowned", "current", "root", "updateFn", "untrack", "cleanNode", "runUpdates", "createSignal", "value", "options", "Object", "assign", "s", "observers", "observerSlots", "comparator", "setter", "value2", "running", "sources", "has", "tValue", "writeSignal", "readSignal", "bind", "createComputed", "c", "createComputation", "push", "updateComputation", "createRenderEffect", "createEffect", "runUserEffects", "SuspenseContext", "useContext", "suspense", "render", "user", "createMemo", "tState", "isPromise", "v", "createResource", "pSource", "p<PERSON><PERSON><PERSON>", "pOptions", "source", "fetcher", "pr", "initP", "loadedUnderTransition", "scheduled", "resolved", "dynamic", "contexts", "Set", "setValue", "storage", "initialValue", "error", "setError", "track", "trigger", "state", "setState", "ssrLoadFrom", "load", "loadEnd", "p", "error2", "key", "onHydrated", "queueMicrotask", "promises", "delete", "completeLoad", "err", "keys", "decrement", "clear", "read", "add", "increment", "refetching", "lookup", "status", "castE<PERSON>r", "then", "e", "defineProperties", "get", "loading", "latest", "refetch", "mutate", "batch", "on", "deps", "isArray3", "Array", "isArray", "prevInput", "defer", "prevValue", "input", "i", "result", "onMount", "onCleanup", "get<PERSON>wner", "runWithOwner", "o", "prev", "prevListener", "handleError", "startTransition", "l", "Promise", "resolve", "t", "disposed", "queue", "res", "transPending", "setTransPending", "useTransition", "createContext", "defaultValue", "Provider", "createProvider", "children", "children2", "memo", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "toArray", "runningTransition", "updates", "lookUpstream", "sSlot", "sourceSlots", "node", "isComp", "TransitionRunning", "pure", "markDownstream", "Error", "time", "runComputation", "nextValue", "tOwned", "for<PERSON>ach", "updatedAt", "init", "ordinary", "factory", "dispose", "triggerInTransition", "inTransition", "x", "runTop", "inFallback", "ancestors", "top", "wait", "completeUpdates", "scheduleQueue", "size", "apply", "e2", "d", "item", "tasks", "userLength", "slice", "ignore", "pop", "index", "obs", "n", "reset", "cause", "runErrors", "fns", "f", "results", "provider", "props", "FALLBACK", "mapArray", "list", "mapFn", "items", "mapped", "disposers", "indexes", "newItems", "newLen", "j", "newIndices", "newIndicesNext", "temp", "tempdisposers", "tempIndexes", "start", "end", "newEnd", "fallback", "disposer", "mapper", "Math", "min", "Map", "set", "indexArray", "signals", "hydrationEnabled", "createComponent", "Comp", "r", "trueFn", "propTraps", "_", "property", "receiver", "deleteProperty", "getOwnPropertyDescriptor", "configurable", "enumerable", "ownKeys", "resolveSource", "resolveSources", "mergeProps", "proxy", "sourcesMap", "defined", "create", "sourceKeys", "getOwnPropertyNames", "i2", "desc", "sources2", "target", "<PERSON><PERSON><PERSON><PERSON>", "defineProperty", "splitProps", "blocked", "flat", "map", "k", "includes", "filter", "otherObject", "objects", "propName", "isDefaultDesc", "writable", "objectIndex", "lazy", "comp", "wrap", "ctx", "mod", "default", "preload", "counter", "createUniqueId", "narrowedError", "name", "For", "each", "Index", "Show", "keyed", "conditionValue", "when", "condition", "child", "Switch", "chs", "switchFunc", "ch", "mps", "func", "mp", "prevFunc", "sel", "Match", "DEV", "booleans", "Properties", "ChildProperties", "Aliases", "className", "htmlFor", "PropAliases", "class", "formnovalidate", "$", "BUTTON", "INPUT", "ismap", "IMG", "nomodule", "SCRIPT", "playsinline", "VIDEO", "readonly", "TEXTAREA", "getPropAlias", "prop", "tagName", "DelegatedEvents", "SVGElements", "SVGNamespace", "xlink", "xml", "<PERSON><PERSON><PERSON><PERSON>", "parentNode", "b<PERSON><PERSON><PERSON>", "aEnd", "bEnd", "aStart", "bStart", "after", "nextS<PERSON>ling", "insertBefore", "remove", "sequence", "<PERSON><PERSON><PERSON><PERSON>", "$$EVENTS", "code", "element", "dispose2", "document", "insert", "<PERSON><PERSON><PERSON><PERSON>", "textContent", "template", "html", "isImportNode", "isSVG", "isMathML", "createElementNS", "createElement", "innerHTML", "content", "importNode", "cloneNode", "delegateEvents", "eventNames", "document2", "window", "addEventListener", "<PERSON><PERSON><PERSON><PERSON>", "clearDelegatedEvents", "removeEventListener", "setAttribute", "isHydrating", "removeAttribute", "setAttributeNS", "namespace", "removeAttributeNS", "setBoolAttribute", "handler", "delegate", "handlerFn", "call", "classList", "classKeys", "prevKeys", "toggleClassKey", "classValue", "style", "nodeStyle", "cssText", "removeProperty", "setProperty", "spread", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "prevProps", "insertExpression", "ref", "use", "arg", "parent", "accessor", "marker", "initial", "skipRef", "assignProp", "getNextElement", "template2", "hydrating", "getHydrationKey", "completed", "isConnected", "toPropertyName", "toLowerCase", "replace", "w", "toUpperCase", "classNames", "trim", "split", "nameLen", "toggle", "isCE", "isProp", "isChildProp", "<PERSON><PERSON><PERSON><PERSON>", "forceProp", "h", "nodeName", "ns", "indexOf", "events", "find", "el", "ev", "type", "oriTarget", "oriCurrentTarget", "currentTarget", "retarget", "handleNode", "disabled", "data", "cancelBubble", "host", "_$host", "contains", "walkUpTree", "_$HY", "<PERSON><PERSON><PERSON>", "path", "unwrapArray", "childNodes", "cleaned", "nodeType", "multi", "toString", "createTextNode", "clean<PERSON><PERSON><PERSON><PERSON>", "array", "currentArray", "normalizeIncomingArray", "nodes", "appendNodes", "append<PERSON><PERSON><PERSON>", "normalized", "unwrap", "replacement", "inserted", "isParent", "isServer", "SVG_NAMESPACE", "Portal", "useShadow", "mount", "body", "HTMLHeadElement", "clean", "setClean", "cleanup", "container", "renderRoot", "attachShadow", "mode", "<PERSON><PERSON><PERSON><PERSON>", "createDynamic", "component", "cached", "component2", "isSvg", "Dynamic", "others", "DoubleIndexedKV", "constructor", "keyToValue", "valueToKey", "get<PERSON><PERSON><PERSON><PERSON>", "getByValue", "Registry", "generateIdentifier", "kv", "register", "identifier", "getIdentifier", "getValue", "ClassRegistry", "classToAllowedProps", "allowProps", "getAllowedProps", "valuesOfObj", "record", "values", "hasOwnProperty", "predicate", "valuesNotNever", "run", "entries", "arr", "findArr", "CustomTransformerRegistry", "transfomers", "transformer", "findApplicable", "isApplicable", "findByName", "getType", "payload", "prototype", "isUndefined", "isNull", "isPlainObject", "getPrototypeOf", "isEmptyObject", "isString", "isNumber", "isNaN", "isBoolean", "isRegExp", "RegExp", "isMap", "isSet", "isSymbol", "isDate", "Date", "valueOf", "isError", "isNaNValue", "isPrimitive", "isBigint", "isInfinite", "Infinity", "isTypedArray", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "DataView", "isURL", "URL", "<PERSON><PERSON><PERSON>", "stringifyPath", "join", "parsePath", "string", "segment", "char", "char<PERSON>t", "isEscapedDot", "isEndOfSegment", "lastSegment", "simpleTransformation", "annotation", "transform", "untransform", "simpleRules", "BigInt", "console", "toISOString", "superJson", "baseError", "message", "allowedErrorProps", "stack", "regex", "lastIndexOf", "flags", "Number", "compositeTransformation", "symbolRule", "isRegistered", "symbolRegistry", "description", "constructorToName", "Int8Array", "Uint8Array", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "Uint8ClampedArray", "reduce", "obj", "ctor", "typedArrayRule", "isInstanceOfRegisteredClass", "potentialClass", "classRegistry", "classRule", "clazz", "allowedProps", "customRule", "customTransformerRegistry", "serialize", "deserialize", "compositeRules", "transformValue", "applicableCompositeRule", "rule", "applicableSimpleRule", "simpleRulesByAnnotation", "untransformValue", "json", "transformation", "getNthKey", "next", "validatePath", "getDeep", "object", "row", "keyOfRow", "setDeep", "isEnd", "last<PERSON>ey", "oldValue", "newValue", "keyToRow", "new<PERSON>ey", "traverse", "tree", "walker2", "origin", "subtree", "nodeValue", "applyValueAnnotations", "plain", "annotations", "applyReferentialEqualityAnnotations", "identicalPaths", "identicalObjectPath", "other", "identicalPath", "isDeep", "addIdentity", "identities", "existingSet", "generateReferentialEqualityAnnotations", "identitites", "dedupe", "rootEqualityPaths", "paths", "sort", "representative<PERSON><PERSON>", "walker", "objectsInThisPath", "seenObjects", "primitive", "seen", "transformedValue", "transformed2", "result2", "transformationResult", "transformed", "innerAnnotations", "recursiveResult", "getType2", "isArray2", "isPlainObject2", "assignProp2", "carry", "newVal", "originalObject", "includeNonenumerable", "propType", "propertyIsEnumerable", "copy", "symbols", "getOwnPropertySymbols", "val", "nonenumerable", "SuperJSON", "output", "meta", "equalityAnnotations", "referentialEqualities", "stringify", "JSON", "parse", "registerClass", "registerSymbol", "registerCustom", "allowErrorProps", "defaultInstance", "getQueryStatusLabel", "query", "fetchStatus", "getObserversCount", "isStale", "getSidedProp", "side", "getQueryStatusColor", "queryState", "observerCount", "getMutationStatusColor", "isPaused", "getQueryStatusColorByLabel", "label", "displayValue", "beautify", "getStatusRank", "q", "queryHashSort", "queryHash", "localeCompare", "dateSort", "dataUpdatedAt", "statusAndDateSort", "sortFns", "getMutationStatusRank", "m", "mutationDateSort", "submittedAt", "mutationStatusSort", "mutationSortFns", "convertRemToPixels", "rem", "parseFloat", "getComputedStyle", "documentElement", "fontSize", "getPreferredColorScheme", "colorScheme", "setColorScheme", "matchMedia", "matches", "updateNestedDataByPath", "oldData", "updatePath", "newData", "head", "tail", "setAsArray", "from", "deleteNestedDataByPath", "deletePath", "idx", "setupStyleSheet", "nonce", "styleExists", "querySelector", "styleTag", "textNode"], "sources": ["/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/node_modules/@tanstack/query-devtools/build/chunk/V5T5VJKG.js"], "sourcesContent": ["// ../../node_modules/.pnpm/solid-js@1.9.5/node_modules/solid-js/dist/solid.js\nvar sharedConfig = {\n  context: void 0,\n  registry: void 0,\n  effects: void 0,\n  done: false,\n  getContextId() {\n    return getContextId(this.context.count);\n  },\n  getNextContextId() {\n    return getContextId(this.context.count++);\n  }\n};\nfunction getContextId(count) {\n  const num = String(count), len = num.length - 1;\n  return sharedConfig.context.id + (len ? String.fromCharCode(96 + len) : \"\") + num;\n}\nfunction setHydrateContext(context) {\n  sharedConfig.context = context;\n}\nfunction nextHydrateContext() {\n  return {\n    ...sharedConfig.context,\n    id: sharedConfig.getNextContextId(),\n    count: 0\n  };\n}\nvar IS_DEV = false;\nvar equalFn = (a, b) => a === b;\nvar $PROXY = Symbol(\"solid-proxy\");\nvar SUPPORTS_PROXY = typeof Proxy === \"function\";\nvar $TRACK = Symbol(\"solid-track\");\nvar signalOptions = {\n  equals: equalFn\n};\nvar ERROR = null;\nvar runEffects = runQueue;\nvar STALE = 1;\nvar PENDING = 2;\nvar UNOWNED = {\n  owned: null,\n  cleanups: null,\n  context: null,\n  owner: null\n};\nvar NO_INIT = {};\nvar Owner = null;\nvar Transition = null;\nvar Scheduler = null;\nvar ExternalSourceConfig = null;\nvar Listener = null;\nvar Updates = null;\nvar Effects = null;\nvar ExecCount = 0;\nfunction createRoot(fn, detachedOwner) {\n  const listener = Listener, owner = Owner, unowned = fn.length === 0, current = detachedOwner === void 0 ? owner : detachedOwner, root = unowned ? UNOWNED : {\n    owned: null,\n    cleanups: null,\n    context: current ? current.context : null,\n    owner: current\n  }, updateFn = unowned ? fn : () => fn(() => untrack(() => cleanNode(root)));\n  Owner = root;\n  Listener = null;\n  try {\n    return runUpdates(updateFn, true);\n  } finally {\n    Listener = listener;\n    Owner = owner;\n  }\n}\nfunction createSignal(value, options) {\n  options = options ? Object.assign({}, signalOptions, options) : signalOptions;\n  const s = {\n    value,\n    observers: null,\n    observerSlots: null,\n    comparator: options.equals || void 0\n  };\n  const setter = (value2) => {\n    if (typeof value2 === \"function\") {\n      if (Transition && Transition.running && Transition.sources.has(s)) value2 = value2(s.tValue);\n      else value2 = value2(s.value);\n    }\n    return writeSignal(s, value2);\n  };\n  return [readSignal.bind(s), setter];\n}\nfunction createComputed(fn, value, options) {\n  const c = createComputation(fn, value, true, STALE);\n  if (Scheduler && Transition && Transition.running) Updates.push(c);\n  else updateComputation(c);\n}\nfunction createRenderEffect(fn, value, options) {\n  const c = createComputation(fn, value, false, STALE);\n  if (Scheduler && Transition && Transition.running) Updates.push(c);\n  else updateComputation(c);\n}\nfunction createEffect(fn, value, options) {\n  runEffects = runUserEffects;\n  const c = createComputation(fn, value, false, STALE), s = SuspenseContext && useContext(SuspenseContext);\n  if (s) c.suspense = s;\n  if (!options || !options.render) c.user = true;\n  Effects ? Effects.push(c) : updateComputation(c);\n}\nfunction createMemo(fn, value, options) {\n  options = options ? Object.assign({}, signalOptions, options) : signalOptions;\n  const c = createComputation(fn, value, true, 0);\n  c.observers = null;\n  c.observerSlots = null;\n  c.comparator = options.equals || void 0;\n  if (Scheduler && Transition && Transition.running) {\n    c.tState = STALE;\n    Updates.push(c);\n  } else updateComputation(c);\n  return readSignal.bind(c);\n}\nfunction isPromise(v) {\n  return v && typeof v === \"object\" && \"then\" in v;\n}\nfunction createResource(pSource, pFetcher, pOptions) {\n  let source;\n  let fetcher;\n  let options;\n  {\n    source = true;\n    fetcher = pSource;\n    options = {};\n  }\n  let pr = null, initP = NO_INIT, id = null, loadedUnderTransition = false, scheduled = false, resolved = \"initialValue\" in options, dynamic = typeof source === \"function\" && createMemo(source);\n  const contexts = /* @__PURE__ */ new Set(), [value, setValue] = (options.storage || createSignal)(options.initialValue), [error, setError] = createSignal(void 0), [track, trigger] = createSignal(void 0, {\n    equals: false\n  }), [state, setState] = createSignal(resolved ? \"ready\" : \"unresolved\");\n  if (sharedConfig.context) {\n    id = sharedConfig.getNextContextId();\n    if (options.ssrLoadFrom === \"initial\") initP = options.initialValue;\n    else if (sharedConfig.load && sharedConfig.has(id)) initP = sharedConfig.load(id);\n  }\n  function loadEnd(p, v, error2, key) {\n    if (pr === p) {\n      pr = null;\n      key !== void 0 && (resolved = true);\n      if ((p === initP || v === initP) && options.onHydrated)\n        queueMicrotask(\n          () => options.onHydrated(key, {\n            value: v\n          })\n        );\n      initP = NO_INIT;\n      if (Transition && p && loadedUnderTransition) {\n        Transition.promises.delete(p);\n        loadedUnderTransition = false;\n        runUpdates(() => {\n          Transition.running = true;\n          completeLoad(v, error2);\n        }, false);\n      } else completeLoad(v, error2);\n    }\n    return v;\n  }\n  function completeLoad(v, err) {\n    runUpdates(() => {\n      if (err === void 0) setValue(() => v);\n      setState(err !== void 0 ? \"errored\" : resolved ? \"ready\" : \"unresolved\");\n      setError(err);\n      for (const c of contexts.keys()) c.decrement();\n      contexts.clear();\n    }, false);\n  }\n  function read() {\n    const c = SuspenseContext && useContext(SuspenseContext), v = value(), err = error();\n    if (err !== void 0 && !pr) throw err;\n    if (Listener && !Listener.user && c) {\n      createComputed(() => {\n        track();\n        if (pr) {\n          if (c.resolved && Transition && loadedUnderTransition) Transition.promises.add(pr);\n          else if (!contexts.has(c)) {\n            c.increment();\n            contexts.add(c);\n          }\n        }\n      });\n    }\n    return v;\n  }\n  function load(refetching = true) {\n    if (refetching !== false && scheduled) return;\n    scheduled = false;\n    const lookup = dynamic ? dynamic() : source;\n    loadedUnderTransition = Transition && Transition.running;\n    if (lookup == null || lookup === false) {\n      loadEnd(pr, untrack(value));\n      return;\n    }\n    if (Transition && pr) Transition.promises.delete(pr);\n    const p = initP !== NO_INIT ? initP : untrack(\n      () => fetcher(lookup, {\n        value: value(),\n        refetching\n      })\n    );\n    if (!isPromise(p)) {\n      loadEnd(pr, p, void 0, lookup);\n      return p;\n    }\n    pr = p;\n    if (\"value\" in p) {\n      if (p.status === \"success\") loadEnd(pr, p.value, void 0, lookup);\n      else loadEnd(pr, void 0, castError(p.value), lookup);\n      return p;\n    }\n    scheduled = true;\n    queueMicrotask(() => scheduled = false);\n    runUpdates(() => {\n      setState(resolved ? \"refreshing\" : \"pending\");\n      trigger();\n    }, false);\n    return p.then(\n      (v) => loadEnd(p, v, void 0, lookup),\n      (e) => loadEnd(p, void 0, castError(e), lookup)\n    );\n  }\n  Object.defineProperties(read, {\n    state: {\n      get: () => state()\n    },\n    error: {\n      get: () => error()\n    },\n    loading: {\n      get() {\n        const s = state();\n        return s === \"pending\" || s === \"refreshing\";\n      }\n    },\n    latest: {\n      get() {\n        if (!resolved) return read();\n        const err = error();\n        if (err && !pr) throw err;\n        return value();\n      }\n    }\n  });\n  if (dynamic) createComputed(() => load(false));\n  else load(false);\n  return [\n    read,\n    {\n      refetch: load,\n      mutate: setValue\n    }\n  ];\n}\nfunction batch(fn) {\n  return runUpdates(fn, false);\n}\nfunction untrack(fn) {\n  if (!ExternalSourceConfig && Listener === null) return fn();\n  const listener = Listener;\n  Listener = null;\n  try {\n    if (ExternalSourceConfig) return ExternalSourceConfig.untrack(fn);\n    return fn();\n  } finally {\n    Listener = listener;\n  }\n}\nfunction on(deps, fn, options) {\n  const isArray3 = Array.isArray(deps);\n  let prevInput;\n  let defer = options && options.defer;\n  return (prevValue) => {\n    let input;\n    if (isArray3) {\n      input = Array(deps.length);\n      for (let i = 0; i < deps.length; i++) input[i] = deps[i]();\n    } else input = deps();\n    if (defer) {\n      defer = false;\n      return prevValue;\n    }\n    const result = untrack(() => fn(input, prevInput, prevValue));\n    prevInput = input;\n    return result;\n  };\n}\nfunction onMount(fn) {\n  createEffect(() => untrack(fn));\n}\nfunction onCleanup(fn) {\n  if (Owner === null) ;\n  else if (Owner.cleanups === null) Owner.cleanups = [fn];\n  else Owner.cleanups.push(fn);\n  return fn;\n}\nfunction getOwner() {\n  return Owner;\n}\nfunction runWithOwner(o, fn) {\n  const prev = Owner;\n  const prevListener = Listener;\n  Owner = o;\n  Listener = null;\n  try {\n    return runUpdates(fn, true);\n  } catch (err) {\n    handleError(err);\n  } finally {\n    Owner = prev;\n    Listener = prevListener;\n  }\n}\nfunction startTransition(fn) {\n  if (Transition && Transition.running) {\n    fn();\n    return Transition.done;\n  }\n  const l = Listener;\n  const o = Owner;\n  return Promise.resolve().then(() => {\n    Listener = l;\n    Owner = o;\n    let t;\n    if (Scheduler || SuspenseContext) {\n      t = Transition || (Transition = {\n        sources: /* @__PURE__ */ new Set(),\n        effects: [],\n        promises: /* @__PURE__ */ new Set(),\n        disposed: /* @__PURE__ */ new Set(),\n        queue: /* @__PURE__ */ new Set(),\n        running: true\n      });\n      t.done || (t.done = new Promise((res) => t.resolve = res));\n      t.running = true;\n    }\n    runUpdates(fn, false);\n    Listener = Owner = null;\n    return t ? t.done : void 0;\n  });\n}\nvar [transPending, setTransPending] = /* @__PURE__ */ createSignal(false);\nfunction useTransition() {\n  return [transPending, startTransition];\n}\nfunction createContext(defaultValue, options) {\n  const id = Symbol(\"context\");\n  return {\n    id,\n    Provider: createProvider(id),\n    defaultValue\n  };\n}\nfunction useContext(context) {\n  let value;\n  return Owner && Owner.context && (value = Owner.context[context.id]) !== void 0 ? value : context.defaultValue;\n}\nfunction children(fn) {\n  const children2 = createMemo(fn);\n  const memo = createMemo(() => resolveChildren(children2()));\n  memo.toArray = () => {\n    const c = memo();\n    return Array.isArray(c) ? c : c != null ? [c] : [];\n  };\n  return memo;\n}\nvar SuspenseContext;\nfunction readSignal() {\n  const runningTransition = Transition && Transition.running;\n  if (this.sources && (runningTransition ? this.tState : this.state)) {\n    if ((runningTransition ? this.tState : this.state) === STALE) updateComputation(this);\n    else {\n      const updates = Updates;\n      Updates = null;\n      runUpdates(() => lookUpstream(this), false);\n      Updates = updates;\n    }\n  }\n  if (Listener) {\n    const sSlot = this.observers ? this.observers.length : 0;\n    if (!Listener.sources) {\n      Listener.sources = [this];\n      Listener.sourceSlots = [sSlot];\n    } else {\n      Listener.sources.push(this);\n      Listener.sourceSlots.push(sSlot);\n    }\n    if (!this.observers) {\n      this.observers = [Listener];\n      this.observerSlots = [Listener.sources.length - 1];\n    } else {\n      this.observers.push(Listener);\n      this.observerSlots.push(Listener.sources.length - 1);\n    }\n  }\n  if (runningTransition && Transition.sources.has(this)) return this.tValue;\n  return this.value;\n}\nfunction writeSignal(node, value, isComp) {\n  let current = Transition && Transition.running && Transition.sources.has(node) ? node.tValue : node.value;\n  if (!node.comparator || !node.comparator(current, value)) {\n    if (Transition) {\n      const TransitionRunning = Transition.running;\n      if (TransitionRunning || !isComp && Transition.sources.has(node)) {\n        Transition.sources.add(node);\n        node.tValue = value;\n      }\n      if (!TransitionRunning) node.value = value;\n    } else node.value = value;\n    if (node.observers && node.observers.length) {\n      runUpdates(() => {\n        for (let i = 0; i < node.observers.length; i += 1) {\n          const o = node.observers[i];\n          const TransitionRunning = Transition && Transition.running;\n          if (TransitionRunning && Transition.disposed.has(o)) continue;\n          if (TransitionRunning ? !o.tState : !o.state) {\n            if (o.pure) Updates.push(o);\n            else Effects.push(o);\n            if (o.observers) markDownstream(o);\n          }\n          if (!TransitionRunning) o.state = STALE;\n          else o.tState = STALE;\n        }\n        if (Updates.length > 1e6) {\n          Updates = [];\n          if (IS_DEV) ;\n          throw new Error();\n        }\n      }, false);\n    }\n  }\n  return value;\n}\nfunction updateComputation(node) {\n  if (!node.fn) return;\n  cleanNode(node);\n  const time = ExecCount;\n  runComputation(\n    node,\n    Transition && Transition.running && Transition.sources.has(node) ? node.tValue : node.value,\n    time\n  );\n  if (Transition && !Transition.running && Transition.sources.has(node)) {\n    queueMicrotask(() => {\n      runUpdates(() => {\n        Transition && (Transition.running = true);\n        Listener = Owner = node;\n        runComputation(node, node.tValue, time);\n        Listener = Owner = null;\n      }, false);\n    });\n  }\n}\nfunction runComputation(node, value, time) {\n  let nextValue;\n  const owner = Owner, listener = Listener;\n  Listener = Owner = node;\n  try {\n    nextValue = node.fn(value);\n  } catch (err) {\n    if (node.pure) {\n      if (Transition && Transition.running) {\n        node.tState = STALE;\n        node.tOwned && node.tOwned.forEach(cleanNode);\n        node.tOwned = void 0;\n      } else {\n        node.state = STALE;\n        node.owned && node.owned.forEach(cleanNode);\n        node.owned = null;\n      }\n    }\n    node.updatedAt = time + 1;\n    return handleError(err);\n  } finally {\n    Listener = listener;\n    Owner = owner;\n  }\n  if (!node.updatedAt || node.updatedAt <= time) {\n    if (node.updatedAt != null && \"observers\" in node) {\n      writeSignal(node, nextValue, true);\n    } else if (Transition && Transition.running && node.pure) {\n      Transition.sources.add(node);\n      node.tValue = nextValue;\n    } else node.value = nextValue;\n    node.updatedAt = time;\n  }\n}\nfunction createComputation(fn, init, pure, state = STALE, options) {\n  const c = {\n    fn,\n    state,\n    updatedAt: null,\n    owned: null,\n    sources: null,\n    sourceSlots: null,\n    cleanups: null,\n    value: init,\n    owner: Owner,\n    context: Owner ? Owner.context : null,\n    pure\n  };\n  if (Transition && Transition.running) {\n    c.state = 0;\n    c.tState = state;\n  }\n  if (Owner === null) ;\n  else if (Owner !== UNOWNED) {\n    if (Transition && Transition.running && Owner.pure) {\n      if (!Owner.tOwned) Owner.tOwned = [c];\n      else Owner.tOwned.push(c);\n    } else {\n      if (!Owner.owned) Owner.owned = [c];\n      else Owner.owned.push(c);\n    }\n  }\n  if (ExternalSourceConfig && c.fn) {\n    const [track, trigger] = createSignal(void 0, {\n      equals: false\n    });\n    const ordinary = ExternalSourceConfig.factory(c.fn, trigger);\n    onCleanup(() => ordinary.dispose());\n    const triggerInTransition = () => startTransition(trigger).then(() => inTransition.dispose());\n    const inTransition = ExternalSourceConfig.factory(c.fn, triggerInTransition);\n    c.fn = (x) => {\n      track();\n      return Transition && Transition.running ? inTransition.track(x) : ordinary.track(x);\n    };\n  }\n  return c;\n}\nfunction runTop(node) {\n  const runningTransition = Transition && Transition.running;\n  if ((runningTransition ? node.tState : node.state) === 0) return;\n  if ((runningTransition ? node.tState : node.state) === PENDING) return lookUpstream(node);\n  if (node.suspense && untrack(node.suspense.inFallback)) return node.suspense.effects.push(node);\n  const ancestors = [node];\n  while ((node = node.owner) && (!node.updatedAt || node.updatedAt < ExecCount)) {\n    if (runningTransition && Transition.disposed.has(node)) return;\n    if (runningTransition ? node.tState : node.state) ancestors.push(node);\n  }\n  for (let i = ancestors.length - 1; i >= 0; i--) {\n    node = ancestors[i];\n    if (runningTransition) {\n      let top = node, prev = ancestors[i + 1];\n      while ((top = top.owner) && top !== prev) {\n        if (Transition.disposed.has(top)) return;\n      }\n    }\n    if ((runningTransition ? node.tState : node.state) === STALE) {\n      updateComputation(node);\n    } else if ((runningTransition ? node.tState : node.state) === PENDING) {\n      const updates = Updates;\n      Updates = null;\n      runUpdates(() => lookUpstream(node, ancestors[0]), false);\n      Updates = updates;\n    }\n  }\n}\nfunction runUpdates(fn, init) {\n  if (Updates) return fn();\n  let wait = false;\n  if (!init) Updates = [];\n  if (Effects) wait = true;\n  else Effects = [];\n  ExecCount++;\n  try {\n    const res = fn();\n    completeUpdates(wait);\n    return res;\n  } catch (err) {\n    if (!wait) Effects = null;\n    Updates = null;\n    handleError(err);\n  }\n}\nfunction completeUpdates(wait) {\n  if (Updates) {\n    if (Scheduler && Transition && Transition.running) scheduleQueue(Updates);\n    else runQueue(Updates);\n    Updates = null;\n  }\n  if (wait) return;\n  let res;\n  if (Transition) {\n    if (!Transition.promises.size && !Transition.queue.size) {\n      const sources = Transition.sources;\n      const disposed = Transition.disposed;\n      Effects.push.apply(Effects, Transition.effects);\n      res = Transition.resolve;\n      for (const e2 of Effects) {\n        \"tState\" in e2 && (e2.state = e2.tState);\n        delete e2.tState;\n      }\n      Transition = null;\n      runUpdates(() => {\n        for (const d of disposed) cleanNode(d);\n        for (const v of sources) {\n          v.value = v.tValue;\n          if (v.owned) {\n            for (let i = 0, len = v.owned.length; i < len; i++) cleanNode(v.owned[i]);\n          }\n          if (v.tOwned) v.owned = v.tOwned;\n          delete v.tValue;\n          delete v.tOwned;\n          v.tState = 0;\n        }\n        setTransPending(false);\n      }, false);\n    } else if (Transition.running) {\n      Transition.running = false;\n      Transition.effects.push.apply(Transition.effects, Effects);\n      Effects = null;\n      setTransPending(true);\n      return;\n    }\n  }\n  const e = Effects;\n  Effects = null;\n  if (e.length) runUpdates(() => runEffects(e), false);\n  if (res) res();\n}\nfunction runQueue(queue) {\n  for (let i = 0; i < queue.length; i++) runTop(queue[i]);\n}\nfunction scheduleQueue(queue) {\n  for (let i = 0; i < queue.length; i++) {\n    const item = queue[i];\n    const tasks = Transition.queue;\n    if (!tasks.has(item)) {\n      tasks.add(item);\n      Scheduler(() => {\n        tasks.delete(item);\n        runUpdates(() => {\n          Transition.running = true;\n          runTop(item);\n        }, false);\n        Transition && (Transition.running = false);\n      });\n    }\n  }\n}\nfunction runUserEffects(queue) {\n  let i, userLength = 0;\n  for (i = 0; i < queue.length; i++) {\n    const e = queue[i];\n    if (!e.user) runTop(e);\n    else queue[userLength++] = e;\n  }\n  if (sharedConfig.context) {\n    if (sharedConfig.count) {\n      sharedConfig.effects || (sharedConfig.effects = []);\n      sharedConfig.effects.push(...queue.slice(0, userLength));\n      return;\n    }\n    setHydrateContext();\n  }\n  if (sharedConfig.effects && (sharedConfig.done || !sharedConfig.count)) {\n    queue = [...sharedConfig.effects, ...queue];\n    userLength += sharedConfig.effects.length;\n    delete sharedConfig.effects;\n  }\n  for (i = 0; i < userLength; i++) runTop(queue[i]);\n}\nfunction lookUpstream(node, ignore) {\n  const runningTransition = Transition && Transition.running;\n  if (runningTransition) node.tState = 0;\n  else node.state = 0;\n  for (let i = 0; i < node.sources.length; i += 1) {\n    const source = node.sources[i];\n    if (source.sources) {\n      const state = runningTransition ? source.tState : source.state;\n      if (state === STALE) {\n        if (source !== ignore && (!source.updatedAt || source.updatedAt < ExecCount))\n          runTop(source);\n      } else if (state === PENDING) lookUpstream(source, ignore);\n    }\n  }\n}\nfunction markDownstream(node) {\n  const runningTransition = Transition && Transition.running;\n  for (let i = 0; i < node.observers.length; i += 1) {\n    const o = node.observers[i];\n    if (runningTransition ? !o.tState : !o.state) {\n      if (runningTransition) o.tState = PENDING;\n      else o.state = PENDING;\n      if (o.pure) Updates.push(o);\n      else Effects.push(o);\n      o.observers && markDownstream(o);\n    }\n  }\n}\nfunction cleanNode(node) {\n  let i;\n  if (node.sources) {\n    while (node.sources.length) {\n      const source = node.sources.pop(), index = node.sourceSlots.pop(), obs = source.observers;\n      if (obs && obs.length) {\n        const n = obs.pop(), s = source.observerSlots.pop();\n        if (index < obs.length) {\n          n.sourceSlots[s] = index;\n          obs[index] = n;\n          source.observerSlots[index] = s;\n        }\n      }\n    }\n  }\n  if (node.tOwned) {\n    for (i = node.tOwned.length - 1; i >= 0; i--) cleanNode(node.tOwned[i]);\n    delete node.tOwned;\n  }\n  if (Transition && Transition.running && node.pure) {\n    reset(node, true);\n  } else if (node.owned) {\n    for (i = node.owned.length - 1; i >= 0; i--) cleanNode(node.owned[i]);\n    node.owned = null;\n  }\n  if (node.cleanups) {\n    for (i = node.cleanups.length - 1; i >= 0; i--) node.cleanups[i]();\n    node.cleanups = null;\n  }\n  if (Transition && Transition.running) node.tState = 0;\n  else node.state = 0;\n}\nfunction reset(node, top) {\n  if (!top) {\n    node.tState = 0;\n    Transition.disposed.add(node);\n  }\n  if (node.owned) {\n    for (let i = 0; i < node.owned.length; i++) reset(node.owned[i]);\n  }\n}\nfunction castError(err) {\n  if (err instanceof Error) return err;\n  return new Error(typeof err === \"string\" ? err : \"Unknown error\", {\n    cause: err\n  });\n}\nfunction runErrors(err, fns, owner) {\n  try {\n    for (const f of fns) f(err);\n  } catch (e) {\n    handleError(e, owner && owner.owner || null);\n  }\n}\nfunction handleError(err, owner = Owner) {\n  const fns = ERROR && owner && owner.context && owner.context[ERROR];\n  const error = castError(err);\n  if (!fns) throw error;\n  if (Effects)\n    Effects.push({\n      fn() {\n        runErrors(error, fns, owner);\n      },\n      state: STALE\n    });\n  else runErrors(error, fns, owner);\n}\nfunction resolveChildren(children2) {\n  if (typeof children2 === \"function\" && !children2.length) return resolveChildren(children2());\n  if (Array.isArray(children2)) {\n    const results = [];\n    for (let i = 0; i < children2.length; i++) {\n      const result = resolveChildren(children2[i]);\n      Array.isArray(result) ? results.push.apply(results, result) : results.push(result);\n    }\n    return results;\n  }\n  return children2;\n}\nfunction createProvider(id, options) {\n  return function provider(props) {\n    let res;\n    createRenderEffect(\n      () => res = untrack(() => {\n        Owner.context = {\n          ...Owner.context,\n          [id]: props.value\n        };\n        return children(() => props.children);\n      }),\n      void 0\n    );\n    return res;\n  };\n}\nvar FALLBACK = Symbol(\"fallback\");\nfunction dispose(d) {\n  for (let i = 0; i < d.length; i++) d[i]();\n}\nfunction mapArray(list, mapFn, options = {}) {\n  let items = [], mapped = [], disposers = [], len = 0, indexes = mapFn.length > 1 ? [] : null;\n  onCleanup(() => dispose(disposers));\n  return () => {\n    let newItems = list() || [], newLen = newItems.length, i, j;\n    newItems[$TRACK];\n    return untrack(() => {\n      let newIndices, newIndicesNext, temp, tempdisposers, tempIndexes, start, end, newEnd, item;\n      if (newLen === 0) {\n        if (len !== 0) {\n          dispose(disposers);\n          disposers = [];\n          items = [];\n          mapped = [];\n          len = 0;\n          indexes && (indexes = []);\n        }\n        if (options.fallback) {\n          items = [FALLBACK];\n          mapped[0] = createRoot((disposer) => {\n            disposers[0] = disposer;\n            return options.fallback();\n          });\n          len = 1;\n        }\n      } else if (len === 0) {\n        mapped = new Array(newLen);\n        for (j = 0; j < newLen; j++) {\n          items[j] = newItems[j];\n          mapped[j] = createRoot(mapper);\n        }\n        len = newLen;\n      } else {\n        temp = new Array(newLen);\n        tempdisposers = new Array(newLen);\n        indexes && (tempIndexes = new Array(newLen));\n        for (start = 0, end = Math.min(len, newLen); start < end && items[start] === newItems[start]; start++) ;\n        for (end = len - 1, newEnd = newLen - 1; end >= start && newEnd >= start && items[end] === newItems[newEnd]; end--, newEnd--) {\n          temp[newEnd] = mapped[end];\n          tempdisposers[newEnd] = disposers[end];\n          indexes && (tempIndexes[newEnd] = indexes[end]);\n        }\n        newIndices = /* @__PURE__ */ new Map();\n        newIndicesNext = new Array(newEnd + 1);\n        for (j = newEnd; j >= start; j--) {\n          item = newItems[j];\n          i = newIndices.get(item);\n          newIndicesNext[j] = i === void 0 ? -1 : i;\n          newIndices.set(item, j);\n        }\n        for (i = start; i <= end; i++) {\n          item = items[i];\n          j = newIndices.get(item);\n          if (j !== void 0 && j !== -1) {\n            temp[j] = mapped[i];\n            tempdisposers[j] = disposers[i];\n            indexes && (tempIndexes[j] = indexes[i]);\n            j = newIndicesNext[j];\n            newIndices.set(item, j);\n          } else disposers[i]();\n        }\n        for (j = start; j < newLen; j++) {\n          if (j in temp) {\n            mapped[j] = temp[j];\n            disposers[j] = tempdisposers[j];\n            if (indexes) {\n              indexes[j] = tempIndexes[j];\n              indexes[j](j);\n            }\n          } else mapped[j] = createRoot(mapper);\n        }\n        mapped = mapped.slice(0, len = newLen);\n        items = newItems.slice(0);\n      }\n      return mapped;\n    });\n    function mapper(disposer) {\n      disposers[j] = disposer;\n      if (indexes) {\n        const [s, set] = createSignal(j);\n        indexes[j] = set;\n        return mapFn(newItems[j], s);\n      }\n      return mapFn(newItems[j]);\n    }\n  };\n}\nfunction indexArray(list, mapFn, options = {}) {\n  let items = [], mapped = [], disposers = [], signals = [], len = 0, i;\n  onCleanup(() => dispose(disposers));\n  return () => {\n    const newItems = list() || [], newLen = newItems.length;\n    newItems[$TRACK];\n    return untrack(() => {\n      if (newLen === 0) {\n        if (len !== 0) {\n          dispose(disposers);\n          disposers = [];\n          items = [];\n          mapped = [];\n          len = 0;\n          signals = [];\n        }\n        if (options.fallback) {\n          items = [FALLBACK];\n          mapped[0] = createRoot((disposer) => {\n            disposers[0] = disposer;\n            return options.fallback();\n          });\n          len = 1;\n        }\n        return mapped;\n      }\n      if (items[0] === FALLBACK) {\n        disposers[0]();\n        disposers = [];\n        items = [];\n        mapped = [];\n        len = 0;\n      }\n      for (i = 0; i < newLen; i++) {\n        if (i < items.length && items[i] !== newItems[i]) {\n          signals[i](() => newItems[i]);\n        } else if (i >= items.length) {\n          mapped[i] = createRoot(mapper);\n        }\n      }\n      for (; i < items.length; i++) {\n        disposers[i]();\n      }\n      len = signals.length = disposers.length = newLen;\n      items = newItems.slice(0);\n      return mapped = mapped.slice(0, len);\n    });\n    function mapper(disposer) {\n      disposers[i] = disposer;\n      const [s, set] = createSignal(newItems[i]);\n      signals[i] = set;\n      return mapFn(s, i);\n    }\n  };\n}\nvar hydrationEnabled = false;\nfunction createComponent(Comp, props) {\n  if (hydrationEnabled) {\n    if (sharedConfig.context) {\n      const c = sharedConfig.context;\n      setHydrateContext(nextHydrateContext());\n      const r = untrack(() => Comp(props || {}));\n      setHydrateContext(c);\n      return r;\n    }\n  }\n  return untrack(() => Comp(props || {}));\n}\nfunction trueFn() {\n  return true;\n}\nvar propTraps = {\n  get(_, property, receiver) {\n    if (property === $PROXY) return receiver;\n    return _.get(property);\n  },\n  has(_, property) {\n    if (property === $PROXY) return true;\n    return _.has(property);\n  },\n  set: trueFn,\n  deleteProperty: trueFn,\n  getOwnPropertyDescriptor(_, property) {\n    return {\n      configurable: true,\n      enumerable: true,\n      get() {\n        return _.get(property);\n      },\n      set: trueFn,\n      deleteProperty: trueFn\n    };\n  },\n  ownKeys(_) {\n    return _.keys();\n  }\n};\nfunction resolveSource(s) {\n  return !(s = typeof s === \"function\" ? s() : s) ? {} : s;\n}\nfunction resolveSources() {\n  for (let i = 0, length = this.length; i < length; ++i) {\n    const v = this[i]();\n    if (v !== void 0) return v;\n  }\n}\nfunction mergeProps(...sources) {\n  let proxy = false;\n  for (let i = 0; i < sources.length; i++) {\n    const s = sources[i];\n    proxy = proxy || !!s && $PROXY in s;\n    sources[i] = typeof s === \"function\" ? (proxy = true, createMemo(s)) : s;\n  }\n  if (SUPPORTS_PROXY && proxy) {\n    return new Proxy(\n      {\n        get(property) {\n          for (let i = sources.length - 1; i >= 0; i--) {\n            const v = resolveSource(sources[i])[property];\n            if (v !== void 0) return v;\n          }\n        },\n        has(property) {\n          for (let i = sources.length - 1; i >= 0; i--) {\n            if (property in resolveSource(sources[i])) return true;\n          }\n          return false;\n        },\n        keys() {\n          const keys = [];\n          for (let i = 0; i < sources.length; i++)\n            keys.push(...Object.keys(resolveSource(sources[i])));\n          return [...new Set(keys)];\n        }\n      },\n      propTraps\n    );\n  }\n  const sourcesMap = {};\n  const defined = /* @__PURE__ */ Object.create(null);\n  for (let i = sources.length - 1; i >= 0; i--) {\n    const source = sources[i];\n    if (!source) continue;\n    const sourceKeys = Object.getOwnPropertyNames(source);\n    for (let i2 = sourceKeys.length - 1; i2 >= 0; i2--) {\n      const key = sourceKeys[i2];\n      if (key === \"__proto__\" || key === \"constructor\") continue;\n      const desc = Object.getOwnPropertyDescriptor(source, key);\n      if (!defined[key]) {\n        defined[key] = desc.get ? {\n          enumerable: true,\n          configurable: true,\n          get: resolveSources.bind(sourcesMap[key] = [desc.get.bind(source)])\n        } : desc.value !== void 0 ? desc : void 0;\n      } else {\n        const sources2 = sourcesMap[key];\n        if (sources2) {\n          if (desc.get) sources2.push(desc.get.bind(source));\n          else if (desc.value !== void 0) sources2.push(() => desc.value);\n        }\n      }\n    }\n  }\n  const target = {};\n  const definedKeys = Object.keys(defined);\n  for (let i = definedKeys.length - 1; i >= 0; i--) {\n    const key = definedKeys[i], desc = defined[key];\n    if (desc && desc.get) Object.defineProperty(target, key, desc);\n    else target[key] = desc ? desc.value : void 0;\n  }\n  return target;\n}\nfunction splitProps(props, ...keys) {\n  if (SUPPORTS_PROXY && $PROXY in props) {\n    const blocked = new Set(keys.length > 1 ? keys.flat() : keys[0]);\n    const res = keys.map((k) => {\n      return new Proxy(\n        {\n          get(property) {\n            return k.includes(property) ? props[property] : void 0;\n          },\n          has(property) {\n            return k.includes(property) && property in props;\n          },\n          keys() {\n            return k.filter((property) => property in props);\n          }\n        },\n        propTraps\n      );\n    });\n    res.push(\n      new Proxy(\n        {\n          get(property) {\n            return blocked.has(property) ? void 0 : props[property];\n          },\n          has(property) {\n            return blocked.has(property) ? false : property in props;\n          },\n          keys() {\n            return Object.keys(props).filter((k) => !blocked.has(k));\n          }\n        },\n        propTraps\n      )\n    );\n    return res;\n  }\n  const otherObject = {};\n  const objects = keys.map(() => ({}));\n  for (const propName of Object.getOwnPropertyNames(props)) {\n    const desc = Object.getOwnPropertyDescriptor(props, propName);\n    const isDefaultDesc = !desc.get && !desc.set && desc.enumerable && desc.writable && desc.configurable;\n    let blocked = false;\n    let objectIndex = 0;\n    for (const k of keys) {\n      if (k.includes(propName)) {\n        blocked = true;\n        isDefaultDesc ? objects[objectIndex][propName] = desc.value : Object.defineProperty(objects[objectIndex], propName, desc);\n      }\n      ++objectIndex;\n    }\n    if (!blocked) {\n      isDefaultDesc ? otherObject[propName] = desc.value : Object.defineProperty(otherObject, propName, desc);\n    }\n  }\n  return [...objects, otherObject];\n}\nfunction lazy(fn) {\n  let comp;\n  let p;\n  const wrap = (props) => {\n    const ctx = sharedConfig.context;\n    if (ctx) {\n      const [s, set] = createSignal();\n      sharedConfig.count || (sharedConfig.count = 0);\n      sharedConfig.count++;\n      (p || (p = fn())).then((mod) => {\n        !sharedConfig.done && setHydrateContext(ctx);\n        sharedConfig.count--;\n        set(() => mod.default);\n        setHydrateContext();\n      });\n      comp = s;\n    } else if (!comp) {\n      const [s] = createResource(() => (p || (p = fn())).then((mod) => mod.default));\n      comp = s;\n    }\n    let Comp;\n    return createMemo(\n      () => (Comp = comp()) ? untrack(() => {\n        if (IS_DEV) ;\n        if (!ctx || sharedConfig.done) return Comp(props);\n        const c = sharedConfig.context;\n        setHydrateContext(ctx);\n        const r = Comp(props);\n        setHydrateContext(c);\n        return r;\n      }) : \"\"\n    );\n  };\n  wrap.preload = () => p || ((p = fn()).then((mod) => comp = () => mod.default), p);\n  return wrap;\n}\nvar counter = 0;\nfunction createUniqueId() {\n  const ctx = sharedConfig.context;\n  return ctx ? sharedConfig.getNextContextId() : `cl-${counter++}`;\n}\nvar narrowedError = (name) => `Stale read from <${name}>.`;\nfunction For(props) {\n  const fallback = \"fallback\" in props && {\n    fallback: () => props.fallback\n  };\n  return createMemo(mapArray(() => props.each, props.children, fallback || void 0));\n}\nfunction Index(props) {\n  const fallback = \"fallback\" in props && {\n    fallback: () => props.fallback\n  };\n  return createMemo(indexArray(() => props.each, props.children, fallback || void 0));\n}\nfunction Show(props) {\n  const keyed = props.keyed;\n  const conditionValue = createMemo(() => props.when, void 0, void 0);\n  const condition = keyed ? conditionValue : createMemo(conditionValue, void 0, {\n    equals: (a, b) => !a === !b\n  });\n  return createMemo(\n    () => {\n      const c = condition();\n      if (c) {\n        const child = props.children;\n        const fn = typeof child === \"function\" && child.length > 0;\n        return fn ? untrack(\n          () => child(\n            keyed ? c : () => {\n              if (!untrack(condition)) throw narrowedError(\"Show\");\n              return conditionValue();\n            }\n          )\n        ) : child;\n      }\n      return props.fallback;\n    },\n    void 0,\n    void 0\n  );\n}\nfunction Switch(props) {\n  const chs = children(() => props.children);\n  const switchFunc = createMemo(() => {\n    const ch = chs();\n    const mps = Array.isArray(ch) ? ch : [ch];\n    let func = () => void 0;\n    for (let i = 0; i < mps.length; i++) {\n      const index = i;\n      const mp = mps[i];\n      const prevFunc = func;\n      const conditionValue = createMemo(\n        () => prevFunc() ? void 0 : mp.when,\n        void 0,\n        void 0\n      );\n      const condition = mp.keyed ? conditionValue : createMemo(conditionValue, void 0, {\n        equals: (a, b) => !a === !b\n      });\n      func = () => prevFunc() || (condition() ? [index, conditionValue, mp] : void 0);\n    }\n    return func;\n  });\n  return createMemo(\n    () => {\n      const sel = switchFunc()();\n      if (!sel) return props.fallback;\n      const [index, conditionValue, mp] = sel;\n      const child = mp.children;\n      const fn = typeof child === \"function\" && child.length > 0;\n      return fn ? untrack(\n        () => child(\n          mp.keyed ? conditionValue() : () => {\n            if (untrack(switchFunc)()?.[0] !== index) throw narrowedError(\"Match\");\n            return conditionValue();\n          }\n        )\n      ) : child;\n    },\n    void 0,\n    void 0\n  );\n}\nfunction Match(props) {\n  return props;\n}\nvar DEV = void 0;\n\n// ../../node_modules/.pnpm/solid-js@1.9.5/node_modules/solid-js/web/dist/web.js\nvar booleans = [\n  \"allowfullscreen\",\n  \"async\",\n  \"autofocus\",\n  \"autoplay\",\n  \"checked\",\n  \"controls\",\n  \"default\",\n  \"disabled\",\n  \"formnovalidate\",\n  \"hidden\",\n  \"indeterminate\",\n  \"inert\",\n  \"ismap\",\n  \"loop\",\n  \"multiple\",\n  \"muted\",\n  \"nomodule\",\n  \"novalidate\",\n  \"open\",\n  \"playsinline\",\n  \"readonly\",\n  \"required\",\n  \"reversed\",\n  \"seamless\",\n  \"selected\"\n];\nvar Properties = /* @__PURE__ */ new Set([\n  \"className\",\n  \"value\",\n  \"readOnly\",\n  \"formNoValidate\",\n  \"isMap\",\n  \"noModule\",\n  \"playsInline\",\n  ...booleans\n]);\nvar ChildProperties = /* @__PURE__ */ new Set([\n  \"innerHTML\",\n  \"textContent\",\n  \"innerText\",\n  \"children\"\n]);\nvar Aliases = /* @__PURE__ */ Object.assign(/* @__PURE__ */ Object.create(null), {\n  className: \"class\",\n  htmlFor: \"for\"\n});\nvar PropAliases = /* @__PURE__ */ Object.assign(/* @__PURE__ */ Object.create(null), {\n  class: \"className\",\n  formnovalidate: {\n    $: \"formNoValidate\",\n    BUTTON: 1,\n    INPUT: 1\n  },\n  ismap: {\n    $: \"isMap\",\n    IMG: 1\n  },\n  nomodule: {\n    $: \"noModule\",\n    SCRIPT: 1\n  },\n  playsinline: {\n    $: \"playsInline\",\n    VIDEO: 1\n  },\n  readonly: {\n    $: \"readOnly\",\n    INPUT: 1,\n    TEXTAREA: 1\n  }\n});\nfunction getPropAlias(prop, tagName) {\n  const a = PropAliases[prop];\n  return typeof a === \"object\" ? a[tagName] ? a[\"$\"] : void 0 : a;\n}\nvar DelegatedEvents = /* @__PURE__ */ new Set([\n  \"beforeinput\",\n  \"click\",\n  \"dblclick\",\n  \"contextmenu\",\n  \"focusin\",\n  \"focusout\",\n  \"input\",\n  \"keydown\",\n  \"keyup\",\n  \"mousedown\",\n  \"mousemove\",\n  \"mouseout\",\n  \"mouseover\",\n  \"mouseup\",\n  \"pointerdown\",\n  \"pointermove\",\n  \"pointerout\",\n  \"pointerover\",\n  \"pointerup\",\n  \"touchend\",\n  \"touchmove\",\n  \"touchstart\"\n]);\nvar SVGElements = /* @__PURE__ */ new Set([\n  \"altGlyph\",\n  \"altGlyphDef\",\n  \"altGlyphItem\",\n  \"animate\",\n  \"animateColor\",\n  \"animateMotion\",\n  \"animateTransform\",\n  \"circle\",\n  \"clipPath\",\n  \"color-profile\",\n  \"cursor\",\n  \"defs\",\n  \"desc\",\n  \"ellipse\",\n  \"feBlend\",\n  \"feColorMatrix\",\n  \"feComponentTransfer\",\n  \"feComposite\",\n  \"feConvolveMatrix\",\n  \"feDiffuseLighting\",\n  \"feDisplacementMap\",\n  \"feDistantLight\",\n  \"feDropShadow\",\n  \"feFlood\",\n  \"feFuncA\",\n  \"feFuncB\",\n  \"feFuncG\",\n  \"feFuncR\",\n  \"feGaussianBlur\",\n  \"feImage\",\n  \"feMerge\",\n  \"feMergeNode\",\n  \"feMorphology\",\n  \"feOffset\",\n  \"fePointLight\",\n  \"feSpecularLighting\",\n  \"feSpotLight\",\n  \"feTile\",\n  \"feTurbulence\",\n  \"filter\",\n  \"font\",\n  \"font-face\",\n  \"font-face-format\",\n  \"font-face-name\",\n  \"font-face-src\",\n  \"font-face-uri\",\n  \"foreignObject\",\n  \"g\",\n  \"glyph\",\n  \"glyphRef\",\n  \"hkern\",\n  \"image\",\n  \"line\",\n  \"linearGradient\",\n  \"marker\",\n  \"mask\",\n  \"metadata\",\n  \"missing-glyph\",\n  \"mpath\",\n  \"path\",\n  \"pattern\",\n  \"polygon\",\n  \"polyline\",\n  \"radialGradient\",\n  \"rect\",\n  \"set\",\n  \"stop\",\n  \"svg\",\n  \"switch\",\n  \"symbol\",\n  \"text\",\n  \"textPath\",\n  \"tref\",\n  \"tspan\",\n  \"use\",\n  \"view\",\n  \"vkern\"\n]);\nvar SVGNamespace = {\n  xlink: \"http://www.w3.org/1999/xlink\",\n  xml: \"http://www.w3.org/XML/1998/namespace\"\n};\nfunction reconcileArrays(parentNode, a, b) {\n  let bLength = b.length, aEnd = a.length, bEnd = bLength, aStart = 0, bStart = 0, after = a[aEnd - 1].nextSibling, map = null;\n  while (aStart < aEnd || bStart < bEnd) {\n    if (a[aStart] === b[bStart]) {\n      aStart++;\n      bStart++;\n      continue;\n    }\n    while (a[aEnd - 1] === b[bEnd - 1]) {\n      aEnd--;\n      bEnd--;\n    }\n    if (aEnd === aStart) {\n      const node = bEnd < bLength ? bStart ? b[bStart - 1].nextSibling : b[bEnd - bStart] : after;\n      while (bStart < bEnd) parentNode.insertBefore(b[bStart++], node);\n    } else if (bEnd === bStart) {\n      while (aStart < aEnd) {\n        if (!map || !map.has(a[aStart])) a[aStart].remove();\n        aStart++;\n      }\n    } else if (a[aStart] === b[bEnd - 1] && b[bStart] === a[aEnd - 1]) {\n      const node = a[--aEnd].nextSibling;\n      parentNode.insertBefore(b[bStart++], a[aStart++].nextSibling);\n      parentNode.insertBefore(b[--bEnd], node);\n      a[aEnd] = b[bEnd];\n    } else {\n      if (!map) {\n        map = /* @__PURE__ */ new Map();\n        let i = bStart;\n        while (i < bEnd) map.set(b[i], i++);\n      }\n      const index = map.get(a[aStart]);\n      if (index != null) {\n        if (bStart < index && index < bEnd) {\n          let i = aStart, sequence = 1, t;\n          while (++i < aEnd && i < bEnd) {\n            if ((t = map.get(a[i])) == null || t !== index + sequence) break;\n            sequence++;\n          }\n          if (sequence > index - bStart) {\n            const node = a[aStart];\n            while (bStart < index) parentNode.insertBefore(b[bStart++], node);\n          } else parentNode.replaceChild(b[bStart++], a[aStart++]);\n        } else aStart++;\n      } else a[aStart++].remove();\n    }\n  }\n}\nvar $$EVENTS = \"_$DX_DELEGATE\";\nfunction render(code, element, init, options = {}) {\n  let disposer;\n  createRoot((dispose2) => {\n    disposer = dispose2;\n    element === document ? code() : insert(element, code(), element.firstChild ? null : void 0, init);\n  }, options.owner);\n  return () => {\n    disposer();\n    element.textContent = \"\";\n  };\n}\nfunction template(html, isImportNode, isSVG, isMathML) {\n  let node;\n  const create = () => {\n    const t = isMathML ? document.createElementNS(\"http://www.w3.org/1998/Math/MathML\", \"template\") : document.createElement(\"template\");\n    t.innerHTML = html;\n    return isSVG ? t.content.firstChild.firstChild : isMathML ? t.firstChild : t.content.firstChild;\n  };\n  const fn = isImportNode ? () => untrack(() => document.importNode(node || (node = create()), true)) : () => (node || (node = create())).cloneNode(true);\n  fn.cloneNode = fn;\n  return fn;\n}\nfunction delegateEvents(eventNames, document2 = window.document) {\n  const e = document2[$$EVENTS] || (document2[$$EVENTS] = /* @__PURE__ */ new Set());\n  for (let i = 0, l = eventNames.length; i < l; i++) {\n    const name = eventNames[i];\n    if (!e.has(name)) {\n      e.add(name);\n      document2.addEventListener(name, eventHandler);\n    }\n  }\n}\nfunction clearDelegatedEvents(document2 = window.document) {\n  if (document2[$$EVENTS]) {\n    for (let name of document2[$$EVENTS].keys()) document2.removeEventListener(name, eventHandler);\n    delete document2[$$EVENTS];\n  }\n}\nfunction setAttribute(node, name, value) {\n  if (isHydrating(node)) return;\n  if (value == null) node.removeAttribute(name);\n  else node.setAttribute(name, value);\n}\nfunction setAttributeNS(node, namespace, name, value) {\n  if (isHydrating(node)) return;\n  if (value == null) node.removeAttributeNS(namespace, name);\n  else node.setAttributeNS(namespace, name, value);\n}\nfunction setBoolAttribute(node, name, value) {\n  if (isHydrating(node)) return;\n  value ? node.setAttribute(name, \"\") : node.removeAttribute(name);\n}\nfunction className(node, value) {\n  if (isHydrating(node)) return;\n  if (value == null) node.removeAttribute(\"class\");\n  else node.className = value;\n}\nfunction addEventListener(node, name, handler, delegate) {\n  if (delegate) {\n    if (Array.isArray(handler)) {\n      node[`$$${name}`] = handler[0];\n      node[`$$${name}Data`] = handler[1];\n    } else node[`$$${name}`] = handler;\n  } else if (Array.isArray(handler)) {\n    const handlerFn = handler[0];\n    node.addEventListener(name, handler[0] = (e) => handlerFn.call(node, handler[1], e));\n  } else node.addEventListener(name, handler, typeof handler !== \"function\" && handler);\n}\nfunction classList(node, value, prev = {}) {\n  const classKeys = Object.keys(value || {}), prevKeys = Object.keys(prev);\n  let i, len;\n  for (i = 0, len = prevKeys.length; i < len; i++) {\n    const key = prevKeys[i];\n    if (!key || key === \"undefined\" || value[key]) continue;\n    toggleClassKey(node, key, false);\n    delete prev[key];\n  }\n  for (i = 0, len = classKeys.length; i < len; i++) {\n    const key = classKeys[i], classValue = !!value[key];\n    if (!key || key === \"undefined\" || prev[key] === classValue || !classValue) continue;\n    toggleClassKey(node, key, true);\n    prev[key] = classValue;\n  }\n  return prev;\n}\nfunction style(node, value, prev) {\n  if (!value) return prev ? setAttribute(node, \"style\") : value;\n  const nodeStyle = node.style;\n  if (typeof value === \"string\") return nodeStyle.cssText = value;\n  typeof prev === \"string\" && (nodeStyle.cssText = prev = void 0);\n  prev || (prev = {});\n  value || (value = {});\n  let v, s;\n  for (s in prev) {\n    value[s] == null && nodeStyle.removeProperty(s);\n    delete prev[s];\n  }\n  for (s in value) {\n    v = value[s];\n    if (v !== prev[s]) {\n      nodeStyle.setProperty(s, v);\n      prev[s] = v;\n    }\n  }\n  return prev;\n}\nfunction spread(node, props = {}, isSVG, skipChildren) {\n  const prevProps = {};\n  if (!skipChildren) {\n    createRenderEffect(\n      () => prevProps.children = insertExpression(node, props.children, prevProps.children)\n    );\n  }\n  createRenderEffect(() => typeof props.ref === \"function\" && use(props.ref, node));\n  createRenderEffect(() => assign(node, props, isSVG, true, prevProps, true));\n  return prevProps;\n}\nfunction use(fn, element, arg) {\n  return untrack(() => fn(element, arg));\n}\nfunction insert(parent, accessor, marker, initial) {\n  if (marker !== void 0 && !initial) initial = [];\n  if (typeof accessor !== \"function\") return insertExpression(parent, accessor, initial, marker);\n  createRenderEffect((current) => insertExpression(parent, accessor(), current, marker), initial);\n}\nfunction assign(node, props, isSVG, skipChildren, prevProps = {}, skipRef = false) {\n  props || (props = {});\n  for (const prop in prevProps) {\n    if (!(prop in props)) {\n      if (prop === \"children\") continue;\n      prevProps[prop] = assignProp(node, prop, null, prevProps[prop], isSVG, skipRef, props);\n    }\n  }\n  for (const prop in props) {\n    if (prop === \"children\") {\n      continue;\n    }\n    const value = props[prop];\n    prevProps[prop] = assignProp(node, prop, value, prevProps[prop], isSVG, skipRef, props);\n  }\n}\nfunction getNextElement(template2) {\n  let node, key, hydrating = isHydrating();\n  if (!hydrating || !(node = sharedConfig.registry.get(key = getHydrationKey()))) {\n    return template2();\n  }\n  if (sharedConfig.completed) sharedConfig.completed.add(node);\n  sharedConfig.registry.delete(key);\n  return node;\n}\nfunction isHydrating(node) {\n  return !!sharedConfig.context && !sharedConfig.done && (!node || node.isConnected);\n}\nfunction toPropertyName(name) {\n  return name.toLowerCase().replace(/-([a-z])/g, (_, w) => w.toUpperCase());\n}\nfunction toggleClassKey(node, key, value) {\n  const classNames = key.trim().split(/\\s+/);\n  for (let i = 0, nameLen = classNames.length; i < nameLen; i++)\n    node.classList.toggle(classNames[i], value);\n}\nfunction assignProp(node, prop, value, prev, isSVG, skipRef, props) {\n  let isCE, isProp, isChildProp, propAlias, forceProp;\n  if (prop === \"style\") return style(node, value, prev);\n  if (prop === \"classList\") return classList(node, value, prev);\n  if (value === prev) return prev;\n  if (prop === \"ref\") {\n    if (!skipRef) value(node);\n  } else if (prop.slice(0, 3) === \"on:\") {\n    const e = prop.slice(3);\n    prev && node.removeEventListener(e, prev, typeof prev !== \"function\" && prev);\n    value && node.addEventListener(e, value, typeof value !== \"function\" && value);\n  } else if (prop.slice(0, 10) === \"oncapture:\") {\n    const e = prop.slice(10);\n    prev && node.removeEventListener(e, prev, true);\n    value && node.addEventListener(e, value, true);\n  } else if (prop.slice(0, 2) === \"on\") {\n    const name = prop.slice(2).toLowerCase();\n    const delegate = DelegatedEvents.has(name);\n    if (!delegate && prev) {\n      const h = Array.isArray(prev) ? prev[0] : prev;\n      node.removeEventListener(name, h);\n    }\n    if (delegate || value) {\n      addEventListener(node, name, value, delegate);\n      delegate && delegateEvents([name]);\n    }\n  } else if (prop.slice(0, 5) === \"attr:\") {\n    setAttribute(node, prop.slice(5), value);\n  } else if (prop.slice(0, 5) === \"bool:\") {\n    setBoolAttribute(node, prop.slice(5), value);\n  } else if ((forceProp = prop.slice(0, 5) === \"prop:\") || (isChildProp = ChildProperties.has(prop)) || !isSVG && ((propAlias = getPropAlias(prop, node.tagName)) || (isProp = Properties.has(prop))) || (isCE = node.nodeName.includes(\"-\") || \"is\" in props)) {\n    if (forceProp) {\n      prop = prop.slice(5);\n      isProp = true;\n    } else if (isHydrating(node)) return value;\n    if (prop === \"class\" || prop === \"className\") className(node, value);\n    else if (isCE && !isProp && !isChildProp) node[toPropertyName(prop)] = value;\n    else node[propAlias || prop] = value;\n  } else {\n    const ns = isSVG && prop.indexOf(\":\") > -1 && SVGNamespace[prop.split(\":\")[0]];\n    if (ns) setAttributeNS(node, ns, prop, value);\n    else setAttribute(node, Aliases[prop] || prop, value);\n  }\n  return value;\n}\nfunction eventHandler(e) {\n  if (sharedConfig.registry && sharedConfig.events) {\n    if (sharedConfig.events.find(([el, ev]) => ev === e)) return;\n  }\n  let node = e.target;\n  const key = `$$${e.type}`;\n  const oriTarget = e.target;\n  const oriCurrentTarget = e.currentTarget;\n  const retarget = (value) => Object.defineProperty(e, \"target\", {\n    configurable: true,\n    value\n  });\n  const handleNode = () => {\n    const handler = node[key];\n    if (handler && !node.disabled) {\n      const data = node[`${key}Data`];\n      data !== void 0 ? handler.call(node, data, e) : handler.call(node, e);\n      if (e.cancelBubble) return;\n    }\n    node.host && typeof node.host !== \"string\" && !node.host._$host && node.contains(e.target) && retarget(node.host);\n    return true;\n  };\n  const walkUpTree = () => {\n    while (handleNode() && (node = node._$host || node.parentNode || node.host)) ;\n  };\n  Object.defineProperty(e, \"currentTarget\", {\n    configurable: true,\n    get() {\n      return node || document;\n    }\n  });\n  if (sharedConfig.registry && !sharedConfig.done) sharedConfig.done = _$HY.done = true;\n  if (e.composedPath) {\n    const path = e.composedPath();\n    retarget(path[0]);\n    for (let i = 0; i < path.length - 2; i++) {\n      node = path[i];\n      if (!handleNode()) break;\n      if (node._$host) {\n        node = node._$host;\n        walkUpTree();\n        break;\n      }\n      if (node.parentNode === oriCurrentTarget) {\n        break;\n      }\n    }\n  } else walkUpTree();\n  retarget(oriTarget);\n}\nfunction insertExpression(parent, value, current, marker, unwrapArray) {\n  const hydrating = isHydrating(parent);\n  if (hydrating) {\n    !current && (current = [...parent.childNodes]);\n    let cleaned = [];\n    for (let i = 0; i < current.length; i++) {\n      const node = current[i];\n      if (node.nodeType === 8 && node.data.slice(0, 2) === \"!$\") node.remove();\n      else cleaned.push(node);\n    }\n    current = cleaned;\n  }\n  while (typeof current === \"function\") current = current();\n  if (value === current) return current;\n  const t = typeof value, multi = marker !== void 0;\n  parent = multi && current[0] && current[0].parentNode || parent;\n  if (t === \"string\" || t === \"number\") {\n    if (hydrating) return current;\n    if (t === \"number\") {\n      value = value.toString();\n      if (value === current) return current;\n    }\n    if (multi) {\n      let node = current[0];\n      if (node && node.nodeType === 3) {\n        node.data !== value && (node.data = value);\n      } else node = document.createTextNode(value);\n      current = cleanChildren(parent, current, marker, node);\n    } else {\n      if (current !== \"\" && typeof current === \"string\") {\n        current = parent.firstChild.data = value;\n      } else current = parent.textContent = value;\n    }\n  } else if (value == null || t === \"boolean\") {\n    if (hydrating) return current;\n    current = cleanChildren(parent, current, marker);\n  } else if (t === \"function\") {\n    createRenderEffect(() => {\n      let v = value();\n      while (typeof v === \"function\") v = v();\n      current = insertExpression(parent, v, current, marker);\n    });\n    return () => current;\n  } else if (Array.isArray(value)) {\n    const array = [];\n    const currentArray = current && Array.isArray(current);\n    if (normalizeIncomingArray(array, value, current, unwrapArray)) {\n      createRenderEffect(() => current = insertExpression(parent, array, current, marker, true));\n      return () => current;\n    }\n    if (hydrating) {\n      if (!array.length) return current;\n      if (marker === void 0) return current = [...parent.childNodes];\n      let node = array[0];\n      if (node.parentNode !== parent) return current;\n      const nodes = [node];\n      while ((node = node.nextSibling) !== marker) nodes.push(node);\n      return current = nodes;\n    }\n    if (array.length === 0) {\n      current = cleanChildren(parent, current, marker);\n      if (multi) return current;\n    } else if (currentArray) {\n      if (current.length === 0) {\n        appendNodes(parent, array, marker);\n      } else reconcileArrays(parent, current, array);\n    } else {\n      current && cleanChildren(parent);\n      appendNodes(parent, array);\n    }\n    current = array;\n  } else if (value.nodeType) {\n    if (hydrating && value.parentNode) return current = multi ? [value] : value;\n    if (Array.isArray(current)) {\n      if (multi) return current = cleanChildren(parent, current, marker, value);\n      cleanChildren(parent, current, null, value);\n    } else if (current == null || current === \"\" || !parent.firstChild) {\n      parent.appendChild(value);\n    } else parent.replaceChild(value, parent.firstChild);\n    current = value;\n  } else ;\n  return current;\n}\nfunction normalizeIncomingArray(normalized, array, current, unwrap) {\n  let dynamic = false;\n  for (let i = 0, len = array.length; i < len; i++) {\n    let item = array[i], prev = current && current[normalized.length], t;\n    if (item == null || item === true || item === false) ;\n    else if ((t = typeof item) === \"object\" && item.nodeType) {\n      normalized.push(item);\n    } else if (Array.isArray(item)) {\n      dynamic = normalizeIncomingArray(normalized, item, prev) || dynamic;\n    } else if (t === \"function\") {\n      if (unwrap) {\n        while (typeof item === \"function\") item = item();\n        dynamic = normalizeIncomingArray(\n          normalized,\n          Array.isArray(item) ? item : [item],\n          Array.isArray(prev) ? prev : [prev]\n        ) || dynamic;\n      } else {\n        normalized.push(item);\n        dynamic = true;\n      }\n    } else {\n      const value = String(item);\n      if (prev && prev.nodeType === 3 && prev.data === value) normalized.push(prev);\n      else normalized.push(document.createTextNode(value));\n    }\n  }\n  return dynamic;\n}\nfunction appendNodes(parent, array, marker = null) {\n  for (let i = 0, len = array.length; i < len; i++) parent.insertBefore(array[i], marker);\n}\nfunction cleanChildren(parent, current, marker, replacement) {\n  if (marker === void 0) return parent.textContent = \"\";\n  const node = replacement || document.createTextNode(\"\");\n  if (current.length) {\n    let inserted = false;\n    for (let i = current.length - 1; i >= 0; i--) {\n      const el = current[i];\n      if (node !== el) {\n        const isParent = el.parentNode === parent;\n        if (!inserted && !i)\n          isParent ? parent.replaceChild(node, el) : parent.insertBefore(node, marker);\n        else isParent && el.remove();\n      } else inserted = true;\n    }\n  } else parent.insertBefore(node, marker);\n  return [node];\n}\nfunction getHydrationKey() {\n  return sharedConfig.getNextContextId();\n}\nvar isServer = false;\nvar SVG_NAMESPACE = \"http://www.w3.org/2000/svg\";\nfunction createElement(tagName, isSVG = false) {\n  return isSVG ? document.createElementNS(SVG_NAMESPACE, tagName) : document.createElement(tagName);\n}\nfunction Portal(props) {\n  const { useShadow } = props, marker = document.createTextNode(\"\"), mount = () => props.mount || document.body, owner = getOwner();\n  let content;\n  let hydrating = !!sharedConfig.context;\n  createEffect(\n    () => {\n      if (hydrating) getOwner().user = hydrating = false;\n      content || (content = runWithOwner(owner, () => createMemo(() => props.children)));\n      const el = mount();\n      if (el instanceof HTMLHeadElement) {\n        const [clean, setClean] = createSignal(false);\n        const cleanup = () => setClean(true);\n        createRoot((dispose2) => insert(el, () => !clean() ? content() : dispose2(), null));\n        onCleanup(cleanup);\n      } else {\n        const container = createElement(props.isSVG ? \"g\" : \"div\", props.isSVG), renderRoot = useShadow && container.attachShadow ? container.attachShadow({\n          mode: \"open\"\n        }) : container;\n        Object.defineProperty(container, \"_$host\", {\n          get() {\n            return marker.parentNode;\n          },\n          configurable: true\n        });\n        insert(renderRoot, content);\n        el.appendChild(container);\n        props.ref && props.ref(container);\n        onCleanup(() => el.removeChild(container));\n      }\n    },\n    void 0,\n    {\n      render: !hydrating\n    }\n  );\n  return marker;\n}\nfunction createDynamic(component, props) {\n  const cached = createMemo(component);\n  return createMemo(() => {\n    const component2 = cached();\n    switch (typeof component2) {\n      case \"function\":\n        return untrack(() => component2(props));\n      case \"string\":\n        const isSvg = SVGElements.has(component2);\n        const el = sharedConfig.context ? getNextElement() : createElement(component2, isSvg);\n        spread(el, props, isSvg);\n        return el;\n    }\n  });\n}\nfunction Dynamic(props) {\n  const [, others] = splitProps(props, [\"component\"]);\n  return createDynamic(() => props.component, others);\n}\n\n// ../../node_modules/.pnpm/superjson@2.2.1/node_modules/superjson/dist/double-indexed-kv.js\nvar DoubleIndexedKV = class {\n  constructor() {\n    this.keyToValue = /* @__PURE__ */ new Map();\n    this.valueToKey = /* @__PURE__ */ new Map();\n  }\n  set(key, value) {\n    this.keyToValue.set(key, value);\n    this.valueToKey.set(value, key);\n  }\n  getByKey(key) {\n    return this.keyToValue.get(key);\n  }\n  getByValue(value) {\n    return this.valueToKey.get(value);\n  }\n  clear() {\n    this.keyToValue.clear();\n    this.valueToKey.clear();\n  }\n};\n\n// ../../node_modules/.pnpm/superjson@2.2.1/node_modules/superjson/dist/registry.js\nvar Registry = class {\n  constructor(generateIdentifier) {\n    this.generateIdentifier = generateIdentifier;\n    this.kv = new DoubleIndexedKV();\n  }\n  register(value, identifier) {\n    if (this.kv.getByValue(value)) {\n      return;\n    }\n    if (!identifier) {\n      identifier = this.generateIdentifier(value);\n    }\n    this.kv.set(identifier, value);\n  }\n  clear() {\n    this.kv.clear();\n  }\n  getIdentifier(value) {\n    return this.kv.getByValue(value);\n  }\n  getValue(identifier) {\n    return this.kv.getByKey(identifier);\n  }\n};\n\n// ../../node_modules/.pnpm/superjson@2.2.1/node_modules/superjson/dist/class-registry.js\nvar ClassRegistry = class extends Registry {\n  constructor() {\n    super((c) => c.name);\n    this.classToAllowedProps = /* @__PURE__ */ new Map();\n  }\n  register(value, options) {\n    if (typeof options === \"object\") {\n      if (options.allowProps) {\n        this.classToAllowedProps.set(value, options.allowProps);\n      }\n      super.register(value, options.identifier);\n    } else {\n      super.register(value, options);\n    }\n  }\n  getAllowedProps(value) {\n    return this.classToAllowedProps.get(value);\n  }\n};\n\n// ../../node_modules/.pnpm/superjson@2.2.1/node_modules/superjson/dist/util.js\nfunction valuesOfObj(record) {\n  if (\"values\" in Object) {\n    return Object.values(record);\n  }\n  const values = [];\n  for (const key in record) {\n    if (record.hasOwnProperty(key)) {\n      values.push(record[key]);\n    }\n  }\n  return values;\n}\nfunction find(record, predicate) {\n  const values = valuesOfObj(record);\n  if (\"find\" in values) {\n    return values.find(predicate);\n  }\n  const valuesNotNever = values;\n  for (let i = 0; i < valuesNotNever.length; i++) {\n    const value = valuesNotNever[i];\n    if (predicate(value)) {\n      return value;\n    }\n  }\n  return void 0;\n}\nfunction forEach(record, run) {\n  Object.entries(record).forEach(([key, value]) => run(value, key));\n}\nfunction includes(arr, value) {\n  return arr.indexOf(value) !== -1;\n}\nfunction findArr(record, predicate) {\n  for (let i = 0; i < record.length; i++) {\n    const value = record[i];\n    if (predicate(value)) {\n      return value;\n    }\n  }\n  return void 0;\n}\n\n// ../../node_modules/.pnpm/superjson@2.2.1/node_modules/superjson/dist/custom-transformer-registry.js\nvar CustomTransformerRegistry = class {\n  constructor() {\n    this.transfomers = {};\n  }\n  register(transformer) {\n    this.transfomers[transformer.name] = transformer;\n  }\n  findApplicable(v) {\n    return find(this.transfomers, (transformer) => transformer.isApplicable(v));\n  }\n  findByName(name) {\n    return this.transfomers[name];\n  }\n};\n\n// ../../node_modules/.pnpm/superjson@2.2.1/node_modules/superjson/dist/is.js\nvar getType = (payload) => Object.prototype.toString.call(payload).slice(8, -1);\nvar isUndefined = (payload) => typeof payload === \"undefined\";\nvar isNull = (payload) => payload === null;\nvar isPlainObject = (payload) => {\n  if (typeof payload !== \"object\" || payload === null)\n    return false;\n  if (payload === Object.prototype)\n    return false;\n  if (Object.getPrototypeOf(payload) === null)\n    return true;\n  return Object.getPrototypeOf(payload) === Object.prototype;\n};\nvar isEmptyObject = (payload) => isPlainObject(payload) && Object.keys(payload).length === 0;\nvar isArray = (payload) => Array.isArray(payload);\nvar isString = (payload) => typeof payload === \"string\";\nvar isNumber = (payload) => typeof payload === \"number\" && !isNaN(payload);\nvar isBoolean = (payload) => typeof payload === \"boolean\";\nvar isRegExp = (payload) => payload instanceof RegExp;\nvar isMap = (payload) => payload instanceof Map;\nvar isSet = (payload) => payload instanceof Set;\nvar isSymbol = (payload) => getType(payload) === \"Symbol\";\nvar isDate = (payload) => payload instanceof Date && !isNaN(payload.valueOf());\nvar isError = (payload) => payload instanceof Error;\nvar isNaNValue = (payload) => typeof payload === \"number\" && isNaN(payload);\nvar isPrimitive = (payload) => isBoolean(payload) || isNull(payload) || isUndefined(payload) || isNumber(payload) || isString(payload) || isSymbol(payload);\nvar isBigint = (payload) => typeof payload === \"bigint\";\nvar isInfinite = (payload) => payload === Infinity || payload === -Infinity;\nvar isTypedArray = (payload) => ArrayBuffer.isView(payload) && !(payload instanceof DataView);\nvar isURL = (payload) => payload instanceof URL;\n\n// ../../node_modules/.pnpm/superjson@2.2.1/node_modules/superjson/dist/pathstringifier.js\nvar escapeKey = (key) => key.replace(/\\./g, \"\\\\.\");\nvar stringifyPath = (path) => path.map(String).map(escapeKey).join(\".\");\nvar parsePath = (string) => {\n  const result = [];\n  let segment = \"\";\n  for (let i = 0; i < string.length; i++) {\n    let char = string.charAt(i);\n    const isEscapedDot = char === \"\\\\\" && string.charAt(i + 1) === \".\";\n    if (isEscapedDot) {\n      segment += \".\";\n      i++;\n      continue;\n    }\n    const isEndOfSegment = char === \".\";\n    if (isEndOfSegment) {\n      result.push(segment);\n      segment = \"\";\n      continue;\n    }\n    segment += char;\n  }\n  const lastSegment = segment;\n  result.push(lastSegment);\n  return result;\n};\n\n// ../../node_modules/.pnpm/superjson@2.2.1/node_modules/superjson/dist/transformer.js\nfunction simpleTransformation(isApplicable, annotation, transform, untransform) {\n  return {\n    isApplicable,\n    annotation,\n    transform,\n    untransform\n  };\n}\nvar simpleRules = [\n  simpleTransformation(isUndefined, \"undefined\", () => null, () => void 0),\n  simpleTransformation(isBigint, \"bigint\", (v) => v.toString(), (v) => {\n    if (typeof BigInt !== \"undefined\") {\n      return BigInt(v);\n    }\n    console.error(\"Please add a BigInt polyfill.\");\n    return v;\n  }),\n  simpleTransformation(isDate, \"Date\", (v) => v.toISOString(), (v) => new Date(v)),\n  simpleTransformation(isError, \"Error\", (v, superJson) => {\n    const baseError = {\n      name: v.name,\n      message: v.message\n    };\n    superJson.allowedErrorProps.forEach((prop) => {\n      baseError[prop] = v[prop];\n    });\n    return baseError;\n  }, (v, superJson) => {\n    const e = new Error(v.message);\n    e.name = v.name;\n    e.stack = v.stack;\n    superJson.allowedErrorProps.forEach((prop) => {\n      e[prop] = v[prop];\n    });\n    return e;\n  }),\n  simpleTransformation(isRegExp, \"regexp\", (v) => \"\" + v, (regex) => {\n    const body = regex.slice(1, regex.lastIndexOf(\"/\"));\n    const flags = regex.slice(regex.lastIndexOf(\"/\") + 1);\n    return new RegExp(body, flags);\n  }),\n  simpleTransformation(\n    isSet,\n    \"set\",\n    // (sets only exist in es6+)\n    // eslint-disable-next-line es5/no-es6-methods\n    (v) => [...v.values()],\n    (v) => new Set(v)\n  ),\n  simpleTransformation(isMap, \"map\", (v) => [...v.entries()], (v) => new Map(v)),\n  simpleTransformation((v) => isNaNValue(v) || isInfinite(v), \"number\", (v) => {\n    if (isNaNValue(v)) {\n      return \"NaN\";\n    }\n    if (v > 0) {\n      return \"Infinity\";\n    } else {\n      return \"-Infinity\";\n    }\n  }, Number),\n  simpleTransformation((v) => v === 0 && 1 / v === -Infinity, \"number\", () => {\n    return \"-0\";\n  }, Number),\n  simpleTransformation(isURL, \"URL\", (v) => v.toString(), (v) => new URL(v))\n];\nfunction compositeTransformation(isApplicable, annotation, transform, untransform) {\n  return {\n    isApplicable,\n    annotation,\n    transform,\n    untransform\n  };\n}\nvar symbolRule = compositeTransformation((s, superJson) => {\n  if (isSymbol(s)) {\n    const isRegistered = !!superJson.symbolRegistry.getIdentifier(s);\n    return isRegistered;\n  }\n  return false;\n}, (s, superJson) => {\n  const identifier = superJson.symbolRegistry.getIdentifier(s);\n  return [\"symbol\", identifier];\n}, (v) => v.description, (_, a, superJson) => {\n  const value = superJson.symbolRegistry.getValue(a[1]);\n  if (!value) {\n    throw new Error(\"Trying to deserialize unknown symbol\");\n  }\n  return value;\n});\nvar constructorToName = [\n  Int8Array,\n  Uint8Array,\n  Int16Array,\n  Uint16Array,\n  Int32Array,\n  Uint32Array,\n  Float32Array,\n  Float64Array,\n  Uint8ClampedArray\n].reduce((obj, ctor) => {\n  obj[ctor.name] = ctor;\n  return obj;\n}, {});\nvar typedArrayRule = compositeTransformation(isTypedArray, (v) => [\"typed-array\", v.constructor.name], (v) => [...v], (v, a) => {\n  const ctor = constructorToName[a[1]];\n  if (!ctor) {\n    throw new Error(\"Trying to deserialize unknown typed array\");\n  }\n  return new ctor(v);\n});\nfunction isInstanceOfRegisteredClass(potentialClass, superJson) {\n  if (potentialClass?.constructor) {\n    const isRegistered = !!superJson.classRegistry.getIdentifier(potentialClass.constructor);\n    return isRegistered;\n  }\n  return false;\n}\nvar classRule = compositeTransformation(isInstanceOfRegisteredClass, (clazz, superJson) => {\n  const identifier = superJson.classRegistry.getIdentifier(clazz.constructor);\n  return [\"class\", identifier];\n}, (clazz, superJson) => {\n  const allowedProps = superJson.classRegistry.getAllowedProps(clazz.constructor);\n  if (!allowedProps) {\n    return { ...clazz };\n  }\n  const result = {};\n  allowedProps.forEach((prop) => {\n    result[prop] = clazz[prop];\n  });\n  return result;\n}, (v, a, superJson) => {\n  const clazz = superJson.classRegistry.getValue(a[1]);\n  if (!clazz) {\n    throw new Error(\"Trying to deserialize unknown class - check https://github.com/blitz-js/superjson/issues/116#issuecomment-773996564\");\n  }\n  return Object.assign(Object.create(clazz.prototype), v);\n});\nvar customRule = compositeTransformation((value, superJson) => {\n  return !!superJson.customTransformerRegistry.findApplicable(value);\n}, (value, superJson) => {\n  const transformer = superJson.customTransformerRegistry.findApplicable(value);\n  return [\"custom\", transformer.name];\n}, (value, superJson) => {\n  const transformer = superJson.customTransformerRegistry.findApplicable(value);\n  return transformer.serialize(value);\n}, (v, a, superJson) => {\n  const transformer = superJson.customTransformerRegistry.findByName(a[1]);\n  if (!transformer) {\n    throw new Error(\"Trying to deserialize unknown custom value\");\n  }\n  return transformer.deserialize(v);\n});\nvar compositeRules = [classRule, symbolRule, customRule, typedArrayRule];\nvar transformValue = (value, superJson) => {\n  const applicableCompositeRule = findArr(compositeRules, (rule) => rule.isApplicable(value, superJson));\n  if (applicableCompositeRule) {\n    return {\n      value: applicableCompositeRule.transform(value, superJson),\n      type: applicableCompositeRule.annotation(value, superJson)\n    };\n  }\n  const applicableSimpleRule = findArr(simpleRules, (rule) => rule.isApplicable(value, superJson));\n  if (applicableSimpleRule) {\n    return {\n      value: applicableSimpleRule.transform(value, superJson),\n      type: applicableSimpleRule.annotation\n    };\n  }\n  return void 0;\n};\nvar simpleRulesByAnnotation = {};\nsimpleRules.forEach((rule) => {\n  simpleRulesByAnnotation[rule.annotation] = rule;\n});\nvar untransformValue = (json, type, superJson) => {\n  if (isArray(type)) {\n    switch (type[0]) {\n      case \"symbol\":\n        return symbolRule.untransform(json, type, superJson);\n      case \"class\":\n        return classRule.untransform(json, type, superJson);\n      case \"custom\":\n        return customRule.untransform(json, type, superJson);\n      case \"typed-array\":\n        return typedArrayRule.untransform(json, type, superJson);\n      default:\n        throw new Error(\"Unknown transformation: \" + type);\n    }\n  } else {\n    const transformation = simpleRulesByAnnotation[type];\n    if (!transformation) {\n      throw new Error(\"Unknown transformation: \" + type);\n    }\n    return transformation.untransform(json, superJson);\n  }\n};\n\n// ../../node_modules/.pnpm/superjson@2.2.1/node_modules/superjson/dist/accessDeep.js\nvar getNthKey = (value, n) => {\n  const keys = value.keys();\n  while (n > 0) {\n    keys.next();\n    n--;\n  }\n  return keys.next().value;\n};\nfunction validatePath(path) {\n  if (includes(path, \"__proto__\")) {\n    throw new Error(\"__proto__ is not allowed as a property\");\n  }\n  if (includes(path, \"prototype\")) {\n    throw new Error(\"prototype is not allowed as a property\");\n  }\n  if (includes(path, \"constructor\")) {\n    throw new Error(\"constructor is not allowed as a property\");\n  }\n}\nvar getDeep = (object, path) => {\n  validatePath(path);\n  for (let i = 0; i < path.length; i++) {\n    const key = path[i];\n    if (isSet(object)) {\n      object = getNthKey(object, +key);\n    } else if (isMap(object)) {\n      const row = +key;\n      const type = +path[++i] === 0 ? \"key\" : \"value\";\n      const keyOfRow = getNthKey(object, row);\n      switch (type) {\n        case \"key\":\n          object = keyOfRow;\n          break;\n        case \"value\":\n          object = object.get(keyOfRow);\n          break;\n      }\n    } else {\n      object = object[key];\n    }\n  }\n  return object;\n};\nvar setDeep = (object, path, mapper) => {\n  validatePath(path);\n  if (path.length === 0) {\n    return mapper(object);\n  }\n  let parent = object;\n  for (let i = 0; i < path.length - 1; i++) {\n    const key = path[i];\n    if (isArray(parent)) {\n      const index = +key;\n      parent = parent[index];\n    } else if (isPlainObject(parent)) {\n      parent = parent[key];\n    } else if (isSet(parent)) {\n      const row = +key;\n      parent = getNthKey(parent, row);\n    } else if (isMap(parent)) {\n      const isEnd = i === path.length - 2;\n      if (isEnd) {\n        break;\n      }\n      const row = +key;\n      const type = +path[++i] === 0 ? \"key\" : \"value\";\n      const keyOfRow = getNthKey(parent, row);\n      switch (type) {\n        case \"key\":\n          parent = keyOfRow;\n          break;\n        case \"value\":\n          parent = parent.get(keyOfRow);\n          break;\n      }\n    }\n  }\n  const lastKey = path[path.length - 1];\n  if (isArray(parent)) {\n    parent[+lastKey] = mapper(parent[+lastKey]);\n  } else if (isPlainObject(parent)) {\n    parent[lastKey] = mapper(parent[lastKey]);\n  }\n  if (isSet(parent)) {\n    const oldValue = getNthKey(parent, +lastKey);\n    const newValue = mapper(oldValue);\n    if (oldValue !== newValue) {\n      parent.delete(oldValue);\n      parent.add(newValue);\n    }\n  }\n  if (isMap(parent)) {\n    const row = +path[path.length - 2];\n    const keyToRow = getNthKey(parent, row);\n    const type = +lastKey === 0 ? \"key\" : \"value\";\n    switch (type) {\n      case \"key\": {\n        const newKey = mapper(keyToRow);\n        parent.set(newKey, parent.get(keyToRow));\n        if (newKey !== keyToRow) {\n          parent.delete(keyToRow);\n        }\n        break;\n      }\n      case \"value\": {\n        parent.set(keyToRow, mapper(parent.get(keyToRow)));\n        break;\n      }\n    }\n  }\n  return object;\n};\n\n// ../../node_modules/.pnpm/superjson@2.2.1/node_modules/superjson/dist/plainer.js\nfunction traverse(tree, walker2, origin = []) {\n  if (!tree) {\n    return;\n  }\n  if (!isArray(tree)) {\n    forEach(tree, (subtree, key) => traverse(subtree, walker2, [...origin, ...parsePath(key)]));\n    return;\n  }\n  const [nodeValue, children2] = tree;\n  if (children2) {\n    forEach(children2, (child, key) => {\n      traverse(child, walker2, [...origin, ...parsePath(key)]);\n    });\n  }\n  walker2(nodeValue, origin);\n}\nfunction applyValueAnnotations(plain, annotations, superJson) {\n  traverse(annotations, (type, path) => {\n    plain = setDeep(plain, path, (v) => untransformValue(v, type, superJson));\n  });\n  return plain;\n}\nfunction applyReferentialEqualityAnnotations(plain, annotations) {\n  function apply(identicalPaths, path) {\n    const object = getDeep(plain, parsePath(path));\n    identicalPaths.map(parsePath).forEach((identicalObjectPath) => {\n      plain = setDeep(plain, identicalObjectPath, () => object);\n    });\n  }\n  if (isArray(annotations)) {\n    const [root, other] = annotations;\n    root.forEach((identicalPath) => {\n      plain = setDeep(plain, parsePath(identicalPath), () => plain);\n    });\n    if (other) {\n      forEach(other, apply);\n    }\n  } else {\n    forEach(annotations, apply);\n  }\n  return plain;\n}\nvar isDeep = (object, superJson) => isPlainObject(object) || isArray(object) || isMap(object) || isSet(object) || isInstanceOfRegisteredClass(object, superJson);\nfunction addIdentity(object, path, identities) {\n  const existingSet = identities.get(object);\n  if (existingSet) {\n    existingSet.push(path);\n  } else {\n    identities.set(object, [path]);\n  }\n}\nfunction generateReferentialEqualityAnnotations(identitites, dedupe) {\n  const result = {};\n  let rootEqualityPaths = void 0;\n  identitites.forEach((paths) => {\n    if (paths.length <= 1) {\n      return;\n    }\n    if (!dedupe) {\n      paths = paths.map((path) => path.map(String)).sort((a, b) => a.length - b.length);\n    }\n    const [representativePath, ...identicalPaths] = paths;\n    if (representativePath.length === 0) {\n      rootEqualityPaths = identicalPaths.map(stringifyPath);\n    } else {\n      result[stringifyPath(representativePath)] = identicalPaths.map(stringifyPath);\n    }\n  });\n  if (rootEqualityPaths) {\n    if (isEmptyObject(result)) {\n      return [rootEqualityPaths];\n    } else {\n      return [rootEqualityPaths, result];\n    }\n  } else {\n    return isEmptyObject(result) ? void 0 : result;\n  }\n}\nvar walker = (object, identities, superJson, dedupe, path = [], objectsInThisPath = [], seenObjects = /* @__PURE__ */ new Map()) => {\n  const primitive = isPrimitive(object);\n  if (!primitive) {\n    addIdentity(object, path, identities);\n    const seen = seenObjects.get(object);\n    if (seen) {\n      return dedupe ? {\n        transformedValue: null\n      } : seen;\n    }\n  }\n  if (!isDeep(object, superJson)) {\n    const transformed2 = transformValue(object, superJson);\n    const result2 = transformed2 ? {\n      transformedValue: transformed2.value,\n      annotations: [transformed2.type]\n    } : {\n      transformedValue: object\n    };\n    if (!primitive) {\n      seenObjects.set(object, result2);\n    }\n    return result2;\n  }\n  if (includes(objectsInThisPath, object)) {\n    return {\n      transformedValue: null\n    };\n  }\n  const transformationResult = transformValue(object, superJson);\n  const transformed = transformationResult?.value ?? object;\n  const transformedValue = isArray(transformed) ? [] : {};\n  const innerAnnotations = {};\n  forEach(transformed, (value, index) => {\n    if (index === \"__proto__\" || index === \"constructor\" || index === \"prototype\") {\n      throw new Error(`Detected property ${index}. This is a prototype pollution risk, please remove it from your object.`);\n    }\n    const recursiveResult = walker(value, identities, superJson, dedupe, [...path, index], [...objectsInThisPath, object], seenObjects);\n    transformedValue[index] = recursiveResult.transformedValue;\n    if (isArray(recursiveResult.annotations)) {\n      innerAnnotations[index] = recursiveResult.annotations;\n    } else if (isPlainObject(recursiveResult.annotations)) {\n      forEach(recursiveResult.annotations, (tree, key) => {\n        innerAnnotations[escapeKey(index) + \".\" + key] = tree;\n      });\n    }\n  });\n  const result = isEmptyObject(innerAnnotations) ? {\n    transformedValue,\n    annotations: !!transformationResult ? [transformationResult.type] : void 0\n  } : {\n    transformedValue,\n    annotations: !!transformationResult ? [transformationResult.type, innerAnnotations] : innerAnnotations\n  };\n  if (!primitive) {\n    seenObjects.set(object, result);\n  }\n  return result;\n};\n\n// ../../node_modules/.pnpm/is-what@4.1.16/node_modules/is-what/dist/index.js\nfunction getType2(payload) {\n  return Object.prototype.toString.call(payload).slice(8, -1);\n}\nfunction isArray2(payload) {\n  return getType2(payload) === \"Array\";\n}\nfunction isPlainObject2(payload) {\n  if (getType2(payload) !== \"Object\")\n    return false;\n  const prototype = Object.getPrototypeOf(payload);\n  return !!prototype && prototype.constructor === Object && prototype === Object.prototype;\n}\n\n// ../../node_modules/.pnpm/copy-anything@3.0.5/node_modules/copy-anything/dist/index.js\nfunction assignProp2(carry, key, newVal, originalObject, includeNonenumerable) {\n  const propType = {}.propertyIsEnumerable.call(originalObject, key) ? \"enumerable\" : \"nonenumerable\";\n  if (propType === \"enumerable\")\n    carry[key] = newVal;\n  if (includeNonenumerable && propType === \"nonenumerable\") {\n    Object.defineProperty(carry, key, {\n      value: newVal,\n      enumerable: false,\n      writable: true,\n      configurable: true\n    });\n  }\n}\nfunction copy(target, options = {}) {\n  if (isArray2(target)) {\n    return target.map((item) => copy(item, options));\n  }\n  if (!isPlainObject2(target)) {\n    return target;\n  }\n  const props = Object.getOwnPropertyNames(target);\n  const symbols = Object.getOwnPropertySymbols(target);\n  return [...props, ...symbols].reduce((carry, key) => {\n    if (isArray2(options.props) && !options.props.includes(key)) {\n      return carry;\n    }\n    const val = target[key];\n    const newVal = copy(val, options);\n    assignProp2(carry, key, newVal, target, options.nonenumerable);\n    return carry;\n  }, {});\n}\n\n// ../../node_modules/.pnpm/superjson@2.2.1/node_modules/superjson/dist/index.js\nvar SuperJSON = class {\n  /**\n   * @param dedupeReferentialEqualities  If true, SuperJSON will make sure only one instance of referentially equal objects are serialized and the rest are replaced with `null`.\n   */\n  constructor({ dedupe = false } = {}) {\n    this.classRegistry = new ClassRegistry();\n    this.symbolRegistry = new Registry((s) => s.description ?? \"\");\n    this.customTransformerRegistry = new CustomTransformerRegistry();\n    this.allowedErrorProps = [];\n    this.dedupe = dedupe;\n  }\n  serialize(object) {\n    const identities = /* @__PURE__ */ new Map();\n    const output = walker(object, identities, this, this.dedupe);\n    const res = {\n      json: output.transformedValue\n    };\n    if (output.annotations) {\n      res.meta = {\n        ...res.meta,\n        values: output.annotations\n      };\n    }\n    const equalityAnnotations = generateReferentialEqualityAnnotations(identities, this.dedupe);\n    if (equalityAnnotations) {\n      res.meta = {\n        ...res.meta,\n        referentialEqualities: equalityAnnotations\n      };\n    }\n    return res;\n  }\n  deserialize(payload) {\n    const { json, meta } = payload;\n    let result = copy(json);\n    if (meta?.values) {\n      result = applyValueAnnotations(result, meta.values, this);\n    }\n    if (meta?.referentialEqualities) {\n      result = applyReferentialEqualityAnnotations(result, meta.referentialEqualities);\n    }\n    return result;\n  }\n  stringify(object) {\n    return JSON.stringify(this.serialize(object));\n  }\n  parse(string) {\n    return this.deserialize(JSON.parse(string));\n  }\n  registerClass(v, options) {\n    this.classRegistry.register(v, options);\n  }\n  registerSymbol(v, identifier) {\n    this.symbolRegistry.register(v, identifier);\n  }\n  registerCustom(transformer, name) {\n    this.customTransformerRegistry.register({\n      name,\n      ...transformer\n    });\n  }\n  allowErrorProps(...props) {\n    this.allowedErrorProps.push(...props);\n  }\n};\nSuperJSON.defaultInstance = new SuperJSON();\nSuperJSON.serialize = SuperJSON.defaultInstance.serialize.bind(SuperJSON.defaultInstance);\nSuperJSON.deserialize = SuperJSON.defaultInstance.deserialize.bind(SuperJSON.defaultInstance);\nSuperJSON.stringify = SuperJSON.defaultInstance.stringify.bind(SuperJSON.defaultInstance);\nSuperJSON.parse = SuperJSON.defaultInstance.parse.bind(SuperJSON.defaultInstance);\nSuperJSON.registerClass = SuperJSON.defaultInstance.registerClass.bind(SuperJSON.defaultInstance);\nSuperJSON.registerSymbol = SuperJSON.defaultInstance.registerSymbol.bind(SuperJSON.defaultInstance);\nSuperJSON.registerCustom = SuperJSON.defaultInstance.registerCustom.bind(SuperJSON.defaultInstance);\nSuperJSON.allowErrorProps = SuperJSON.defaultInstance.allowErrorProps.bind(SuperJSON.defaultInstance);\nvar serialize = SuperJSON.serialize;\nSuperJSON.deserialize;\nvar stringify = SuperJSON.stringify;\nSuperJSON.parse;\nSuperJSON.registerClass;\nSuperJSON.registerCustom;\nSuperJSON.registerSymbol;\nSuperJSON.allowErrorProps;\n\n// src/utils.tsx\nfunction getQueryStatusLabel(query) {\n  return query.state.fetchStatus === \"fetching\" ? \"fetching\" : !query.getObserversCount() ? \"inactive\" : query.state.fetchStatus === \"paused\" ? \"paused\" : query.isStale() ? \"stale\" : \"fresh\";\n}\nfunction getSidedProp(prop, side) {\n  return `${prop}${side.charAt(0).toUpperCase() + side.slice(1)}`;\n}\nfunction getQueryStatusColor({\n  queryState,\n  observerCount,\n  isStale\n}) {\n  return queryState.fetchStatus === \"fetching\" ? \"blue\" : !observerCount ? \"gray\" : queryState.fetchStatus === \"paused\" ? \"purple\" : isStale ? \"yellow\" : \"green\";\n}\nfunction getMutationStatusColor({\n  status,\n  isPaused\n}) {\n  return isPaused ? \"purple\" : status === \"error\" ? \"red\" : status === \"pending\" ? \"yellow\" : status === \"success\" ? \"green\" : \"gray\";\n}\nfunction getQueryStatusColorByLabel(label) {\n  return label === \"fresh\" ? \"green\" : label === \"stale\" ? \"yellow\" : label === \"paused\" ? \"purple\" : label === \"inactive\" ? \"gray\" : \"blue\";\n}\nvar displayValue = (value, beautify = false) => {\n  const {\n    json\n  } = serialize(value);\n  return JSON.stringify(json, null, beautify ? 2 : void 0);\n};\nvar getStatusRank = (q) => q.state.fetchStatus !== \"idle\" ? 0 : !q.getObserversCount() ? 3 : q.isStale() ? 2 : 1;\nvar queryHashSort = (a, b) => a.queryHash.localeCompare(b.queryHash);\nvar dateSort = (a, b) => a.state.dataUpdatedAt < b.state.dataUpdatedAt ? 1 : -1;\nvar statusAndDateSort = (a, b) => {\n  if (getStatusRank(a) === getStatusRank(b)) {\n    return dateSort(a, b);\n  }\n  return getStatusRank(a) > getStatusRank(b) ? 1 : -1;\n};\nvar sortFns = {\n  status: statusAndDateSort,\n  \"query hash\": queryHashSort,\n  \"last updated\": dateSort\n};\nvar getMutationStatusRank = (m) => m.state.isPaused ? 0 : m.state.status === \"error\" ? 2 : m.state.status === \"pending\" ? 1 : 3;\nvar mutationDateSort = (a, b) => a.state.submittedAt < b.state.submittedAt ? 1 : -1;\nvar mutationStatusSort = (a, b) => {\n  if (getMutationStatusRank(a) === getMutationStatusRank(b)) {\n    return mutationDateSort(a, b);\n  }\n  return getMutationStatusRank(a) > getMutationStatusRank(b) ? 1 : -1;\n};\nvar mutationSortFns = {\n  status: mutationStatusSort,\n  \"last updated\": mutationDateSort\n};\nvar convertRemToPixels = (rem) => {\n  return rem * parseFloat(getComputedStyle(document.documentElement).fontSize);\n};\nvar getPreferredColorScheme = () => {\n  const [colorScheme, setColorScheme] = createSignal(\"dark\");\n  onMount(() => {\n    const query = window.matchMedia(\"(prefers-color-scheme: dark)\");\n    setColorScheme(query.matches ? \"dark\" : \"light\");\n    const listener = (e) => {\n      setColorScheme(e.matches ? \"dark\" : \"light\");\n    };\n    query.addEventListener(\"change\", listener);\n    onCleanup(() => query.removeEventListener(\"change\", listener));\n  });\n  return colorScheme;\n};\nvar updateNestedDataByPath = (oldData, updatePath, value) => {\n  if (updatePath.length === 0) {\n    return value;\n  }\n  if (oldData instanceof Map) {\n    const newData = new Map(oldData);\n    if (updatePath.length === 1) {\n      newData.set(updatePath[0], value);\n      return newData;\n    }\n    const [head, ...tail] = updatePath;\n    newData.set(head, updateNestedDataByPath(newData.get(head), tail, value));\n    return newData;\n  }\n  if (oldData instanceof Set) {\n    const setAsArray = updateNestedDataByPath(Array.from(oldData), updatePath, value);\n    return new Set(setAsArray);\n  }\n  if (Array.isArray(oldData)) {\n    const newData = [...oldData];\n    if (updatePath.length === 1) {\n      newData[updatePath[0]] = value;\n      return newData;\n    }\n    const [head, ...tail] = updatePath;\n    newData[head] = updateNestedDataByPath(newData[head], tail, value);\n    return newData;\n  }\n  if (oldData instanceof Object) {\n    const newData = {\n      ...oldData\n    };\n    if (updatePath.length === 1) {\n      newData[updatePath[0]] = value;\n      return newData;\n    }\n    const [head, ...tail] = updatePath;\n    newData[head] = updateNestedDataByPath(newData[head], tail, value);\n    return newData;\n  }\n  return oldData;\n};\nvar deleteNestedDataByPath = (oldData, deletePath) => {\n  if (oldData instanceof Map) {\n    const newData = new Map(oldData);\n    if (deletePath.length === 1) {\n      newData.delete(deletePath[0]);\n      return newData;\n    }\n    const [head, ...tail] = deletePath;\n    newData.set(head, deleteNestedDataByPath(newData.get(head), tail));\n    return newData;\n  }\n  if (oldData instanceof Set) {\n    const setAsArray = deleteNestedDataByPath(Array.from(oldData), deletePath);\n    return new Set(setAsArray);\n  }\n  if (Array.isArray(oldData)) {\n    const newData = [...oldData];\n    if (deletePath.length === 1) {\n      return newData.filter((_, idx) => idx.toString() !== deletePath[0]);\n    }\n    const [head, ...tail] = deletePath;\n    newData[head] = deleteNestedDataByPath(newData[head], tail);\n    return newData;\n  }\n  if (oldData instanceof Object) {\n    const newData = {\n      ...oldData\n    };\n    if (deletePath.length === 1) {\n      delete newData[deletePath[0]];\n      return newData;\n    }\n    const [head, ...tail] = deletePath;\n    newData[head] = deleteNestedDataByPath(newData[head], tail);\n    return newData;\n  }\n  return oldData;\n};\nvar setupStyleSheet = (nonce, target) => {\n  if (!nonce) return;\n  const styleExists = document.querySelector(\"#_goober\") || target?.querySelector(\"#_goober\");\n  if (styleExists) return;\n  const styleTag = document.createElement(\"style\");\n  const textNode = document.createTextNode(\"\");\n  styleTag.appendChild(textNode);\n  styleTag.id = \"_goober\";\n  styleTag.setAttribute(\"nonce\", nonce);\n  if (target) {\n    target.appendChild(styleTag);\n  } else {\n    document.head.appendChild(styleTag);\n  }\n};\n\nexport { $TRACK, DEV, Dynamic, For, Index, Match, Portal, Show, Switch, addEventListener, batch, className, clearDelegatedEvents, convertRemToPixels, createComponent, createComputed, createContext, createEffect, createMemo, createRenderEffect, createRoot, createSignal, createUniqueId, delegateEvents, deleteNestedDataByPath, displayValue, getMutationStatusColor, getOwner, getPreferredColorScheme, getQueryStatusColor, getQueryStatusColorByLabel, getQueryStatusLabel, getSidedProp, insert, isServer, lazy, mergeProps, mutationSortFns, on, onCleanup, onMount, render, serialize, setAttribute, setupStyleSheet, sortFns, splitProps, spread, stringify, template, untrack, updateNestedDataByPath, use, useContext, useTransition };\n"], "mappings": "AAAA;AACA,IAAIA,YAAY,GAAG;EACjBC,OAAO,EAAE,KAAK,CAAC;EACfC,QAAQ,EAAE,KAAK,CAAC;EAChBC,OAAO,EAAE,KAAK,CAAC;EACfC,IAAI,EAAE,KAAK;EACXC,YAAYA,CAAA,EAAG;IACb,OAAOA,YAAY,CAAC,IAAI,CAACJ,OAAO,CAACK,KAAK,CAAC;EACzC,CAAC;EACDC,gBAAgBA,CAAA,EAAG;IACjB,OAAOF,YAAY,CAAC,IAAI,CAACJ,OAAO,CAACK,KAAK,EAAE,CAAC;EAC3C;AACF,CAAC;AACD,SAASD,YAAYA,CAACC,KAAK,EAAE;EAC3B,MAAME,GAAG,GAAGC,MAAM,CAACH,KAAK,CAAC;IAAEI,GAAG,GAAGF,GAAG,CAACG,MAAM,GAAG,CAAC;EAC/C,OAAOX,YAAY,CAACC,OAAO,CAACW,EAAE,IAAIF,GAAG,GAAGD,MAAM,CAACI,YAAY,CAAC,EAAE,GAAGH,GAAG,CAAC,GAAG,EAAE,CAAC,GAAGF,GAAG;AACnF;AACA,SAASM,iBAAiBA,CAACb,OAAO,EAAE;EAClCD,YAAY,CAACC,OAAO,GAAGA,OAAO;AAChC;AACA,SAASc,kBAAkBA,CAAA,EAAG;EAC5B,OAAO;IACL,GAAGf,YAAY,CAACC,OAAO;IACvBW,EAAE,EAAEZ,YAAY,CAACO,gBAAgB,CAAC,CAAC;IACnCD,KAAK,EAAE;EACT,CAAC;AACH;AACA,IAAIU,MAAM,GAAG,KAAK;AAClB,IAAIC,OAAO,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,KAAKC,CAAC;AAC/B,IAAIC,MAAM,GAAGC,MAAM,CAAC,aAAa,CAAC;AAClC,IAAIC,cAAc,GAAG,OAAOC,KAAK,KAAK,UAAU;AAChD,IAAIC,MAAM,GAAGH,MAAM,CAAC,aAAa,CAAC;AAClC,IAAII,aAAa,GAAG;EAClBC,MAAM,EAAET;AACV,CAAC;AACD,IAAIU,KAAK,GAAG,IAAI;AAChB,IAAIC,UAAU,GAAGC,QAAQ;AACzB,IAAIC,KAAK,GAAG,CAAC;AACb,IAAIC,OAAO,GAAG,CAAC;AACf,IAAIC,OAAO,GAAG;EACZC,KAAK,EAAE,IAAI;EACXC,QAAQ,EAAE,IAAI;EACdjC,OAAO,EAAE,IAAI;EACbkC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,OAAO,GAAG,CAAC,CAAC;AAChB,IAAIC,KAAK,GAAG,IAAI;AAChB,IAAIC,UAAU,GAAG,IAAI;AACrB,IAAIC,SAAS,GAAG,IAAI;AACpB,IAAIC,oBAAoB,GAAG,IAAI;AAC/B,IAAIC,QAAQ,GAAG,IAAI;AACnB,IAAIC,OAAO,GAAG,IAAI;AAClB,IAAIC,OAAO,GAAG,IAAI;AAClB,IAAIC,SAAS,GAAG,CAAC;AACjB,SAASC,UAAUA,CAACC,EAAE,EAAEC,aAAa,EAAE;EACrC,MAAMC,QAAQ,GAAGP,QAAQ;IAAEN,KAAK,GAAGE,KAAK;IAAEY,OAAO,GAAGH,EAAE,CAACnC,MAAM,KAAK,CAAC;IAAEuC,OAAO,GAAGH,aAAa,KAAK,KAAK,CAAC,GAAGZ,KAAK,GAAGY,aAAa;IAAEI,IAAI,GAAGF,OAAO,GAAGjB,OAAO,GAAG;MAC1JC,KAAK,EAAE,IAAI;MACXC,QAAQ,EAAE,IAAI;MACdjC,OAAO,EAAEiD,OAAO,GAAGA,OAAO,CAACjD,OAAO,GAAG,IAAI;MACzCkC,KAAK,EAAEe;IACT,CAAC;IAAEE,QAAQ,GAAGH,OAAO,GAAGH,EAAE,GAAG,MAAMA,EAAE,CAAC,MAAMO,OAAO,CAAC,MAAMC,SAAS,CAACH,IAAI,CAAC,CAAC,CAAC;EAC3Ed,KAAK,GAAGc,IAAI;EACZV,QAAQ,GAAG,IAAI;EACf,IAAI;IACF,OAAOc,UAAU,CAACH,QAAQ,EAAE,IAAI,CAAC;EACnC,CAAC,SAAS;IACRX,QAAQ,GAAGO,QAAQ;IACnBX,KAAK,GAAGF,KAAK;EACf;AACF;AACA,SAASqB,YAAYA,CAACC,KAAK,EAAEC,OAAO,EAAE;EACpCA,OAAO,GAAGA,OAAO,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEnC,aAAa,EAAEiC,OAAO,CAAC,GAAGjC,aAAa;EAC7E,MAAMoC,CAAC,GAAG;IACRJ,KAAK;IACLK,SAAS,EAAE,IAAI;IACfC,aAAa,EAAE,IAAI;IACnBC,UAAU,EAAEN,OAAO,CAAChC,MAAM,IAAI,KAAK;EACrC,CAAC;EACD,MAAMuC,MAAM,GAAIC,MAAM,IAAK;IACzB,IAAI,OAAOA,MAAM,KAAK,UAAU,EAAE;MAChC,IAAI5B,UAAU,IAAIA,UAAU,CAAC6B,OAAO,IAAI7B,UAAU,CAAC8B,OAAO,CAACC,GAAG,CAACR,CAAC,CAAC,EAAEK,MAAM,GAAGA,MAAM,CAACL,CAAC,CAACS,MAAM,CAAC,CAAC,KACxFJ,MAAM,GAAGA,MAAM,CAACL,CAAC,CAACJ,KAAK,CAAC;IAC/B;IACA,OAAOc,WAAW,CAACV,CAAC,EAAEK,MAAM,CAAC;EAC/B,CAAC;EACD,OAAO,CAACM,UAAU,CAACC,IAAI,CAACZ,CAAC,CAAC,EAAEI,MAAM,CAAC;AACrC;AACA,SAASS,cAAcA,CAAC5B,EAAE,EAAEW,KAAK,EAAEC,OAAO,EAAE;EAC1C,MAAMiB,CAAC,GAAGC,iBAAiB,CAAC9B,EAAE,EAAEW,KAAK,EAAE,IAAI,EAAE3B,KAAK,CAAC;EACnD,IAAIS,SAAS,IAAID,UAAU,IAAIA,UAAU,CAAC6B,OAAO,EAAEzB,OAAO,CAACmC,IAAI,CAACF,CAAC,CAAC,CAAC,KAC9DG,iBAAiB,CAACH,CAAC,CAAC;AAC3B;AACA,SAASI,kBAAkBA,CAACjC,EAAE,EAAEW,KAAK,EAAEC,OAAO,EAAE;EAC9C,MAAMiB,CAAC,GAAGC,iBAAiB,CAAC9B,EAAE,EAAEW,KAAK,EAAE,KAAK,EAAE3B,KAAK,CAAC;EACpD,IAAIS,SAAS,IAAID,UAAU,IAAIA,UAAU,CAAC6B,OAAO,EAAEzB,OAAO,CAACmC,IAAI,CAACF,CAAC,CAAC,CAAC,KAC9DG,iBAAiB,CAACH,CAAC,CAAC;AAC3B;AACA,SAASK,YAAYA,CAAClC,EAAE,EAAEW,KAAK,EAAEC,OAAO,EAAE;EACxC9B,UAAU,GAAGqD,cAAc;EAC3B,MAAMN,CAAC,GAAGC,iBAAiB,CAAC9B,EAAE,EAAEW,KAAK,EAAE,KAAK,EAAE3B,KAAK,CAAC;IAAE+B,CAAC,GAAGqB,eAAe,IAAIC,UAAU,CAACD,eAAe,CAAC;EACxG,IAAIrB,CAAC,EAAEc,CAAC,CAACS,QAAQ,GAAGvB,CAAC;EACrB,IAAI,CAACH,OAAO,IAAI,CAACA,OAAO,CAAC2B,MAAM,EAAEV,CAAC,CAACW,IAAI,GAAG,IAAI;EAC9C3C,OAAO,GAAGA,OAAO,CAACkC,IAAI,CAACF,CAAC,CAAC,GAAGG,iBAAiB,CAACH,CAAC,CAAC;AAClD;AACA,SAASY,UAAUA,CAACzC,EAAE,EAAEW,KAAK,EAAEC,OAAO,EAAE;EACtCA,OAAO,GAAGA,OAAO,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEnC,aAAa,EAAEiC,OAAO,CAAC,GAAGjC,aAAa;EAC7E,MAAMkD,CAAC,GAAGC,iBAAiB,CAAC9B,EAAE,EAAEW,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;EAC/CkB,CAAC,CAACb,SAAS,GAAG,IAAI;EAClBa,CAAC,CAACZ,aAAa,GAAG,IAAI;EACtBY,CAAC,CAACX,UAAU,GAAGN,OAAO,CAAChC,MAAM,IAAI,KAAK,CAAC;EACvC,IAAIa,SAAS,IAAID,UAAU,IAAIA,UAAU,CAAC6B,OAAO,EAAE;IACjDQ,CAAC,CAACa,MAAM,GAAG1D,KAAK;IAChBY,OAAO,CAACmC,IAAI,CAACF,CAAC,CAAC;EACjB,CAAC,MAAMG,iBAAiB,CAACH,CAAC,CAAC;EAC3B,OAAOH,UAAU,CAACC,IAAI,CAACE,CAAC,CAAC;AAC3B;AACA,SAASc,SAASA,CAACC,CAAC,EAAE;EACpB,OAAOA,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,IAAI,MAAM,IAAIA,CAAC;AAClD;AACA,SAASC,cAAcA,CAACC,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;EACnD,IAAIC,MAAM;EACV,IAAIC,OAAO;EACX,IAAItC,OAAO;EACX;IACEqC,MAAM,GAAG,IAAI;IACbC,OAAO,GAAGJ,OAAO;IACjBlC,OAAO,GAAG,CAAC,CAAC;EACd;EACA,IAAIuC,EAAE,GAAG,IAAI;IAAEC,KAAK,GAAG9D,OAAO;IAAExB,EAAE,GAAG,IAAI;IAAEuF,qBAAqB,GAAG,KAAK;IAAEC,SAAS,GAAG,KAAK;IAAEC,QAAQ,GAAG,cAAc,IAAI3C,OAAO;IAAE4C,OAAO,GAAG,OAAOP,MAAM,KAAK,UAAU,IAAIR,UAAU,CAACQ,MAAM,CAAC;EAC/L,MAAMQ,QAAQ,GAAG,eAAgB,IAAIC,GAAG,CAAC,CAAC;IAAE,CAAC/C,KAAK,EAAEgD,QAAQ,CAAC,GAAG,CAAC/C,OAAO,CAACgD,OAAO,IAAIlD,YAAY,EAAEE,OAAO,CAACiD,YAAY,CAAC;IAAE,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGrD,YAAY,CAAC,KAAK,CAAC,CAAC;IAAE,CAACsD,KAAK,EAAEC,OAAO,CAAC,GAAGvD,YAAY,CAAC,KAAK,CAAC,EAAE;MACzM9B,MAAM,EAAE;IACV,CAAC,CAAC;IAAE,CAACsF,KAAK,EAAEC,QAAQ,CAAC,GAAGzD,YAAY,CAAC6C,QAAQ,GAAG,OAAO,GAAG,YAAY,CAAC;EACvE,IAAIrG,YAAY,CAACC,OAAO,EAAE;IACxBW,EAAE,GAAGZ,YAAY,CAACO,gBAAgB,CAAC,CAAC;IACpC,IAAImD,OAAO,CAACwD,WAAW,KAAK,SAAS,EAAEhB,KAAK,GAAGxC,OAAO,CAACiD,YAAY,CAAC,KAC/D,IAAI3G,YAAY,CAACmH,IAAI,IAAInH,YAAY,CAACqE,GAAG,CAACzD,EAAE,CAAC,EAAEsF,KAAK,GAAGlG,YAAY,CAACmH,IAAI,CAACvG,EAAE,CAAC;EACnF;EACA,SAASwG,OAAOA,CAACC,CAAC,EAAE3B,CAAC,EAAE4B,MAAM,EAAEC,GAAG,EAAE;IAClC,IAAItB,EAAE,KAAKoB,CAAC,EAAE;MACZpB,EAAE,GAAG,IAAI;MACTsB,GAAG,KAAK,KAAK,CAAC,KAAKlB,QAAQ,GAAG,IAAI,CAAC;MACnC,IAAI,CAACgB,CAAC,KAAKnB,KAAK,IAAIR,CAAC,KAAKQ,KAAK,KAAKxC,OAAO,CAAC8D,UAAU,EACpDC,cAAc,CACZ,MAAM/D,OAAO,CAAC8D,UAAU,CAACD,GAAG,EAAE;QAC5B9D,KAAK,EAAEiC;MACT,CAAC,CACH,CAAC;MACHQ,KAAK,GAAG9D,OAAO;MACf,IAAIE,UAAU,IAAI+E,CAAC,IAAIlB,qBAAqB,EAAE;QAC5C7D,UAAU,CAACoF,QAAQ,CAACC,MAAM,CAACN,CAAC,CAAC;QAC7BlB,qBAAqB,GAAG,KAAK;QAC7B5C,UAAU,CAAC,MAAM;UACfjB,UAAU,CAAC6B,OAAO,GAAG,IAAI;UACzByD,YAAY,CAAClC,CAAC,EAAE4B,MAAM,CAAC;QACzB,CAAC,EAAE,KAAK,CAAC;MACX,CAAC,MAAMM,YAAY,CAAClC,CAAC,EAAE4B,MAAM,CAAC;IAChC;IACA,OAAO5B,CAAC;EACV;EACA,SAASkC,YAAYA,CAAClC,CAAC,EAAEmC,GAAG,EAAE;IAC5BtE,UAAU,CAAC,MAAM;MACf,IAAIsE,GAAG,KAAK,KAAK,CAAC,EAAEpB,QAAQ,CAAC,MAAMf,CAAC,CAAC;MACrCuB,QAAQ,CAACY,GAAG,KAAK,KAAK,CAAC,GAAG,SAAS,GAAGxB,QAAQ,GAAG,OAAO,GAAG,YAAY,CAAC;MACxEQ,QAAQ,CAACgB,GAAG,CAAC;MACb,KAAK,MAAMlD,CAAC,IAAI4B,QAAQ,CAACuB,IAAI,CAAC,CAAC,EAAEnD,CAAC,CAACoD,SAAS,CAAC,CAAC;MAC9CxB,QAAQ,CAACyB,KAAK,CAAC,CAAC;IAClB,CAAC,EAAE,KAAK,CAAC;EACX;EACA,SAASC,IAAIA,CAAA,EAAG;IACd,MAAMtD,CAAC,GAAGO,eAAe,IAAIC,UAAU,CAACD,eAAe,CAAC;MAAEQ,CAAC,GAAGjC,KAAK,CAAC,CAAC;MAAEoE,GAAG,GAAGjB,KAAK,CAAC,CAAC;IACpF,IAAIiB,GAAG,KAAK,KAAK,CAAC,IAAI,CAAC5B,EAAE,EAAE,MAAM4B,GAAG;IACpC,IAAIpF,QAAQ,IAAI,CAACA,QAAQ,CAAC6C,IAAI,IAAIX,CAAC,EAAE;MACnCD,cAAc,CAAC,MAAM;QACnBoC,KAAK,CAAC,CAAC;QACP,IAAIb,EAAE,EAAE;UACN,IAAItB,CAAC,CAAC0B,QAAQ,IAAI/D,UAAU,IAAI6D,qBAAqB,EAAE7D,UAAU,CAACoF,QAAQ,CAACQ,GAAG,CAACjC,EAAE,CAAC,CAAC,KAC9E,IAAI,CAACM,QAAQ,CAAClC,GAAG,CAACM,CAAC,CAAC,EAAE;YACzBA,CAAC,CAACwD,SAAS,CAAC,CAAC;YACb5B,QAAQ,CAAC2B,GAAG,CAACvD,CAAC,CAAC;UACjB;QACF;MACF,CAAC,CAAC;IACJ;IACA,OAAOe,CAAC;EACV;EACA,SAASyB,IAAIA,CAACiB,UAAU,GAAG,IAAI,EAAE;IAC/B,IAAIA,UAAU,KAAK,KAAK,IAAIhC,SAAS,EAAE;IACvCA,SAAS,GAAG,KAAK;IACjB,MAAMiC,MAAM,GAAG/B,OAAO,GAAGA,OAAO,CAAC,CAAC,GAAGP,MAAM;IAC3CI,qBAAqB,GAAG7D,UAAU,IAAIA,UAAU,CAAC6B,OAAO;IACxD,IAAIkE,MAAM,IAAI,IAAI,IAAIA,MAAM,KAAK,KAAK,EAAE;MACtCjB,OAAO,CAACnB,EAAE,EAAE5C,OAAO,CAACI,KAAK,CAAC,CAAC;MAC3B;IACF;IACA,IAAInB,UAAU,IAAI2D,EAAE,EAAE3D,UAAU,CAACoF,QAAQ,CAACC,MAAM,CAAC1B,EAAE,CAAC;IACpD,MAAMoB,CAAC,GAAGnB,KAAK,KAAK9D,OAAO,GAAG8D,KAAK,GAAG7C,OAAO,CAC3C,MAAM2C,OAAO,CAACqC,MAAM,EAAE;MACpB5E,KAAK,EAAEA,KAAK,CAAC,CAAC;MACd2E;IACF,CAAC,CACH,CAAC;IACD,IAAI,CAAC3C,SAAS,CAAC4B,CAAC,CAAC,EAAE;MACjBD,OAAO,CAACnB,EAAE,EAAEoB,CAAC,EAAE,KAAK,CAAC,EAAEgB,MAAM,CAAC;MAC9B,OAAOhB,CAAC;IACV;IACApB,EAAE,GAAGoB,CAAC;IACN,IAAI,OAAO,IAAIA,CAAC,EAAE;MAChB,IAAIA,CAAC,CAACiB,MAAM,KAAK,SAAS,EAAElB,OAAO,CAACnB,EAAE,EAAEoB,CAAC,CAAC5D,KAAK,EAAE,KAAK,CAAC,EAAE4E,MAAM,CAAC,CAAC,KAC5DjB,OAAO,CAACnB,EAAE,EAAE,KAAK,CAAC,EAAEsC,SAAS,CAAClB,CAAC,CAAC5D,KAAK,CAAC,EAAE4E,MAAM,CAAC;MACpD,OAAOhB,CAAC;IACV;IACAjB,SAAS,GAAG,IAAI;IAChBqB,cAAc,CAAC,MAAMrB,SAAS,GAAG,KAAK,CAAC;IACvC7C,UAAU,CAAC,MAAM;MACf0D,QAAQ,CAACZ,QAAQ,GAAG,YAAY,GAAG,SAAS,CAAC;MAC7CU,OAAO,CAAC,CAAC;IACX,CAAC,EAAE,KAAK,CAAC;IACT,OAAOM,CAAC,CAACmB,IAAI,CACV9C,CAAC,IAAK0B,OAAO,CAACC,CAAC,EAAE3B,CAAC,EAAE,KAAK,CAAC,EAAE2C,MAAM,CAAC,EACnCI,CAAC,IAAKrB,OAAO,CAACC,CAAC,EAAE,KAAK,CAAC,EAAEkB,SAAS,CAACE,CAAC,CAAC,EAAEJ,MAAM,CAChD,CAAC;EACH;EACA1E,MAAM,CAAC+E,gBAAgB,CAACT,IAAI,EAAE;IAC5BjB,KAAK,EAAE;MACL2B,GAAG,EAAEA,CAAA,KAAM3B,KAAK,CAAC;IACnB,CAAC;IACDJ,KAAK,EAAE;MACL+B,GAAG,EAAEA,CAAA,KAAM/B,KAAK,CAAC;IACnB,CAAC;IACDgC,OAAO,EAAE;MACPD,GAAGA,CAAA,EAAG;QACJ,MAAM9E,CAAC,GAAGmD,KAAK,CAAC,CAAC;QACjB,OAAOnD,CAAC,KAAK,SAAS,IAAIA,CAAC,KAAK,YAAY;MAC9C;IACF,CAAC;IACDgF,MAAM,EAAE;MACNF,GAAGA,CAAA,EAAG;QACJ,IAAI,CAACtC,QAAQ,EAAE,OAAO4B,IAAI,CAAC,CAAC;QAC5B,MAAMJ,GAAG,GAAGjB,KAAK,CAAC,CAAC;QACnB,IAAIiB,GAAG,IAAI,CAAC5B,EAAE,EAAE,MAAM4B,GAAG;QACzB,OAAOpE,KAAK,CAAC,CAAC;MAChB;IACF;EACF,CAAC,CAAC;EACF,IAAI6C,OAAO,EAAE5B,cAAc,CAAC,MAAMyC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAC1CA,IAAI,CAAC,KAAK,CAAC;EAChB,OAAO,CACLc,IAAI,EACJ;IACEa,OAAO,EAAE3B,IAAI;IACb4B,MAAM,EAAEtC;EACV,CAAC,CACF;AACH;AACA,SAASuC,KAAKA,CAAClG,EAAE,EAAE;EACjB,OAAOS,UAAU,CAACT,EAAE,EAAE,KAAK,CAAC;AAC9B;AACA,SAASO,OAAOA,CAACP,EAAE,EAAE;EACnB,IAAI,CAACN,oBAAoB,IAAIC,QAAQ,KAAK,IAAI,EAAE,OAAOK,EAAE,CAAC,CAAC;EAC3D,MAAME,QAAQ,GAAGP,QAAQ;EACzBA,QAAQ,GAAG,IAAI;EACf,IAAI;IACF,IAAID,oBAAoB,EAAE,OAAOA,oBAAoB,CAACa,OAAO,CAACP,EAAE,CAAC;IACjE,OAAOA,EAAE,CAAC,CAAC;EACb,CAAC,SAAS;IACRL,QAAQ,GAAGO,QAAQ;EACrB;AACF;AACA,SAASiG,EAAEA,CAACC,IAAI,EAAEpG,EAAE,EAAEY,OAAO,EAAE;EAC7B,MAAMyF,QAAQ,GAAGC,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC;EACpC,IAAII,SAAS;EACb,IAAIC,KAAK,GAAG7F,OAAO,IAAIA,OAAO,CAAC6F,KAAK;EACpC,OAAQC,SAAS,IAAK;IACpB,IAAIC,KAAK;IACT,IAAIN,QAAQ,EAAE;MACZM,KAAK,GAAGL,KAAK,CAACF,IAAI,CAACvI,MAAM,CAAC;MAC1B,KAAK,IAAI+I,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,IAAI,CAACvI,MAAM,EAAE+I,CAAC,EAAE,EAAED,KAAK,CAACC,CAAC,CAAC,GAAGR,IAAI,CAACQ,CAAC,CAAC,CAAC,CAAC;IAC5D,CAAC,MAAMD,KAAK,GAAGP,IAAI,CAAC,CAAC;IACrB,IAAIK,KAAK,EAAE;MACTA,KAAK,GAAG,KAAK;MACb,OAAOC,SAAS;IAClB;IACA,MAAMG,MAAM,GAAGtG,OAAO,CAAC,MAAMP,EAAE,CAAC2G,KAAK,EAAEH,SAAS,EAAEE,SAAS,CAAC,CAAC;IAC7DF,SAAS,GAAGG,KAAK;IACjB,OAAOE,MAAM;EACf,CAAC;AACH;AACA,SAASC,OAAOA,CAAC9G,EAAE,EAAE;EACnBkC,YAAY,CAAC,MAAM3B,OAAO,CAACP,EAAE,CAAC,CAAC;AACjC;AACA,SAAS+G,SAASA,CAAC/G,EAAE,EAAE;EACrB,IAAIT,KAAK,KAAK,IAAI,EAAE,CAAC,KAChB,IAAIA,KAAK,CAACH,QAAQ,KAAK,IAAI,EAAEG,KAAK,CAACH,QAAQ,GAAG,CAACY,EAAE,CAAC,CAAC,KACnDT,KAAK,CAACH,QAAQ,CAAC2C,IAAI,CAAC/B,EAAE,CAAC;EAC5B,OAAOA,EAAE;AACX;AACA,SAASgH,QAAQA,CAAA,EAAG;EAClB,OAAOzH,KAAK;AACd;AACA,SAAS0H,YAAYA,CAACC,CAAC,EAAElH,EAAE,EAAE;EAC3B,MAAMmH,IAAI,GAAG5H,KAAK;EAClB,MAAM6H,YAAY,GAAGzH,QAAQ;EAC7BJ,KAAK,GAAG2H,CAAC;EACTvH,QAAQ,GAAG,IAAI;EACf,IAAI;IACF,OAAOc,UAAU,CAACT,EAAE,EAAE,IAAI,CAAC;EAC7B,CAAC,CAAC,OAAO+E,GAAG,EAAE;IACZsC,WAAW,CAACtC,GAAG,CAAC;EAClB,CAAC,SAAS;IACRxF,KAAK,GAAG4H,IAAI;IACZxH,QAAQ,GAAGyH,YAAY;EACzB;AACF;AACA,SAASE,eAAeA,CAACtH,EAAE,EAAE;EAC3B,IAAIR,UAAU,IAAIA,UAAU,CAAC6B,OAAO,EAAE;IACpCrB,EAAE,CAAC,CAAC;IACJ,OAAOR,UAAU,CAAClC,IAAI;EACxB;EACA,MAAMiK,CAAC,GAAG5H,QAAQ;EAClB,MAAMuH,CAAC,GAAG3H,KAAK;EACf,OAAOiI,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC/B,IAAI,CAAC,MAAM;IAClC/F,QAAQ,GAAG4H,CAAC;IACZhI,KAAK,GAAG2H,CAAC;IACT,IAAIQ,CAAC;IACL,IAAIjI,SAAS,IAAI2C,eAAe,EAAE;MAChCsF,CAAC,GAAGlI,UAAU,KAAKA,UAAU,GAAG;QAC9B8B,OAAO,EAAE,eAAgB,IAAIoC,GAAG,CAAC,CAAC;QAClCrG,OAAO,EAAE,EAAE;QACXuH,QAAQ,EAAE,eAAgB,IAAIlB,GAAG,CAAC,CAAC;QACnCiE,QAAQ,EAAE,eAAgB,IAAIjE,GAAG,CAAC,CAAC;QACnCkE,KAAK,EAAE,eAAgB,IAAIlE,GAAG,CAAC,CAAC;QAChCrC,OAAO,EAAE;MACX,CAAC,CAAC;MACFqG,CAAC,CAACpK,IAAI,KAAKoK,CAAC,CAACpK,IAAI,GAAG,IAAIkK,OAAO,CAAEK,GAAG,IAAKH,CAAC,CAACD,OAAO,GAAGI,GAAG,CAAC,CAAC;MAC1DH,CAAC,CAACrG,OAAO,GAAG,IAAI;IAClB;IACAZ,UAAU,CAACT,EAAE,EAAE,KAAK,CAAC;IACrBL,QAAQ,GAAGJ,KAAK,GAAG,IAAI;IACvB,OAAOmI,CAAC,GAAGA,CAAC,CAACpK,IAAI,GAAG,KAAK,CAAC;EAC5B,CAAC,CAAC;AACJ;AACA,IAAI,CAACwK,YAAY,EAAEC,eAAe,CAAC,GAAG,eAAgBrH,YAAY,CAAC,KAAK,CAAC;AACzE,SAASsH,aAAaA,CAAA,EAAG;EACvB,OAAO,CAACF,YAAY,EAAER,eAAe,CAAC;AACxC;AACA,SAASW,aAAaA,CAACC,YAAY,EAAEtH,OAAO,EAAE;EAC5C,MAAM9C,EAAE,GAAGS,MAAM,CAAC,SAAS,CAAC;EAC5B,OAAO;IACLT,EAAE;IACFqK,QAAQ,EAAEC,cAAc,CAACtK,EAAE,CAAC;IAC5BoK;EACF,CAAC;AACH;AACA,SAAS7F,UAAUA,CAAClF,OAAO,EAAE;EAC3B,IAAIwD,KAAK;EACT,OAAOpB,KAAK,IAAIA,KAAK,CAACpC,OAAO,IAAI,CAACwD,KAAK,GAAGpB,KAAK,CAACpC,OAAO,CAACA,OAAO,CAACW,EAAE,CAAC,MAAM,KAAK,CAAC,GAAG6C,KAAK,GAAGxD,OAAO,CAAC+K,YAAY;AAChH;AACA,SAASG,QAAQA,CAACrI,EAAE,EAAE;EACpB,MAAMsI,SAAS,GAAG7F,UAAU,CAACzC,EAAE,CAAC;EAChC,MAAMuI,IAAI,GAAG9F,UAAU,CAAC,MAAM+F,eAAe,CAACF,SAAS,CAAC,CAAC,CAAC,CAAC;EAC3DC,IAAI,CAACE,OAAO,GAAG,MAAM;IACnB,MAAM5G,CAAC,GAAG0G,IAAI,CAAC,CAAC;IAChB,OAAOjC,KAAK,CAACC,OAAO,CAAC1E,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,IAAI,IAAI,GAAG,CAACA,CAAC,CAAC,GAAG,EAAE;EACpD,CAAC;EACD,OAAO0G,IAAI;AACb;AACA,IAAInG,eAAe;AACnB,SAASV,UAAUA,CAAA,EAAG;EACpB,MAAMgH,iBAAiB,GAAGlJ,UAAU,IAAIA,UAAU,CAAC6B,OAAO;EAC1D,IAAI,IAAI,CAACC,OAAO,KAAKoH,iBAAiB,GAAG,IAAI,CAAChG,MAAM,GAAG,IAAI,CAACwB,KAAK,CAAC,EAAE;IAClE,IAAI,CAACwE,iBAAiB,GAAG,IAAI,CAAChG,MAAM,GAAG,IAAI,CAACwB,KAAK,MAAMlF,KAAK,EAAEgD,iBAAiB,CAAC,IAAI,CAAC,CAAC,KACjF;MACH,MAAM2G,OAAO,GAAG/I,OAAO;MACvBA,OAAO,GAAG,IAAI;MACda,UAAU,CAAC,MAAMmI,YAAY,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC;MAC3ChJ,OAAO,GAAG+I,OAAO;IACnB;EACF;EACA,IAAIhJ,QAAQ,EAAE;IACZ,MAAMkJ,KAAK,GAAG,IAAI,CAAC7H,SAAS,GAAG,IAAI,CAACA,SAAS,CAACnD,MAAM,GAAG,CAAC;IACxD,IAAI,CAAC8B,QAAQ,CAAC2B,OAAO,EAAE;MACrB3B,QAAQ,CAAC2B,OAAO,GAAG,CAAC,IAAI,CAAC;MACzB3B,QAAQ,CAACmJ,WAAW,GAAG,CAACD,KAAK,CAAC;IAChC,CAAC,MAAM;MACLlJ,QAAQ,CAAC2B,OAAO,CAACS,IAAI,CAAC,IAAI,CAAC;MAC3BpC,QAAQ,CAACmJ,WAAW,CAAC/G,IAAI,CAAC8G,KAAK,CAAC;IAClC;IACA,IAAI,CAAC,IAAI,CAAC7H,SAAS,EAAE;MACnB,IAAI,CAACA,SAAS,GAAG,CAACrB,QAAQ,CAAC;MAC3B,IAAI,CAACsB,aAAa,GAAG,CAACtB,QAAQ,CAAC2B,OAAO,CAACzD,MAAM,GAAG,CAAC,CAAC;IACpD,CAAC,MAAM;MACL,IAAI,CAACmD,SAAS,CAACe,IAAI,CAACpC,QAAQ,CAAC;MAC7B,IAAI,CAACsB,aAAa,CAACc,IAAI,CAACpC,QAAQ,CAAC2B,OAAO,CAACzD,MAAM,GAAG,CAAC,CAAC;IACtD;EACF;EACA,IAAI6K,iBAAiB,IAAIlJ,UAAU,CAAC8B,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC,EAAE,OAAO,IAAI,CAACC,MAAM;EACzE,OAAO,IAAI,CAACb,KAAK;AACnB;AACA,SAASc,WAAWA,CAACsH,IAAI,EAAEpI,KAAK,EAAEqI,MAAM,EAAE;EACxC,IAAI5I,OAAO,GAAGZ,UAAU,IAAIA,UAAU,CAAC6B,OAAO,IAAI7B,UAAU,CAAC8B,OAAO,CAACC,GAAG,CAACwH,IAAI,CAAC,GAAGA,IAAI,CAACvH,MAAM,GAAGuH,IAAI,CAACpI,KAAK;EACzG,IAAI,CAACoI,IAAI,CAAC7H,UAAU,IAAI,CAAC6H,IAAI,CAAC7H,UAAU,CAACd,OAAO,EAAEO,KAAK,CAAC,EAAE;IACxD,IAAInB,UAAU,EAAE;MACd,MAAMyJ,iBAAiB,GAAGzJ,UAAU,CAAC6B,OAAO;MAC5C,IAAI4H,iBAAiB,IAAI,CAACD,MAAM,IAAIxJ,UAAU,CAAC8B,OAAO,CAACC,GAAG,CAACwH,IAAI,CAAC,EAAE;QAChEvJ,UAAU,CAAC8B,OAAO,CAAC8D,GAAG,CAAC2D,IAAI,CAAC;QAC5BA,IAAI,CAACvH,MAAM,GAAGb,KAAK;MACrB;MACA,IAAI,CAACsI,iBAAiB,EAAEF,IAAI,CAACpI,KAAK,GAAGA,KAAK;IAC5C,CAAC,MAAMoI,IAAI,CAACpI,KAAK,GAAGA,KAAK;IACzB,IAAIoI,IAAI,CAAC/H,SAAS,IAAI+H,IAAI,CAAC/H,SAAS,CAACnD,MAAM,EAAE;MAC3C4C,UAAU,CAAC,MAAM;QACf,KAAK,IAAImG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmC,IAAI,CAAC/H,SAAS,CAACnD,MAAM,EAAE+I,CAAC,IAAI,CAAC,EAAE;UACjD,MAAMM,CAAC,GAAG6B,IAAI,CAAC/H,SAAS,CAAC4F,CAAC,CAAC;UAC3B,MAAMqC,iBAAiB,GAAGzJ,UAAU,IAAIA,UAAU,CAAC6B,OAAO;UAC1D,IAAI4H,iBAAiB,IAAIzJ,UAAU,CAACmI,QAAQ,CAACpG,GAAG,CAAC2F,CAAC,CAAC,EAAE;UACrD,IAAI+B,iBAAiB,GAAG,CAAC/B,CAAC,CAACxE,MAAM,GAAG,CAACwE,CAAC,CAAChD,KAAK,EAAE;YAC5C,IAAIgD,CAAC,CAACgC,IAAI,EAAEtJ,OAAO,CAACmC,IAAI,CAACmF,CAAC,CAAC,CAAC,KACvBrH,OAAO,CAACkC,IAAI,CAACmF,CAAC,CAAC;YACpB,IAAIA,CAAC,CAAClG,SAAS,EAAEmI,cAAc,CAACjC,CAAC,CAAC;UACpC;UACA,IAAI,CAAC+B,iBAAiB,EAAE/B,CAAC,CAAChD,KAAK,GAAGlF,KAAK,CAAC,KACnCkI,CAAC,CAACxE,MAAM,GAAG1D,KAAK;QACvB;QACA,IAAIY,OAAO,CAAC/B,MAAM,GAAG,GAAG,EAAE;UACxB+B,OAAO,GAAG,EAAE;UACZ,IAAI1B,MAAM,EAAE;UACZ,MAAM,IAAIkL,KAAK,CAAC,CAAC;QACnB;MACF,CAAC,EAAE,KAAK,CAAC;IACX;EACF;EACA,OAAOzI,KAAK;AACd;AACA,SAASqB,iBAAiBA,CAAC+G,IAAI,EAAE;EAC/B,IAAI,CAACA,IAAI,CAAC/I,EAAE,EAAE;EACdQ,SAAS,CAACuI,IAAI,CAAC;EACf,MAAMM,IAAI,GAAGvJ,SAAS;EACtBwJ,cAAc,CACZP,IAAI,EACJvJ,UAAU,IAAIA,UAAU,CAAC6B,OAAO,IAAI7B,UAAU,CAAC8B,OAAO,CAACC,GAAG,CAACwH,IAAI,CAAC,GAAGA,IAAI,CAACvH,MAAM,GAAGuH,IAAI,CAACpI,KAAK,EAC3F0I,IACF,CAAC;EACD,IAAI7J,UAAU,IAAI,CAACA,UAAU,CAAC6B,OAAO,IAAI7B,UAAU,CAAC8B,OAAO,CAACC,GAAG,CAACwH,IAAI,CAAC,EAAE;IACrEpE,cAAc,CAAC,MAAM;MACnBlE,UAAU,CAAC,MAAM;QACfjB,UAAU,KAAKA,UAAU,CAAC6B,OAAO,GAAG,IAAI,CAAC;QACzC1B,QAAQ,GAAGJ,KAAK,GAAGwJ,IAAI;QACvBO,cAAc,CAACP,IAAI,EAAEA,IAAI,CAACvH,MAAM,EAAE6H,IAAI,CAAC;QACvC1J,QAAQ,GAAGJ,KAAK,GAAG,IAAI;MACzB,CAAC,EAAE,KAAK,CAAC;IACX,CAAC,CAAC;EACJ;AACF;AACA,SAAS+J,cAAcA,CAACP,IAAI,EAAEpI,KAAK,EAAE0I,IAAI,EAAE;EACzC,IAAIE,SAAS;EACb,MAAMlK,KAAK,GAAGE,KAAK;IAAEW,QAAQ,GAAGP,QAAQ;EACxCA,QAAQ,GAAGJ,KAAK,GAAGwJ,IAAI;EACvB,IAAI;IACFQ,SAAS,GAAGR,IAAI,CAAC/I,EAAE,CAACW,KAAK,CAAC;EAC5B,CAAC,CAAC,OAAOoE,GAAG,EAAE;IACZ,IAAIgE,IAAI,CAACG,IAAI,EAAE;MACb,IAAI1J,UAAU,IAAIA,UAAU,CAAC6B,OAAO,EAAE;QACpC0H,IAAI,CAACrG,MAAM,GAAG1D,KAAK;QACnB+J,IAAI,CAACS,MAAM,IAAIT,IAAI,CAACS,MAAM,CAACC,OAAO,CAACjJ,SAAS,CAAC;QAC7CuI,IAAI,CAACS,MAAM,GAAG,KAAK,CAAC;MACtB,CAAC,MAAM;QACLT,IAAI,CAAC7E,KAAK,GAAGlF,KAAK;QAClB+J,IAAI,CAAC5J,KAAK,IAAI4J,IAAI,CAAC5J,KAAK,CAACsK,OAAO,CAACjJ,SAAS,CAAC;QAC3CuI,IAAI,CAAC5J,KAAK,GAAG,IAAI;MACnB;IACF;IACA4J,IAAI,CAACW,SAAS,GAAGL,IAAI,GAAG,CAAC;IACzB,OAAOhC,WAAW,CAACtC,GAAG,CAAC;EACzB,CAAC,SAAS;IACRpF,QAAQ,GAAGO,QAAQ;IACnBX,KAAK,GAAGF,KAAK;EACf;EACA,IAAI,CAAC0J,IAAI,CAACW,SAAS,IAAIX,IAAI,CAACW,SAAS,IAAIL,IAAI,EAAE;IAC7C,IAAIN,IAAI,CAACW,SAAS,IAAI,IAAI,IAAI,WAAW,IAAIX,IAAI,EAAE;MACjDtH,WAAW,CAACsH,IAAI,EAAEQ,SAAS,EAAE,IAAI,CAAC;IACpC,CAAC,MAAM,IAAI/J,UAAU,IAAIA,UAAU,CAAC6B,OAAO,IAAI0H,IAAI,CAACG,IAAI,EAAE;MACxD1J,UAAU,CAAC8B,OAAO,CAAC8D,GAAG,CAAC2D,IAAI,CAAC;MAC5BA,IAAI,CAACvH,MAAM,GAAG+H,SAAS;IACzB,CAAC,MAAMR,IAAI,CAACpI,KAAK,GAAG4I,SAAS;IAC7BR,IAAI,CAACW,SAAS,GAAGL,IAAI;EACvB;AACF;AACA,SAASvH,iBAAiBA,CAAC9B,EAAE,EAAE2J,IAAI,EAAET,IAAI,EAAEhF,KAAK,GAAGlF,KAAK,EAAE4B,OAAO,EAAE;EACjE,MAAMiB,CAAC,GAAG;IACR7B,EAAE;IACFkE,KAAK;IACLwF,SAAS,EAAE,IAAI;IACfvK,KAAK,EAAE,IAAI;IACXmC,OAAO,EAAE,IAAI;IACbwH,WAAW,EAAE,IAAI;IACjB1J,QAAQ,EAAE,IAAI;IACduB,KAAK,EAAEgJ,IAAI;IACXtK,KAAK,EAAEE,KAAK;IACZpC,OAAO,EAAEoC,KAAK,GAAGA,KAAK,CAACpC,OAAO,GAAG,IAAI;IACrC+L;EACF,CAAC;EACD,IAAI1J,UAAU,IAAIA,UAAU,CAAC6B,OAAO,EAAE;IACpCQ,CAAC,CAACqC,KAAK,GAAG,CAAC;IACXrC,CAAC,CAACa,MAAM,GAAGwB,KAAK;EAClB;EACA,IAAI3E,KAAK,KAAK,IAAI,EAAE,CAAC,KAChB,IAAIA,KAAK,KAAKL,OAAO,EAAE;IAC1B,IAAIM,UAAU,IAAIA,UAAU,CAAC6B,OAAO,IAAI9B,KAAK,CAAC2J,IAAI,EAAE;MAClD,IAAI,CAAC3J,KAAK,CAACiK,MAAM,EAAEjK,KAAK,CAACiK,MAAM,GAAG,CAAC3H,CAAC,CAAC,CAAC,KACjCtC,KAAK,CAACiK,MAAM,CAACzH,IAAI,CAACF,CAAC,CAAC;IAC3B,CAAC,MAAM;MACL,IAAI,CAACtC,KAAK,CAACJ,KAAK,EAAEI,KAAK,CAACJ,KAAK,GAAG,CAAC0C,CAAC,CAAC,CAAC,KAC/BtC,KAAK,CAACJ,KAAK,CAAC4C,IAAI,CAACF,CAAC,CAAC;IAC1B;EACF;EACA,IAAInC,oBAAoB,IAAImC,CAAC,CAAC7B,EAAE,EAAE;IAChC,MAAM,CAACgE,KAAK,EAAEC,OAAO,CAAC,GAAGvD,YAAY,CAAC,KAAK,CAAC,EAAE;MAC5C9B,MAAM,EAAE;IACV,CAAC,CAAC;IACF,MAAMgL,QAAQ,GAAGlK,oBAAoB,CAACmK,OAAO,CAAChI,CAAC,CAAC7B,EAAE,EAAEiE,OAAO,CAAC;IAC5D8C,SAAS,CAAC,MAAM6C,QAAQ,CAACE,OAAO,CAAC,CAAC,CAAC;IACnC,MAAMC,mBAAmB,GAAGA,CAAA,KAAMzC,eAAe,CAACrD,OAAO,CAAC,CAACyB,IAAI,CAAC,MAAMsE,YAAY,CAACF,OAAO,CAAC,CAAC,CAAC;IAC7F,MAAME,YAAY,GAAGtK,oBAAoB,CAACmK,OAAO,CAAChI,CAAC,CAAC7B,EAAE,EAAE+J,mBAAmB,CAAC;IAC5ElI,CAAC,CAAC7B,EAAE,GAAIiK,CAAC,IAAK;MACZjG,KAAK,CAAC,CAAC;MACP,OAAOxE,UAAU,IAAIA,UAAU,CAAC6B,OAAO,GAAG2I,YAAY,CAAChG,KAAK,CAACiG,CAAC,CAAC,GAAGL,QAAQ,CAAC5F,KAAK,CAACiG,CAAC,CAAC;IACrF,CAAC;EACH;EACA,OAAOpI,CAAC;AACV;AACA,SAASqI,MAAMA,CAACnB,IAAI,EAAE;EACpB,MAAML,iBAAiB,GAAGlJ,UAAU,IAAIA,UAAU,CAAC6B,OAAO;EAC1D,IAAI,CAACqH,iBAAiB,GAAGK,IAAI,CAACrG,MAAM,GAAGqG,IAAI,CAAC7E,KAAK,MAAM,CAAC,EAAE;EAC1D,IAAI,CAACwE,iBAAiB,GAAGK,IAAI,CAACrG,MAAM,GAAGqG,IAAI,CAAC7E,KAAK,MAAMjF,OAAO,EAAE,OAAO2J,YAAY,CAACG,IAAI,CAAC;EACzF,IAAIA,IAAI,CAACzG,QAAQ,IAAI/B,OAAO,CAACwI,IAAI,CAACzG,QAAQ,CAAC6H,UAAU,CAAC,EAAE,OAAOpB,IAAI,CAACzG,QAAQ,CAACjF,OAAO,CAAC0E,IAAI,CAACgH,IAAI,CAAC;EAC/F,MAAMqB,SAAS,GAAG,CAACrB,IAAI,CAAC;EACxB,OAAO,CAACA,IAAI,GAAGA,IAAI,CAAC1J,KAAK,MAAM,CAAC0J,IAAI,CAACW,SAAS,IAAIX,IAAI,CAACW,SAAS,GAAG5J,SAAS,CAAC,EAAE;IAC7E,IAAI4I,iBAAiB,IAAIlJ,UAAU,CAACmI,QAAQ,CAACpG,GAAG,CAACwH,IAAI,CAAC,EAAE;IACxD,IAAIL,iBAAiB,GAAGK,IAAI,CAACrG,MAAM,GAAGqG,IAAI,CAAC7E,KAAK,EAAEkG,SAAS,CAACrI,IAAI,CAACgH,IAAI,CAAC;EACxE;EACA,KAAK,IAAInC,CAAC,GAAGwD,SAAS,CAACvM,MAAM,GAAG,CAAC,EAAE+I,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC9CmC,IAAI,GAAGqB,SAAS,CAACxD,CAAC,CAAC;IACnB,IAAI8B,iBAAiB,EAAE;MACrB,IAAI2B,GAAG,GAAGtB,IAAI;QAAE5B,IAAI,GAAGiD,SAAS,CAACxD,CAAC,GAAG,CAAC,CAAC;MACvC,OAAO,CAACyD,GAAG,GAAGA,GAAG,CAAChL,KAAK,KAAKgL,GAAG,KAAKlD,IAAI,EAAE;QACxC,IAAI3H,UAAU,CAACmI,QAAQ,CAACpG,GAAG,CAAC8I,GAAG,CAAC,EAAE;MACpC;IACF;IACA,IAAI,CAAC3B,iBAAiB,GAAGK,IAAI,CAACrG,MAAM,GAAGqG,IAAI,CAAC7E,KAAK,MAAMlF,KAAK,EAAE;MAC5DgD,iBAAiB,CAAC+G,IAAI,CAAC;IACzB,CAAC,MAAM,IAAI,CAACL,iBAAiB,GAAGK,IAAI,CAACrG,MAAM,GAAGqG,IAAI,CAAC7E,KAAK,MAAMjF,OAAO,EAAE;MACrE,MAAM0J,OAAO,GAAG/I,OAAO;MACvBA,OAAO,GAAG,IAAI;MACda,UAAU,CAAC,MAAMmI,YAAY,CAACG,IAAI,EAAEqB,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;MACzDxK,OAAO,GAAG+I,OAAO;IACnB;EACF;AACF;AACA,SAASlI,UAAUA,CAACT,EAAE,EAAE2J,IAAI,EAAE;EAC5B,IAAI/J,OAAO,EAAE,OAAOI,EAAE,CAAC,CAAC;EACxB,IAAIsK,IAAI,GAAG,KAAK;EAChB,IAAI,CAACX,IAAI,EAAE/J,OAAO,GAAG,EAAE;EACvB,IAAIC,OAAO,EAAEyK,IAAI,GAAG,IAAI,CAAC,KACpBzK,OAAO,GAAG,EAAE;EACjBC,SAAS,EAAE;EACX,IAAI;IACF,MAAM+H,GAAG,GAAG7H,EAAE,CAAC,CAAC;IAChBuK,eAAe,CAACD,IAAI,CAAC;IACrB,OAAOzC,GAAG;EACZ,CAAC,CAAC,OAAO9C,GAAG,EAAE;IACZ,IAAI,CAACuF,IAAI,EAAEzK,OAAO,GAAG,IAAI;IACzBD,OAAO,GAAG,IAAI;IACdyH,WAAW,CAACtC,GAAG,CAAC;EAClB;AACF;AACA,SAASwF,eAAeA,CAACD,IAAI,EAAE;EAC7B,IAAI1K,OAAO,EAAE;IACX,IAAIH,SAAS,IAAID,UAAU,IAAIA,UAAU,CAAC6B,OAAO,EAAEmJ,aAAa,CAAC5K,OAAO,CAAC,CAAC,KACrEb,QAAQ,CAACa,OAAO,CAAC;IACtBA,OAAO,GAAG,IAAI;EAChB;EACA,IAAI0K,IAAI,EAAE;EACV,IAAIzC,GAAG;EACP,IAAIrI,UAAU,EAAE;IACd,IAAI,CAACA,UAAU,CAACoF,QAAQ,CAAC6F,IAAI,IAAI,CAACjL,UAAU,CAACoI,KAAK,CAAC6C,IAAI,EAAE;MACvD,MAAMnJ,OAAO,GAAG9B,UAAU,CAAC8B,OAAO;MAClC,MAAMqG,QAAQ,GAAGnI,UAAU,CAACmI,QAAQ;MACpC9H,OAAO,CAACkC,IAAI,CAAC2I,KAAK,CAAC7K,OAAO,EAAEL,UAAU,CAACnC,OAAO,CAAC;MAC/CwK,GAAG,GAAGrI,UAAU,CAACiI,OAAO;MACxB,KAAK,MAAMkD,EAAE,IAAI9K,OAAO,EAAE;QACxB,QAAQ,IAAI8K,EAAE,KAAKA,EAAE,CAACzG,KAAK,GAAGyG,EAAE,CAACjI,MAAM,CAAC;QACxC,OAAOiI,EAAE,CAACjI,MAAM;MAClB;MACAlD,UAAU,GAAG,IAAI;MACjBiB,UAAU,CAAC,MAAM;QACf,KAAK,MAAMmK,CAAC,IAAIjD,QAAQ,EAAEnH,SAAS,CAACoK,CAAC,CAAC;QACtC,KAAK,MAAMhI,CAAC,IAAItB,OAAO,EAAE;UACvBsB,CAAC,CAACjC,KAAK,GAAGiC,CAAC,CAACpB,MAAM;UAClB,IAAIoB,CAAC,CAACzD,KAAK,EAAE;YACX,KAAK,IAAIyH,CAAC,GAAG,CAAC,EAAEhJ,GAAG,GAAGgF,CAAC,CAACzD,KAAK,CAACtB,MAAM,EAAE+I,CAAC,GAAGhJ,GAAG,EAAEgJ,CAAC,EAAE,EAAEpG,SAAS,CAACoC,CAAC,CAACzD,KAAK,CAACyH,CAAC,CAAC,CAAC;UAC3E;UACA,IAAIhE,CAAC,CAAC4G,MAAM,EAAE5G,CAAC,CAACzD,KAAK,GAAGyD,CAAC,CAAC4G,MAAM;UAChC,OAAO5G,CAAC,CAACpB,MAAM;UACf,OAAOoB,CAAC,CAAC4G,MAAM;UACf5G,CAAC,CAACF,MAAM,GAAG,CAAC;QACd;QACAqF,eAAe,CAAC,KAAK,CAAC;MACxB,CAAC,EAAE,KAAK,CAAC;IACX,CAAC,MAAM,IAAIvI,UAAU,CAAC6B,OAAO,EAAE;MAC7B7B,UAAU,CAAC6B,OAAO,GAAG,KAAK;MAC1B7B,UAAU,CAACnC,OAAO,CAAC0E,IAAI,CAAC2I,KAAK,CAAClL,UAAU,CAACnC,OAAO,EAAEwC,OAAO,CAAC;MAC1DA,OAAO,GAAG,IAAI;MACdkI,eAAe,CAAC,IAAI,CAAC;MACrB;IACF;EACF;EACA,MAAMpC,CAAC,GAAG9F,OAAO;EACjBA,OAAO,GAAG,IAAI;EACd,IAAI8F,CAAC,CAAC9H,MAAM,EAAE4C,UAAU,CAAC,MAAM3B,UAAU,CAAC6G,CAAC,CAAC,EAAE,KAAK,CAAC;EACpD,IAAIkC,GAAG,EAAEA,GAAG,CAAC,CAAC;AAChB;AACA,SAAS9I,QAAQA,CAAC6I,KAAK,EAAE;EACvB,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgB,KAAK,CAAC/J,MAAM,EAAE+I,CAAC,EAAE,EAAEsD,MAAM,CAACtC,KAAK,CAAChB,CAAC,CAAC,CAAC;AACzD;AACA,SAAS4D,aAAaA,CAAC5C,KAAK,EAAE;EAC5B,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgB,KAAK,CAAC/J,MAAM,EAAE+I,CAAC,EAAE,EAAE;IACrC,MAAMiE,IAAI,GAAGjD,KAAK,CAAChB,CAAC,CAAC;IACrB,MAAMkE,KAAK,GAAGtL,UAAU,CAACoI,KAAK;IAC9B,IAAI,CAACkD,KAAK,CAACvJ,GAAG,CAACsJ,IAAI,CAAC,EAAE;MACpBC,KAAK,CAAC1F,GAAG,CAACyF,IAAI,CAAC;MACfpL,SAAS,CAAC,MAAM;QACdqL,KAAK,CAACjG,MAAM,CAACgG,IAAI,CAAC;QAClBpK,UAAU,CAAC,MAAM;UACfjB,UAAU,CAAC6B,OAAO,GAAG,IAAI;UACzB6I,MAAM,CAACW,IAAI,CAAC;QACd,CAAC,EAAE,KAAK,CAAC;QACTrL,UAAU,KAAKA,UAAU,CAAC6B,OAAO,GAAG,KAAK,CAAC;MAC5C,CAAC,CAAC;IACJ;EACF;AACF;AACA,SAASc,cAAcA,CAACyF,KAAK,EAAE;EAC7B,IAAIhB,CAAC;IAAEmE,UAAU,GAAG,CAAC;EACrB,KAAKnE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgB,KAAK,CAAC/J,MAAM,EAAE+I,CAAC,EAAE,EAAE;IACjC,MAAMjB,CAAC,GAAGiC,KAAK,CAAChB,CAAC,CAAC;IAClB,IAAI,CAACjB,CAAC,CAACnD,IAAI,EAAE0H,MAAM,CAACvE,CAAC,CAAC,CAAC,KAClBiC,KAAK,CAACmD,UAAU,EAAE,CAAC,GAAGpF,CAAC;EAC9B;EACA,IAAIzI,YAAY,CAACC,OAAO,EAAE;IACxB,IAAID,YAAY,CAACM,KAAK,EAAE;MACtBN,YAAY,CAACG,OAAO,KAAKH,YAAY,CAACG,OAAO,GAAG,EAAE,CAAC;MACnDH,YAAY,CAACG,OAAO,CAAC0E,IAAI,CAAC,GAAG6F,KAAK,CAACoD,KAAK,CAAC,CAAC,EAAED,UAAU,CAAC,CAAC;MACxD;IACF;IACA/M,iBAAiB,CAAC,CAAC;EACrB;EACA,IAAId,YAAY,CAACG,OAAO,KAAKH,YAAY,CAACI,IAAI,IAAI,CAACJ,YAAY,CAACM,KAAK,CAAC,EAAE;IACtEoK,KAAK,GAAG,CAAC,GAAG1K,YAAY,CAACG,OAAO,EAAE,GAAGuK,KAAK,CAAC;IAC3CmD,UAAU,IAAI7N,YAAY,CAACG,OAAO,CAACQ,MAAM;IACzC,OAAOX,YAAY,CAACG,OAAO;EAC7B;EACA,KAAKuJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmE,UAAU,EAAEnE,CAAC,EAAE,EAAEsD,MAAM,CAACtC,KAAK,CAAChB,CAAC,CAAC,CAAC;AACnD;AACA,SAASgC,YAAYA,CAACG,IAAI,EAAEkC,MAAM,EAAE;EAClC,MAAMvC,iBAAiB,GAAGlJ,UAAU,IAAIA,UAAU,CAAC6B,OAAO;EAC1D,IAAIqH,iBAAiB,EAAEK,IAAI,CAACrG,MAAM,GAAG,CAAC,CAAC,KAClCqG,IAAI,CAAC7E,KAAK,GAAG,CAAC;EACnB,KAAK,IAAI0C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmC,IAAI,CAACzH,OAAO,CAACzD,MAAM,EAAE+I,CAAC,IAAI,CAAC,EAAE;IAC/C,MAAM3D,MAAM,GAAG8F,IAAI,CAACzH,OAAO,CAACsF,CAAC,CAAC;IAC9B,IAAI3D,MAAM,CAAC3B,OAAO,EAAE;MAClB,MAAM4C,KAAK,GAAGwE,iBAAiB,GAAGzF,MAAM,CAACP,MAAM,GAAGO,MAAM,CAACiB,KAAK;MAC9D,IAAIA,KAAK,KAAKlF,KAAK,EAAE;QACnB,IAAIiE,MAAM,KAAKgI,MAAM,KAAK,CAAChI,MAAM,CAACyG,SAAS,IAAIzG,MAAM,CAACyG,SAAS,GAAG5J,SAAS,CAAC,EAC1EoK,MAAM,CAACjH,MAAM,CAAC;MAClB,CAAC,MAAM,IAAIiB,KAAK,KAAKjF,OAAO,EAAE2J,YAAY,CAAC3F,MAAM,EAAEgI,MAAM,CAAC;IAC5D;EACF;AACF;AACA,SAAS9B,cAAcA,CAACJ,IAAI,EAAE;EAC5B,MAAML,iBAAiB,GAAGlJ,UAAU,IAAIA,UAAU,CAAC6B,OAAO;EAC1D,KAAK,IAAIuF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmC,IAAI,CAAC/H,SAAS,CAACnD,MAAM,EAAE+I,CAAC,IAAI,CAAC,EAAE;IACjD,MAAMM,CAAC,GAAG6B,IAAI,CAAC/H,SAAS,CAAC4F,CAAC,CAAC;IAC3B,IAAI8B,iBAAiB,GAAG,CAACxB,CAAC,CAACxE,MAAM,GAAG,CAACwE,CAAC,CAAChD,KAAK,EAAE;MAC5C,IAAIwE,iBAAiB,EAAExB,CAAC,CAACxE,MAAM,GAAGzD,OAAO,CAAC,KACrCiI,CAAC,CAAChD,KAAK,GAAGjF,OAAO;MACtB,IAAIiI,CAAC,CAACgC,IAAI,EAAEtJ,OAAO,CAACmC,IAAI,CAACmF,CAAC,CAAC,CAAC,KACvBrH,OAAO,CAACkC,IAAI,CAACmF,CAAC,CAAC;MACpBA,CAAC,CAAClG,SAAS,IAAImI,cAAc,CAACjC,CAAC,CAAC;IAClC;EACF;AACF;AACA,SAAS1G,SAASA,CAACuI,IAAI,EAAE;EACvB,IAAInC,CAAC;EACL,IAAImC,IAAI,CAACzH,OAAO,EAAE;IAChB,OAAOyH,IAAI,CAACzH,OAAO,CAACzD,MAAM,EAAE;MAC1B,MAAMoF,MAAM,GAAG8F,IAAI,CAACzH,OAAO,CAAC4J,GAAG,CAAC,CAAC;QAAEC,KAAK,GAAGpC,IAAI,CAACD,WAAW,CAACoC,GAAG,CAAC,CAAC;QAAEE,GAAG,GAAGnI,MAAM,CAACjC,SAAS;MACzF,IAAIoK,GAAG,IAAIA,GAAG,CAACvN,MAAM,EAAE;QACrB,MAAMwN,CAAC,GAAGD,GAAG,CAACF,GAAG,CAAC,CAAC;UAAEnK,CAAC,GAAGkC,MAAM,CAAChC,aAAa,CAACiK,GAAG,CAAC,CAAC;QACnD,IAAIC,KAAK,GAAGC,GAAG,CAACvN,MAAM,EAAE;UACtBwN,CAAC,CAACvC,WAAW,CAAC/H,CAAC,CAAC,GAAGoK,KAAK;UACxBC,GAAG,CAACD,KAAK,CAAC,GAAGE,CAAC;UACdpI,MAAM,CAAChC,aAAa,CAACkK,KAAK,CAAC,GAAGpK,CAAC;QACjC;MACF;IACF;EACF;EACA,IAAIgI,IAAI,CAACS,MAAM,EAAE;IACf,KAAK5C,CAAC,GAAGmC,IAAI,CAACS,MAAM,CAAC3L,MAAM,GAAG,CAAC,EAAE+I,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAEpG,SAAS,CAACuI,IAAI,CAACS,MAAM,CAAC5C,CAAC,CAAC,CAAC;IACvE,OAAOmC,IAAI,CAACS,MAAM;EACpB;EACA,IAAIhK,UAAU,IAAIA,UAAU,CAAC6B,OAAO,IAAI0H,IAAI,CAACG,IAAI,EAAE;IACjDoC,KAAK,CAACvC,IAAI,EAAE,IAAI,CAAC;EACnB,CAAC,MAAM,IAAIA,IAAI,CAAC5J,KAAK,EAAE;IACrB,KAAKyH,CAAC,GAAGmC,IAAI,CAAC5J,KAAK,CAACtB,MAAM,GAAG,CAAC,EAAE+I,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAEpG,SAAS,CAACuI,IAAI,CAAC5J,KAAK,CAACyH,CAAC,CAAC,CAAC;IACrEmC,IAAI,CAAC5J,KAAK,GAAG,IAAI;EACnB;EACA,IAAI4J,IAAI,CAAC3J,QAAQ,EAAE;IACjB,KAAKwH,CAAC,GAAGmC,IAAI,CAAC3J,QAAQ,CAACvB,MAAM,GAAG,CAAC,EAAE+I,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAEmC,IAAI,CAAC3J,QAAQ,CAACwH,CAAC,CAAC,CAAC,CAAC;IAClEmC,IAAI,CAAC3J,QAAQ,GAAG,IAAI;EACtB;EACA,IAAII,UAAU,IAAIA,UAAU,CAAC6B,OAAO,EAAE0H,IAAI,CAACrG,MAAM,GAAG,CAAC,CAAC,KACjDqG,IAAI,CAAC7E,KAAK,GAAG,CAAC;AACrB;AACA,SAASoH,KAAKA,CAACvC,IAAI,EAAEsB,GAAG,EAAE;EACxB,IAAI,CAACA,GAAG,EAAE;IACRtB,IAAI,CAACrG,MAAM,GAAG,CAAC;IACflD,UAAU,CAACmI,QAAQ,CAACvC,GAAG,CAAC2D,IAAI,CAAC;EAC/B;EACA,IAAIA,IAAI,CAAC5J,KAAK,EAAE;IACd,KAAK,IAAIyH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmC,IAAI,CAAC5J,KAAK,CAACtB,MAAM,EAAE+I,CAAC,EAAE,EAAE0E,KAAK,CAACvC,IAAI,CAAC5J,KAAK,CAACyH,CAAC,CAAC,CAAC;EAClE;AACF;AACA,SAASnB,SAASA,CAACV,GAAG,EAAE;EACtB,IAAIA,GAAG,YAAYqE,KAAK,EAAE,OAAOrE,GAAG;EACpC,OAAO,IAAIqE,KAAK,CAAC,OAAOrE,GAAG,KAAK,QAAQ,GAAGA,GAAG,GAAG,eAAe,EAAE;IAChEwG,KAAK,EAAExG;EACT,CAAC,CAAC;AACJ;AACA,SAASyG,SAASA,CAACzG,GAAG,EAAE0G,GAAG,EAAEpM,KAAK,EAAE;EAClC,IAAI;IACF,KAAK,MAAMqM,CAAC,IAAID,GAAG,EAAEC,CAAC,CAAC3G,GAAG,CAAC;EAC7B,CAAC,CAAC,OAAOY,CAAC,EAAE;IACV0B,WAAW,CAAC1B,CAAC,EAAEtG,KAAK,IAAIA,KAAK,CAACA,KAAK,IAAI,IAAI,CAAC;EAC9C;AACF;AACA,SAASgI,WAAWA,CAACtC,GAAG,EAAE1F,KAAK,GAAGE,KAAK,EAAE;EACvC,MAAMkM,GAAG,GAAG5M,KAAK,IAAIQ,KAAK,IAAIA,KAAK,CAAClC,OAAO,IAAIkC,KAAK,CAAClC,OAAO,CAAC0B,KAAK,CAAC;EACnE,MAAMiF,KAAK,GAAG2B,SAAS,CAACV,GAAG,CAAC;EAC5B,IAAI,CAAC0G,GAAG,EAAE,MAAM3H,KAAK;EACrB,IAAIjE,OAAO,EACTA,OAAO,CAACkC,IAAI,CAAC;IACX/B,EAAEA,CAAA,EAAG;MACHwL,SAAS,CAAC1H,KAAK,EAAE2H,GAAG,EAAEpM,KAAK,CAAC;IAC9B,CAAC;IACD6E,KAAK,EAAElF;EACT,CAAC,CAAC,CAAC,KACAwM,SAAS,CAAC1H,KAAK,EAAE2H,GAAG,EAAEpM,KAAK,CAAC;AACnC;AACA,SAASmJ,eAAeA,CAACF,SAAS,EAAE;EAClC,IAAI,OAAOA,SAAS,KAAK,UAAU,IAAI,CAACA,SAAS,CAACzK,MAAM,EAAE,OAAO2K,eAAe,CAACF,SAAS,CAAC,CAAC,CAAC;EAC7F,IAAIhC,KAAK,CAACC,OAAO,CAAC+B,SAAS,CAAC,EAAE;IAC5B,MAAMqD,OAAO,GAAG,EAAE;IAClB,KAAK,IAAI/E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0B,SAAS,CAACzK,MAAM,EAAE+I,CAAC,EAAE,EAAE;MACzC,MAAMC,MAAM,GAAG2B,eAAe,CAACF,SAAS,CAAC1B,CAAC,CAAC,CAAC;MAC5CN,KAAK,CAACC,OAAO,CAACM,MAAM,CAAC,GAAG8E,OAAO,CAAC5J,IAAI,CAAC2I,KAAK,CAACiB,OAAO,EAAE9E,MAAM,CAAC,GAAG8E,OAAO,CAAC5J,IAAI,CAAC8E,MAAM,CAAC;IACpF;IACA,OAAO8E,OAAO;EAChB;EACA,OAAOrD,SAAS;AAClB;AACA,SAASF,cAAcA,CAACtK,EAAE,EAAE8C,OAAO,EAAE;EACnC,OAAO,SAASgL,QAAQA,CAACC,KAAK,EAAE;IAC9B,IAAIhE,GAAG;IACP5F,kBAAkB,CAChB,MAAM4F,GAAG,GAAGtH,OAAO,CAAC,MAAM;MACxBhB,KAAK,CAACpC,OAAO,GAAG;QACd,GAAGoC,KAAK,CAACpC,OAAO;QAChB,CAACW,EAAE,GAAG+N,KAAK,CAAClL;MACd,CAAC;MACD,OAAO0H,QAAQ,CAAC,MAAMwD,KAAK,CAACxD,QAAQ,CAAC;IACvC,CAAC,CAAC,EACF,KAAK,CACP,CAAC;IACD,OAAOR,GAAG;EACZ,CAAC;AACH;AACA,IAAIiE,QAAQ,GAAGvN,MAAM,CAAC,UAAU,CAAC;AACjC,SAASuL,OAAOA,CAACc,CAAC,EAAE;EAClB,KAAK,IAAIhE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgE,CAAC,CAAC/M,MAAM,EAAE+I,CAAC,EAAE,EAAEgE,CAAC,CAAChE,CAAC,CAAC,CAAC,CAAC;AAC3C;AACA,SAASmF,QAAQA,CAACC,IAAI,EAAEC,KAAK,EAAErL,OAAO,GAAG,CAAC,CAAC,EAAE;EAC3C,IAAIsL,KAAK,GAAG,EAAE;IAAEC,MAAM,GAAG,EAAE;IAAEC,SAAS,GAAG,EAAE;IAAExO,GAAG,GAAG,CAAC;IAAEyO,OAAO,GAAGJ,KAAK,CAACpO,MAAM,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI;EAC5FkJ,SAAS,CAAC,MAAM+C,OAAO,CAACsC,SAAS,CAAC,CAAC;EACnC,OAAO,MAAM;IACX,IAAIE,QAAQ,GAAGN,IAAI,CAAC,CAAC,IAAI,EAAE;MAAEO,MAAM,GAAGD,QAAQ,CAACzO,MAAM;MAAE+I,CAAC;MAAE4F,CAAC;IAC3DF,QAAQ,CAAC5N,MAAM,CAAC;IAChB,OAAO6B,OAAO,CAAC,MAAM;MACnB,IAAIkM,UAAU,EAAEC,cAAc,EAAEC,IAAI,EAAEC,aAAa,EAAEC,WAAW,EAAEC,KAAK,EAAEC,GAAG,EAAEC,MAAM,EAAEnC,IAAI;MAC1F,IAAI0B,MAAM,KAAK,CAAC,EAAE;QAChB,IAAI3O,GAAG,KAAK,CAAC,EAAE;UACbkM,OAAO,CAACsC,SAAS,CAAC;UAClBA,SAAS,GAAG,EAAE;UACdF,KAAK,GAAG,EAAE;UACVC,MAAM,GAAG,EAAE;UACXvO,GAAG,GAAG,CAAC;UACPyO,OAAO,KAAKA,OAAO,GAAG,EAAE,CAAC;QAC3B;QACA,IAAIzL,OAAO,CAACqM,QAAQ,EAAE;UACpBf,KAAK,GAAG,CAACJ,QAAQ,CAAC;UAClBK,MAAM,CAAC,CAAC,CAAC,GAAGpM,UAAU,CAAEmN,QAAQ,IAAK;YACnCd,SAAS,CAAC,CAAC,CAAC,GAAGc,QAAQ;YACvB,OAAOtM,OAAO,CAACqM,QAAQ,CAAC,CAAC;UAC3B,CAAC,CAAC;UACFrP,GAAG,GAAG,CAAC;QACT;MACF,CAAC,MAAM,IAAIA,GAAG,KAAK,CAAC,EAAE;QACpBuO,MAAM,GAAG,IAAI7F,KAAK,CAACiG,MAAM,CAAC;QAC1B,KAAKC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,MAAM,EAAEC,CAAC,EAAE,EAAE;UAC3BN,KAAK,CAACM,CAAC,CAAC,GAAGF,QAAQ,CAACE,CAAC,CAAC;UACtBL,MAAM,CAACK,CAAC,CAAC,GAAGzM,UAAU,CAACoN,MAAM,CAAC;QAChC;QACAvP,GAAG,GAAG2O,MAAM;MACd,CAAC,MAAM;QACLI,IAAI,GAAG,IAAIrG,KAAK,CAACiG,MAAM,CAAC;QACxBK,aAAa,GAAG,IAAItG,KAAK,CAACiG,MAAM,CAAC;QACjCF,OAAO,KAAKQ,WAAW,GAAG,IAAIvG,KAAK,CAACiG,MAAM,CAAC,CAAC;QAC5C,KAAKO,KAAK,GAAG,CAAC,EAAEC,GAAG,GAAGK,IAAI,CAACC,GAAG,CAACzP,GAAG,EAAE2O,MAAM,CAAC,EAAEO,KAAK,GAAGC,GAAG,IAAIb,KAAK,CAACY,KAAK,CAAC,KAAKR,QAAQ,CAACQ,KAAK,CAAC,EAAEA,KAAK,EAAE,CAAE;QACvG,KAAKC,GAAG,GAAGnP,GAAG,GAAG,CAAC,EAAEoP,MAAM,GAAGT,MAAM,GAAG,CAAC,EAAEQ,GAAG,IAAID,KAAK,IAAIE,MAAM,IAAIF,KAAK,IAAIZ,KAAK,CAACa,GAAG,CAAC,KAAKT,QAAQ,CAACU,MAAM,CAAC,EAAED,GAAG,EAAE,EAAEC,MAAM,EAAE,EAAE;UAC5HL,IAAI,CAACK,MAAM,CAAC,GAAGb,MAAM,CAACY,GAAG,CAAC;UAC1BH,aAAa,CAACI,MAAM,CAAC,GAAGZ,SAAS,CAACW,GAAG,CAAC;UACtCV,OAAO,KAAKQ,WAAW,CAACG,MAAM,CAAC,GAAGX,OAAO,CAACU,GAAG,CAAC,CAAC;QACjD;QACAN,UAAU,GAAG,eAAgB,IAAIa,GAAG,CAAC,CAAC;QACtCZ,cAAc,GAAG,IAAIpG,KAAK,CAAC0G,MAAM,GAAG,CAAC,CAAC;QACtC,KAAKR,CAAC,GAAGQ,MAAM,EAAER,CAAC,IAAIM,KAAK,EAAEN,CAAC,EAAE,EAAE;UAChC3B,IAAI,GAAGyB,QAAQ,CAACE,CAAC,CAAC;UAClB5F,CAAC,GAAG6F,UAAU,CAAC5G,GAAG,CAACgF,IAAI,CAAC;UACxB6B,cAAc,CAACF,CAAC,CAAC,GAAG5F,CAAC,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,CAAC;UACzC6F,UAAU,CAACc,GAAG,CAAC1C,IAAI,EAAE2B,CAAC,CAAC;QACzB;QACA,KAAK5F,CAAC,GAAGkG,KAAK,EAAElG,CAAC,IAAImG,GAAG,EAAEnG,CAAC,EAAE,EAAE;UAC7BiE,IAAI,GAAGqB,KAAK,CAACtF,CAAC,CAAC;UACf4F,CAAC,GAAGC,UAAU,CAAC5G,GAAG,CAACgF,IAAI,CAAC;UACxB,IAAI2B,CAAC,KAAK,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC,CAAC,EAAE;YAC5BG,IAAI,CAACH,CAAC,CAAC,GAAGL,MAAM,CAACvF,CAAC,CAAC;YACnBgG,aAAa,CAACJ,CAAC,CAAC,GAAGJ,SAAS,CAACxF,CAAC,CAAC;YAC/ByF,OAAO,KAAKQ,WAAW,CAACL,CAAC,CAAC,GAAGH,OAAO,CAACzF,CAAC,CAAC,CAAC;YACxC4F,CAAC,GAAGE,cAAc,CAACF,CAAC,CAAC;YACrBC,UAAU,CAACc,GAAG,CAAC1C,IAAI,EAAE2B,CAAC,CAAC;UACzB,CAAC,MAAMJ,SAAS,CAACxF,CAAC,CAAC,CAAC,CAAC;QACvB;QACA,KAAK4F,CAAC,GAAGM,KAAK,EAAEN,CAAC,GAAGD,MAAM,EAAEC,CAAC,EAAE,EAAE;UAC/B,IAAIA,CAAC,IAAIG,IAAI,EAAE;YACbR,MAAM,CAACK,CAAC,CAAC,GAAGG,IAAI,CAACH,CAAC,CAAC;YACnBJ,SAAS,CAACI,CAAC,CAAC,GAAGI,aAAa,CAACJ,CAAC,CAAC;YAC/B,IAAIH,OAAO,EAAE;cACXA,OAAO,CAACG,CAAC,CAAC,GAAGK,WAAW,CAACL,CAAC,CAAC;cAC3BH,OAAO,CAACG,CAAC,CAAC,CAACA,CAAC,CAAC;YACf;UACF,CAAC,MAAML,MAAM,CAACK,CAAC,CAAC,GAAGzM,UAAU,CAACoN,MAAM,CAAC;QACvC;QACAhB,MAAM,GAAGA,MAAM,CAACnB,KAAK,CAAC,CAAC,EAAEpN,GAAG,GAAG2O,MAAM,CAAC;QACtCL,KAAK,GAAGI,QAAQ,CAACtB,KAAK,CAAC,CAAC,CAAC;MAC3B;MACA,OAAOmB,MAAM;IACf,CAAC,CAAC;IACF,SAASgB,MAAMA,CAACD,QAAQ,EAAE;MACxBd,SAAS,CAACI,CAAC,CAAC,GAAGU,QAAQ;MACvB,IAAIb,OAAO,EAAE;QACX,MAAM,CAACtL,CAAC,EAAEwM,GAAG,CAAC,GAAG7M,YAAY,CAAC8L,CAAC,CAAC;QAChCH,OAAO,CAACG,CAAC,CAAC,GAAGe,GAAG;QAChB,OAAOtB,KAAK,CAACK,QAAQ,CAACE,CAAC,CAAC,EAAEzL,CAAC,CAAC;MAC9B;MACA,OAAOkL,KAAK,CAACK,QAAQ,CAACE,CAAC,CAAC,CAAC;IAC3B;EACF,CAAC;AACH;AACA,SAASgB,UAAUA,CAACxB,IAAI,EAAEC,KAAK,EAAErL,OAAO,GAAG,CAAC,CAAC,EAAE;EAC7C,IAAIsL,KAAK,GAAG,EAAE;IAAEC,MAAM,GAAG,EAAE;IAAEC,SAAS,GAAG,EAAE;IAAEqB,OAAO,GAAG,EAAE;IAAE7P,GAAG,GAAG,CAAC;IAAEgJ,CAAC;EACrEG,SAAS,CAAC,MAAM+C,OAAO,CAACsC,SAAS,CAAC,CAAC;EACnC,OAAO,MAAM;IACX,MAAME,QAAQ,GAAGN,IAAI,CAAC,CAAC,IAAI,EAAE;MAAEO,MAAM,GAAGD,QAAQ,CAACzO,MAAM;IACvDyO,QAAQ,CAAC5N,MAAM,CAAC;IAChB,OAAO6B,OAAO,CAAC,MAAM;MACnB,IAAIgM,MAAM,KAAK,CAAC,EAAE;QAChB,IAAI3O,GAAG,KAAK,CAAC,EAAE;UACbkM,OAAO,CAACsC,SAAS,CAAC;UAClBA,SAAS,GAAG,EAAE;UACdF,KAAK,GAAG,EAAE;UACVC,MAAM,GAAG,EAAE;UACXvO,GAAG,GAAG,CAAC;UACP6P,OAAO,GAAG,EAAE;QACd;QACA,IAAI7M,OAAO,CAACqM,QAAQ,EAAE;UACpBf,KAAK,GAAG,CAACJ,QAAQ,CAAC;UAClBK,MAAM,CAAC,CAAC,CAAC,GAAGpM,UAAU,CAAEmN,QAAQ,IAAK;YACnCd,SAAS,CAAC,CAAC,CAAC,GAAGc,QAAQ;YACvB,OAAOtM,OAAO,CAACqM,QAAQ,CAAC,CAAC;UAC3B,CAAC,CAAC;UACFrP,GAAG,GAAG,CAAC;QACT;QACA,OAAOuO,MAAM;MACf;MACA,IAAID,KAAK,CAAC,CAAC,CAAC,KAAKJ,QAAQ,EAAE;QACzBM,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QACdA,SAAS,GAAG,EAAE;QACdF,KAAK,GAAG,EAAE;QACVC,MAAM,GAAG,EAAE;QACXvO,GAAG,GAAG,CAAC;MACT;MACA,KAAKgJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2F,MAAM,EAAE3F,CAAC,EAAE,EAAE;QAC3B,IAAIA,CAAC,GAAGsF,KAAK,CAACrO,MAAM,IAAIqO,KAAK,CAACtF,CAAC,CAAC,KAAK0F,QAAQ,CAAC1F,CAAC,CAAC,EAAE;UAChD6G,OAAO,CAAC7G,CAAC,CAAC,CAAC,MAAM0F,QAAQ,CAAC1F,CAAC,CAAC,CAAC;QAC/B,CAAC,MAAM,IAAIA,CAAC,IAAIsF,KAAK,CAACrO,MAAM,EAAE;UAC5BsO,MAAM,CAACvF,CAAC,CAAC,GAAG7G,UAAU,CAACoN,MAAM,CAAC;QAChC;MACF;MACA,OAAOvG,CAAC,GAAGsF,KAAK,CAACrO,MAAM,EAAE+I,CAAC,EAAE,EAAE;QAC5BwF,SAAS,CAACxF,CAAC,CAAC,CAAC,CAAC;MAChB;MACAhJ,GAAG,GAAG6P,OAAO,CAAC5P,MAAM,GAAGuO,SAAS,CAACvO,MAAM,GAAG0O,MAAM;MAChDL,KAAK,GAAGI,QAAQ,CAACtB,KAAK,CAAC,CAAC,CAAC;MACzB,OAAOmB,MAAM,GAAGA,MAAM,CAACnB,KAAK,CAAC,CAAC,EAAEpN,GAAG,CAAC;IACtC,CAAC,CAAC;IACF,SAASuP,MAAMA,CAACD,QAAQ,EAAE;MACxBd,SAAS,CAACxF,CAAC,CAAC,GAAGsG,QAAQ;MACvB,MAAM,CAACnM,CAAC,EAAEwM,GAAG,CAAC,GAAG7M,YAAY,CAAC4L,QAAQ,CAAC1F,CAAC,CAAC,CAAC;MAC1C6G,OAAO,CAAC7G,CAAC,CAAC,GAAG2G,GAAG;MAChB,OAAOtB,KAAK,CAAClL,CAAC,EAAE6F,CAAC,CAAC;IACpB;EACF,CAAC;AACH;AACA,IAAI8G,gBAAgB,GAAG,KAAK;AAC5B,SAASC,eAAeA,CAACC,IAAI,EAAE/B,KAAK,EAAE;EACpC,IAAI6B,gBAAgB,EAAE;IACpB,IAAIxQ,YAAY,CAACC,OAAO,EAAE;MACxB,MAAM0E,CAAC,GAAG3E,YAAY,CAACC,OAAO;MAC9Ba,iBAAiB,CAACC,kBAAkB,CAAC,CAAC,CAAC;MACvC,MAAM4P,CAAC,GAAGtN,OAAO,CAAC,MAAMqN,IAAI,CAAC/B,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC;MAC1C7N,iBAAiB,CAAC6D,CAAC,CAAC;MACpB,OAAOgM,CAAC;IACV;EACF;EACA,OAAOtN,OAAO,CAAC,MAAMqN,IAAI,CAAC/B,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC;AACzC;AACA,SAASiC,MAAMA,CAAA,EAAG;EAChB,OAAO,IAAI;AACb;AACA,IAAIC,SAAS,GAAG;EACdlI,GAAGA,CAACmI,CAAC,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;IACzB,IAAID,QAAQ,KAAK3P,MAAM,EAAE,OAAO4P,QAAQ;IACxC,OAAOF,CAAC,CAACnI,GAAG,CAACoI,QAAQ,CAAC;EACxB,CAAC;EACD1M,GAAGA,CAACyM,CAAC,EAAEC,QAAQ,EAAE;IACf,IAAIA,QAAQ,KAAK3P,MAAM,EAAE,OAAO,IAAI;IACpC,OAAO0P,CAAC,CAACzM,GAAG,CAAC0M,QAAQ,CAAC;EACxB,CAAC;EACDV,GAAG,EAAEO,MAAM;EACXK,cAAc,EAAEL,MAAM;EACtBM,wBAAwBA,CAACJ,CAAC,EAAEC,QAAQ,EAAE;IACpC,OAAO;MACLI,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,IAAI;MAChBzI,GAAGA,CAAA,EAAG;QACJ,OAAOmI,CAAC,CAACnI,GAAG,CAACoI,QAAQ,CAAC;MACxB,CAAC;MACDV,GAAG,EAAEO,MAAM;MACXK,cAAc,EAAEL;IAClB,CAAC;EACH,CAAC;EACDS,OAAOA,CAACP,CAAC,EAAE;IACT,OAAOA,CAAC,CAAChJ,IAAI,CAAC,CAAC;EACjB;AACF,CAAC;AACD,SAASwJ,aAAaA,CAACzN,CAAC,EAAE;EACxB,OAAO,EAAEA,CAAC,GAAG,OAAOA,CAAC,KAAK,UAAU,GAAGA,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,GAAG,CAAC,CAAC,GAAGA,CAAC;AAC1D;AACA,SAAS0N,cAAcA,CAAA,EAAG;EACxB,KAAK,IAAI7H,CAAC,GAAG,CAAC,EAAE/I,MAAM,GAAG,IAAI,CAACA,MAAM,EAAE+I,CAAC,GAAG/I,MAAM,EAAE,EAAE+I,CAAC,EAAE;IACrD,MAAMhE,CAAC,GAAG,IAAI,CAACgE,CAAC,CAAC,CAAC,CAAC;IACnB,IAAIhE,CAAC,KAAK,KAAK,CAAC,EAAE,OAAOA,CAAC;EAC5B;AACF;AACA,SAAS8L,UAAUA,CAAC,GAAGpN,OAAO,EAAE;EAC9B,IAAIqN,KAAK,GAAG,KAAK;EACjB,KAAK,IAAI/H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtF,OAAO,CAACzD,MAAM,EAAE+I,CAAC,EAAE,EAAE;IACvC,MAAM7F,CAAC,GAAGO,OAAO,CAACsF,CAAC,CAAC;IACpB+H,KAAK,GAAGA,KAAK,IAAI,CAAC,CAAC5N,CAAC,IAAIzC,MAAM,IAAIyC,CAAC;IACnCO,OAAO,CAACsF,CAAC,CAAC,GAAG,OAAO7F,CAAC,KAAK,UAAU,IAAI4N,KAAK,GAAG,IAAI,EAAElM,UAAU,CAAC1B,CAAC,CAAC,IAAIA,CAAC;EAC1E;EACA,IAAIvC,cAAc,IAAImQ,KAAK,EAAE;IAC3B,OAAO,IAAIlQ,KAAK,CACd;MACEoH,GAAGA,CAACoI,QAAQ,EAAE;QACZ,KAAK,IAAIrH,CAAC,GAAGtF,OAAO,CAACzD,MAAM,GAAG,CAAC,EAAE+I,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;UAC5C,MAAMhE,CAAC,GAAG4L,aAAa,CAAClN,OAAO,CAACsF,CAAC,CAAC,CAAC,CAACqH,QAAQ,CAAC;UAC7C,IAAIrL,CAAC,KAAK,KAAK,CAAC,EAAE,OAAOA,CAAC;QAC5B;MACF,CAAC;MACDrB,GAAGA,CAAC0M,QAAQ,EAAE;QACZ,KAAK,IAAIrH,CAAC,GAAGtF,OAAO,CAACzD,MAAM,GAAG,CAAC,EAAE+I,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;UAC5C,IAAIqH,QAAQ,IAAIO,aAAa,CAAClN,OAAO,CAACsF,CAAC,CAAC,CAAC,EAAE,OAAO,IAAI;QACxD;QACA,OAAO,KAAK;MACd,CAAC;MACD5B,IAAIA,CAAA,EAAG;QACL,MAAMA,IAAI,GAAG,EAAE;QACf,KAAK,IAAI4B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtF,OAAO,CAACzD,MAAM,EAAE+I,CAAC,EAAE,EACrC5B,IAAI,CAACjD,IAAI,CAAC,GAAGlB,MAAM,CAACmE,IAAI,CAACwJ,aAAa,CAAClN,OAAO,CAACsF,CAAC,CAAC,CAAC,CAAC,CAAC;QACtD,OAAO,CAAC,GAAG,IAAIlD,GAAG,CAACsB,IAAI,CAAC,CAAC;MAC3B;IACF,CAAC,EACD+I,SACF,CAAC;EACH;EACA,MAAMa,UAAU,GAAG,CAAC,CAAC;EACrB,MAAMC,OAAO,GAAG,eAAgBhO,MAAM,CAACiO,MAAM,CAAC,IAAI,CAAC;EACnD,KAAK,IAAIlI,CAAC,GAAGtF,OAAO,CAACzD,MAAM,GAAG,CAAC,EAAE+I,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC5C,MAAM3D,MAAM,GAAG3B,OAAO,CAACsF,CAAC,CAAC;IACzB,IAAI,CAAC3D,MAAM,EAAE;IACb,MAAM8L,UAAU,GAAGlO,MAAM,CAACmO,mBAAmB,CAAC/L,MAAM,CAAC;IACrD,KAAK,IAAIgM,EAAE,GAAGF,UAAU,CAAClR,MAAM,GAAG,CAAC,EAAEoR,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;MAClD,MAAMxK,GAAG,GAAGsK,UAAU,CAACE,EAAE,CAAC;MAC1B,IAAIxK,GAAG,KAAK,WAAW,IAAIA,GAAG,KAAK,aAAa,EAAE;MAClD,MAAMyK,IAAI,GAAGrO,MAAM,CAACuN,wBAAwB,CAACnL,MAAM,EAAEwB,GAAG,CAAC;MACzD,IAAI,CAACoK,OAAO,CAACpK,GAAG,CAAC,EAAE;QACjBoK,OAAO,CAACpK,GAAG,CAAC,GAAGyK,IAAI,CAACrJ,GAAG,GAAG;UACxByI,UAAU,EAAE,IAAI;UAChBD,YAAY,EAAE,IAAI;UAClBxI,GAAG,EAAE4I,cAAc,CAAC9M,IAAI,CAACiN,UAAU,CAACnK,GAAG,CAAC,GAAG,CAACyK,IAAI,CAACrJ,GAAG,CAAClE,IAAI,CAACsB,MAAM,CAAC,CAAC;QACpE,CAAC,GAAGiM,IAAI,CAACvO,KAAK,KAAK,KAAK,CAAC,GAAGuO,IAAI,GAAG,KAAK,CAAC;MAC3C,CAAC,MAAM;QACL,MAAMC,QAAQ,GAAGP,UAAU,CAACnK,GAAG,CAAC;QAChC,IAAI0K,QAAQ,EAAE;UACZ,IAAID,IAAI,CAACrJ,GAAG,EAAEsJ,QAAQ,CAACpN,IAAI,CAACmN,IAAI,CAACrJ,GAAG,CAAClE,IAAI,CAACsB,MAAM,CAAC,CAAC,CAAC,KAC9C,IAAIiM,IAAI,CAACvO,KAAK,KAAK,KAAK,CAAC,EAAEwO,QAAQ,CAACpN,IAAI,CAAC,MAAMmN,IAAI,CAACvO,KAAK,CAAC;QACjE;MACF;IACF;EACF;EACA,MAAMyO,MAAM,GAAG,CAAC,CAAC;EACjB,MAAMC,WAAW,GAAGxO,MAAM,CAACmE,IAAI,CAAC6J,OAAO,CAAC;EACxC,KAAK,IAAIjI,CAAC,GAAGyI,WAAW,CAACxR,MAAM,GAAG,CAAC,EAAE+I,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAChD,MAAMnC,GAAG,GAAG4K,WAAW,CAACzI,CAAC,CAAC;MAAEsI,IAAI,GAAGL,OAAO,CAACpK,GAAG,CAAC;IAC/C,IAAIyK,IAAI,IAAIA,IAAI,CAACrJ,GAAG,EAAEhF,MAAM,CAACyO,cAAc,CAACF,MAAM,EAAE3K,GAAG,EAAEyK,IAAI,CAAC,CAAC,KAC1DE,MAAM,CAAC3K,GAAG,CAAC,GAAGyK,IAAI,GAAGA,IAAI,CAACvO,KAAK,GAAG,KAAK,CAAC;EAC/C;EACA,OAAOyO,MAAM;AACf;AACA,SAASG,UAAUA,CAAC1D,KAAK,EAAE,GAAG7G,IAAI,EAAE;EAClC,IAAIxG,cAAc,IAAIF,MAAM,IAAIuN,KAAK,EAAE;IACrC,MAAM2D,OAAO,GAAG,IAAI9L,GAAG,CAACsB,IAAI,CAACnH,MAAM,GAAG,CAAC,GAAGmH,IAAI,CAACyK,IAAI,CAAC,CAAC,GAAGzK,IAAI,CAAC,CAAC,CAAC,CAAC;IAChE,MAAM6C,GAAG,GAAG7C,IAAI,CAAC0K,GAAG,CAAEC,CAAC,IAAK;MAC1B,OAAO,IAAIlR,KAAK,CACd;QACEoH,GAAGA,CAACoI,QAAQ,EAAE;UACZ,OAAO0B,CAAC,CAACC,QAAQ,CAAC3B,QAAQ,CAAC,GAAGpC,KAAK,CAACoC,QAAQ,CAAC,GAAG,KAAK,CAAC;QACxD,CAAC;QACD1M,GAAGA,CAAC0M,QAAQ,EAAE;UACZ,OAAO0B,CAAC,CAACC,QAAQ,CAAC3B,QAAQ,CAAC,IAAIA,QAAQ,IAAIpC,KAAK;QAClD,CAAC;QACD7G,IAAIA,CAAA,EAAG;UACL,OAAO2K,CAAC,CAACE,MAAM,CAAE5B,QAAQ,IAAKA,QAAQ,IAAIpC,KAAK,CAAC;QAClD;MACF,CAAC,EACDkC,SACF,CAAC;IACH,CAAC,CAAC;IACFlG,GAAG,CAAC9F,IAAI,CACN,IAAItD,KAAK,CACP;MACEoH,GAAGA,CAACoI,QAAQ,EAAE;QACZ,OAAOuB,OAAO,CAACjO,GAAG,CAAC0M,QAAQ,CAAC,GAAG,KAAK,CAAC,GAAGpC,KAAK,CAACoC,QAAQ,CAAC;MACzD,CAAC;MACD1M,GAAGA,CAAC0M,QAAQ,EAAE;QACZ,OAAOuB,OAAO,CAACjO,GAAG,CAAC0M,QAAQ,CAAC,GAAG,KAAK,GAAGA,QAAQ,IAAIpC,KAAK;MAC1D,CAAC;MACD7G,IAAIA,CAAA,EAAG;QACL,OAAOnE,MAAM,CAACmE,IAAI,CAAC6G,KAAK,CAAC,CAACgE,MAAM,CAAEF,CAAC,IAAK,CAACH,OAAO,CAACjO,GAAG,CAACoO,CAAC,CAAC,CAAC;MAC1D;IACF,CAAC,EACD5B,SACF,CACF,CAAC;IACD,OAAOlG,GAAG;EACZ;EACA,MAAMiI,WAAW,GAAG,CAAC,CAAC;EACtB,MAAMC,OAAO,GAAG/K,IAAI,CAAC0K,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;EACpC,KAAK,MAAMM,QAAQ,IAAInP,MAAM,CAACmO,mBAAmB,CAACnD,KAAK,CAAC,EAAE;IACxD,MAAMqD,IAAI,GAAGrO,MAAM,CAACuN,wBAAwB,CAACvC,KAAK,EAAEmE,QAAQ,CAAC;IAC7D,MAAMC,aAAa,GAAG,CAACf,IAAI,CAACrJ,GAAG,IAAI,CAACqJ,IAAI,CAAC3B,GAAG,IAAI2B,IAAI,CAACZ,UAAU,IAAIY,IAAI,CAACgB,QAAQ,IAAIhB,IAAI,CAACb,YAAY;IACrG,IAAImB,OAAO,GAAG,KAAK;IACnB,IAAIW,WAAW,GAAG,CAAC;IACnB,KAAK,MAAMR,CAAC,IAAI3K,IAAI,EAAE;MACpB,IAAI2K,CAAC,CAACC,QAAQ,CAACI,QAAQ,CAAC,EAAE;QACxBR,OAAO,GAAG,IAAI;QACdS,aAAa,GAAGF,OAAO,CAACI,WAAW,CAAC,CAACH,QAAQ,CAAC,GAAGd,IAAI,CAACvO,KAAK,GAAGE,MAAM,CAACyO,cAAc,CAACS,OAAO,CAACI,WAAW,CAAC,EAAEH,QAAQ,EAAEd,IAAI,CAAC;MAC3H;MACA,EAAEiB,WAAW;IACf;IACA,IAAI,CAACX,OAAO,EAAE;MACZS,aAAa,GAAGH,WAAW,CAACE,QAAQ,CAAC,GAAGd,IAAI,CAACvO,KAAK,GAAGE,MAAM,CAACyO,cAAc,CAACQ,WAAW,EAAEE,QAAQ,EAAEd,IAAI,CAAC;IACzG;EACF;EACA,OAAO,CAAC,GAAGa,OAAO,EAAED,WAAW,CAAC;AAClC;AACA,SAASM,IAAIA,CAACpQ,EAAE,EAAE;EAChB,IAAIqQ,IAAI;EACR,IAAI9L,CAAC;EACL,MAAM+L,IAAI,GAAIzE,KAAK,IAAK;IACtB,MAAM0E,GAAG,GAAGrT,YAAY,CAACC,OAAO;IAChC,IAAIoT,GAAG,EAAE;MACP,MAAM,CAACxP,CAAC,EAAEwM,GAAG,CAAC,GAAG7M,YAAY,CAAC,CAAC;MAC/BxD,YAAY,CAACM,KAAK,KAAKN,YAAY,CAACM,KAAK,GAAG,CAAC,CAAC;MAC9CN,YAAY,CAACM,KAAK,EAAE;MACpB,CAAC+G,CAAC,KAAKA,CAAC,GAAGvE,EAAE,CAAC,CAAC,CAAC,EAAE0F,IAAI,CAAE8K,GAAG,IAAK;QAC9B,CAACtT,YAAY,CAACI,IAAI,IAAIU,iBAAiB,CAACuS,GAAG,CAAC;QAC5CrT,YAAY,CAACM,KAAK,EAAE;QACpB+P,GAAG,CAAC,MAAMiD,GAAG,CAACC,OAAO,CAAC;QACtBzS,iBAAiB,CAAC,CAAC;MACrB,CAAC,CAAC;MACFqS,IAAI,GAAGtP,CAAC;IACV,CAAC,MAAM,IAAI,CAACsP,IAAI,EAAE;MAChB,MAAM,CAACtP,CAAC,CAAC,GAAG8B,cAAc,CAAC,MAAM,CAAC0B,CAAC,KAAKA,CAAC,GAAGvE,EAAE,CAAC,CAAC,CAAC,EAAE0F,IAAI,CAAE8K,GAAG,IAAKA,GAAG,CAACC,OAAO,CAAC,CAAC;MAC9EJ,IAAI,GAAGtP,CAAC;IACV;IACA,IAAI6M,IAAI;IACR,OAAOnL,UAAU,CACf,MAAM,CAACmL,IAAI,GAAGyC,IAAI,CAAC,CAAC,IAAI9P,OAAO,CAAC,MAAM;MACpC,IAAIrC,MAAM,EAAE;MACZ,IAAI,CAACqS,GAAG,IAAIrT,YAAY,CAACI,IAAI,EAAE,OAAOsQ,IAAI,CAAC/B,KAAK,CAAC;MACjD,MAAMhK,CAAC,GAAG3E,YAAY,CAACC,OAAO;MAC9Ba,iBAAiB,CAACuS,GAAG,CAAC;MACtB,MAAM1C,CAAC,GAAGD,IAAI,CAAC/B,KAAK,CAAC;MACrB7N,iBAAiB,CAAC6D,CAAC,CAAC;MACpB,OAAOgM,CAAC;IACV,CAAC,CAAC,GAAG,EACP,CAAC;EACH,CAAC;EACDyC,IAAI,CAACI,OAAO,GAAG,MAAMnM,CAAC,KAAK,CAACA,CAAC,GAAGvE,EAAE,CAAC,CAAC,EAAE0F,IAAI,CAAE8K,GAAG,IAAKH,IAAI,GAAGA,CAAA,KAAMG,GAAG,CAACC,OAAO,CAAC,EAAElM,CAAC,CAAC;EACjF,OAAO+L,IAAI;AACb;AACA,IAAIK,OAAO,GAAG,CAAC;AACf,SAASC,cAAcA,CAAA,EAAG;EACxB,MAAML,GAAG,GAAGrT,YAAY,CAACC,OAAO;EAChC,OAAOoT,GAAG,GAAGrT,YAAY,CAACO,gBAAgB,CAAC,CAAC,GAAG,MAAMkT,OAAO,EAAE,EAAE;AAClE;AACA,IAAIE,aAAa,GAAIC,IAAI,IAAK,oBAAoBA,IAAI,IAAI;AAC1D,SAASC,GAAGA,CAAClF,KAAK,EAAE;EAClB,MAAMoB,QAAQ,GAAG,UAAU,IAAIpB,KAAK,IAAI;IACtCoB,QAAQ,EAAEA,CAAA,KAAMpB,KAAK,CAACoB;EACxB,CAAC;EACD,OAAOxK,UAAU,CAACsJ,QAAQ,CAAC,MAAMF,KAAK,CAACmF,IAAI,EAAEnF,KAAK,CAACxD,QAAQ,EAAE4E,QAAQ,IAAI,KAAK,CAAC,CAAC,CAAC;AACnF;AACA,SAASgE,KAAKA,CAACpF,KAAK,EAAE;EACpB,MAAMoB,QAAQ,GAAG,UAAU,IAAIpB,KAAK,IAAI;IACtCoB,QAAQ,EAAEA,CAAA,KAAMpB,KAAK,CAACoB;EACxB,CAAC;EACD,OAAOxK,UAAU,CAAC+K,UAAU,CAAC,MAAM3B,KAAK,CAACmF,IAAI,EAAEnF,KAAK,CAACxD,QAAQ,EAAE4E,QAAQ,IAAI,KAAK,CAAC,CAAC,CAAC;AACrF;AACA,SAASiE,IAAIA,CAACrF,KAAK,EAAE;EACnB,MAAMsF,KAAK,GAAGtF,KAAK,CAACsF,KAAK;EACzB,MAAMC,cAAc,GAAG3O,UAAU,CAAC,MAAMoJ,KAAK,CAACwF,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,CAAC;EACnE,MAAMC,SAAS,GAAGH,KAAK,GAAGC,cAAc,GAAG3O,UAAU,CAAC2O,cAAc,EAAE,KAAK,CAAC,EAAE;IAC5ExS,MAAM,EAAEA,CAACR,CAAC,EAAEC,CAAC,KAAK,CAACD,CAAC,KAAK,CAACC;EAC5B,CAAC,CAAC;EACF,OAAOoE,UAAU,CACf,MAAM;IACJ,MAAMZ,CAAC,GAAGyP,SAAS,CAAC,CAAC;IACrB,IAAIzP,CAAC,EAAE;MACL,MAAM0P,KAAK,GAAG1F,KAAK,CAACxD,QAAQ;MAC5B,MAAMrI,EAAE,GAAG,OAAOuR,KAAK,KAAK,UAAU,IAAIA,KAAK,CAAC1T,MAAM,GAAG,CAAC;MAC1D,OAAOmC,EAAE,GAAGO,OAAO,CACjB,MAAMgR,KAAK,CACTJ,KAAK,GAAGtP,CAAC,GAAG,MAAM;QAChB,IAAI,CAACtB,OAAO,CAAC+Q,SAAS,CAAC,EAAE,MAAMT,aAAa,CAAC,MAAM,CAAC;QACpD,OAAOO,cAAc,CAAC,CAAC;MACzB,CACF,CACF,CAAC,GAAGG,KAAK;IACX;IACA,OAAO1F,KAAK,CAACoB,QAAQ;EACvB,CAAC,EACD,KAAK,CAAC,EACN,KAAK,CACP,CAAC;AACH;AACA,SAASuE,MAAMA,CAAC3F,KAAK,EAAE;EACrB,MAAM4F,GAAG,GAAGpJ,QAAQ,CAAC,MAAMwD,KAAK,CAACxD,QAAQ,CAAC;EAC1C,MAAMqJ,UAAU,GAAGjP,UAAU,CAAC,MAAM;IAClC,MAAMkP,EAAE,GAAGF,GAAG,CAAC,CAAC;IAChB,MAAMG,GAAG,GAAGtL,KAAK,CAACC,OAAO,CAACoL,EAAE,CAAC,GAAGA,EAAE,GAAG,CAACA,EAAE,CAAC;IACzC,IAAIE,IAAI,GAAGA,CAAA,KAAM,KAAK,CAAC;IACvB,KAAK,IAAIjL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgL,GAAG,CAAC/T,MAAM,EAAE+I,CAAC,EAAE,EAAE;MACnC,MAAMuE,KAAK,GAAGvE,CAAC;MACf,MAAMkL,EAAE,GAAGF,GAAG,CAAChL,CAAC,CAAC;MACjB,MAAMmL,QAAQ,GAAGF,IAAI;MACrB,MAAMT,cAAc,GAAG3O,UAAU,CAC/B,MAAMsP,QAAQ,CAAC,CAAC,GAAG,KAAK,CAAC,GAAGD,EAAE,CAACT,IAAI,EACnC,KAAK,CAAC,EACN,KAAK,CACP,CAAC;MACD,MAAMC,SAAS,GAAGQ,EAAE,CAACX,KAAK,GAAGC,cAAc,GAAG3O,UAAU,CAAC2O,cAAc,EAAE,KAAK,CAAC,EAAE;QAC/ExS,MAAM,EAAEA,CAACR,CAAC,EAAEC,CAAC,KAAK,CAACD,CAAC,KAAK,CAACC;MAC5B,CAAC,CAAC;MACFwT,IAAI,GAAGA,CAAA,KAAME,QAAQ,CAAC,CAAC,KAAKT,SAAS,CAAC,CAAC,GAAG,CAACnG,KAAK,EAAEiG,cAAc,EAAEU,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC;IACjF;IACA,OAAOD,IAAI;EACb,CAAC,CAAC;EACF,OAAOpP,UAAU,CACf,MAAM;IACJ,MAAMuP,GAAG,GAAGN,UAAU,CAAC,CAAC,CAAC,CAAC;IAC1B,IAAI,CAACM,GAAG,EAAE,OAAOnG,KAAK,CAACoB,QAAQ;IAC/B,MAAM,CAAC9B,KAAK,EAAEiG,cAAc,EAAEU,EAAE,CAAC,GAAGE,GAAG;IACvC,MAAMT,KAAK,GAAGO,EAAE,CAACzJ,QAAQ;IACzB,MAAMrI,EAAE,GAAG,OAAOuR,KAAK,KAAK,UAAU,IAAIA,KAAK,CAAC1T,MAAM,GAAG,CAAC;IAC1D,OAAOmC,EAAE,GAAGO,OAAO,CACjB,MAAMgR,KAAK,CACTO,EAAE,CAACX,KAAK,GAAGC,cAAc,CAAC,CAAC,GAAG,MAAM;MAClC,IAAI7Q,OAAO,CAACmR,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAKvG,KAAK,EAAE,MAAM0F,aAAa,CAAC,OAAO,CAAC;MACtE,OAAOO,cAAc,CAAC,CAAC;IACzB,CACF,CACF,CAAC,GAAGG,KAAK;EACX,CAAC,EACD,KAAK,CAAC,EACN,KAAK,CACP,CAAC;AACH;AACA,SAASU,KAAKA,CAACpG,KAAK,EAAE;EACpB,OAAOA,KAAK;AACd;AACA,IAAIqG,GAAG,GAAG,KAAK,CAAC;;AAEhB;AACA,IAAIC,QAAQ,GAAG,CACb,iBAAiB,EACjB,OAAO,EACP,WAAW,EACX,UAAU,EACV,SAAS,EACT,UAAU,EACV,SAAS,EACT,UAAU,EACV,gBAAgB,EAChB,QAAQ,EACR,eAAe,EACf,OAAO,EACP,OAAO,EACP,MAAM,EACN,UAAU,EACV,OAAO,EACP,UAAU,EACV,YAAY,EACZ,MAAM,EACN,aAAa,EACb,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,CACX;AACD,IAAIC,UAAU,GAAG,eAAgB,IAAI1O,GAAG,CAAC,CACvC,WAAW,EACX,OAAO,EACP,UAAU,EACV,gBAAgB,EAChB,OAAO,EACP,UAAU,EACV,aAAa,EACb,GAAGyO,QAAQ,CACZ,CAAC;AACF,IAAIE,eAAe,GAAG,eAAgB,IAAI3O,GAAG,CAAC,CAC5C,WAAW,EACX,aAAa,EACb,WAAW,EACX,UAAU,CACX,CAAC;AACF,IAAI4O,OAAO,GAAG,eAAgBzR,MAAM,CAACC,MAAM,CAAC,eAAgBD,MAAM,CAACiO,MAAM,CAAC,IAAI,CAAC,EAAE;EAC/EyD,SAAS,EAAE,OAAO;EAClBC,OAAO,EAAE;AACX,CAAC,CAAC;AACF,IAAIC,WAAW,GAAG,eAAgB5R,MAAM,CAACC,MAAM,CAAC,eAAgBD,MAAM,CAACiO,MAAM,CAAC,IAAI,CAAC,EAAE;EACnF4D,KAAK,EAAE,WAAW;EAClBC,cAAc,EAAE;IACdC,CAAC,EAAE,gBAAgB;IACnBC,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE;EACT,CAAC;EACDC,KAAK,EAAE;IACLH,CAAC,EAAE,OAAO;IACVI,GAAG,EAAE;EACP,CAAC;EACDC,QAAQ,EAAE;IACRL,CAAC,EAAE,UAAU;IACbM,MAAM,EAAE;EACV,CAAC;EACDC,WAAW,EAAE;IACXP,CAAC,EAAE,aAAa;IAChBQ,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRT,CAAC,EAAE,UAAU;IACbE,KAAK,EAAE,CAAC;IACRQ,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC;AACF,SAASC,YAAYA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACnC,MAAMrV,CAAC,GAAGqU,WAAW,CAACe,IAAI,CAAC;EAC3B,OAAO,OAAOpV,CAAC,KAAK,QAAQ,GAAGA,CAAC,CAACqV,OAAO,CAAC,GAAGrV,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAGA,CAAC;AACjE;AACA,IAAIsV,eAAe,GAAG,eAAgB,IAAIhQ,GAAG,CAAC,CAC5C,aAAa,EACb,OAAO,EACP,UAAU,EACV,aAAa,EACb,SAAS,EACT,UAAU,EACV,OAAO,EACP,SAAS,EACT,OAAO,EACP,WAAW,EACX,WAAW,EACX,UAAU,EACV,WAAW,EACX,SAAS,EACT,aAAa,EACb,aAAa,EACb,YAAY,EACZ,aAAa,EACb,WAAW,EACX,UAAU,EACV,WAAW,EACX,YAAY,CACb,CAAC;AACF,IAAIiQ,WAAW,GAAG,eAAgB,IAAIjQ,GAAG,CAAC,CACxC,UAAU,EACV,aAAa,EACb,cAAc,EACd,SAAS,EACT,cAAc,EACd,eAAe,EACf,kBAAkB,EAClB,QAAQ,EACR,UAAU,EACV,eAAe,EACf,QAAQ,EACR,MAAM,EACN,MAAM,EACN,SAAS,EACT,SAAS,EACT,eAAe,EACf,qBAAqB,EACrB,aAAa,EACb,kBAAkB,EAClB,mBAAmB,EACnB,mBAAmB,EACnB,gBAAgB,EAChB,cAAc,EACd,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,gBAAgB,EAChB,SAAS,EACT,SAAS,EACT,aAAa,EACb,cAAc,EACd,UAAU,EACV,cAAc,EACd,oBAAoB,EACpB,aAAa,EACb,QAAQ,EACR,cAAc,EACd,QAAQ,EACR,MAAM,EACN,WAAW,EACX,kBAAkB,EAClB,gBAAgB,EAChB,eAAe,EACf,eAAe,EACf,eAAe,EACf,GAAG,EACH,OAAO,EACP,UAAU,EACV,OAAO,EACP,OAAO,EACP,MAAM,EACN,gBAAgB,EAChB,QAAQ,EACR,MAAM,EACN,UAAU,EACV,eAAe,EACf,OAAO,EACP,MAAM,EACN,SAAS,EACT,SAAS,EACT,UAAU,EACV,gBAAgB,EAChB,MAAM,EACN,KAAK,EACL,MAAM,EACN,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,UAAU,EACV,MAAM,EACN,OAAO,EACP,KAAK,EACL,MAAM,EACN,OAAO,CACR,CAAC;AACF,IAAIkQ,YAAY,GAAG;EACjBC,KAAK,EAAE,8BAA8B;EACrCC,GAAG,EAAE;AACP,CAAC;AACD,SAASC,eAAeA,CAACC,UAAU,EAAE5V,CAAC,EAAEC,CAAC,EAAE;EACzC,IAAI4V,OAAO,GAAG5V,CAAC,CAACR,MAAM;IAAEqW,IAAI,GAAG9V,CAAC,CAACP,MAAM;IAAEsW,IAAI,GAAGF,OAAO;IAAEG,MAAM,GAAG,CAAC;IAAEC,MAAM,GAAG,CAAC;IAAEC,KAAK,GAAGlW,CAAC,CAAC8V,IAAI,GAAG,CAAC,CAAC,CAACK,WAAW;IAAE7E,GAAG,GAAG,IAAI;EAC5H,OAAO0E,MAAM,GAAGF,IAAI,IAAIG,MAAM,GAAGF,IAAI,EAAE;IACrC,IAAI/V,CAAC,CAACgW,MAAM,CAAC,KAAK/V,CAAC,CAACgW,MAAM,CAAC,EAAE;MAC3BD,MAAM,EAAE;MACRC,MAAM,EAAE;MACR;IACF;IACA,OAAOjW,CAAC,CAAC8V,IAAI,GAAG,CAAC,CAAC,KAAK7V,CAAC,CAAC8V,IAAI,GAAG,CAAC,CAAC,EAAE;MAClCD,IAAI,EAAE;MACNC,IAAI,EAAE;IACR;IACA,IAAID,IAAI,KAAKE,MAAM,EAAE;MACnB,MAAMrL,IAAI,GAAGoL,IAAI,GAAGF,OAAO,GAAGI,MAAM,GAAGhW,CAAC,CAACgW,MAAM,GAAG,CAAC,CAAC,CAACE,WAAW,GAAGlW,CAAC,CAAC8V,IAAI,GAAGE,MAAM,CAAC,GAAGC,KAAK;MAC3F,OAAOD,MAAM,GAAGF,IAAI,EAAEH,UAAU,CAACQ,YAAY,CAACnW,CAAC,CAACgW,MAAM,EAAE,CAAC,EAAEtL,IAAI,CAAC;IAClE,CAAC,MAAM,IAAIoL,IAAI,KAAKE,MAAM,EAAE;MAC1B,OAAOD,MAAM,GAAGF,IAAI,EAAE;QACpB,IAAI,CAACxE,GAAG,IAAI,CAACA,GAAG,CAACnO,GAAG,CAACnD,CAAC,CAACgW,MAAM,CAAC,CAAC,EAAEhW,CAAC,CAACgW,MAAM,CAAC,CAACK,MAAM,CAAC,CAAC;QACnDL,MAAM,EAAE;MACV;IACF,CAAC,MAAM,IAAIhW,CAAC,CAACgW,MAAM,CAAC,KAAK/V,CAAC,CAAC8V,IAAI,GAAG,CAAC,CAAC,IAAI9V,CAAC,CAACgW,MAAM,CAAC,KAAKjW,CAAC,CAAC8V,IAAI,GAAG,CAAC,CAAC,EAAE;MACjE,MAAMnL,IAAI,GAAG3K,CAAC,CAAC,EAAE8V,IAAI,CAAC,CAACK,WAAW;MAClCP,UAAU,CAACQ,YAAY,CAACnW,CAAC,CAACgW,MAAM,EAAE,CAAC,EAAEjW,CAAC,CAACgW,MAAM,EAAE,CAAC,CAACG,WAAW,CAAC;MAC7DP,UAAU,CAACQ,YAAY,CAACnW,CAAC,CAAC,EAAE8V,IAAI,CAAC,EAAEpL,IAAI,CAAC;MACxC3K,CAAC,CAAC8V,IAAI,CAAC,GAAG7V,CAAC,CAAC8V,IAAI,CAAC;IACnB,CAAC,MAAM;MACL,IAAI,CAACzE,GAAG,EAAE;QACRA,GAAG,GAAG,eAAgB,IAAIpC,GAAG,CAAC,CAAC;QAC/B,IAAI1G,CAAC,GAAGyN,MAAM;QACd,OAAOzN,CAAC,GAAGuN,IAAI,EAAEzE,GAAG,CAACnC,GAAG,CAAClP,CAAC,CAACuI,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC;MACrC;MACA,MAAMuE,KAAK,GAAGuE,GAAG,CAAC7J,GAAG,CAACzH,CAAC,CAACgW,MAAM,CAAC,CAAC;MAChC,IAAIjJ,KAAK,IAAI,IAAI,EAAE;QACjB,IAAIkJ,MAAM,GAAGlJ,KAAK,IAAIA,KAAK,GAAGgJ,IAAI,EAAE;UAClC,IAAIvN,CAAC,GAAGwN,MAAM;YAAEM,QAAQ,GAAG,CAAC;YAAEhN,CAAC;UAC/B,OAAO,EAAEd,CAAC,GAAGsN,IAAI,IAAItN,CAAC,GAAGuN,IAAI,EAAE;YAC7B,IAAI,CAACzM,CAAC,GAAGgI,GAAG,CAAC7J,GAAG,CAACzH,CAAC,CAACwI,CAAC,CAAC,CAAC,KAAK,IAAI,IAAIc,CAAC,KAAKyD,KAAK,GAAGuJ,QAAQ,EAAE;YAC3DA,QAAQ,EAAE;UACZ;UACA,IAAIA,QAAQ,GAAGvJ,KAAK,GAAGkJ,MAAM,EAAE;YAC7B,MAAMtL,IAAI,GAAG3K,CAAC,CAACgW,MAAM,CAAC;YACtB,OAAOC,MAAM,GAAGlJ,KAAK,EAAE6I,UAAU,CAACQ,YAAY,CAACnW,CAAC,CAACgW,MAAM,EAAE,CAAC,EAAEtL,IAAI,CAAC;UACnE,CAAC,MAAMiL,UAAU,CAACW,YAAY,CAACtW,CAAC,CAACgW,MAAM,EAAE,CAAC,EAAEjW,CAAC,CAACgW,MAAM,EAAE,CAAC,CAAC;QAC1D,CAAC,MAAMA,MAAM,EAAE;MACjB,CAAC,MAAMhW,CAAC,CAACgW,MAAM,EAAE,CAAC,CAACK,MAAM,CAAC,CAAC;IAC7B;EACF;AACF;AACA,IAAIG,QAAQ,GAAG,eAAe;AAC9B,SAASrS,MAAMA,CAACsS,IAAI,EAAEC,OAAO,EAAEnL,IAAI,EAAE/I,OAAO,GAAG,CAAC,CAAC,EAAE;EACjD,IAAIsM,QAAQ;EACZnN,UAAU,CAAEgV,QAAQ,IAAK;IACvB7H,QAAQ,GAAG6H,QAAQ;IACnBD,OAAO,KAAKE,QAAQ,GAAGH,IAAI,CAAC,CAAC,GAAGI,MAAM,CAACH,OAAO,EAAED,IAAI,CAAC,CAAC,EAAEC,OAAO,CAACI,UAAU,GAAG,IAAI,GAAG,KAAK,CAAC,EAAEvL,IAAI,CAAC;EACnG,CAAC,EAAE/I,OAAO,CAACvB,KAAK,CAAC;EACjB,OAAO,MAAM;IACX6N,QAAQ,CAAC,CAAC;IACV4H,OAAO,CAACK,WAAW,GAAG,EAAE;EAC1B,CAAC;AACH;AACA,SAASC,QAAQA,CAACC,IAAI,EAAEC,YAAY,EAAEC,KAAK,EAAEC,QAAQ,EAAE;EACrD,IAAIzM,IAAI;EACR,MAAM+F,MAAM,GAAGA,CAAA,KAAM;IACnB,MAAMpH,CAAC,GAAG8N,QAAQ,GAAGR,QAAQ,CAACS,eAAe,CAAC,oCAAoC,EAAE,UAAU,CAAC,GAAGT,QAAQ,CAACU,aAAa,CAAC,UAAU,CAAC;IACpIhO,CAAC,CAACiO,SAAS,GAAGN,IAAI;IAClB,OAAOE,KAAK,GAAG7N,CAAC,CAACkO,OAAO,CAACV,UAAU,CAACA,UAAU,GAAGM,QAAQ,GAAG9N,CAAC,CAACwN,UAAU,GAAGxN,CAAC,CAACkO,OAAO,CAACV,UAAU;EACjG,CAAC;EACD,MAAMlV,EAAE,GAAGsV,YAAY,GAAG,MAAM/U,OAAO,CAAC,MAAMyU,QAAQ,CAACa,UAAU,CAAC9M,IAAI,KAAKA,IAAI,GAAG+F,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC/F,IAAI,KAAKA,IAAI,GAAG+F,MAAM,CAAC,CAAC,CAAC,EAAEgH,SAAS,CAAC,IAAI,CAAC;EACvJ9V,EAAE,CAAC8V,SAAS,GAAG9V,EAAE;EACjB,OAAOA,EAAE;AACX;AACA,SAAS+V,cAAcA,CAACC,UAAU,EAAEC,SAAS,GAAGC,MAAM,CAAClB,QAAQ,EAAE;EAC/D,MAAMrP,CAAC,GAAGsQ,SAAS,CAACrB,QAAQ,CAAC,KAAKqB,SAAS,CAACrB,QAAQ,CAAC,GAAG,eAAgB,IAAIlR,GAAG,CAAC,CAAC,CAAC;EAClF,KAAK,IAAIkD,CAAC,GAAG,CAAC,EAAEW,CAAC,GAAGyO,UAAU,CAACnY,MAAM,EAAE+I,CAAC,GAAGW,CAAC,EAAEX,CAAC,EAAE,EAAE;IACjD,MAAMkK,IAAI,GAAGkF,UAAU,CAACpP,CAAC,CAAC;IAC1B,IAAI,CAACjB,CAAC,CAACpE,GAAG,CAACuP,IAAI,CAAC,EAAE;MAChBnL,CAAC,CAACP,GAAG,CAAC0L,IAAI,CAAC;MACXmF,SAAS,CAACE,gBAAgB,CAACrF,IAAI,EAAEsF,YAAY,CAAC;IAChD;EACF;AACF;AACA,SAASC,oBAAoBA,CAACJ,SAAS,GAAGC,MAAM,CAAClB,QAAQ,EAAE;EACzD,IAAIiB,SAAS,CAACrB,QAAQ,CAAC,EAAE;IACvB,KAAK,IAAI9D,IAAI,IAAImF,SAAS,CAACrB,QAAQ,CAAC,CAAC5P,IAAI,CAAC,CAAC,EAAEiR,SAAS,CAACK,mBAAmB,CAACxF,IAAI,EAAEsF,YAAY,CAAC;IAC9F,OAAOH,SAAS,CAACrB,QAAQ,CAAC;EAC5B;AACF;AACA,SAAS2B,YAAYA,CAACxN,IAAI,EAAE+H,IAAI,EAAEnQ,KAAK,EAAE;EACvC,IAAI6V,WAAW,CAACzN,IAAI,CAAC,EAAE;EACvB,IAAIpI,KAAK,IAAI,IAAI,EAAEoI,IAAI,CAAC0N,eAAe,CAAC3F,IAAI,CAAC,CAAC,KACzC/H,IAAI,CAACwN,YAAY,CAACzF,IAAI,EAAEnQ,KAAK,CAAC;AACrC;AACA,SAAS+V,cAAcA,CAAC3N,IAAI,EAAE4N,SAAS,EAAE7F,IAAI,EAAEnQ,KAAK,EAAE;EACpD,IAAI6V,WAAW,CAACzN,IAAI,CAAC,EAAE;EACvB,IAAIpI,KAAK,IAAI,IAAI,EAAEoI,IAAI,CAAC6N,iBAAiB,CAACD,SAAS,EAAE7F,IAAI,CAAC,CAAC,KACtD/H,IAAI,CAAC2N,cAAc,CAACC,SAAS,EAAE7F,IAAI,EAAEnQ,KAAK,CAAC;AAClD;AACA,SAASkW,gBAAgBA,CAAC9N,IAAI,EAAE+H,IAAI,EAAEnQ,KAAK,EAAE;EAC3C,IAAI6V,WAAW,CAACzN,IAAI,CAAC,EAAE;EACvBpI,KAAK,GAAGoI,IAAI,CAACwN,YAAY,CAACzF,IAAI,EAAE,EAAE,CAAC,GAAG/H,IAAI,CAAC0N,eAAe,CAAC3F,IAAI,CAAC;AAClE;AACA,SAASyB,SAASA,CAACxJ,IAAI,EAAEpI,KAAK,EAAE;EAC9B,IAAI6V,WAAW,CAACzN,IAAI,CAAC,EAAE;EACvB,IAAIpI,KAAK,IAAI,IAAI,EAAEoI,IAAI,CAAC0N,eAAe,CAAC,OAAO,CAAC,CAAC,KAC5C1N,IAAI,CAACwJ,SAAS,GAAG5R,KAAK;AAC7B;AACA,SAASwV,gBAAgBA,CAACpN,IAAI,EAAE+H,IAAI,EAAEgG,OAAO,EAAEC,QAAQ,EAAE;EACvD,IAAIA,QAAQ,EAAE;IACZ,IAAIzQ,KAAK,CAACC,OAAO,CAACuQ,OAAO,CAAC,EAAE;MAC1B/N,IAAI,CAAC,KAAK+H,IAAI,EAAE,CAAC,GAAGgG,OAAO,CAAC,CAAC,CAAC;MAC9B/N,IAAI,CAAC,KAAK+H,IAAI,MAAM,CAAC,GAAGgG,OAAO,CAAC,CAAC,CAAC;IACpC,CAAC,MAAM/N,IAAI,CAAC,KAAK+H,IAAI,EAAE,CAAC,GAAGgG,OAAO;EACpC,CAAC,MAAM,IAAIxQ,KAAK,CAACC,OAAO,CAACuQ,OAAO,CAAC,EAAE;IACjC,MAAME,SAAS,GAAGF,OAAO,CAAC,CAAC,CAAC;IAC5B/N,IAAI,CAACoN,gBAAgB,CAACrF,IAAI,EAAEgG,OAAO,CAAC,CAAC,CAAC,GAAInR,CAAC,IAAKqR,SAAS,CAACC,IAAI,CAAClO,IAAI,EAAE+N,OAAO,CAAC,CAAC,CAAC,EAAEnR,CAAC,CAAC,CAAC;EACtF,CAAC,MAAMoD,IAAI,CAACoN,gBAAgB,CAACrF,IAAI,EAAEgG,OAAO,EAAE,OAAOA,OAAO,KAAK,UAAU,IAAIA,OAAO,CAAC;AACvF;AACA,SAASI,SAASA,CAACnO,IAAI,EAAEpI,KAAK,EAAEwG,IAAI,GAAG,CAAC,CAAC,EAAE;EACzC,MAAMgQ,SAAS,GAAGtW,MAAM,CAACmE,IAAI,CAACrE,KAAK,IAAI,CAAC,CAAC,CAAC;IAAEyW,QAAQ,GAAGvW,MAAM,CAACmE,IAAI,CAACmC,IAAI,CAAC;EACxE,IAAIP,CAAC,EAAEhJ,GAAG;EACV,KAAKgJ,CAAC,GAAG,CAAC,EAAEhJ,GAAG,GAAGwZ,QAAQ,CAACvZ,MAAM,EAAE+I,CAAC,GAAGhJ,GAAG,EAAEgJ,CAAC,EAAE,EAAE;IAC/C,MAAMnC,GAAG,GAAG2S,QAAQ,CAACxQ,CAAC,CAAC;IACvB,IAAI,CAACnC,GAAG,IAAIA,GAAG,KAAK,WAAW,IAAI9D,KAAK,CAAC8D,GAAG,CAAC,EAAE;IAC/C4S,cAAc,CAACtO,IAAI,EAAEtE,GAAG,EAAE,KAAK,CAAC;IAChC,OAAO0C,IAAI,CAAC1C,GAAG,CAAC;EAClB;EACA,KAAKmC,CAAC,GAAG,CAAC,EAAEhJ,GAAG,GAAGuZ,SAAS,CAACtZ,MAAM,EAAE+I,CAAC,GAAGhJ,GAAG,EAAEgJ,CAAC,EAAE,EAAE;IAChD,MAAMnC,GAAG,GAAG0S,SAAS,CAACvQ,CAAC,CAAC;MAAE0Q,UAAU,GAAG,CAAC,CAAC3W,KAAK,CAAC8D,GAAG,CAAC;IACnD,IAAI,CAACA,GAAG,IAAIA,GAAG,KAAK,WAAW,IAAI0C,IAAI,CAAC1C,GAAG,CAAC,KAAK6S,UAAU,IAAI,CAACA,UAAU,EAAE;IAC5ED,cAAc,CAACtO,IAAI,EAAEtE,GAAG,EAAE,IAAI,CAAC;IAC/B0C,IAAI,CAAC1C,GAAG,CAAC,GAAG6S,UAAU;EACxB;EACA,OAAOnQ,IAAI;AACb;AACA,SAASoQ,KAAKA,CAACxO,IAAI,EAAEpI,KAAK,EAAEwG,IAAI,EAAE;EAChC,IAAI,CAACxG,KAAK,EAAE,OAAOwG,IAAI,GAAGoP,YAAY,CAACxN,IAAI,EAAE,OAAO,CAAC,GAAGpI,KAAK;EAC7D,MAAM6W,SAAS,GAAGzO,IAAI,CAACwO,KAAK;EAC5B,IAAI,OAAO5W,KAAK,KAAK,QAAQ,EAAE,OAAO6W,SAAS,CAACC,OAAO,GAAG9W,KAAK;EAC/D,OAAOwG,IAAI,KAAK,QAAQ,KAAKqQ,SAAS,CAACC,OAAO,GAAGtQ,IAAI,GAAG,KAAK,CAAC,CAAC;EAC/DA,IAAI,KAAKA,IAAI,GAAG,CAAC,CAAC,CAAC;EACnBxG,KAAK,KAAKA,KAAK,GAAG,CAAC,CAAC,CAAC;EACrB,IAAIiC,CAAC,EAAE7B,CAAC;EACR,KAAKA,CAAC,IAAIoG,IAAI,EAAE;IACdxG,KAAK,CAACI,CAAC,CAAC,IAAI,IAAI,IAAIyW,SAAS,CAACE,cAAc,CAAC3W,CAAC,CAAC;IAC/C,OAAOoG,IAAI,CAACpG,CAAC,CAAC;EAChB;EACA,KAAKA,CAAC,IAAIJ,KAAK,EAAE;IACfiC,CAAC,GAAGjC,KAAK,CAACI,CAAC,CAAC;IACZ,IAAI6B,CAAC,KAAKuE,IAAI,CAACpG,CAAC,CAAC,EAAE;MACjByW,SAAS,CAACG,WAAW,CAAC5W,CAAC,EAAE6B,CAAC,CAAC;MAC3BuE,IAAI,CAACpG,CAAC,CAAC,GAAG6B,CAAC;IACb;EACF;EACA,OAAOuE,IAAI;AACb;AACA,SAASyQ,MAAMA,CAAC7O,IAAI,EAAE8C,KAAK,GAAG,CAAC,CAAC,EAAE0J,KAAK,EAAEsC,YAAY,EAAE;EACrD,MAAMC,SAAS,GAAG,CAAC,CAAC;EACpB,IAAI,CAACD,YAAY,EAAE;IACjB5V,kBAAkB,CAChB,MAAM6V,SAAS,CAACzP,QAAQ,GAAG0P,gBAAgB,CAAChP,IAAI,EAAE8C,KAAK,CAACxD,QAAQ,EAAEyP,SAAS,CAACzP,QAAQ,CACtF,CAAC;EACH;EACApG,kBAAkB,CAAC,MAAM,OAAO4J,KAAK,CAACmM,GAAG,KAAK,UAAU,IAAIC,GAAG,CAACpM,KAAK,CAACmM,GAAG,EAAEjP,IAAI,CAAC,CAAC;EACjF9G,kBAAkB,CAAC,MAAMnB,MAAM,CAACiI,IAAI,EAAE8C,KAAK,EAAE0J,KAAK,EAAE,IAAI,EAAEuC,SAAS,EAAE,IAAI,CAAC,CAAC;EAC3E,OAAOA,SAAS;AAClB;AACA,SAASG,GAAGA,CAACjY,EAAE,EAAE8U,OAAO,EAAEoD,GAAG,EAAE;EAC7B,OAAO3X,OAAO,CAAC,MAAMP,EAAE,CAAC8U,OAAO,EAAEoD,GAAG,CAAC,CAAC;AACxC;AACA,SAASjD,MAAMA,CAACkD,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAE;EACjD,IAAID,MAAM,KAAK,KAAK,CAAC,IAAI,CAACC,OAAO,EAAEA,OAAO,GAAG,EAAE;EAC/C,IAAI,OAAOF,QAAQ,KAAK,UAAU,EAAE,OAAOL,gBAAgB,CAACI,MAAM,EAAEC,QAAQ,EAAEE,OAAO,EAAED,MAAM,CAAC;EAC9FpW,kBAAkB,CAAE7B,OAAO,IAAK2X,gBAAgB,CAACI,MAAM,EAAEC,QAAQ,CAAC,CAAC,EAAEhY,OAAO,EAAEiY,MAAM,CAAC,EAAEC,OAAO,CAAC;AACjG;AACA,SAASxX,MAAMA,CAACiI,IAAI,EAAE8C,KAAK,EAAE0J,KAAK,EAAEsC,YAAY,EAAEC,SAAS,GAAG,CAAC,CAAC,EAAES,OAAO,GAAG,KAAK,EAAE;EACjF1M,KAAK,KAAKA,KAAK,GAAG,CAAC,CAAC,CAAC;EACrB,KAAK,MAAM2H,IAAI,IAAIsE,SAAS,EAAE;IAC5B,IAAI,EAAEtE,IAAI,IAAI3H,KAAK,CAAC,EAAE;MACpB,IAAI2H,IAAI,KAAK,UAAU,EAAE;MACzBsE,SAAS,CAACtE,IAAI,CAAC,GAAGgF,UAAU,CAACzP,IAAI,EAAEyK,IAAI,EAAE,IAAI,EAAEsE,SAAS,CAACtE,IAAI,CAAC,EAAE+B,KAAK,EAAEgD,OAAO,EAAE1M,KAAK,CAAC;IACxF;EACF;EACA,KAAK,MAAM2H,IAAI,IAAI3H,KAAK,EAAE;IACxB,IAAI2H,IAAI,KAAK,UAAU,EAAE;MACvB;IACF;IACA,MAAM7S,KAAK,GAAGkL,KAAK,CAAC2H,IAAI,CAAC;IACzBsE,SAAS,CAACtE,IAAI,CAAC,GAAGgF,UAAU,CAACzP,IAAI,EAAEyK,IAAI,EAAE7S,KAAK,EAAEmX,SAAS,CAACtE,IAAI,CAAC,EAAE+B,KAAK,EAAEgD,OAAO,EAAE1M,KAAK,CAAC;EACzF;AACF;AACA,SAAS4M,cAAcA,CAACC,SAAS,EAAE;EACjC,IAAI3P,IAAI;IAAEtE,GAAG;IAAEkU,SAAS,GAAGnC,WAAW,CAAC,CAAC;EACxC,IAAI,CAACmC,SAAS,IAAI,EAAE5P,IAAI,GAAG7L,YAAY,CAACE,QAAQ,CAACyI,GAAG,CAACpB,GAAG,GAAGmU,eAAe,CAAC,CAAC,CAAC,CAAC,EAAE;IAC9E,OAAOF,SAAS,CAAC,CAAC;EACpB;EACA,IAAIxb,YAAY,CAAC2b,SAAS,EAAE3b,YAAY,CAAC2b,SAAS,CAACzT,GAAG,CAAC2D,IAAI,CAAC;EAC5D7L,YAAY,CAACE,QAAQ,CAACyH,MAAM,CAACJ,GAAG,CAAC;EACjC,OAAOsE,IAAI;AACb;AACA,SAASyN,WAAWA,CAACzN,IAAI,EAAE;EACzB,OAAO,CAAC,CAAC7L,YAAY,CAACC,OAAO,IAAI,CAACD,YAAY,CAACI,IAAI,KAAK,CAACyL,IAAI,IAAIA,IAAI,CAAC+P,WAAW,CAAC;AACpF;AACA,SAASC,cAAcA,CAACjI,IAAI,EAAE;EAC5B,OAAOA,IAAI,CAACkI,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,WAAW,EAAE,CAACjL,CAAC,EAAEkL,CAAC,KAAKA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;AAC3E;AACA,SAAS9B,cAAcA,CAACtO,IAAI,EAAEtE,GAAG,EAAE9D,KAAK,EAAE;EACxC,MAAMyY,UAAU,GAAG3U,GAAG,CAAC4U,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,KAAK,CAAC;EAC1C,KAAK,IAAI1S,CAAC,GAAG,CAAC,EAAE2S,OAAO,GAAGH,UAAU,CAACvb,MAAM,EAAE+I,CAAC,GAAG2S,OAAO,EAAE3S,CAAC,EAAE,EAC3DmC,IAAI,CAACmO,SAAS,CAACsC,MAAM,CAACJ,UAAU,CAACxS,CAAC,CAAC,EAAEjG,KAAK,CAAC;AAC/C;AACA,SAAS6X,UAAUA,CAACzP,IAAI,EAAEyK,IAAI,EAAE7S,KAAK,EAAEwG,IAAI,EAAEoO,KAAK,EAAEgD,OAAO,EAAE1M,KAAK,EAAE;EAClE,IAAI4N,IAAI,EAAEC,MAAM,EAAEC,WAAW,EAAEC,SAAS,EAAEC,SAAS;EACnD,IAAIrG,IAAI,KAAK,OAAO,EAAE,OAAO+D,KAAK,CAACxO,IAAI,EAAEpI,KAAK,EAAEwG,IAAI,CAAC;EACrD,IAAIqM,IAAI,KAAK,WAAW,EAAE,OAAO0D,SAAS,CAACnO,IAAI,EAAEpI,KAAK,EAAEwG,IAAI,CAAC;EAC7D,IAAIxG,KAAK,KAAKwG,IAAI,EAAE,OAAOA,IAAI;EAC/B,IAAIqM,IAAI,KAAK,KAAK,EAAE;IAClB,IAAI,CAAC+E,OAAO,EAAE5X,KAAK,CAACoI,IAAI,CAAC;EAC3B,CAAC,MAAM,IAAIyK,IAAI,CAACxI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,KAAK,EAAE;IACrC,MAAMrF,CAAC,GAAG6N,IAAI,CAACxI,KAAK,CAAC,CAAC,CAAC;IACvB7D,IAAI,IAAI4B,IAAI,CAACuN,mBAAmB,CAAC3Q,CAAC,EAAEwB,IAAI,EAAE,OAAOA,IAAI,KAAK,UAAU,IAAIA,IAAI,CAAC;IAC7ExG,KAAK,IAAIoI,IAAI,CAACoN,gBAAgB,CAACxQ,CAAC,EAAEhF,KAAK,EAAE,OAAOA,KAAK,KAAK,UAAU,IAAIA,KAAK,CAAC;EAChF,CAAC,MAAM,IAAI6S,IAAI,CAACxI,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,YAAY,EAAE;IAC7C,MAAMrF,CAAC,GAAG6N,IAAI,CAACxI,KAAK,CAAC,EAAE,CAAC;IACxB7D,IAAI,IAAI4B,IAAI,CAACuN,mBAAmB,CAAC3Q,CAAC,EAAEwB,IAAI,EAAE,IAAI,CAAC;IAC/CxG,KAAK,IAAIoI,IAAI,CAACoN,gBAAgB,CAACxQ,CAAC,EAAEhF,KAAK,EAAE,IAAI,CAAC;EAChD,CAAC,MAAM,IAAI6S,IAAI,CAACxI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,EAAE;IACpC,MAAM8F,IAAI,GAAG0C,IAAI,CAACxI,KAAK,CAAC,CAAC,CAAC,CAACgO,WAAW,CAAC,CAAC;IACxC,MAAMjC,QAAQ,GAAGrD,eAAe,CAACnS,GAAG,CAACuP,IAAI,CAAC;IAC1C,IAAI,CAACiG,QAAQ,IAAI5P,IAAI,EAAE;MACrB,MAAM2S,CAAC,GAAGxT,KAAK,CAACC,OAAO,CAACY,IAAI,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI;MAC9C4B,IAAI,CAACuN,mBAAmB,CAACxF,IAAI,EAAEgJ,CAAC,CAAC;IACnC;IACA,IAAI/C,QAAQ,IAAIpW,KAAK,EAAE;MACrBwV,gBAAgB,CAACpN,IAAI,EAAE+H,IAAI,EAAEnQ,KAAK,EAAEoW,QAAQ,CAAC;MAC7CA,QAAQ,IAAIhB,cAAc,CAAC,CAACjF,IAAI,CAAC,CAAC;IACpC;EACF,CAAC,MAAM,IAAI0C,IAAI,CAACxI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,OAAO,EAAE;IACvCuL,YAAY,CAACxN,IAAI,EAAEyK,IAAI,CAACxI,KAAK,CAAC,CAAC,CAAC,EAAErK,KAAK,CAAC;EAC1C,CAAC,MAAM,IAAI6S,IAAI,CAACxI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,OAAO,EAAE;IACvC6L,gBAAgB,CAAC9N,IAAI,EAAEyK,IAAI,CAACxI,KAAK,CAAC,CAAC,CAAC,EAAErK,KAAK,CAAC;EAC9C,CAAC,MAAM,IAAI,CAACkZ,SAAS,GAAGrG,IAAI,CAACxI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,OAAO,MAAM2O,WAAW,GAAGtH,eAAe,CAAC9Q,GAAG,CAACiS,IAAI,CAAC,CAAC,IAAI,CAAC+B,KAAK,KAAK,CAACqE,SAAS,GAAGrG,YAAY,CAACC,IAAI,EAAEzK,IAAI,CAAC0K,OAAO,CAAC,MAAMiG,MAAM,GAAGtH,UAAU,CAAC7Q,GAAG,CAACiS,IAAI,CAAC,CAAC,CAAC,KAAKiG,IAAI,GAAG1Q,IAAI,CAACgR,QAAQ,CAACnK,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI/D,KAAK,CAAC,EAAE;IAC5P,IAAIgO,SAAS,EAAE;MACbrG,IAAI,GAAGA,IAAI,CAACxI,KAAK,CAAC,CAAC,CAAC;MACpB0O,MAAM,GAAG,IAAI;IACf,CAAC,MAAM,IAAIlD,WAAW,CAACzN,IAAI,CAAC,EAAE,OAAOpI,KAAK;IAC1C,IAAI6S,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,WAAW,EAAEjB,SAAS,CAACxJ,IAAI,EAAEpI,KAAK,CAAC,CAAC,KAChE,IAAI8Y,IAAI,IAAI,CAACC,MAAM,IAAI,CAACC,WAAW,EAAE5Q,IAAI,CAACgQ,cAAc,CAACvF,IAAI,CAAC,CAAC,GAAG7S,KAAK,CAAC,KACxEoI,IAAI,CAAC6Q,SAAS,IAAIpG,IAAI,CAAC,GAAG7S,KAAK;EACtC,CAAC,MAAM;IACL,MAAMqZ,EAAE,GAAGzE,KAAK,IAAI/B,IAAI,CAACyG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAIrG,YAAY,CAACJ,IAAI,CAAC8F,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9E,IAAIU,EAAE,EAAEtD,cAAc,CAAC3N,IAAI,EAAEiR,EAAE,EAAExG,IAAI,EAAE7S,KAAK,CAAC,CAAC,KACzC4V,YAAY,CAACxN,IAAI,EAAEuJ,OAAO,CAACkB,IAAI,CAAC,IAAIA,IAAI,EAAE7S,KAAK,CAAC;EACvD;EACA,OAAOA,KAAK;AACd;AACA,SAASyV,YAAYA,CAACzQ,CAAC,EAAE;EACvB,IAAIzI,YAAY,CAACE,QAAQ,IAAIF,YAAY,CAACgd,MAAM,EAAE;IAChD,IAAIhd,YAAY,CAACgd,MAAM,CAACC,IAAI,CAAC,CAAC,CAACC,EAAE,EAAEC,EAAE,CAAC,KAAKA,EAAE,KAAK1U,CAAC,CAAC,EAAE;EACxD;EACA,IAAIoD,IAAI,GAAGpD,CAAC,CAACyJ,MAAM;EACnB,MAAM3K,GAAG,GAAG,KAAKkB,CAAC,CAAC2U,IAAI,EAAE;EACzB,MAAMC,SAAS,GAAG5U,CAAC,CAACyJ,MAAM;EAC1B,MAAMoL,gBAAgB,GAAG7U,CAAC,CAAC8U,aAAa;EACxC,MAAMC,QAAQ,GAAI/Z,KAAK,IAAKE,MAAM,CAACyO,cAAc,CAAC3J,CAAC,EAAE,QAAQ,EAAE;IAC7D0I,YAAY,EAAE,IAAI;IAClB1N;EACF,CAAC,CAAC;EACF,MAAMga,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAM7D,OAAO,GAAG/N,IAAI,CAACtE,GAAG,CAAC;IACzB,IAAIqS,OAAO,IAAI,CAAC/N,IAAI,CAAC6R,QAAQ,EAAE;MAC7B,MAAMC,IAAI,GAAG9R,IAAI,CAAC,GAAGtE,GAAG,MAAM,CAAC;MAC/BoW,IAAI,KAAK,KAAK,CAAC,GAAG/D,OAAO,CAACG,IAAI,CAAClO,IAAI,EAAE8R,IAAI,EAAElV,CAAC,CAAC,GAAGmR,OAAO,CAACG,IAAI,CAAClO,IAAI,EAAEpD,CAAC,CAAC;MACrE,IAAIA,CAAC,CAACmV,YAAY,EAAE;IACtB;IACA/R,IAAI,CAACgS,IAAI,IAAI,OAAOhS,IAAI,CAACgS,IAAI,KAAK,QAAQ,IAAI,CAAChS,IAAI,CAACgS,IAAI,CAACC,MAAM,IAAIjS,IAAI,CAACkS,QAAQ,CAACtV,CAAC,CAACyJ,MAAM,CAAC,IAAIsL,QAAQ,CAAC3R,IAAI,CAACgS,IAAI,CAAC;IACjH,OAAO,IAAI;EACb,CAAC;EACD,MAAMG,UAAU,GAAGA,CAAA,KAAM;IACvB,OAAOP,UAAU,CAAC,CAAC,KAAK5R,IAAI,GAAGA,IAAI,CAACiS,MAAM,IAAIjS,IAAI,CAACiL,UAAU,IAAIjL,IAAI,CAACgS,IAAI,CAAC,CAAE;EAC/E,CAAC;EACDla,MAAM,CAACyO,cAAc,CAAC3J,CAAC,EAAE,eAAe,EAAE;IACxC0I,YAAY,EAAE,IAAI;IAClBxI,GAAGA,CAAA,EAAG;MACJ,OAAOkD,IAAI,IAAIiM,QAAQ;IACzB;EACF,CAAC,CAAC;EACF,IAAI9X,YAAY,CAACE,QAAQ,IAAI,CAACF,YAAY,CAACI,IAAI,EAAEJ,YAAY,CAACI,IAAI,GAAG6d,IAAI,CAAC7d,IAAI,GAAG,IAAI;EACrF,IAAIqI,CAAC,CAACyV,YAAY,EAAE;IAClB,MAAMC,IAAI,GAAG1V,CAAC,CAACyV,YAAY,CAAC,CAAC;IAC7BV,QAAQ,CAACW,IAAI,CAAC,CAAC,CAAC,CAAC;IACjB,KAAK,IAAIzU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyU,IAAI,CAACxd,MAAM,GAAG,CAAC,EAAE+I,CAAC,EAAE,EAAE;MACxCmC,IAAI,GAAGsS,IAAI,CAACzU,CAAC,CAAC;MACd,IAAI,CAAC+T,UAAU,CAAC,CAAC,EAAE;MACnB,IAAI5R,IAAI,CAACiS,MAAM,EAAE;QACfjS,IAAI,GAAGA,IAAI,CAACiS,MAAM;QAClBE,UAAU,CAAC,CAAC;QACZ;MACF;MACA,IAAInS,IAAI,CAACiL,UAAU,KAAKwG,gBAAgB,EAAE;QACxC;MACF;IACF;EACF,CAAC,MAAMU,UAAU,CAAC,CAAC;EACnBR,QAAQ,CAACH,SAAS,CAAC;AACrB;AACA,SAASxC,gBAAgBA,CAACI,MAAM,EAAExX,KAAK,EAAEP,OAAO,EAAEiY,MAAM,EAAEiD,WAAW,EAAE;EACrE,MAAM3C,SAAS,GAAGnC,WAAW,CAAC2B,MAAM,CAAC;EACrC,IAAIQ,SAAS,EAAE;IACb,CAACvY,OAAO,KAAKA,OAAO,GAAG,CAAC,GAAG+X,MAAM,CAACoD,UAAU,CAAC,CAAC;IAC9C,IAAIC,OAAO,GAAG,EAAE;IAChB,KAAK,IAAI5U,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxG,OAAO,CAACvC,MAAM,EAAE+I,CAAC,EAAE,EAAE;MACvC,MAAMmC,IAAI,GAAG3I,OAAO,CAACwG,CAAC,CAAC;MACvB,IAAImC,IAAI,CAAC0S,QAAQ,KAAK,CAAC,IAAI1S,IAAI,CAAC8R,IAAI,CAAC7P,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,EAAEjC,IAAI,CAAC0L,MAAM,CAAC,CAAC,CAAC,KACpE+G,OAAO,CAACzZ,IAAI,CAACgH,IAAI,CAAC;IACzB;IACA3I,OAAO,GAAGob,OAAO;EACnB;EACA,OAAO,OAAOpb,OAAO,KAAK,UAAU,EAAEA,OAAO,GAAGA,OAAO,CAAC,CAAC;EACzD,IAAIO,KAAK,KAAKP,OAAO,EAAE,OAAOA,OAAO;EACrC,MAAMsH,CAAC,GAAG,OAAO/G,KAAK;IAAE+a,KAAK,GAAGrD,MAAM,KAAK,KAAK,CAAC;EACjDF,MAAM,GAAGuD,KAAK,IAAItb,OAAO,CAAC,CAAC,CAAC,IAAIA,OAAO,CAAC,CAAC,CAAC,CAAC4T,UAAU,IAAImE,MAAM;EAC/D,IAAIzQ,CAAC,KAAK,QAAQ,IAAIA,CAAC,KAAK,QAAQ,EAAE;IACpC,IAAIiR,SAAS,EAAE,OAAOvY,OAAO;IAC7B,IAAIsH,CAAC,KAAK,QAAQ,EAAE;MAClB/G,KAAK,GAAGA,KAAK,CAACgb,QAAQ,CAAC,CAAC;MACxB,IAAIhb,KAAK,KAAKP,OAAO,EAAE,OAAOA,OAAO;IACvC;IACA,IAAIsb,KAAK,EAAE;MACT,IAAI3S,IAAI,GAAG3I,OAAO,CAAC,CAAC,CAAC;MACrB,IAAI2I,IAAI,IAAIA,IAAI,CAAC0S,QAAQ,KAAK,CAAC,EAAE;QAC/B1S,IAAI,CAAC8R,IAAI,KAAKla,KAAK,KAAKoI,IAAI,CAAC8R,IAAI,GAAGla,KAAK,CAAC;MAC5C,CAAC,MAAMoI,IAAI,GAAGiM,QAAQ,CAAC4G,cAAc,CAACjb,KAAK,CAAC;MAC5CP,OAAO,GAAGyb,aAAa,CAAC1D,MAAM,EAAE/X,OAAO,EAAEiY,MAAM,EAAEtP,IAAI,CAAC;IACxD,CAAC,MAAM;MACL,IAAI3I,OAAO,KAAK,EAAE,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;QACjDA,OAAO,GAAG+X,MAAM,CAACjD,UAAU,CAAC2F,IAAI,GAAGla,KAAK;MAC1C,CAAC,MAAMP,OAAO,GAAG+X,MAAM,CAAChD,WAAW,GAAGxU,KAAK;IAC7C;EACF,CAAC,MAAM,IAAIA,KAAK,IAAI,IAAI,IAAI+G,CAAC,KAAK,SAAS,EAAE;IAC3C,IAAIiR,SAAS,EAAE,OAAOvY,OAAO;IAC7BA,OAAO,GAAGyb,aAAa,CAAC1D,MAAM,EAAE/X,OAAO,EAAEiY,MAAM,CAAC;EAClD,CAAC,MAAM,IAAI3Q,CAAC,KAAK,UAAU,EAAE;IAC3BzF,kBAAkB,CAAC,MAAM;MACvB,IAAIW,CAAC,GAAGjC,KAAK,CAAC,CAAC;MACf,OAAO,OAAOiC,CAAC,KAAK,UAAU,EAAEA,CAAC,GAAGA,CAAC,CAAC,CAAC;MACvCxC,OAAO,GAAG2X,gBAAgB,CAACI,MAAM,EAAEvV,CAAC,EAAExC,OAAO,EAAEiY,MAAM,CAAC;IACxD,CAAC,CAAC;IACF,OAAO,MAAMjY,OAAO;EACtB,CAAC,MAAM,IAAIkG,KAAK,CAACC,OAAO,CAAC5F,KAAK,CAAC,EAAE;IAC/B,MAAMmb,KAAK,GAAG,EAAE;IAChB,MAAMC,YAAY,GAAG3b,OAAO,IAAIkG,KAAK,CAACC,OAAO,CAACnG,OAAO,CAAC;IACtD,IAAI4b,sBAAsB,CAACF,KAAK,EAAEnb,KAAK,EAAEP,OAAO,EAAEkb,WAAW,CAAC,EAAE;MAC9DrZ,kBAAkB,CAAC,MAAM7B,OAAO,GAAG2X,gBAAgB,CAACI,MAAM,EAAE2D,KAAK,EAAE1b,OAAO,EAAEiY,MAAM,EAAE,IAAI,CAAC,CAAC;MAC1F,OAAO,MAAMjY,OAAO;IACtB;IACA,IAAIuY,SAAS,EAAE;MACb,IAAI,CAACmD,KAAK,CAACje,MAAM,EAAE,OAAOuC,OAAO;MACjC,IAAIiY,MAAM,KAAK,KAAK,CAAC,EAAE,OAAOjY,OAAO,GAAG,CAAC,GAAG+X,MAAM,CAACoD,UAAU,CAAC;MAC9D,IAAIxS,IAAI,GAAG+S,KAAK,CAAC,CAAC,CAAC;MACnB,IAAI/S,IAAI,CAACiL,UAAU,KAAKmE,MAAM,EAAE,OAAO/X,OAAO;MAC9C,MAAM6b,KAAK,GAAG,CAAClT,IAAI,CAAC;MACpB,OAAO,CAACA,IAAI,GAAGA,IAAI,CAACwL,WAAW,MAAM8D,MAAM,EAAE4D,KAAK,CAACla,IAAI,CAACgH,IAAI,CAAC;MAC7D,OAAO3I,OAAO,GAAG6b,KAAK;IACxB;IACA,IAAIH,KAAK,CAACje,MAAM,KAAK,CAAC,EAAE;MACtBuC,OAAO,GAAGyb,aAAa,CAAC1D,MAAM,EAAE/X,OAAO,EAAEiY,MAAM,CAAC;MAChD,IAAIqD,KAAK,EAAE,OAAOtb,OAAO;IAC3B,CAAC,MAAM,IAAI2b,YAAY,EAAE;MACvB,IAAI3b,OAAO,CAACvC,MAAM,KAAK,CAAC,EAAE;QACxBqe,WAAW,CAAC/D,MAAM,EAAE2D,KAAK,EAAEzD,MAAM,CAAC;MACpC,CAAC,MAAMtE,eAAe,CAACoE,MAAM,EAAE/X,OAAO,EAAE0b,KAAK,CAAC;IAChD,CAAC,MAAM;MACL1b,OAAO,IAAIyb,aAAa,CAAC1D,MAAM,CAAC;MAChC+D,WAAW,CAAC/D,MAAM,EAAE2D,KAAK,CAAC;IAC5B;IACA1b,OAAO,GAAG0b,KAAK;EACjB,CAAC,MAAM,IAAInb,KAAK,CAAC8a,QAAQ,EAAE;IACzB,IAAI9C,SAAS,IAAIhY,KAAK,CAACqT,UAAU,EAAE,OAAO5T,OAAO,GAAGsb,KAAK,GAAG,CAAC/a,KAAK,CAAC,GAAGA,KAAK;IAC3E,IAAI2F,KAAK,CAACC,OAAO,CAACnG,OAAO,CAAC,EAAE;MAC1B,IAAIsb,KAAK,EAAE,OAAOtb,OAAO,GAAGyb,aAAa,CAAC1D,MAAM,EAAE/X,OAAO,EAAEiY,MAAM,EAAE1X,KAAK,CAAC;MACzEkb,aAAa,CAAC1D,MAAM,EAAE/X,OAAO,EAAE,IAAI,EAAEO,KAAK,CAAC;IAC7C,CAAC,MAAM,IAAIP,OAAO,IAAI,IAAI,IAAIA,OAAO,KAAK,EAAE,IAAI,CAAC+X,MAAM,CAACjD,UAAU,EAAE;MAClEiD,MAAM,CAACgE,WAAW,CAACxb,KAAK,CAAC;IAC3B,CAAC,MAAMwX,MAAM,CAACxD,YAAY,CAAChU,KAAK,EAAEwX,MAAM,CAACjD,UAAU,CAAC;IACpD9U,OAAO,GAAGO,KAAK;EACjB,CAAC,MAAM;EACP,OAAOP,OAAO;AAChB;AACA,SAAS4b,sBAAsBA,CAACI,UAAU,EAAEN,KAAK,EAAE1b,OAAO,EAAEic,MAAM,EAAE;EAClE,IAAI7Y,OAAO,GAAG,KAAK;EACnB,KAAK,IAAIoD,CAAC,GAAG,CAAC,EAAEhJ,GAAG,GAAGke,KAAK,CAACje,MAAM,EAAE+I,CAAC,GAAGhJ,GAAG,EAAEgJ,CAAC,EAAE,EAAE;IAChD,IAAIiE,IAAI,GAAGiR,KAAK,CAAClV,CAAC,CAAC;MAAEO,IAAI,GAAG/G,OAAO,IAAIA,OAAO,CAACgc,UAAU,CAACve,MAAM,CAAC;MAAE6J,CAAC;IACpE,IAAImD,IAAI,IAAI,IAAI,IAAIA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,EAAE,CAAC,KACjD,IAAI,CAACnD,CAAC,GAAG,OAAOmD,IAAI,MAAM,QAAQ,IAAIA,IAAI,CAAC4Q,QAAQ,EAAE;MACxDW,UAAU,CAACra,IAAI,CAAC8I,IAAI,CAAC;IACvB,CAAC,MAAM,IAAIvE,KAAK,CAACC,OAAO,CAACsE,IAAI,CAAC,EAAE;MAC9BrH,OAAO,GAAGwY,sBAAsB,CAACI,UAAU,EAAEvR,IAAI,EAAE1D,IAAI,CAAC,IAAI3D,OAAO;IACrE,CAAC,MAAM,IAAIkE,CAAC,KAAK,UAAU,EAAE;MAC3B,IAAI2U,MAAM,EAAE;QACV,OAAO,OAAOxR,IAAI,KAAK,UAAU,EAAEA,IAAI,GAAGA,IAAI,CAAC,CAAC;QAChDrH,OAAO,GAAGwY,sBAAsB,CAC9BI,UAAU,EACV9V,KAAK,CAACC,OAAO,CAACsE,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAAC,EACnCvE,KAAK,CAACC,OAAO,CAACY,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CACpC,CAAC,IAAI3D,OAAO;MACd,CAAC,MAAM;QACL4Y,UAAU,CAACra,IAAI,CAAC8I,IAAI,CAAC;QACrBrH,OAAO,GAAG,IAAI;MAChB;IACF,CAAC,MAAM;MACL,MAAM7C,KAAK,GAAGhD,MAAM,CAACkN,IAAI,CAAC;MAC1B,IAAI1D,IAAI,IAAIA,IAAI,CAACsU,QAAQ,KAAK,CAAC,IAAItU,IAAI,CAAC0T,IAAI,KAAKla,KAAK,EAAEyb,UAAU,CAACra,IAAI,CAACoF,IAAI,CAAC,CAAC,KACzEiV,UAAU,CAACra,IAAI,CAACiT,QAAQ,CAAC4G,cAAc,CAACjb,KAAK,CAAC,CAAC;IACtD;EACF;EACA,OAAO6C,OAAO;AAChB;AACA,SAAS0Y,WAAWA,CAAC/D,MAAM,EAAE2D,KAAK,EAAEzD,MAAM,GAAG,IAAI,EAAE;EACjD,KAAK,IAAIzR,CAAC,GAAG,CAAC,EAAEhJ,GAAG,GAAGke,KAAK,CAACje,MAAM,EAAE+I,CAAC,GAAGhJ,GAAG,EAAEgJ,CAAC,EAAE,EAAEuR,MAAM,CAAC3D,YAAY,CAACsH,KAAK,CAAClV,CAAC,CAAC,EAAEyR,MAAM,CAAC;AACzF;AACA,SAASwD,aAAaA,CAAC1D,MAAM,EAAE/X,OAAO,EAAEiY,MAAM,EAAEiE,WAAW,EAAE;EAC3D,IAAIjE,MAAM,KAAK,KAAK,CAAC,EAAE,OAAOF,MAAM,CAAChD,WAAW,GAAG,EAAE;EACrD,MAAMpM,IAAI,GAAGuT,WAAW,IAAItH,QAAQ,CAAC4G,cAAc,CAAC,EAAE,CAAC;EACvD,IAAIxb,OAAO,CAACvC,MAAM,EAAE;IAClB,IAAI0e,QAAQ,GAAG,KAAK;IACpB,KAAK,IAAI3V,CAAC,GAAGxG,OAAO,CAACvC,MAAM,GAAG,CAAC,EAAE+I,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC5C,MAAMwT,EAAE,GAAGha,OAAO,CAACwG,CAAC,CAAC;MACrB,IAAImC,IAAI,KAAKqR,EAAE,EAAE;QACf,MAAMoC,QAAQ,GAAGpC,EAAE,CAACpG,UAAU,KAAKmE,MAAM;QACzC,IAAI,CAACoE,QAAQ,IAAI,CAAC3V,CAAC,EACjB4V,QAAQ,GAAGrE,MAAM,CAACxD,YAAY,CAAC5L,IAAI,EAAEqR,EAAE,CAAC,GAAGjC,MAAM,CAAC3D,YAAY,CAACzL,IAAI,EAAEsP,MAAM,CAAC,CAAC,KAC1EmE,QAAQ,IAAIpC,EAAE,CAAC3F,MAAM,CAAC,CAAC;MAC9B,CAAC,MAAM8H,QAAQ,GAAG,IAAI;IACxB;EACF,CAAC,MAAMpE,MAAM,CAAC3D,YAAY,CAACzL,IAAI,EAAEsP,MAAM,CAAC;EACxC,OAAO,CAACtP,IAAI,CAAC;AACf;AACA,SAAS6P,eAAeA,CAAA,EAAG;EACzB,OAAO1b,YAAY,CAACO,gBAAgB,CAAC,CAAC;AACxC;AACA,IAAIgf,QAAQ,GAAG,KAAK;AACpB,IAAIC,aAAa,GAAG,4BAA4B;AAChD,SAAShH,aAAaA,CAACjC,OAAO,EAAE8B,KAAK,GAAG,KAAK,EAAE;EAC7C,OAAOA,KAAK,GAAGP,QAAQ,CAACS,eAAe,CAACiH,aAAa,EAAEjJ,OAAO,CAAC,GAAGuB,QAAQ,CAACU,aAAa,CAACjC,OAAO,CAAC;AACnG;AACA,SAASkJ,MAAMA,CAAC9Q,KAAK,EAAE;EACrB,MAAM;MAAE+Q;IAAU,CAAC,GAAG/Q,KAAK;IAAEwM,MAAM,GAAGrD,QAAQ,CAAC4G,cAAc,CAAC,EAAE,CAAC;IAAEiB,KAAK,GAAGA,CAAA,KAAMhR,KAAK,CAACgR,KAAK,IAAI7H,QAAQ,CAAC8H,IAAI;IAAEzd,KAAK,GAAG2H,QAAQ,CAAC,CAAC;EACjI,IAAI4O,OAAO;EACX,IAAI+C,SAAS,GAAG,CAAC,CAACzb,YAAY,CAACC,OAAO;EACtC+E,YAAY,CACV,MAAM;IACJ,IAAIyW,SAAS,EAAE3R,QAAQ,CAAC,CAAC,CAACxE,IAAI,GAAGmW,SAAS,GAAG,KAAK;IAClD/C,OAAO,KAAKA,OAAO,GAAG3O,YAAY,CAAC5H,KAAK,EAAE,MAAMoD,UAAU,CAAC,MAAMoJ,KAAK,CAACxD,QAAQ,CAAC,CAAC,CAAC;IAClF,MAAM+R,EAAE,GAAGyC,KAAK,CAAC,CAAC;IAClB,IAAIzC,EAAE,YAAY2C,eAAe,EAAE;MACjC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGvc,YAAY,CAAC,KAAK,CAAC;MAC7C,MAAMwc,OAAO,GAAGA,CAAA,KAAMD,QAAQ,CAAC,IAAI,CAAC;MACpCld,UAAU,CAAEgV,QAAQ,IAAKE,MAAM,CAACmF,EAAE,EAAE,MAAM,CAAC4C,KAAK,CAAC,CAAC,GAAGpH,OAAO,CAAC,CAAC,GAAGb,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;MACnFhO,SAAS,CAACmW,OAAO,CAAC;IACpB,CAAC,MAAM;MACL,MAAMC,SAAS,GAAGzH,aAAa,CAAC7J,KAAK,CAAC0J,KAAK,GAAG,GAAG,GAAG,KAAK,EAAE1J,KAAK,CAAC0J,KAAK,CAAC;QAAE6H,UAAU,GAAGR,SAAS,IAAIO,SAAS,CAACE,YAAY,GAAGF,SAAS,CAACE,YAAY,CAAC;UACjJC,IAAI,EAAE;QACR,CAAC,CAAC,GAAGH,SAAS;MACdtc,MAAM,CAACyO,cAAc,CAAC6N,SAAS,EAAE,QAAQ,EAAE;QACzCtX,GAAGA,CAAA,EAAG;UACJ,OAAOwS,MAAM,CAACrE,UAAU;QAC1B,CAAC;QACD3F,YAAY,EAAE;MAChB,CAAC,CAAC;MACF4G,MAAM,CAACmI,UAAU,EAAExH,OAAO,CAAC;MAC3BwE,EAAE,CAAC+B,WAAW,CAACgB,SAAS,CAAC;MACzBtR,KAAK,CAACmM,GAAG,IAAInM,KAAK,CAACmM,GAAG,CAACmF,SAAS,CAAC;MACjCpW,SAAS,CAAC,MAAMqT,EAAE,CAACmD,WAAW,CAACJ,SAAS,CAAC,CAAC;IAC5C;EACF,CAAC,EACD,KAAK,CAAC,EACN;IACE5a,MAAM,EAAE,CAACoW;EACX,CACF,CAAC;EACD,OAAON,MAAM;AACf;AACA,SAASmF,aAAaA,CAACC,SAAS,EAAE5R,KAAK,EAAE;EACvC,MAAM6R,MAAM,GAAGjb,UAAU,CAACgb,SAAS,CAAC;EACpC,OAAOhb,UAAU,CAAC,MAAM;IACtB,MAAMkb,UAAU,GAAGD,MAAM,CAAC,CAAC;IAC3B,QAAQ,OAAOC,UAAU;MACvB,KAAK,UAAU;QACb,OAAOpd,OAAO,CAAC,MAAMod,UAAU,CAAC9R,KAAK,CAAC,CAAC;MACzC,KAAK,QAAQ;QACX,MAAM+R,KAAK,GAAGjK,WAAW,CAACpS,GAAG,CAACoc,UAAU,CAAC;QACzC,MAAMvD,EAAE,GAAGld,YAAY,CAACC,OAAO,GAAGsb,cAAc,CAAC,CAAC,GAAG/C,aAAa,CAACiI,UAAU,EAAEC,KAAK,CAAC;QACrFhG,MAAM,CAACwC,EAAE,EAAEvO,KAAK,EAAE+R,KAAK,CAAC;QACxB,OAAOxD,EAAE;IACb;EACF,CAAC,CAAC;AACJ;AACA,SAASyD,OAAOA,CAAChS,KAAK,EAAE;EACtB,MAAM,GAAGiS,MAAM,CAAC,GAAGvO,UAAU,CAAC1D,KAAK,EAAE,CAAC,WAAW,CAAC,CAAC;EACnD,OAAO2R,aAAa,CAAC,MAAM3R,KAAK,CAAC4R,SAAS,EAAEK,MAAM,CAAC;AACrD;;AAEA;AACA,IAAIC,eAAe,GAAG,MAAM;EAC1BC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,UAAU,GAAG,eAAgB,IAAI3Q,GAAG,CAAC,CAAC;IAC3C,IAAI,CAAC4Q,UAAU,GAAG,eAAgB,IAAI5Q,GAAG,CAAC,CAAC;EAC7C;EACAC,GAAGA,CAAC9I,GAAG,EAAE9D,KAAK,EAAE;IACd,IAAI,CAACsd,UAAU,CAAC1Q,GAAG,CAAC9I,GAAG,EAAE9D,KAAK,CAAC;IAC/B,IAAI,CAACud,UAAU,CAAC3Q,GAAG,CAAC5M,KAAK,EAAE8D,GAAG,CAAC;EACjC;EACA0Z,QAAQA,CAAC1Z,GAAG,EAAE;IACZ,OAAO,IAAI,CAACwZ,UAAU,CAACpY,GAAG,CAACpB,GAAG,CAAC;EACjC;EACA2Z,UAAUA,CAACzd,KAAK,EAAE;IAChB,OAAO,IAAI,CAACud,UAAU,CAACrY,GAAG,CAAClF,KAAK,CAAC;EACnC;EACAuE,KAAKA,CAAA,EAAG;IACN,IAAI,CAAC+Y,UAAU,CAAC/Y,KAAK,CAAC,CAAC;IACvB,IAAI,CAACgZ,UAAU,CAAChZ,KAAK,CAAC,CAAC;EACzB;AACF,CAAC;;AAED;AACA,IAAImZ,QAAQ,GAAG,MAAM;EACnBL,WAAWA,CAACM,kBAAkB,EAAE;IAC9B,IAAI,CAACA,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,EAAE,GAAG,IAAIR,eAAe,CAAC,CAAC;EACjC;EACAS,QAAQA,CAAC7d,KAAK,EAAE8d,UAAU,EAAE;IAC1B,IAAI,IAAI,CAACF,EAAE,CAACH,UAAU,CAACzd,KAAK,CAAC,EAAE;MAC7B;IACF;IACA,IAAI,CAAC8d,UAAU,EAAE;MACfA,UAAU,GAAG,IAAI,CAACH,kBAAkB,CAAC3d,KAAK,CAAC;IAC7C;IACA,IAAI,CAAC4d,EAAE,CAAChR,GAAG,CAACkR,UAAU,EAAE9d,KAAK,CAAC;EAChC;EACAuE,KAAKA,CAAA,EAAG;IACN,IAAI,CAACqZ,EAAE,CAACrZ,KAAK,CAAC,CAAC;EACjB;EACAwZ,aAAaA,CAAC/d,KAAK,EAAE;IACnB,OAAO,IAAI,CAAC4d,EAAE,CAACH,UAAU,CAACzd,KAAK,CAAC;EAClC;EACAge,QAAQA,CAACF,UAAU,EAAE;IACnB,OAAO,IAAI,CAACF,EAAE,CAACJ,QAAQ,CAACM,UAAU,CAAC;EACrC;AACF,CAAC;;AAED;AACA,IAAIG,aAAa,GAAG,cAAcP,QAAQ,CAAC;EACzCL,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAEnc,CAAC,IAAKA,CAAC,CAACiP,IAAI,CAAC;IACpB,IAAI,CAAC+N,mBAAmB,GAAG,eAAgB,IAAIvR,GAAG,CAAC,CAAC;EACtD;EACAkR,QAAQA,CAAC7d,KAAK,EAAEC,OAAO,EAAE;IACvB,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MAC/B,IAAIA,OAAO,CAACke,UAAU,EAAE;QACtB,IAAI,CAACD,mBAAmB,CAACtR,GAAG,CAAC5M,KAAK,EAAEC,OAAO,CAACke,UAAU,CAAC;MACzD;MACA,KAAK,CAACN,QAAQ,CAAC7d,KAAK,EAAEC,OAAO,CAAC6d,UAAU,CAAC;IAC3C,CAAC,MAAM;MACL,KAAK,CAACD,QAAQ,CAAC7d,KAAK,EAAEC,OAAO,CAAC;IAChC;EACF;EACAme,eAAeA,CAACpe,KAAK,EAAE;IACrB,OAAO,IAAI,CAACke,mBAAmB,CAAChZ,GAAG,CAAClF,KAAK,CAAC;EAC5C;AACF,CAAC;;AAED;AACA,SAASqe,WAAWA,CAACC,MAAM,EAAE;EAC3B,IAAI,QAAQ,IAAIpe,MAAM,EAAE;IACtB,OAAOA,MAAM,CAACqe,MAAM,CAACD,MAAM,CAAC;EAC9B;EACA,MAAMC,MAAM,GAAG,EAAE;EACjB,KAAK,MAAMza,GAAG,IAAIwa,MAAM,EAAE;IACxB,IAAIA,MAAM,CAACE,cAAc,CAAC1a,GAAG,CAAC,EAAE;MAC9Bya,MAAM,CAACnd,IAAI,CAACkd,MAAM,CAACxa,GAAG,CAAC,CAAC;IAC1B;EACF;EACA,OAAOya,MAAM;AACf;AACA,SAAS/E,IAAIA,CAAC8E,MAAM,EAAEG,SAAS,EAAE;EAC/B,MAAMF,MAAM,GAAGF,WAAW,CAACC,MAAM,CAAC;EAClC,IAAI,MAAM,IAAIC,MAAM,EAAE;IACpB,OAAOA,MAAM,CAAC/E,IAAI,CAACiF,SAAS,CAAC;EAC/B;EACA,MAAMC,cAAc,GAAGH,MAAM;EAC7B,KAAK,IAAItY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyY,cAAc,CAACxhB,MAAM,EAAE+I,CAAC,EAAE,EAAE;IAC9C,MAAMjG,KAAK,GAAG0e,cAAc,CAACzY,CAAC,CAAC;IAC/B,IAAIwY,SAAS,CAACze,KAAK,CAAC,EAAE;MACpB,OAAOA,KAAK;IACd;EACF;EACA,OAAO,KAAK,CAAC;AACf;AACA,SAAS8I,OAAOA,CAACwV,MAAM,EAAEK,GAAG,EAAE;EAC5Bze,MAAM,CAAC0e,OAAO,CAACN,MAAM,CAAC,CAACxV,OAAO,CAAC,CAAC,CAAChF,GAAG,EAAE9D,KAAK,CAAC,KAAK2e,GAAG,CAAC3e,KAAK,EAAE8D,GAAG,CAAC,CAAC;AACnE;AACA,SAASmL,QAAQA,CAAC4P,GAAG,EAAE7e,KAAK,EAAE;EAC5B,OAAO6e,GAAG,CAACvF,OAAO,CAACtZ,KAAK,CAAC,KAAK,CAAC,CAAC;AAClC;AACA,SAAS8e,OAAOA,CAACR,MAAM,EAAEG,SAAS,EAAE;EAClC,KAAK,IAAIxY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqY,MAAM,CAACphB,MAAM,EAAE+I,CAAC,EAAE,EAAE;IACtC,MAAMjG,KAAK,GAAGse,MAAM,CAACrY,CAAC,CAAC;IACvB,IAAIwY,SAAS,CAACze,KAAK,CAAC,EAAE;MACpB,OAAOA,KAAK;IACd;EACF;EACA,OAAO,KAAK,CAAC;AACf;;AAEA;AACA,IAAI+e,yBAAyB,GAAG,MAAM;EACpC1B,WAAWA,CAAA,EAAG;IACZ,IAAI,CAAC2B,WAAW,GAAG,CAAC,CAAC;EACvB;EACAnB,QAAQA,CAACoB,WAAW,EAAE;IACpB,IAAI,CAACD,WAAW,CAACC,WAAW,CAAC9O,IAAI,CAAC,GAAG8O,WAAW;EAClD;EACAC,cAAcA,CAACjd,CAAC,EAAE;IAChB,OAAOuX,IAAI,CAAC,IAAI,CAACwF,WAAW,EAAGC,WAAW,IAAKA,WAAW,CAACE,YAAY,CAACld,CAAC,CAAC,CAAC;EAC7E;EACAmd,UAAUA,CAACjP,IAAI,EAAE;IACf,OAAO,IAAI,CAAC6O,WAAW,CAAC7O,IAAI,CAAC;EAC/B;AACF,CAAC;;AAED;AACA,IAAIkP,OAAO,GAAIC,OAAO,IAAKpf,MAAM,CAACqf,SAAS,CAACvE,QAAQ,CAAC1E,IAAI,CAACgJ,OAAO,CAAC,CAACjV,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC/E,IAAImV,WAAW,GAAIF,OAAO,IAAK,OAAOA,OAAO,KAAK,WAAW;AAC7D,IAAIG,MAAM,GAAIH,OAAO,IAAKA,OAAO,KAAK,IAAI;AAC1C,IAAII,aAAa,GAAIJ,OAAO,IAAK;EAC/B,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,IAAI,EACjD,OAAO,KAAK;EACd,IAAIA,OAAO,KAAKpf,MAAM,CAACqf,SAAS,EAC9B,OAAO,KAAK;EACd,IAAIrf,MAAM,CAACyf,cAAc,CAACL,OAAO,CAAC,KAAK,IAAI,EACzC,OAAO,IAAI;EACb,OAAOpf,MAAM,CAACyf,cAAc,CAACL,OAAO,CAAC,KAAKpf,MAAM,CAACqf,SAAS;AAC5D,CAAC;AACD,IAAIK,aAAa,GAAIN,OAAO,IAAKI,aAAa,CAACJ,OAAO,CAAC,IAAIpf,MAAM,CAACmE,IAAI,CAACib,OAAO,CAAC,CAACpiB,MAAM,KAAK,CAAC;AAC5F,IAAI0I,OAAO,GAAI0Z,OAAO,IAAK3Z,KAAK,CAACC,OAAO,CAAC0Z,OAAO,CAAC;AACjD,IAAIO,QAAQ,GAAIP,OAAO,IAAK,OAAOA,OAAO,KAAK,QAAQ;AACvD,IAAIQ,QAAQ,GAAIR,OAAO,IAAK,OAAOA,OAAO,KAAK,QAAQ,IAAI,CAACS,KAAK,CAACT,OAAO,CAAC;AAC1E,IAAIU,SAAS,GAAIV,OAAO,IAAK,OAAOA,OAAO,KAAK,SAAS;AACzD,IAAIW,QAAQ,GAAIX,OAAO,IAAKA,OAAO,YAAYY,MAAM;AACrD,IAAIC,KAAK,GAAIb,OAAO,IAAKA,OAAO,YAAY3S,GAAG;AAC/C,IAAIyT,KAAK,GAAId,OAAO,IAAKA,OAAO,YAAYvc,GAAG;AAC/C,IAAIsd,QAAQ,GAAIf,OAAO,IAAKD,OAAO,CAACC,OAAO,CAAC,KAAK,QAAQ;AACzD,IAAIgB,MAAM,GAAIhB,OAAO,IAAKA,OAAO,YAAYiB,IAAI,IAAI,CAACR,KAAK,CAACT,OAAO,CAACkB,OAAO,CAAC,CAAC,CAAC;AAC9E,IAAIC,OAAO,GAAInB,OAAO,IAAKA,OAAO,YAAY7W,KAAK;AACnD,IAAIiY,UAAU,GAAIpB,OAAO,IAAK,OAAOA,OAAO,KAAK,QAAQ,IAAIS,KAAK,CAACT,OAAO,CAAC;AAC3E,IAAIqB,WAAW,GAAIrB,OAAO,IAAKU,SAAS,CAACV,OAAO,CAAC,IAAIG,MAAM,CAACH,OAAO,CAAC,IAAIE,WAAW,CAACF,OAAO,CAAC,IAAIQ,QAAQ,CAACR,OAAO,CAAC,IAAIO,QAAQ,CAACP,OAAO,CAAC,IAAIe,QAAQ,CAACf,OAAO,CAAC;AAC3J,IAAIsB,QAAQ,GAAItB,OAAO,IAAK,OAAOA,OAAO,KAAK,QAAQ;AACvD,IAAIuB,UAAU,GAAIvB,OAAO,IAAKA,OAAO,KAAKwB,QAAQ,IAAIxB,OAAO,KAAK,CAACwB,QAAQ;AAC3E,IAAIC,YAAY,GAAIzB,OAAO,IAAK0B,WAAW,CAACC,MAAM,CAAC3B,OAAO,CAAC,IAAI,EAAEA,OAAO,YAAY4B,QAAQ,CAAC;AAC7F,IAAIC,KAAK,GAAI7B,OAAO,IAAKA,OAAO,YAAY8B,GAAG;;AAE/C;AACA,IAAIC,SAAS,GAAIvd,GAAG,IAAKA,GAAG,CAACwU,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;AAClD,IAAIgJ,aAAa,GAAI5G,IAAI,IAAKA,IAAI,CAAC3L,GAAG,CAAC/R,MAAM,CAAC,CAAC+R,GAAG,CAACsS,SAAS,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC;AACvE,IAAIC,SAAS,GAAIC,MAAM,IAAK;EAC1B,MAAMvb,MAAM,GAAG,EAAE;EACjB,IAAIwb,OAAO,GAAG,EAAE;EAChB,KAAK,IAAIzb,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwb,MAAM,CAACvkB,MAAM,EAAE+I,CAAC,EAAE,EAAE;IACtC,IAAI0b,IAAI,GAAGF,MAAM,CAACG,MAAM,CAAC3b,CAAC,CAAC;IAC3B,MAAM4b,YAAY,GAAGF,IAAI,KAAK,IAAI,IAAIF,MAAM,CAACG,MAAM,CAAC3b,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG;IAClE,IAAI4b,YAAY,EAAE;MAChBH,OAAO,IAAI,GAAG;MACdzb,CAAC,EAAE;MACH;IACF;IACA,MAAM6b,cAAc,GAAGH,IAAI,KAAK,GAAG;IACnC,IAAIG,cAAc,EAAE;MAClB5b,MAAM,CAAC9E,IAAI,CAACsgB,OAAO,CAAC;MACpBA,OAAO,GAAG,EAAE;MACZ;IACF;IACAA,OAAO,IAAIC,IAAI;EACjB;EACA,MAAMI,WAAW,GAAGL,OAAO;EAC3Bxb,MAAM,CAAC9E,IAAI,CAAC2gB,WAAW,CAAC;EACxB,OAAO7b,MAAM;AACf,CAAC;;AAED;AACA,SAAS8b,oBAAoBA,CAAC7C,YAAY,EAAE8C,UAAU,EAAEC,SAAS,EAAEC,WAAW,EAAE;EAC9E,OAAO;IACLhD,YAAY;IACZ8C,UAAU;IACVC,SAAS;IACTC;EACF,CAAC;AACH;AACA,IAAIC,WAAW,GAAG,CAChBJ,oBAAoB,CAACxC,WAAW,EAAE,WAAW,EAAE,MAAM,IAAI,EAAE,MAAM,KAAK,CAAC,CAAC,EACxEwC,oBAAoB,CAACpB,QAAQ,EAAE,QAAQ,EAAG3e,CAAC,IAAKA,CAAC,CAAC+Y,QAAQ,CAAC,CAAC,EAAG/Y,CAAC,IAAK;EACnE,IAAI,OAAOogB,MAAM,KAAK,WAAW,EAAE;IACjC,OAAOA,MAAM,CAACpgB,CAAC,CAAC;EAClB;EACAqgB,OAAO,CAACnf,KAAK,CAAC,+BAA+B,CAAC;EAC9C,OAAOlB,CAAC;AACV,CAAC,CAAC,EACF+f,oBAAoB,CAAC1B,MAAM,EAAE,MAAM,EAAGre,CAAC,IAAKA,CAAC,CAACsgB,WAAW,CAAC,CAAC,EAAGtgB,CAAC,IAAK,IAAIse,IAAI,CAACte,CAAC,CAAC,CAAC,EAChF+f,oBAAoB,CAACvB,OAAO,EAAE,OAAO,EAAE,CAACxe,CAAC,EAAEugB,SAAS,KAAK;EACvD,MAAMC,SAAS,GAAG;IAChBtS,IAAI,EAAElO,CAAC,CAACkO,IAAI;IACZuS,OAAO,EAAEzgB,CAAC,CAACygB;EACb,CAAC;EACDF,SAAS,CAACG,iBAAiB,CAAC7Z,OAAO,CAAE+J,IAAI,IAAK;IAC5C4P,SAAS,CAAC5P,IAAI,CAAC,GAAG5Q,CAAC,CAAC4Q,IAAI,CAAC;EAC3B,CAAC,CAAC;EACF,OAAO4P,SAAS;AAClB,CAAC,EAAE,CAACxgB,CAAC,EAAEugB,SAAS,KAAK;EACnB,MAAMxd,CAAC,GAAG,IAAIyD,KAAK,CAACxG,CAAC,CAACygB,OAAO,CAAC;EAC9B1d,CAAC,CAACmL,IAAI,GAAGlO,CAAC,CAACkO,IAAI;EACfnL,CAAC,CAAC4d,KAAK,GAAG3gB,CAAC,CAAC2gB,KAAK;EACjBJ,SAAS,CAACG,iBAAiB,CAAC7Z,OAAO,CAAE+J,IAAI,IAAK;IAC5C7N,CAAC,CAAC6N,IAAI,CAAC,GAAG5Q,CAAC,CAAC4Q,IAAI,CAAC;EACnB,CAAC,CAAC;EACF,OAAO7N,CAAC;AACV,CAAC,CAAC,EACFgd,oBAAoB,CAAC/B,QAAQ,EAAE,QAAQ,EAAGhe,CAAC,IAAK,EAAE,GAAGA,CAAC,EAAG4gB,KAAK,IAAK;EACjE,MAAM1G,IAAI,GAAG0G,KAAK,CAACxY,KAAK,CAAC,CAAC,EAAEwY,KAAK,CAACC,WAAW,CAAC,GAAG,CAAC,CAAC;EACnD,MAAMC,KAAK,GAAGF,KAAK,CAACxY,KAAK,CAACwY,KAAK,CAACC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACrD,OAAO,IAAI5C,MAAM,CAAC/D,IAAI,EAAE4G,KAAK,CAAC;AAChC,CAAC,CAAC,EACFf,oBAAoB,CAClB5B,KAAK,EACL,KAAK;AACL;AACA;AACCne,CAAC,IAAK,CAAC,GAAGA,CAAC,CAACsc,MAAM,CAAC,CAAC,CAAC,EACrBtc,CAAC,IAAK,IAAIc,GAAG,CAACd,CAAC,CAClB,CAAC,EACD+f,oBAAoB,CAAC7B,KAAK,EAAE,KAAK,EAAGle,CAAC,IAAK,CAAC,GAAGA,CAAC,CAAC2c,OAAO,CAAC,CAAC,CAAC,EAAG3c,CAAC,IAAK,IAAI0K,GAAG,CAAC1K,CAAC,CAAC,CAAC,EAC9E+f,oBAAoB,CAAE/f,CAAC,IAAKye,UAAU,CAACze,CAAC,CAAC,IAAI4e,UAAU,CAAC5e,CAAC,CAAC,EAAE,QAAQ,EAAGA,CAAC,IAAK;EAC3E,IAAIye,UAAU,CAACze,CAAC,CAAC,EAAE;IACjB,OAAO,KAAK;EACd;EACA,IAAIA,CAAC,GAAG,CAAC,EAAE;IACT,OAAO,UAAU;EACnB,CAAC,MAAM;IACL,OAAO,WAAW;EACpB;AACF,CAAC,EAAE+gB,MAAM,CAAC,EACVhB,oBAAoB,CAAE/f,CAAC,IAAKA,CAAC,KAAK,CAAC,IAAI,CAAC,GAAGA,CAAC,KAAK,CAAC6e,QAAQ,EAAE,QAAQ,EAAE,MAAM;EAC1E,OAAO,IAAI;AACb,CAAC,EAAEkC,MAAM,CAAC,EACVhB,oBAAoB,CAACb,KAAK,EAAE,KAAK,EAAGlf,CAAC,IAAKA,CAAC,CAAC+Y,QAAQ,CAAC,CAAC,EAAG/Y,CAAC,IAAK,IAAImf,GAAG,CAACnf,CAAC,CAAC,CAAC,CAC3E;AACD,SAASghB,uBAAuBA,CAAC9D,YAAY,EAAE8C,UAAU,EAAEC,SAAS,EAAEC,WAAW,EAAE;EACjF,OAAO;IACLhD,YAAY;IACZ8C,UAAU;IACVC,SAAS;IACTC;EACF,CAAC;AACH;AACA,IAAIe,UAAU,GAAGD,uBAAuB,CAAC,CAAC7iB,CAAC,EAAEoiB,SAAS,KAAK;EACzD,IAAInC,QAAQ,CAACjgB,CAAC,CAAC,EAAE;IACf,MAAM+iB,YAAY,GAAG,CAAC,CAACX,SAAS,CAACY,cAAc,CAACrF,aAAa,CAAC3d,CAAC,CAAC;IAChE,OAAO+iB,YAAY;EACrB;EACA,OAAO,KAAK;AACd,CAAC,EAAE,CAAC/iB,CAAC,EAAEoiB,SAAS,KAAK;EACnB,MAAM1E,UAAU,GAAG0E,SAAS,CAACY,cAAc,CAACrF,aAAa,CAAC3d,CAAC,CAAC;EAC5D,OAAO,CAAC,QAAQ,EAAE0d,UAAU,CAAC;AAC/B,CAAC,EAAG7b,CAAC,IAAKA,CAAC,CAACohB,WAAW,EAAE,CAAChW,CAAC,EAAE5P,CAAC,EAAE+kB,SAAS,KAAK;EAC5C,MAAMxiB,KAAK,GAAGwiB,SAAS,CAACY,cAAc,CAACpF,QAAQ,CAACvgB,CAAC,CAAC,CAAC,CAAC,CAAC;EACrD,IAAI,CAACuC,KAAK,EAAE;IACV,MAAM,IAAIyI,KAAK,CAAC,sCAAsC,CAAC;EACzD;EACA,OAAOzI,KAAK;AACd,CAAC,CAAC;AACF,IAAIsjB,iBAAiB,GAAG,CACtBC,SAAS,EACTC,UAAU,EACVC,UAAU,EACVC,WAAW,EACXC,UAAU,EACVC,WAAW,EACXC,YAAY,EACZC,YAAY,EACZC,iBAAiB,CAClB,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAK;EACtBD,GAAG,CAACC,IAAI,CAAC/T,IAAI,CAAC,GAAG+T,IAAI;EACrB,OAAOD,GAAG;AACZ,CAAC,EAAE,CAAC,CAAC,CAAC;AACN,IAAIE,cAAc,GAAGlB,uBAAuB,CAAClC,YAAY,EAAG9e,CAAC,IAAK,CAAC,aAAa,EAAEA,CAAC,CAACob,WAAW,CAAClN,IAAI,CAAC,EAAGlO,CAAC,IAAK,CAAC,GAAGA,CAAC,CAAC,EAAE,CAACA,CAAC,EAAExE,CAAC,KAAK;EAC9H,MAAMymB,IAAI,GAAGZ,iBAAiB,CAAC7lB,CAAC,CAAC,CAAC,CAAC,CAAC;EACpC,IAAI,CAACymB,IAAI,EAAE;IACT,MAAM,IAAIzb,KAAK,CAAC,2CAA2C,CAAC;EAC9D;EACA,OAAO,IAAIyb,IAAI,CAACjiB,CAAC,CAAC;AACpB,CAAC,CAAC;AACF,SAASmiB,2BAA2BA,CAACC,cAAc,EAAE7B,SAAS,EAAE;EAC9D,IAAI6B,cAAc,EAAEhH,WAAW,EAAE;IAC/B,MAAM8F,YAAY,GAAG,CAAC,CAACX,SAAS,CAAC8B,aAAa,CAACvG,aAAa,CAACsG,cAAc,CAAChH,WAAW,CAAC;IACxF,OAAO8F,YAAY;EACrB;EACA,OAAO,KAAK;AACd;AACA,IAAIoB,SAAS,GAAGtB,uBAAuB,CAACmB,2BAA2B,EAAE,CAACI,KAAK,EAAEhC,SAAS,KAAK;EACzF,MAAM1E,UAAU,GAAG0E,SAAS,CAAC8B,aAAa,CAACvG,aAAa,CAACyG,KAAK,CAACnH,WAAW,CAAC;EAC3E,OAAO,CAAC,OAAO,EAAES,UAAU,CAAC;AAC9B,CAAC,EAAE,CAAC0G,KAAK,EAAEhC,SAAS,KAAK;EACvB,MAAMiC,YAAY,GAAGjC,SAAS,CAAC8B,aAAa,CAAClG,eAAe,CAACoG,KAAK,CAACnH,WAAW,CAAC;EAC/E,IAAI,CAACoH,YAAY,EAAE;IACjB,OAAO;MAAE,GAAGD;IAAM,CAAC;EACrB;EACA,MAAMte,MAAM,GAAG,CAAC,CAAC;EACjBue,YAAY,CAAC3b,OAAO,CAAE+J,IAAI,IAAK;IAC7B3M,MAAM,CAAC2M,IAAI,CAAC,GAAG2R,KAAK,CAAC3R,IAAI,CAAC;EAC5B,CAAC,CAAC;EACF,OAAO3M,MAAM;AACf,CAAC,EAAE,CAACjE,CAAC,EAAExE,CAAC,EAAE+kB,SAAS,KAAK;EACtB,MAAMgC,KAAK,GAAGhC,SAAS,CAAC8B,aAAa,CAACtG,QAAQ,CAACvgB,CAAC,CAAC,CAAC,CAAC,CAAC;EACpD,IAAI,CAAC+mB,KAAK,EAAE;IACV,MAAM,IAAI/b,KAAK,CAAC,qHAAqH,CAAC;EACxI;EACA,OAAOvI,MAAM,CAACC,MAAM,CAACD,MAAM,CAACiO,MAAM,CAACqW,KAAK,CAACjF,SAAS,CAAC,EAAEtd,CAAC,CAAC;AACzD,CAAC,CAAC;AACF,IAAIyiB,UAAU,GAAGzB,uBAAuB,CAAC,CAACjjB,KAAK,EAAEwiB,SAAS,KAAK;EAC7D,OAAO,CAAC,CAACA,SAAS,CAACmC,yBAAyB,CAACzF,cAAc,CAAClf,KAAK,CAAC;AACpE,CAAC,EAAE,CAACA,KAAK,EAAEwiB,SAAS,KAAK;EACvB,MAAMvD,WAAW,GAAGuD,SAAS,CAACmC,yBAAyB,CAACzF,cAAc,CAAClf,KAAK,CAAC;EAC7E,OAAO,CAAC,QAAQ,EAAEif,WAAW,CAAC9O,IAAI,CAAC;AACrC,CAAC,EAAE,CAACnQ,KAAK,EAAEwiB,SAAS,KAAK;EACvB,MAAMvD,WAAW,GAAGuD,SAAS,CAACmC,yBAAyB,CAACzF,cAAc,CAAClf,KAAK,CAAC;EAC7E,OAAOif,WAAW,CAAC2F,SAAS,CAAC5kB,KAAK,CAAC;AACrC,CAAC,EAAE,CAACiC,CAAC,EAAExE,CAAC,EAAE+kB,SAAS,KAAK;EACtB,MAAMvD,WAAW,GAAGuD,SAAS,CAACmC,yBAAyB,CAACvF,UAAU,CAAC3hB,CAAC,CAAC,CAAC,CAAC,CAAC;EACxE,IAAI,CAACwhB,WAAW,EAAE;IAChB,MAAM,IAAIxW,KAAK,CAAC,4CAA4C,CAAC;EAC/D;EACA,OAAOwW,WAAW,CAAC4F,WAAW,CAAC5iB,CAAC,CAAC;AACnC,CAAC,CAAC;AACF,IAAI6iB,cAAc,GAAG,CAACP,SAAS,EAAErB,UAAU,EAAEwB,UAAU,EAAEP,cAAc,CAAC;AACxE,IAAIY,cAAc,GAAGA,CAAC/kB,KAAK,EAAEwiB,SAAS,KAAK;EACzC,MAAMwC,uBAAuB,GAAGlG,OAAO,CAACgG,cAAc,EAAGG,IAAI,IAAKA,IAAI,CAAC9F,YAAY,CAACnf,KAAK,EAAEwiB,SAAS,CAAC,CAAC;EACtG,IAAIwC,uBAAuB,EAAE;IAC3B,OAAO;MACLhlB,KAAK,EAAEglB,uBAAuB,CAAC9C,SAAS,CAACliB,KAAK,EAAEwiB,SAAS,CAAC;MAC1D7I,IAAI,EAAEqL,uBAAuB,CAAC/C,UAAU,CAACjiB,KAAK,EAAEwiB,SAAS;IAC3D,CAAC;EACH;EACA,MAAM0C,oBAAoB,GAAGpG,OAAO,CAACsD,WAAW,EAAG6C,IAAI,IAAKA,IAAI,CAAC9F,YAAY,CAACnf,KAAK,EAAEwiB,SAAS,CAAC,CAAC;EAChG,IAAI0C,oBAAoB,EAAE;IACxB,OAAO;MACLllB,KAAK,EAAEklB,oBAAoB,CAAChD,SAAS,CAACliB,KAAK,EAAEwiB,SAAS,CAAC;MACvD7I,IAAI,EAAEuL,oBAAoB,CAACjD;IAC7B,CAAC;EACH;EACA,OAAO,KAAK,CAAC;AACf,CAAC;AACD,IAAIkD,uBAAuB,GAAG,CAAC,CAAC;AAChC/C,WAAW,CAACtZ,OAAO,CAAEmc,IAAI,IAAK;EAC5BE,uBAAuB,CAACF,IAAI,CAAChD,UAAU,CAAC,GAAGgD,IAAI;AACjD,CAAC,CAAC;AACF,IAAIG,gBAAgB,GAAGA,CAACC,IAAI,EAAE1L,IAAI,EAAE6I,SAAS,KAAK;EAChD,IAAI5c,OAAO,CAAC+T,IAAI,CAAC,EAAE;IACjB,QAAQA,IAAI,CAAC,CAAC,CAAC;MACb,KAAK,QAAQ;QACX,OAAOuJ,UAAU,CAACf,WAAW,CAACkD,IAAI,EAAE1L,IAAI,EAAE6I,SAAS,CAAC;MACtD,KAAK,OAAO;QACV,OAAO+B,SAAS,CAACpC,WAAW,CAACkD,IAAI,EAAE1L,IAAI,EAAE6I,SAAS,CAAC;MACrD,KAAK,QAAQ;QACX,OAAOkC,UAAU,CAACvC,WAAW,CAACkD,IAAI,EAAE1L,IAAI,EAAE6I,SAAS,CAAC;MACtD,KAAK,aAAa;QAChB,OAAO2B,cAAc,CAAChC,WAAW,CAACkD,IAAI,EAAE1L,IAAI,EAAE6I,SAAS,CAAC;MAC1D;QACE,MAAM,IAAI/Z,KAAK,CAAC,0BAA0B,GAAGkR,IAAI,CAAC;IACtD;EACF,CAAC,MAAM;IACL,MAAM2L,cAAc,GAAGH,uBAAuB,CAACxL,IAAI,CAAC;IACpD,IAAI,CAAC2L,cAAc,EAAE;MACnB,MAAM,IAAI7c,KAAK,CAAC,0BAA0B,GAAGkR,IAAI,CAAC;IACpD;IACA,OAAO2L,cAAc,CAACnD,WAAW,CAACkD,IAAI,EAAE7C,SAAS,CAAC;EACpD;AACF,CAAC;;AAED;AACA,IAAI+C,SAAS,GAAGA,CAACvlB,KAAK,EAAE0K,CAAC,KAAK;EAC5B,MAAMrG,IAAI,GAAGrE,KAAK,CAACqE,IAAI,CAAC,CAAC;EACzB,OAAOqG,CAAC,GAAG,CAAC,EAAE;IACZrG,IAAI,CAACmhB,IAAI,CAAC,CAAC;IACX9a,CAAC,EAAE;EACL;EACA,OAAOrG,IAAI,CAACmhB,IAAI,CAAC,CAAC,CAACxlB,KAAK;AAC1B,CAAC;AACD,SAASylB,YAAYA,CAAC/K,IAAI,EAAE;EAC1B,IAAIzL,QAAQ,CAACyL,IAAI,EAAE,WAAW,CAAC,EAAE;IAC/B,MAAM,IAAIjS,KAAK,CAAC,wCAAwC,CAAC;EAC3D;EACA,IAAIwG,QAAQ,CAACyL,IAAI,EAAE,WAAW,CAAC,EAAE;IAC/B,MAAM,IAAIjS,KAAK,CAAC,wCAAwC,CAAC;EAC3D;EACA,IAAIwG,QAAQ,CAACyL,IAAI,EAAE,aAAa,CAAC,EAAE;IACjC,MAAM,IAAIjS,KAAK,CAAC,0CAA0C,CAAC;EAC7D;AACF;AACA,IAAIid,OAAO,GAAGA,CAACC,MAAM,EAAEjL,IAAI,KAAK;EAC9B+K,YAAY,CAAC/K,IAAI,CAAC;EAClB,KAAK,IAAIzU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyU,IAAI,CAACxd,MAAM,EAAE+I,CAAC,EAAE,EAAE;IACpC,MAAMnC,GAAG,GAAG4W,IAAI,CAACzU,CAAC,CAAC;IACnB,IAAIma,KAAK,CAACuF,MAAM,CAAC,EAAE;MACjBA,MAAM,GAAGJ,SAAS,CAACI,MAAM,EAAE,CAAC7hB,GAAG,CAAC;IAClC,CAAC,MAAM,IAAIqc,KAAK,CAACwF,MAAM,CAAC,EAAE;MACxB,MAAMC,GAAG,GAAG,CAAC9hB,GAAG;MAChB,MAAM6V,IAAI,GAAG,CAACe,IAAI,CAAC,EAAEzU,CAAC,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,OAAO;MAC/C,MAAM4f,QAAQ,GAAGN,SAAS,CAACI,MAAM,EAAEC,GAAG,CAAC;MACvC,QAAQjM,IAAI;QACV,KAAK,KAAK;UACRgM,MAAM,GAAGE,QAAQ;UACjB;QACF,KAAK,OAAO;UACVF,MAAM,GAAGA,MAAM,CAACzgB,GAAG,CAAC2gB,QAAQ,CAAC;UAC7B;MACJ;IACF,CAAC,MAAM;MACLF,MAAM,GAAGA,MAAM,CAAC7hB,GAAG,CAAC;IACtB;EACF;EACA,OAAO6hB,MAAM;AACf,CAAC;AACD,IAAIG,OAAO,GAAGA,CAACH,MAAM,EAAEjL,IAAI,EAAElO,MAAM,KAAK;EACtCiZ,YAAY,CAAC/K,IAAI,CAAC;EAClB,IAAIA,IAAI,CAACxd,MAAM,KAAK,CAAC,EAAE;IACrB,OAAOsP,MAAM,CAACmZ,MAAM,CAAC;EACvB;EACA,IAAInO,MAAM,GAAGmO,MAAM;EACnB,KAAK,IAAI1f,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyU,IAAI,CAACxd,MAAM,GAAG,CAAC,EAAE+I,CAAC,EAAE,EAAE;IACxC,MAAMnC,GAAG,GAAG4W,IAAI,CAACzU,CAAC,CAAC;IACnB,IAAIL,OAAO,CAAC4R,MAAM,CAAC,EAAE;MACnB,MAAMhN,KAAK,GAAG,CAAC1G,GAAG;MAClB0T,MAAM,GAAGA,MAAM,CAAChN,KAAK,CAAC;IACxB,CAAC,MAAM,IAAIkV,aAAa,CAAClI,MAAM,CAAC,EAAE;MAChCA,MAAM,GAAGA,MAAM,CAAC1T,GAAG,CAAC;IACtB,CAAC,MAAM,IAAIsc,KAAK,CAAC5I,MAAM,CAAC,EAAE;MACxB,MAAMoO,GAAG,GAAG,CAAC9hB,GAAG;MAChB0T,MAAM,GAAG+N,SAAS,CAAC/N,MAAM,EAAEoO,GAAG,CAAC;IACjC,CAAC,MAAM,IAAIzF,KAAK,CAAC3I,MAAM,CAAC,EAAE;MACxB,MAAMuO,KAAK,GAAG9f,CAAC,KAAKyU,IAAI,CAACxd,MAAM,GAAG,CAAC;MACnC,IAAI6oB,KAAK,EAAE;QACT;MACF;MACA,MAAMH,GAAG,GAAG,CAAC9hB,GAAG;MAChB,MAAM6V,IAAI,GAAG,CAACe,IAAI,CAAC,EAAEzU,CAAC,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,OAAO;MAC/C,MAAM4f,QAAQ,GAAGN,SAAS,CAAC/N,MAAM,EAAEoO,GAAG,CAAC;MACvC,QAAQjM,IAAI;QACV,KAAK,KAAK;UACRnC,MAAM,GAAGqO,QAAQ;UACjB;QACF,KAAK,OAAO;UACVrO,MAAM,GAAGA,MAAM,CAACtS,GAAG,CAAC2gB,QAAQ,CAAC;UAC7B;MACJ;IACF;EACF;EACA,MAAMG,OAAO,GAAGtL,IAAI,CAACA,IAAI,CAACxd,MAAM,GAAG,CAAC,CAAC;EACrC,IAAI0I,OAAO,CAAC4R,MAAM,CAAC,EAAE;IACnBA,MAAM,CAAC,CAACwO,OAAO,CAAC,GAAGxZ,MAAM,CAACgL,MAAM,CAAC,CAACwO,OAAO,CAAC,CAAC;EAC7C,CAAC,MAAM,IAAItG,aAAa,CAAClI,MAAM,CAAC,EAAE;IAChCA,MAAM,CAACwO,OAAO,CAAC,GAAGxZ,MAAM,CAACgL,MAAM,CAACwO,OAAO,CAAC,CAAC;EAC3C;EACA,IAAI5F,KAAK,CAAC5I,MAAM,CAAC,EAAE;IACjB,MAAMyO,QAAQ,GAAGV,SAAS,CAAC/N,MAAM,EAAE,CAACwO,OAAO,CAAC;IAC5C,MAAME,QAAQ,GAAG1Z,MAAM,CAACyZ,QAAQ,CAAC;IACjC,IAAIA,QAAQ,KAAKC,QAAQ,EAAE;MACzB1O,MAAM,CAACtT,MAAM,CAAC+hB,QAAQ,CAAC;MACvBzO,MAAM,CAAC/S,GAAG,CAACyhB,QAAQ,CAAC;IACtB;EACF;EACA,IAAI/F,KAAK,CAAC3I,MAAM,CAAC,EAAE;IACjB,MAAMoO,GAAG,GAAG,CAAClL,IAAI,CAACA,IAAI,CAACxd,MAAM,GAAG,CAAC,CAAC;IAClC,MAAMipB,QAAQ,GAAGZ,SAAS,CAAC/N,MAAM,EAAEoO,GAAG,CAAC;IACvC,MAAMjM,IAAI,GAAG,CAACqM,OAAO,KAAK,CAAC,GAAG,KAAK,GAAG,OAAO;IAC7C,QAAQrM,IAAI;MACV,KAAK,KAAK;QAAE;UACV,MAAMyM,MAAM,GAAG5Z,MAAM,CAAC2Z,QAAQ,CAAC;UAC/B3O,MAAM,CAAC5K,GAAG,CAACwZ,MAAM,EAAE5O,MAAM,CAACtS,GAAG,CAACihB,QAAQ,CAAC,CAAC;UACxC,IAAIC,MAAM,KAAKD,QAAQ,EAAE;YACvB3O,MAAM,CAACtT,MAAM,CAACiiB,QAAQ,CAAC;UACzB;UACA;QACF;MACA,KAAK,OAAO;QAAE;UACZ3O,MAAM,CAAC5K,GAAG,CAACuZ,QAAQ,EAAE3Z,MAAM,CAACgL,MAAM,CAACtS,GAAG,CAACihB,QAAQ,CAAC,CAAC,CAAC;UAClD;QACF;IACF;EACF;EACA,OAAOR,MAAM;AACf,CAAC;;AAED;AACA,SAASU,QAAQA,CAACC,IAAI,EAAEC,OAAO,EAAEC,MAAM,GAAG,EAAE,EAAE;EAC5C,IAAI,CAACF,IAAI,EAAE;IACT;EACF;EACA,IAAI,CAAC1gB,OAAO,CAAC0gB,IAAI,CAAC,EAAE;IAClBxd,OAAO,CAACwd,IAAI,EAAE,CAACG,OAAO,EAAE3iB,GAAG,KAAKuiB,QAAQ,CAACI,OAAO,EAAEF,OAAO,EAAE,CAAC,GAAGC,MAAM,EAAE,GAAGhF,SAAS,CAAC1d,GAAG,CAAC,CAAC,CAAC,CAAC;IAC3F;EACF;EACA,MAAM,CAAC4iB,SAAS,EAAE/e,SAAS,CAAC,GAAG2e,IAAI;EACnC,IAAI3e,SAAS,EAAE;IACbmB,OAAO,CAACnB,SAAS,EAAE,CAACiJ,KAAK,EAAE9M,GAAG,KAAK;MACjCuiB,QAAQ,CAACzV,KAAK,EAAE2V,OAAO,EAAE,CAAC,GAAGC,MAAM,EAAE,GAAGhF,SAAS,CAAC1d,GAAG,CAAC,CAAC,CAAC;IAC1D,CAAC,CAAC;EACJ;EACAyiB,OAAO,CAACG,SAAS,EAAEF,MAAM,CAAC;AAC5B;AACA,SAASG,qBAAqBA,CAACC,KAAK,EAAEC,WAAW,EAAErE,SAAS,EAAE;EAC5D6D,QAAQ,CAACQ,WAAW,EAAE,CAAClN,IAAI,EAAEe,IAAI,KAAK;IACpCkM,KAAK,GAAGd,OAAO,CAACc,KAAK,EAAElM,IAAI,EAAGzY,CAAC,IAAKmjB,gBAAgB,CAACnjB,CAAC,EAAE0X,IAAI,EAAE6I,SAAS,CAAC,CAAC;EAC3E,CAAC,CAAC;EACF,OAAOoE,KAAK;AACd;AACA,SAASE,mCAAmCA,CAACF,KAAK,EAAEC,WAAW,EAAE;EAC/D,SAAS9c,KAAKA,CAACgd,cAAc,EAAErM,IAAI,EAAE;IACnC,MAAMiL,MAAM,GAAGD,OAAO,CAACkB,KAAK,EAAEpF,SAAS,CAAC9G,IAAI,CAAC,CAAC;IAC9CqM,cAAc,CAAChY,GAAG,CAACyS,SAAS,CAAC,CAAC1Y,OAAO,CAAEke,mBAAmB,IAAK;MAC7DJ,KAAK,GAAGd,OAAO,CAACc,KAAK,EAAEI,mBAAmB,EAAE,MAAMrB,MAAM,CAAC;IAC3D,CAAC,CAAC;EACJ;EACA,IAAI/f,OAAO,CAACihB,WAAW,CAAC,EAAE;IACxB,MAAM,CAACnnB,IAAI,EAAEunB,KAAK,CAAC,GAAGJ,WAAW;IACjCnnB,IAAI,CAACoJ,OAAO,CAAEoe,aAAa,IAAK;MAC9BN,KAAK,GAAGd,OAAO,CAACc,KAAK,EAAEpF,SAAS,CAAC0F,aAAa,CAAC,EAAE,MAAMN,KAAK,CAAC;IAC/D,CAAC,CAAC;IACF,IAAIK,KAAK,EAAE;MACTne,OAAO,CAACme,KAAK,EAAEld,KAAK,CAAC;IACvB;EACF,CAAC,MAAM;IACLjB,OAAO,CAAC+d,WAAW,EAAE9c,KAAK,CAAC;EAC7B;EACA,OAAO6c,KAAK;AACd;AACA,IAAIO,MAAM,GAAGA,CAACxB,MAAM,EAAEnD,SAAS,KAAK9C,aAAa,CAACiG,MAAM,CAAC,IAAI/f,OAAO,CAAC+f,MAAM,CAAC,IAAIxF,KAAK,CAACwF,MAAM,CAAC,IAAIvF,KAAK,CAACuF,MAAM,CAAC,IAAIvB,2BAA2B,CAACuB,MAAM,EAAEnD,SAAS,CAAC;AAChK,SAAS4E,WAAWA,CAACzB,MAAM,EAAEjL,IAAI,EAAE2M,UAAU,EAAE;EAC7C,MAAMC,WAAW,GAAGD,UAAU,CAACniB,GAAG,CAACygB,MAAM,CAAC;EAC1C,IAAI2B,WAAW,EAAE;IACfA,WAAW,CAAClmB,IAAI,CAACsZ,IAAI,CAAC;EACxB,CAAC,MAAM;IACL2M,UAAU,CAACza,GAAG,CAAC+Y,MAAM,EAAE,CAACjL,IAAI,CAAC,CAAC;EAChC;AACF;AACA,SAAS6M,sCAAsCA,CAACC,WAAW,EAAEC,MAAM,EAAE;EACnE,MAAMvhB,MAAM,GAAG,CAAC,CAAC;EACjB,IAAIwhB,iBAAiB,GAAG,KAAK,CAAC;EAC9BF,WAAW,CAAC1e,OAAO,CAAE6e,KAAK,IAAK;IAC7B,IAAIA,KAAK,CAACzqB,MAAM,IAAI,CAAC,EAAE;MACrB;IACF;IACA,IAAI,CAACuqB,MAAM,EAAE;MACXE,KAAK,GAAGA,KAAK,CAAC5Y,GAAG,CAAE2L,IAAI,IAAKA,IAAI,CAAC3L,GAAG,CAAC/R,MAAM,CAAC,CAAC,CAAC4qB,IAAI,CAAC,CAACnqB,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACP,MAAM,GAAGQ,CAAC,CAACR,MAAM,CAAC;IACnF;IACA,MAAM,CAAC2qB,kBAAkB,EAAE,GAAGd,cAAc,CAAC,GAAGY,KAAK;IACrD,IAAIE,kBAAkB,CAAC3qB,MAAM,KAAK,CAAC,EAAE;MACnCwqB,iBAAiB,GAAGX,cAAc,CAAChY,GAAG,CAACuS,aAAa,CAAC;IACvD,CAAC,MAAM;MACLpb,MAAM,CAACob,aAAa,CAACuG,kBAAkB,CAAC,CAAC,GAAGd,cAAc,CAAChY,GAAG,CAACuS,aAAa,CAAC;IAC/E;EACF,CAAC,CAAC;EACF,IAAIoG,iBAAiB,EAAE;IACrB,IAAI9H,aAAa,CAAC1Z,MAAM,CAAC,EAAE;MACzB,OAAO,CAACwhB,iBAAiB,CAAC;IAC5B,CAAC,MAAM;MACL,OAAO,CAACA,iBAAiB,EAAExhB,MAAM,CAAC;IACpC;EACF,CAAC,MAAM;IACL,OAAO0Z,aAAa,CAAC1Z,MAAM,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM;EAChD;AACF;AACA,IAAI4hB,MAAM,GAAGA,CAACnC,MAAM,EAAE0B,UAAU,EAAE7E,SAAS,EAAEiF,MAAM,EAAE/M,IAAI,GAAG,EAAE,EAAEqN,iBAAiB,GAAG,EAAE,EAAEC,WAAW,GAAG,eAAgB,IAAIrb,GAAG,CAAC,CAAC,KAAK;EAClI,MAAMsb,SAAS,GAAGtH,WAAW,CAACgF,MAAM,CAAC;EACrC,IAAI,CAACsC,SAAS,EAAE;IACdb,WAAW,CAACzB,MAAM,EAAEjL,IAAI,EAAE2M,UAAU,CAAC;IACrC,MAAMa,IAAI,GAAGF,WAAW,CAAC9iB,GAAG,CAACygB,MAAM,CAAC;IACpC,IAAIuC,IAAI,EAAE;MACR,OAAOT,MAAM,GAAG;QACdU,gBAAgB,EAAE;MACpB,CAAC,GAAGD,IAAI;IACV;EACF;EACA,IAAI,CAACf,MAAM,CAACxB,MAAM,EAAEnD,SAAS,CAAC,EAAE;IAC9B,MAAM4F,YAAY,GAAGrD,cAAc,CAACY,MAAM,EAAEnD,SAAS,CAAC;IACtD,MAAM6F,OAAO,GAAGD,YAAY,GAAG;MAC7BD,gBAAgB,EAAEC,YAAY,CAACpoB,KAAK;MACpC6mB,WAAW,EAAE,CAACuB,YAAY,CAACzO,IAAI;IACjC,CAAC,GAAG;MACFwO,gBAAgB,EAAExC;IACpB,CAAC;IACD,IAAI,CAACsC,SAAS,EAAE;MACdD,WAAW,CAACpb,GAAG,CAAC+Y,MAAM,EAAE0C,OAAO,CAAC;IAClC;IACA,OAAOA,OAAO;EAChB;EACA,IAAIpZ,QAAQ,CAAC8Y,iBAAiB,EAAEpC,MAAM,CAAC,EAAE;IACvC,OAAO;MACLwC,gBAAgB,EAAE;IACpB,CAAC;EACH;EACA,MAAMG,oBAAoB,GAAGvD,cAAc,CAACY,MAAM,EAAEnD,SAAS,CAAC;EAC9D,MAAM+F,WAAW,GAAGD,oBAAoB,EAAEtoB,KAAK,IAAI2lB,MAAM;EACzD,MAAMwC,gBAAgB,GAAGviB,OAAO,CAAC2iB,WAAW,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;EACvD,MAAMC,gBAAgB,GAAG,CAAC,CAAC;EAC3B1f,OAAO,CAACyf,WAAW,EAAE,CAACvoB,KAAK,EAAEwK,KAAK,KAAK;IACrC,IAAIA,KAAK,KAAK,WAAW,IAAIA,KAAK,KAAK,aAAa,IAAIA,KAAK,KAAK,WAAW,EAAE;MAC7E,MAAM,IAAI/B,KAAK,CAAC,qBAAqB+B,KAAK,0EAA0E,CAAC;IACvH;IACA,MAAMie,eAAe,GAAGX,MAAM,CAAC9nB,KAAK,EAAEqnB,UAAU,EAAE7E,SAAS,EAAEiF,MAAM,EAAE,CAAC,GAAG/M,IAAI,EAAElQ,KAAK,CAAC,EAAE,CAAC,GAAGud,iBAAiB,EAAEpC,MAAM,CAAC,EAAEqC,WAAW,CAAC;IACnIG,gBAAgB,CAAC3d,KAAK,CAAC,GAAGie,eAAe,CAACN,gBAAgB;IAC1D,IAAIviB,OAAO,CAAC6iB,eAAe,CAAC5B,WAAW,CAAC,EAAE;MACxC2B,gBAAgB,CAAChe,KAAK,CAAC,GAAGie,eAAe,CAAC5B,WAAW;IACvD,CAAC,MAAM,IAAInH,aAAa,CAAC+I,eAAe,CAAC5B,WAAW,CAAC,EAAE;MACrD/d,OAAO,CAAC2f,eAAe,CAAC5B,WAAW,EAAE,CAACP,IAAI,EAAExiB,GAAG,KAAK;QAClD0kB,gBAAgB,CAACnH,SAAS,CAAC7W,KAAK,CAAC,GAAG,GAAG,GAAG1G,GAAG,CAAC,GAAGwiB,IAAI;MACvD,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,MAAMpgB,MAAM,GAAG0Z,aAAa,CAAC4I,gBAAgB,CAAC,GAAG;IAC/CL,gBAAgB;IAChBtB,WAAW,EAAE,CAAC,CAACyB,oBAAoB,GAAG,CAACA,oBAAoB,CAAC3O,IAAI,CAAC,GAAG,KAAK;EAC3E,CAAC,GAAG;IACFwO,gBAAgB;IAChBtB,WAAW,EAAE,CAAC,CAACyB,oBAAoB,GAAG,CAACA,oBAAoB,CAAC3O,IAAI,EAAE6O,gBAAgB,CAAC,GAAGA;EACxF,CAAC;EACD,IAAI,CAACP,SAAS,EAAE;IACdD,WAAW,CAACpb,GAAG,CAAC+Y,MAAM,EAAEzf,MAAM,CAAC;EACjC;EACA,OAAOA,MAAM;AACf,CAAC;;AAED;AACA,SAASwiB,QAAQA,CAACpJ,OAAO,EAAE;EACzB,OAAOpf,MAAM,CAACqf,SAAS,CAACvE,QAAQ,CAAC1E,IAAI,CAACgJ,OAAO,CAAC,CAACjV,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC7D;AACA,SAASse,QAAQA,CAACrJ,OAAO,EAAE;EACzB,OAAOoJ,QAAQ,CAACpJ,OAAO,CAAC,KAAK,OAAO;AACtC;AACA,SAASsJ,cAAcA,CAACtJ,OAAO,EAAE;EAC/B,IAAIoJ,QAAQ,CAACpJ,OAAO,CAAC,KAAK,QAAQ,EAChC,OAAO,KAAK;EACd,MAAMC,SAAS,GAAGrf,MAAM,CAACyf,cAAc,CAACL,OAAO,CAAC;EAChD,OAAO,CAAC,CAACC,SAAS,IAAIA,SAAS,CAAClC,WAAW,KAAKnd,MAAM,IAAIqf,SAAS,KAAKrf,MAAM,CAACqf,SAAS;AAC1F;;AAEA;AACA,SAASsJ,WAAWA,CAACC,KAAK,EAAEhlB,GAAG,EAAEilB,MAAM,EAAEC,cAAc,EAAEC,oBAAoB,EAAE;EAC7E,MAAMC,QAAQ,GAAG,CAAC,CAAC,CAACC,oBAAoB,CAAC7S,IAAI,CAAC0S,cAAc,EAAEllB,GAAG,CAAC,GAAG,YAAY,GAAG,eAAe;EACnG,IAAIolB,QAAQ,KAAK,YAAY,EAC3BJ,KAAK,CAAChlB,GAAG,CAAC,GAAGilB,MAAM;EACrB,IAAIE,oBAAoB,IAAIC,QAAQ,KAAK,eAAe,EAAE;IACxDhpB,MAAM,CAACyO,cAAc,CAACma,KAAK,EAAEhlB,GAAG,EAAE;MAChC9D,KAAK,EAAE+oB,MAAM;MACbpb,UAAU,EAAE,KAAK;MACjB4B,QAAQ,EAAE,IAAI;MACd7B,YAAY,EAAE;IAChB,CAAC,CAAC;EACJ;AACF;AACA,SAAS0b,IAAIA,CAAC3a,MAAM,EAAExO,OAAO,GAAG,CAAC,CAAC,EAAE;EAClC,IAAI0oB,QAAQ,CAACla,MAAM,CAAC,EAAE;IACpB,OAAOA,MAAM,CAACM,GAAG,CAAE7E,IAAI,IAAKkf,IAAI,CAAClf,IAAI,EAAEjK,OAAO,CAAC,CAAC;EAClD;EACA,IAAI,CAAC2oB,cAAc,CAACna,MAAM,CAAC,EAAE;IAC3B,OAAOA,MAAM;EACf;EACA,MAAMvD,KAAK,GAAGhL,MAAM,CAACmO,mBAAmB,CAACI,MAAM,CAAC;EAChD,MAAM4a,OAAO,GAAGnpB,MAAM,CAACopB,qBAAqB,CAAC7a,MAAM,CAAC;EACpD,OAAO,CAAC,GAAGvD,KAAK,EAAE,GAAGme,OAAO,CAAC,CAACrF,MAAM,CAAC,CAAC8E,KAAK,EAAEhlB,GAAG,KAAK;IACnD,IAAI6kB,QAAQ,CAAC1oB,OAAO,CAACiL,KAAK,CAAC,IAAI,CAACjL,OAAO,CAACiL,KAAK,CAAC+D,QAAQ,CAACnL,GAAG,CAAC,EAAE;MAC3D,OAAOglB,KAAK;IACd;IACA,MAAMS,GAAG,GAAG9a,MAAM,CAAC3K,GAAG,CAAC;IACvB,MAAMilB,MAAM,GAAGK,IAAI,CAACG,GAAG,EAAEtpB,OAAO,CAAC;IACjC4oB,WAAW,CAACC,KAAK,EAAEhlB,GAAG,EAAEilB,MAAM,EAAEta,MAAM,EAAExO,OAAO,CAACupB,aAAa,CAAC;IAC9D,OAAOV,KAAK;EACd,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;;AAEA;AACA,IAAIW,SAAS,GAAG,MAAM;EACpB;AACF;AACA;EACEpM,WAAWA,CAAC;IAAEoK,MAAM,GAAG;EAAM,CAAC,GAAG,CAAC,CAAC,EAAE;IACnC,IAAI,CAACnD,aAAa,GAAG,IAAIrG,aAAa,CAAC,CAAC;IACxC,IAAI,CAACmF,cAAc,GAAG,IAAI1F,QAAQ,CAAEtd,CAAC,IAAKA,CAAC,CAACijB,WAAW,IAAI,EAAE,CAAC;IAC9D,IAAI,CAACsB,yBAAyB,GAAG,IAAI5F,yBAAyB,CAAC,CAAC;IAChE,IAAI,CAAC4D,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAAC8E,MAAM,GAAGA,MAAM;EACtB;EACA7C,SAASA,CAACe,MAAM,EAAE;IAChB,MAAM0B,UAAU,GAAG,eAAgB,IAAI1a,GAAG,CAAC,CAAC;IAC5C,MAAM+c,MAAM,GAAG5B,MAAM,CAACnC,MAAM,EAAE0B,UAAU,EAAE,IAAI,EAAE,IAAI,CAACI,MAAM,CAAC;IAC5D,MAAMvgB,GAAG,GAAG;MACVme,IAAI,EAAEqE,MAAM,CAACvB;IACf,CAAC;IACD,IAAIuB,MAAM,CAAC7C,WAAW,EAAE;MACtB3f,GAAG,CAACyiB,IAAI,GAAG;QACT,GAAGziB,GAAG,CAACyiB,IAAI;QACXpL,MAAM,EAAEmL,MAAM,CAAC7C;MACjB,CAAC;IACH;IACA,MAAM+C,mBAAmB,GAAGrC,sCAAsC,CAACF,UAAU,EAAE,IAAI,CAACI,MAAM,CAAC;IAC3F,IAAImC,mBAAmB,EAAE;MACvB1iB,GAAG,CAACyiB,IAAI,GAAG;QACT,GAAGziB,GAAG,CAACyiB,IAAI;QACXE,qBAAqB,EAAED;MACzB,CAAC;IACH;IACA,OAAO1iB,GAAG;EACZ;EACA2d,WAAWA,CAACvF,OAAO,EAAE;IACnB,MAAM;MAAE+F,IAAI;MAAEsE;IAAK,CAAC,GAAGrK,OAAO;IAC9B,IAAIpZ,MAAM,GAAGkjB,IAAI,CAAC/D,IAAI,CAAC;IACvB,IAAIsE,IAAI,EAAEpL,MAAM,EAAE;MAChBrY,MAAM,GAAGygB,qBAAqB,CAACzgB,MAAM,EAAEyjB,IAAI,CAACpL,MAAM,EAAE,IAAI,CAAC;IAC3D;IACA,IAAIoL,IAAI,EAAEE,qBAAqB,EAAE;MAC/B3jB,MAAM,GAAG4gB,mCAAmC,CAAC5gB,MAAM,EAAEyjB,IAAI,CAACE,qBAAqB,CAAC;IAClF;IACA,OAAO3jB,MAAM;EACf;EACA4jB,SAASA,CAACnE,MAAM,EAAE;IAChB,OAAOoE,IAAI,CAACD,SAAS,CAAC,IAAI,CAAClF,SAAS,CAACe,MAAM,CAAC,CAAC;EAC/C;EACAqE,KAAKA,CAACvI,MAAM,EAAE;IACZ,OAAO,IAAI,CAACoD,WAAW,CAACkF,IAAI,CAACC,KAAK,CAACvI,MAAM,CAAC,CAAC;EAC7C;EACAwI,aAAaA,CAAChoB,CAAC,EAAEhC,OAAO,EAAE;IACxB,IAAI,CAACqkB,aAAa,CAACzG,QAAQ,CAAC5b,CAAC,EAAEhC,OAAO,CAAC;EACzC;EACAiqB,cAAcA,CAACjoB,CAAC,EAAE6b,UAAU,EAAE;IAC5B,IAAI,CAACsF,cAAc,CAACvF,QAAQ,CAAC5b,CAAC,EAAE6b,UAAU,CAAC;EAC7C;EACAqM,cAAcA,CAAClL,WAAW,EAAE9O,IAAI,EAAE;IAChC,IAAI,CAACwU,yBAAyB,CAAC9G,QAAQ,CAAC;MACtC1N,IAAI;MACJ,GAAG8O;IACL,CAAC,CAAC;EACJ;EACAmL,eAAeA,CAAC,GAAGlf,KAAK,EAAE;IACxB,IAAI,CAACyX,iBAAiB,CAACvhB,IAAI,CAAC,GAAG8J,KAAK,CAAC;EACvC;AACF,CAAC;AACDue,SAAS,CAACY,eAAe,GAAG,IAAIZ,SAAS,CAAC,CAAC;AAC3CA,SAAS,CAAC7E,SAAS,GAAG6E,SAAS,CAACY,eAAe,CAACzF,SAAS,CAAC5jB,IAAI,CAACyoB,SAAS,CAACY,eAAe,CAAC;AACzFZ,SAAS,CAAC5E,WAAW,GAAG4E,SAAS,CAACY,eAAe,CAACxF,WAAW,CAAC7jB,IAAI,CAACyoB,SAAS,CAACY,eAAe,CAAC;AAC7FZ,SAAS,CAACK,SAAS,GAAGL,SAAS,CAACY,eAAe,CAACP,SAAS,CAAC9oB,IAAI,CAACyoB,SAAS,CAACY,eAAe,CAAC;AACzFZ,SAAS,CAACO,KAAK,GAAGP,SAAS,CAACY,eAAe,CAACL,KAAK,CAAChpB,IAAI,CAACyoB,SAAS,CAACY,eAAe,CAAC;AACjFZ,SAAS,CAACQ,aAAa,GAAGR,SAAS,CAACY,eAAe,CAACJ,aAAa,CAACjpB,IAAI,CAACyoB,SAAS,CAACY,eAAe,CAAC;AACjGZ,SAAS,CAACS,cAAc,GAAGT,SAAS,CAACY,eAAe,CAACH,cAAc,CAAClpB,IAAI,CAACyoB,SAAS,CAACY,eAAe,CAAC;AACnGZ,SAAS,CAACU,cAAc,GAAGV,SAAS,CAACY,eAAe,CAACF,cAAc,CAACnpB,IAAI,CAACyoB,SAAS,CAACY,eAAe,CAAC;AACnGZ,SAAS,CAACW,eAAe,GAAGX,SAAS,CAACY,eAAe,CAACD,eAAe,CAACppB,IAAI,CAACyoB,SAAS,CAACY,eAAe,CAAC;AACrG,IAAIzF,SAAS,GAAG6E,SAAS,CAAC7E,SAAS;AACnC6E,SAAS,CAAC5E,WAAW;AACrB,IAAIiF,SAAS,GAAGL,SAAS,CAACK,SAAS;AACnCL,SAAS,CAACO,KAAK;AACfP,SAAS,CAACQ,aAAa;AACvBR,SAAS,CAACU,cAAc;AACxBV,SAAS,CAACS,cAAc;AACxBT,SAAS,CAACW,eAAe;;AAEzB;AACA,SAASE,mBAAmBA,CAACC,KAAK,EAAE;EAClC,OAAOA,KAAK,CAAChnB,KAAK,CAACinB,WAAW,KAAK,UAAU,GAAG,UAAU,GAAG,CAACD,KAAK,CAACE,iBAAiB,CAAC,CAAC,GAAG,UAAU,GAAGF,KAAK,CAAChnB,KAAK,CAACinB,WAAW,KAAK,QAAQ,GAAG,QAAQ,GAAGD,KAAK,CAACG,OAAO,CAAC,CAAC,GAAG,OAAO,GAAG,OAAO;AAC9L;AACA,SAASC,YAAYA,CAAC9X,IAAI,EAAE+X,IAAI,EAAE;EAChC,OAAO,GAAG/X,IAAI,GAAG+X,IAAI,CAAChJ,MAAM,CAAC,CAAC,CAAC,CAACpJ,WAAW,CAAC,CAAC,GAAGoS,IAAI,CAACvgB,KAAK,CAAC,CAAC,CAAC,EAAE;AACjE;AACA,SAASwgB,mBAAmBA,CAAC;EAC3BC,UAAU;EACVC,aAAa;EACbL;AACF,CAAC,EAAE;EACD,OAAOI,UAAU,CAACN,WAAW,KAAK,UAAU,GAAG,MAAM,GAAG,CAACO,aAAa,GAAG,MAAM,GAAGD,UAAU,CAACN,WAAW,KAAK,QAAQ,GAAG,QAAQ,GAAGE,OAAO,GAAG,QAAQ,GAAG,OAAO;AACjK;AACA,SAASM,sBAAsBA,CAAC;EAC9BnmB,MAAM;EACNomB;AACF,CAAC,EAAE;EACD,OAAOA,QAAQ,GAAG,QAAQ,GAAGpmB,MAAM,KAAK,OAAO,GAAG,KAAK,GAAGA,MAAM,KAAK,SAAS,GAAG,QAAQ,GAAGA,MAAM,KAAK,SAAS,GAAG,OAAO,GAAG,MAAM;AACrI;AACA,SAASqmB,0BAA0BA,CAACC,KAAK,EAAE;EACzC,OAAOA,KAAK,KAAK,OAAO,GAAG,OAAO,GAAGA,KAAK,KAAK,OAAO,GAAG,QAAQ,GAAGA,KAAK,KAAK,QAAQ,GAAG,QAAQ,GAAGA,KAAK,KAAK,UAAU,GAAG,MAAM,GAAG,MAAM;AAC5I;AACA,IAAIC,YAAY,GAAGA,CAACprB,KAAK,EAAEqrB,QAAQ,GAAG,KAAK,KAAK;EAC9C,MAAM;IACJhG;EACF,CAAC,GAAGT,SAAS,CAAC5kB,KAAK,CAAC;EACpB,OAAO+pB,IAAI,CAACD,SAAS,CAACzE,IAAI,EAAE,IAAI,EAAEgG,QAAQ,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;AAC1D,CAAC;AACD,IAAIC,aAAa,GAAIC,CAAC,IAAKA,CAAC,CAAChoB,KAAK,CAACinB,WAAW,KAAK,MAAM,GAAG,CAAC,GAAG,CAACe,CAAC,CAACd,iBAAiB,CAAC,CAAC,GAAG,CAAC,GAAGc,CAAC,CAACb,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;AAChH,IAAIc,aAAa,GAAGA,CAAC/tB,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACguB,SAAS,CAACC,aAAa,CAAChuB,CAAC,CAAC+tB,SAAS,CAAC;AACpE,IAAIE,QAAQ,GAAGA,CAACluB,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC8F,KAAK,CAACqoB,aAAa,GAAGluB,CAAC,CAAC6F,KAAK,CAACqoB,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC;AAC/E,IAAIC,iBAAiB,GAAGA,CAACpuB,CAAC,EAAEC,CAAC,KAAK;EAChC,IAAI4tB,aAAa,CAAC7tB,CAAC,CAAC,KAAK6tB,aAAa,CAAC5tB,CAAC,CAAC,EAAE;IACzC,OAAOiuB,QAAQ,CAACluB,CAAC,EAAEC,CAAC,CAAC;EACvB;EACA,OAAO4tB,aAAa,CAAC7tB,CAAC,CAAC,GAAG6tB,aAAa,CAAC5tB,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACrD,CAAC;AACD,IAAIouB,OAAO,GAAG;EACZjnB,MAAM,EAAEgnB,iBAAiB;EACzB,YAAY,EAAEL,aAAa;EAC3B,cAAc,EAAEG;AAClB,CAAC;AACD,IAAII,qBAAqB,GAAIC,CAAC,IAAKA,CAAC,CAACzoB,KAAK,CAAC0nB,QAAQ,GAAG,CAAC,GAAGe,CAAC,CAACzoB,KAAK,CAACsB,MAAM,KAAK,OAAO,GAAG,CAAC,GAAGmnB,CAAC,CAACzoB,KAAK,CAACsB,MAAM,KAAK,SAAS,GAAG,CAAC,GAAG,CAAC;AAC/H,IAAIonB,gBAAgB,GAAGA,CAACxuB,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC8F,KAAK,CAAC2oB,WAAW,GAAGxuB,CAAC,CAAC6F,KAAK,CAAC2oB,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC;AACnF,IAAIC,kBAAkB,GAAGA,CAAC1uB,CAAC,EAAEC,CAAC,KAAK;EACjC,IAAIquB,qBAAqB,CAACtuB,CAAC,CAAC,KAAKsuB,qBAAqB,CAACruB,CAAC,CAAC,EAAE;IACzD,OAAOuuB,gBAAgB,CAACxuB,CAAC,EAAEC,CAAC,CAAC;EAC/B;EACA,OAAOquB,qBAAqB,CAACtuB,CAAC,CAAC,GAAGsuB,qBAAqB,CAACruB,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACrE,CAAC;AACD,IAAI0uB,eAAe,GAAG;EACpBvnB,MAAM,EAAEsnB,kBAAkB;EAC1B,cAAc,EAAEF;AAClB,CAAC;AACD,IAAII,kBAAkB,GAAIC,GAAG,IAAK;EAChC,OAAOA,GAAG,GAAGC,UAAU,CAACC,gBAAgB,CAACnY,QAAQ,CAACoY,eAAe,CAAC,CAACC,QAAQ,CAAC;AAC9E,CAAC;AACD,IAAIC,uBAAuB,GAAGA,CAAA,KAAM;EAClC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG9sB,YAAY,CAAC,MAAM,CAAC;EAC1DoG,OAAO,CAAC,MAAM;IACZ,MAAMokB,KAAK,GAAGhV,MAAM,CAACuX,UAAU,CAAC,8BAA8B,CAAC;IAC/DD,cAAc,CAACtC,KAAK,CAACwC,OAAO,GAAG,MAAM,GAAG,OAAO,CAAC;IAChD,MAAMxtB,QAAQ,GAAIyF,CAAC,IAAK;MACtB6nB,cAAc,CAAC7nB,CAAC,CAAC+nB,OAAO,GAAG,MAAM,GAAG,OAAO,CAAC;IAC9C,CAAC;IACDxC,KAAK,CAAC/U,gBAAgB,CAAC,QAAQ,EAAEjW,QAAQ,CAAC;IAC1C6G,SAAS,CAAC,MAAMmkB,KAAK,CAAC5U,mBAAmB,CAAC,QAAQ,EAAEpW,QAAQ,CAAC,CAAC;EAChE,CAAC,CAAC;EACF,OAAOqtB,WAAW;AACpB,CAAC;AACD,IAAII,sBAAsB,GAAGA,CAACC,OAAO,EAAEC,UAAU,EAAEltB,KAAK,KAAK;EAC3D,IAAIktB,UAAU,CAAChwB,MAAM,KAAK,CAAC,EAAE;IAC3B,OAAO8C,KAAK;EACd;EACA,IAAIitB,OAAO,YAAYtgB,GAAG,EAAE;IAC1B,MAAMwgB,OAAO,GAAG,IAAIxgB,GAAG,CAACsgB,OAAO,CAAC;IAChC,IAAIC,UAAU,CAAChwB,MAAM,KAAK,CAAC,EAAE;MAC3BiwB,OAAO,CAACvgB,GAAG,CAACsgB,UAAU,CAAC,CAAC,CAAC,EAAEltB,KAAK,CAAC;MACjC,OAAOmtB,OAAO;IAChB;IACA,MAAM,CAACC,IAAI,EAAE,GAAGC,IAAI,CAAC,GAAGH,UAAU;IAClCC,OAAO,CAACvgB,GAAG,CAACwgB,IAAI,EAAEJ,sBAAsB,CAACG,OAAO,CAACjoB,GAAG,CAACkoB,IAAI,CAAC,EAAEC,IAAI,EAAErtB,KAAK,CAAC,CAAC;IACzE,OAAOmtB,OAAO;EAChB;EACA,IAAIF,OAAO,YAAYlqB,GAAG,EAAE;IAC1B,MAAMuqB,UAAU,GAAGN,sBAAsB,CAACrnB,KAAK,CAAC4nB,IAAI,CAACN,OAAO,CAAC,EAAEC,UAAU,EAAEltB,KAAK,CAAC;IACjF,OAAO,IAAI+C,GAAG,CAACuqB,UAAU,CAAC;EAC5B;EACA,IAAI3nB,KAAK,CAACC,OAAO,CAACqnB,OAAO,CAAC,EAAE;IAC1B,MAAME,OAAO,GAAG,CAAC,GAAGF,OAAO,CAAC;IAC5B,IAAIC,UAAU,CAAChwB,MAAM,KAAK,CAAC,EAAE;MAC3BiwB,OAAO,CAACD,UAAU,CAAC,CAAC,CAAC,CAAC,GAAGltB,KAAK;MAC9B,OAAOmtB,OAAO;IAChB;IACA,MAAM,CAACC,IAAI,EAAE,GAAGC,IAAI,CAAC,GAAGH,UAAU;IAClCC,OAAO,CAACC,IAAI,CAAC,GAAGJ,sBAAsB,CAACG,OAAO,CAACC,IAAI,CAAC,EAAEC,IAAI,EAAErtB,KAAK,CAAC;IAClE,OAAOmtB,OAAO;EAChB;EACA,IAAIF,OAAO,YAAY/sB,MAAM,EAAE;IAC7B,MAAMitB,OAAO,GAAG;MACd,GAAGF;IACL,CAAC;IACD,IAAIC,UAAU,CAAChwB,MAAM,KAAK,CAAC,EAAE;MAC3BiwB,OAAO,CAACD,UAAU,CAAC,CAAC,CAAC,CAAC,GAAGltB,KAAK;MAC9B,OAAOmtB,OAAO;IAChB;IACA,MAAM,CAACC,IAAI,EAAE,GAAGC,IAAI,CAAC,GAAGH,UAAU;IAClCC,OAAO,CAACC,IAAI,CAAC,GAAGJ,sBAAsB,CAACG,OAAO,CAACC,IAAI,CAAC,EAAEC,IAAI,EAAErtB,KAAK,CAAC;IAClE,OAAOmtB,OAAO;EAChB;EACA,OAAOF,OAAO;AAChB,CAAC;AACD,IAAIO,sBAAsB,GAAGA,CAACP,OAAO,EAAEQ,UAAU,KAAK;EACpD,IAAIR,OAAO,YAAYtgB,GAAG,EAAE;IAC1B,MAAMwgB,OAAO,GAAG,IAAIxgB,GAAG,CAACsgB,OAAO,CAAC;IAChC,IAAIQ,UAAU,CAACvwB,MAAM,KAAK,CAAC,EAAE;MAC3BiwB,OAAO,CAACjpB,MAAM,CAACupB,UAAU,CAAC,CAAC,CAAC,CAAC;MAC7B,OAAON,OAAO;IAChB;IACA,MAAM,CAACC,IAAI,EAAE,GAAGC,IAAI,CAAC,GAAGI,UAAU;IAClCN,OAAO,CAACvgB,GAAG,CAACwgB,IAAI,EAAEI,sBAAsB,CAACL,OAAO,CAACjoB,GAAG,CAACkoB,IAAI,CAAC,EAAEC,IAAI,CAAC,CAAC;IAClE,OAAOF,OAAO;EAChB;EACA,IAAIF,OAAO,YAAYlqB,GAAG,EAAE;IAC1B,MAAMuqB,UAAU,GAAGE,sBAAsB,CAAC7nB,KAAK,CAAC4nB,IAAI,CAACN,OAAO,CAAC,EAAEQ,UAAU,CAAC;IAC1E,OAAO,IAAI1qB,GAAG,CAACuqB,UAAU,CAAC;EAC5B;EACA,IAAI3nB,KAAK,CAACC,OAAO,CAACqnB,OAAO,CAAC,EAAE;IAC1B,MAAME,OAAO,GAAG,CAAC,GAAGF,OAAO,CAAC;IAC5B,IAAIQ,UAAU,CAACvwB,MAAM,KAAK,CAAC,EAAE;MAC3B,OAAOiwB,OAAO,CAACje,MAAM,CAAC,CAAC7B,CAAC,EAAEqgB,GAAG,KAAKA,GAAG,CAAC1S,QAAQ,CAAC,CAAC,KAAKyS,UAAU,CAAC,CAAC,CAAC,CAAC;IACrE;IACA,MAAM,CAACL,IAAI,EAAE,GAAGC,IAAI,CAAC,GAAGI,UAAU;IAClCN,OAAO,CAACC,IAAI,CAAC,GAAGI,sBAAsB,CAACL,OAAO,CAACC,IAAI,CAAC,EAAEC,IAAI,CAAC;IAC3D,OAAOF,OAAO;EAChB;EACA,IAAIF,OAAO,YAAY/sB,MAAM,EAAE;IAC7B,MAAMitB,OAAO,GAAG;MACd,GAAGF;IACL,CAAC;IACD,IAAIQ,UAAU,CAACvwB,MAAM,KAAK,CAAC,EAAE;MAC3B,OAAOiwB,OAAO,CAACM,UAAU,CAAC,CAAC,CAAC,CAAC;MAC7B,OAAON,OAAO;IAChB;IACA,MAAM,CAACC,IAAI,EAAE,GAAGC,IAAI,CAAC,GAAGI,UAAU;IAClCN,OAAO,CAACC,IAAI,CAAC,GAAGI,sBAAsB,CAACL,OAAO,CAACC,IAAI,CAAC,EAAEC,IAAI,CAAC;IAC3D,OAAOF,OAAO;EAChB;EACA,OAAOF,OAAO;AAChB,CAAC;AACD,IAAIU,eAAe,GAAGA,CAACC,KAAK,EAAEnf,MAAM,KAAK;EACvC,IAAI,CAACmf,KAAK,EAAE;EACZ,MAAMC,WAAW,GAAGxZ,QAAQ,CAACyZ,aAAa,CAAC,UAAU,CAAC,IAAIrf,MAAM,EAAEqf,aAAa,CAAC,UAAU,CAAC;EAC3F,IAAID,WAAW,EAAE;EACjB,MAAME,QAAQ,GAAG1Z,QAAQ,CAACU,aAAa,CAAC,OAAO,CAAC;EAChD,MAAMiZ,QAAQ,GAAG3Z,QAAQ,CAAC4G,cAAc,CAAC,EAAE,CAAC;EAC5C8S,QAAQ,CAACvS,WAAW,CAACwS,QAAQ,CAAC;EAC9BD,QAAQ,CAAC5wB,EAAE,GAAG,SAAS;EACvB4wB,QAAQ,CAACnY,YAAY,CAAC,OAAO,EAAEgY,KAAK,CAAC;EACrC,IAAInf,MAAM,EAAE;IACVA,MAAM,CAAC+M,WAAW,CAACuS,QAAQ,CAAC;EAC9B,CAAC,MAAM;IACL1Z,QAAQ,CAAC+Y,IAAI,CAAC5R,WAAW,CAACuS,QAAQ,CAAC;EACrC;AACF,CAAC;AAED,SAAShwB,MAAM,EAAEwT,GAAG,EAAE2L,OAAO,EAAE9M,GAAG,EAAEE,KAAK,EAAEgB,KAAK,EAAE0K,MAAM,EAAEzL,IAAI,EAAEM,MAAM,EAAE2E,gBAAgB,EAAEjQ,KAAK,EAAEqM,SAAS,EAAE8D,oBAAoB,EAAE2W,kBAAkB,EAAErf,eAAe,EAAE/L,cAAc,EAAEqG,aAAa,EAAE/F,YAAY,EAAEO,UAAU,EAAER,kBAAkB,EAAElC,UAAU,EAAEW,YAAY,EAAEkQ,cAAc,EAAEmF,cAAc,EAAEoY,sBAAsB,EAAEpC,YAAY,EAAEJ,sBAAsB,EAAE3kB,QAAQ,EAAEsmB,uBAAuB,EAAE9B,mBAAmB,EAAEK,0BAA0B,EAAEZ,mBAAmB,EAAEK,YAAY,EAAErW,MAAM,EAAEwH,QAAQ,EAAErM,IAAI,EAAE1B,UAAU,EAAEqe,eAAe,EAAE5mB,EAAE,EAAEY,SAAS,EAAED,OAAO,EAAEvE,MAAM,EAAEgjB,SAAS,EAAEhP,YAAY,EAAE+X,eAAe,EAAE7B,OAAO,EAAEld,UAAU,EAAEqI,MAAM,EAAE6S,SAAS,EAAErV,QAAQ,EAAE7U,OAAO,EAAEotB,sBAAsB,EAAE1V,GAAG,EAAE5V,UAAU,EAAE2F,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
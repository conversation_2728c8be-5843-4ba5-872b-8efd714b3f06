{"ast": null, "code": "\"use client\";\n\n// src/useSuspenseQuery.ts\nimport { QueryObserver, skipToken } from \"@tanstack/query-core\";\nimport { useBaseQuery } from \"./useBaseQuery.js\";\nimport { defaultThrowOnError } from \"./suspense.js\";\nfunction useSuspenseQuery(options, queryClient) {\n  if (process.env.NODE_ENV !== \"production\") {\n    if (options.queryFn === skipToken) {\n      console.error(\"skipToken is not allowed for useSuspenseQuery\");\n    }\n  }\n  return useBaseQuery({\n    ...options,\n    enabled: true,\n    suspense: true,\n    throwOnError: defaultThrowOnError,\n    placeholderData: void 0\n  }, QueryObserver, queryClient);\n}\nexport { useSuspenseQuery };", "map": {"version": 3, "names": ["QueryObserver", "skipToken", "useBaseQuery", "defaultThrowOnError", "useSuspenseQuery", "options", "queryClient", "process", "env", "NODE_ENV", "queryFn", "console", "error", "enabled", "suspense", "throwOnError", "placeholderData"], "sources": ["/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/node_modules/@tanstack/react-query/src/useSuspenseQuery.ts"], "sourcesContent": ["'use client'\nimport { QueryObserver, skipToken } from '@tanstack/query-core'\nimport { useBaseQuery } from './useBaseQuery'\nimport { defaultThrowOnError } from './suspense'\nimport type { UseSuspenseQueryOptions, UseSuspenseQueryResult } from './types'\nimport type { DefaultError, QueryClient, QueryKey } from '@tanstack/query-core'\n\nexport function useSuspenseQuery<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: UseSuspenseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  queryClient?: QueryClient,\n): UseSuspenseQueryResult<TData, TError> {\n  if (process.env.NODE_ENV !== 'production') {\n    if ((options.queryFn as any) === skipToken) {\n      console.error('skipToken is not allowed for useSuspenseQuery')\n    }\n  }\n\n  return useBaseQuery(\n    {\n      ...options,\n      enabled: true,\n      suspense: true,\n      throwOnError: defaultThrowOnError,\n      placeholderData: undefined,\n    },\n    QueryObserver,\n    queryClient,\n  ) as UseSuspenseQueryResult<TData, TError>\n}\n"], "mappings": ";;;AACA,SAASA,aAAA,EAAeC,SAAA,QAAiB;AACzC,SAASC,YAAA,QAAoB;AAC7B,SAASC,mBAAA,QAA2B;AAI7B,SAASC,iBAMdC,OAAA,EACAC,WAAA,EACuC;EACvC,IAAIC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;IACzC,IAAKJ,OAAA,CAAQK,OAAA,KAAoBT,SAAA,EAAW;MAC1CU,OAAA,CAAQC,KAAA,CAAM,+CAA+C;IAC/D;EACF;EAEA,OAAOV,YAAA,CACL;IACE,GAAGG,OAAA;IACHQ,OAAA,EAAS;IACTC,QAAA,EAAU;IACVC,YAAA,EAAcZ,mBAAA;IACda,eAAA,EAAiB;EACnB,GACAhB,aAAA,EACAM,WACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "var _s = $RefreshSig$();\n// Custom hook for experiment management with API communication, caching, and error handling\nimport { useCallback, useMemo } from 'react';\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\nimport { toast } from 'react-hot-toast';\nimport { experimentsApi } from '../services/api';\nimport { useExperimentContext } from '../contexts/ExperimentContext';\n\n// Query keys factory\nexport const experimentKeys = {\n  all: ['experiments'],\n  lists: () => [...experimentKeys.all, 'list'],\n  list: (filters, pagination) => [...experimentKeys.lists(), {\n    filters,\n    pagination\n  }],\n  details: () => [...experimentKeys.all, 'detail'],\n  detail: id => [...experimentKeys.details(), id],\n  analytics: id => [...experimentKeys.all, 'analytics', id]\n};\n\n// Main experiments hook\nexport const useExperiments = (filters = {}, pagination = {\n  page: 1,\n  limit: 20\n}) => {\n  _s();\n  const queryClient = useQueryClient();\n  const {\n    state,\n    setGlobalLoading,\n    addOptimisticUpdate,\n    removeOptimisticUpdate\n  } = useExperimentContext();\n\n  // Fetch experiments query\n  const {\n    data: experimentsResponse,\n    isLoading,\n    isError,\n    error,\n    refetch,\n    isFetching,\n    isPlaceholderData\n  } = useQuery({\n    queryKey: experimentKeys.list(filters, pagination),\n    queryFn: () => experimentsApi.getExperiments(filters, pagination),\n    placeholderData: previousData => previousData,\n    staleTime: state.cacheConfig.staleTime,\n    gcTime: state.cacheConfig.gcTime,\n    refetchOnWindowFocus: state.cacheConfig.refetchOnWindowFocus,\n    refetchOnMount: state.cacheConfig.refetchOnMount\n  });\n\n  // Extract data with fallbacks\n  const experiments = useMemo(() => (experimentsResponse === null || experimentsResponse === void 0 ? void 0 : experimentsResponse.data) || [], [experimentsResponse]);\n  const pagination_info = useMemo(() => experimentsResponse === null || experimentsResponse === void 0 ? void 0 : experimentsResponse.pagination, [experimentsResponse]);\n\n  // Create experiment mutation\n  const createMutation = useMutation({\n    mutationFn: data => experimentsApi.createExperiment(data),\n    onMutate: async newExperiment => {\n      // Cancel outgoing refetches\n      await queryClient.cancelQueries({\n        queryKey: experimentKeys.lists()\n      });\n\n      // Snapshot previous value\n      const previousExperiments = queryClient.getQueryData(experimentKeys.list(filters, pagination));\n\n      // Optimistically update\n      const optimisticExperiment = {\n        id: `temp-${Date.now()}`,\n        tenantId: 'current-tenant',\n        name: newExperiment.name,\n        description: newExperiment.description,\n        status: 'DRAFT',\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString(),\n        createdBy: 'current-user',\n        variants: newExperiment.variants.map((v, i) => ({\n          ...v,\n          id: `temp-variant-${i}`\n        })),\n        tags: newExperiment.tags || [],\n        _count: {\n          userAssignments: 0,\n          events: 0\n        }\n      };\n      queryClient.setQueryData(experimentKeys.list(filters, pagination), old => {\n        if (!old) return old;\n        return {\n          ...old,\n          data: [optimisticExperiment, ...old.data],\n          pagination: {\n            ...old.pagination,\n            total: old.pagination.total + 1\n          }\n        };\n      });\n\n      // Store rollback function\n      addOptimisticUpdate(`create-${optimisticExperiment.id}`, {\n        type: 'create',\n        data: optimisticExperiment,\n        rollback: () => {\n          queryClient.setQueryData(experimentKeys.list(filters, pagination), previousExperiments);\n        }\n      });\n      return {\n        previousExperiments,\n        optimisticExperiment\n      };\n    },\n    onError: (error, newExperiment, context) => {\n      // Rollback optimistic update\n      if (context !== null && context !== void 0 && context.optimisticExperiment) {\n        removeOptimisticUpdate(`create-${context.optimisticExperiment.id}`);\n        queryClient.setQueryData(experimentKeys.list(filters, pagination), context.previousExperiments);\n      }\n      toast.error(`Failed to create experiment: ${error.message}`);\n    },\n    onSuccess: (response, variables, context) => {\n      // Remove optimistic update\n      if (context !== null && context !== void 0 && context.optimisticExperiment) {\n        removeOptimisticUpdate(`create-${context.optimisticExperiment.id}`);\n      }\n\n      // Invalidate and refetch\n      queryClient.invalidateQueries({\n        queryKey: experimentKeys.lists()\n      });\n      toast.success('Experiment created successfully');\n    }\n  });\n\n  // Update experiment mutation\n  const updateMutation = useMutation({\n    mutationFn: ({\n      id,\n      data\n    }) => experimentsApi.updateExperiment(id, data),\n    onMutate: async ({\n      id,\n      data\n    }) => {\n      await queryClient.cancelQueries({\n        queryKey: experimentKeys.detail(id)\n      });\n      await queryClient.cancelQueries({\n        queryKey: experimentKeys.lists()\n      });\n      const previousExperiment = queryClient.getQueryData(experimentKeys.detail(id));\n      const previousList = queryClient.getQueryData(experimentKeys.list(filters, pagination));\n\n      // Optimistically update detail\n      queryClient.setQueryData(experimentKeys.detail(id), old => {\n        if (!old) return old;\n        return {\n          ...old,\n          data: {\n            ...old.data,\n            ...data,\n            updatedAt: new Date().toISOString()\n          }\n        };\n      });\n\n      // Optimistically update list\n      queryClient.setQueryData(experimentKeys.list(filters, pagination), old => {\n        if (!old) return old;\n        return {\n          ...old,\n          data: old.data.map(exp => exp.id === id ? {\n            ...exp,\n            ...data,\n            updatedAt: new Date().toISOString()\n          } : exp)\n        };\n      });\n      return {\n        previousExperiment,\n        previousList\n      };\n    },\n    onError: (error, {\n      id\n    }, context) => {\n      // Rollback\n      if (context !== null && context !== void 0 && context.previousExperiment) {\n        queryClient.setQueryData(experimentKeys.detail(id), context.previousExperiment);\n      }\n      if (context !== null && context !== void 0 && context.previousList) {\n        queryClient.setQueryData(experimentKeys.list(filters, pagination), context.previousList);\n      }\n      toast.error(`Failed to update experiment: ${error.message}`);\n    },\n    onSuccess: (response, {\n      id\n    }) => {\n      queryClient.invalidateQueries({\n        queryKey: experimentKeys.detail(id)\n      });\n      queryClient.invalidateQueries({\n        queryKey: experimentKeys.lists()\n      });\n      toast.success('Experiment updated successfully');\n    }\n  });\n\n  // Delete experiment mutation\n  const deleteMutation = useMutation({\n    mutationFn: id => experimentsApi.deleteExperiment(id),\n    onMutate: async id => {\n      await queryClient.cancelQueries({\n        queryKey: experimentKeys.lists()\n      });\n      const previousList = queryClient.getQueryData(experimentKeys.list(filters, pagination));\n\n      // Optimistically remove from list\n      queryClient.setQueryData(experimentKeys.list(filters, pagination), old => {\n        if (!old) return old;\n        return {\n          ...old,\n          data: old.data.filter(exp => exp.id !== id),\n          pagination: {\n            ...old.pagination,\n            total: old.pagination.total - 1\n          }\n        };\n      });\n      return {\n        previousList\n      };\n    },\n    onError: (error, id, context) => {\n      // Rollback\n      if (context !== null && context !== void 0 && context.previousList) {\n        queryClient.setQueryData(experimentKeys.list(filters, pagination), context.previousList);\n      }\n      toast.error(`Failed to delete experiment: ${error.message}`);\n    },\n    onSuccess: () => {\n      queryClient.invalidateQueries({\n        queryKey: experimentKeys.lists()\n      });\n      toast.success('Experiment deleted successfully');\n    }\n  });\n\n  // Status change mutation\n  const statusMutation = useMutation({\n    mutationFn: ({\n      id,\n      status,\n      data\n    }) => experimentsApi.updateExperimentStatus(id, status, data),\n    onMutate: async ({\n      id,\n      status\n    }) => {\n      await queryClient.cancelQueries({\n        queryKey: experimentKeys.lists()\n      });\n      await queryClient.cancelQueries({\n        queryKey: experimentKeys.detail(id)\n      });\n      const previousList = queryClient.getQueryData(experimentKeys.list(filters, pagination));\n      const previousDetail = queryClient.getQueryData(experimentKeys.detail(id));\n\n      // Optimistically update status\n      const updateStatus = exp => ({\n        ...exp,\n        status,\n        updatedAt: new Date().toISOString()\n      });\n      queryClient.setQueryData(experimentKeys.list(filters, pagination), old => {\n        if (!old) return old;\n        return {\n          ...old,\n          data: old.data.map(exp => exp.id === id ? updateStatus(exp) : exp)\n        };\n      });\n      queryClient.setQueryData(experimentKeys.detail(id), old => {\n        if (!old) return old;\n        return {\n          ...old,\n          data: updateStatus(old.data)\n        };\n      });\n      return {\n        previousList,\n        previousDetail\n      };\n    },\n    onError: (error, {\n      id\n    }, context) => {\n      // Rollback\n      if (context !== null && context !== void 0 && context.previousList) {\n        queryClient.setQueryData(experimentKeys.list(filters, pagination), context.previousList);\n      }\n      if (context !== null && context !== void 0 && context.previousDetail) {\n        queryClient.setQueryData(experimentKeys.detail(id), context.previousDetail);\n      }\n      toast.error(`Failed to update status: ${error.message}`);\n    },\n    onSuccess: (response, {\n      status\n    }) => {\n      queryClient.invalidateQueries({\n        queryKey: experimentKeys.lists()\n      });\n      toast.success(`Experiment ${status.toLowerCase()} successfully`);\n    }\n  });\n\n  // Duplicate experiment mutation\n  const duplicateMutation = useMutation({\n    mutationFn: id => experimentsApi.duplicateExperiment(id),\n    onSuccess: () => {\n      queryClient.invalidateQueries({\n        queryKey: experimentKeys.lists()\n      });\n      toast.success('Experiment duplicated successfully');\n    },\n    onError: error => {\n      toast.error(`Failed to duplicate experiment: ${error.message}`);\n    }\n  });\n\n  // Exposed methods\n  const createExperiment = useCallback(async data => {\n    const result = await createMutation.mutateAsync(data);\n    return result.data;\n  }, [createMutation]);\n  const updateExperiment = useCallback(async (id, data) => {\n    const result = await updateMutation.mutateAsync({\n      id,\n      data\n    });\n    return result.data;\n  }, [updateMutation]);\n  const deleteExperiment = useCallback(async id => {\n    await deleteMutation.mutateAsync(id);\n  }, [deleteMutation]);\n  const changeStatus = useCallback(async (id, status, data) => {\n    const result = await statusMutation.mutateAsync({\n      id,\n      status,\n      data\n    });\n    return result.data;\n  }, [statusMutation]);\n  const duplicateExperiment = useCallback(async id => {\n    const result = await duplicateMutation.mutateAsync(id);\n    return result.data;\n  }, [duplicateMutation]);\n  return {\n    // Data\n    experiments,\n    pagination: pagination_info,\n    // Loading states\n    isLoading,\n    isFetching,\n    isError,\n    error: error === null || error === void 0 ? void 0 : error.message,\n    isPreviousData,\n    // Mutation states\n    isCreating: createMutation.isLoading,\n    isUpdating: updateMutation.isLoading,\n    isDeleting: deleteMutation.isLoading,\n    isChangingStatus: statusMutation.isLoading,\n    isDuplicating: duplicateMutation.isLoading,\n    // Methods\n    createExperiment,\n    updateExperiment,\n    deleteExperiment,\n    changeStatus,\n    duplicateExperiment,\n    refetch,\n    // Raw mutations for advanced usage\n    createMutation,\n    updateMutation,\n    deleteMutation,\n    statusMutation,\n    duplicateMutation\n  };\n};\n_s(useExperiments, \"vGtNKlXz/6oVn+HWudEhxjpPr+o=\", false, function () {\n  return [useQueryClient, useExperimentContext, useQuery, useMutation, useMutation, useMutation, useMutation, useMutation];\n});", "map": {"version": 3, "names": ["useCallback", "useMemo", "useQuery", "useMutation", "useQueryClient", "toast", "experimentsApi", "useExperimentContext", "experimentKeys", "all", "lists", "list", "filters", "pagination", "details", "detail", "id", "analytics", "useExperiments", "page", "limit", "_s", "queryClient", "state", "setGlobalLoading", "addOptimisticUpdate", "removeOptimisticUpdate", "data", "experimentsResponse", "isLoading", "isError", "error", "refetch", "isFetching", "isPlaceholderData", "query<PERSON><PERSON>", "queryFn", "getExperiments", "placeholderData", "previousData", "staleTime", "cacheConfig", "gcTime", "refetchOnWindowFocus", "refetchOnMount", "experiments", "pagination_info", "createMutation", "mutationFn", "createExperiment", "onMutate", "newExperiment", "cancelQueries", "previousExperiments", "getQueryData", "optimisticExperiment", "Date", "now", "tenantId", "name", "description", "status", "createdAt", "toISOString", "updatedAt", "created<PERSON>y", "variants", "map", "v", "i", "tags", "_count", "userAssignments", "events", "setQueryData", "old", "total", "type", "rollback", "onError", "context", "message", "onSuccess", "response", "variables", "invalidateQueries", "success", "updateMutation", "updateExperiment", "previousExperiment", "previousList", "exp", "deleteMutation", "deleteExperiment", "filter", "statusMutation", "updateExperimentStatus", "previousDetail", "updateStatus", "toLowerCase", "duplicateMutation", "duplicateExperiment", "result", "mutateAsync", "changeStatus", "isPreviousData", "isCreating", "isUpdating", "isDeleting", "isChangingStatus", "isDuplicating"], "sources": ["/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/src/hooks/useExperiments.ts"], "sourcesContent": ["// Custom hook for experiment management with API communication, caching, and error handling\nimport { useCallback, useMemo, useEffect } from 'react';\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\nimport { toast } from 'react-hot-toast';\nimport {\n  Experiment,\n  ExperimentFilters,\n  PaginationParams,\n  CreateExperimentForm,\n  UpdateExperimentForm,\n  ExperimentStatus,\n  PaginatedResponse,\n  ApiResponse,\n  ApiError,\n} from '../types/experiment';\nimport { experimentsApi } from '../services/api';\nimport { useExperimentContext } from '../contexts/ExperimentContext';\n\n// Query keys factory\nexport const experimentKeys = {\n  all: ['experiments'] as const,\n  lists: () => [...experimentKeys.all, 'list'] as const,\n  list: (filters: ExperimentFilters, pagination: PaginationParams) =>\n    [...experimentKeys.lists(), { filters, pagination }] as const,\n  details: () => [...experimentKeys.all, 'detail'] as const,\n  detail: (id: string) => [...experimentKeys.details(), id] as const,\n  analytics: (id: string) => [...experimentKeys.all, 'analytics', id] as const,\n};\n\n// Main experiments hook\nexport const useExperiments = (\n  filters: ExperimentFilters = {},\n  pagination: PaginationParams = { page: 1, limit: 20 }\n) => {\n  const queryClient = useQueryClient();\n  const { state, setGlobalLoading, addOptimisticUpdate, removeOptimisticUpdate } = useExperimentContext();\n\n  // Fetch experiments query\n  const {\n    data: experimentsResponse,\n    isLoading,\n    isError,\n    error,\n    refetch,\n    isFetching,\n    isPlaceholderData,\n  } = useQuery({\n    queryKey: experimentKeys.list(filters, pagination),\n    queryFn: () => experimentsApi.getExperiments(filters, pagination),\n    placeholderData: (previousData) => previousData,\n    staleTime: state.cacheConfig.staleTime,\n    gcTime: state.cacheConfig.gcTime,\n    refetchOnWindowFocus: state.cacheConfig.refetchOnWindowFocus,\n    refetchOnMount: state.cacheConfig.refetchOnMount,\n  });\n\n  // Extract data with fallbacks\n  const experiments = useMemo(() => experimentsResponse?.data || [], [experimentsResponse]);\n  const pagination_info = useMemo(() => experimentsResponse?.pagination, [experimentsResponse]);\n\n  // Create experiment mutation\n  const createMutation = useMutation({\n    mutationFn: (data: CreateExperimentForm) => experimentsApi.createExperiment(data),\n    onMutate: async (newExperiment) => {\n      // Cancel outgoing refetches\n      await queryClient.cancelQueries({ queryKey: experimentKeys.lists() });\n\n      // Snapshot previous value\n      const previousExperiments = queryClient.getQueryData(\n        experimentKeys.list(filters, pagination)\n      );\n\n      // Optimistically update\n      const optimisticExperiment: Experiment = {\n        id: `temp-${Date.now()}`,\n        tenantId: 'current-tenant',\n        name: newExperiment.name,\n        description: newExperiment.description,\n        status: 'DRAFT',\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString(),\n        createdBy: 'current-user',\n        variants: newExperiment.variants.map((v, i) => ({ ...v, id: `temp-variant-${i}` })),\n        tags: newExperiment.tags || [],\n        _count: { userAssignments: 0, events: 0 },\n      };\n\n      queryClient.setQueryData(\n        experimentKeys.list(filters, pagination),\n        (old: PaginatedResponse<Experiment> | undefined) => {\n          if (!old) return old;\n          return {\n            ...old,\n            data: [optimisticExperiment, ...old.data],\n            pagination: {\n              ...old.pagination,\n              total: old.pagination.total + 1,\n            },\n          };\n        }\n      );\n\n      // Store rollback function\n      addOptimisticUpdate(`create-${optimisticExperiment.id}`, {\n        type: 'create',\n        data: optimisticExperiment,\n        rollback: () => {\n          queryClient.setQueryData(\n            experimentKeys.list(filters, pagination),\n            previousExperiments\n          );\n        },\n      });\n\n      return { previousExperiments, optimisticExperiment };\n    },\n    onError: (error: ApiError, newExperiment, context) => {\n      // Rollback optimistic update\n      if (context?.optimisticExperiment) {\n        removeOptimisticUpdate(`create-${context.optimisticExperiment.id}`);\n        queryClient.setQueryData(\n          experimentKeys.list(filters, pagination),\n          context.previousExperiments\n        );\n      }\n      toast.error(`Failed to create experiment: ${error.message}`);\n    },\n    onSuccess: (response, variables, context) => {\n      // Remove optimistic update\n      if (context?.optimisticExperiment) {\n        removeOptimisticUpdate(`create-${context.optimisticExperiment.id}`);\n      }\n      \n      // Invalidate and refetch\n      queryClient.invalidateQueries({ queryKey: experimentKeys.lists() });\n      toast.success('Experiment created successfully');\n    },\n  });\n\n  // Update experiment mutation\n  const updateMutation = useMutation({\n    mutationFn: ({ id, data }: { id: string; data: UpdateExperimentForm }) =>\n      experimentsApi.updateExperiment(id, data),\n    onMutate: async ({ id, data }) => {\n      await queryClient.cancelQueries({ queryKey: experimentKeys.detail(id) });\n      await queryClient.cancelQueries({ queryKey: experimentKeys.lists() });\n\n      const previousExperiment = queryClient.getQueryData(experimentKeys.detail(id));\n      const previousList = queryClient.getQueryData(experimentKeys.list(filters, pagination));\n\n      // Optimistically update detail\n      queryClient.setQueryData(experimentKeys.detail(id), (old: ApiResponse<Experiment> | undefined) => {\n        if (!old) return old;\n        return {\n          ...old,\n          data: { ...old.data, ...data, updatedAt: new Date().toISOString() },\n        };\n      });\n\n      // Optimistically update list\n      queryClient.setQueryData(\n        experimentKeys.list(filters, pagination),\n        (old: PaginatedResponse<Experiment> | undefined) => {\n          if (!old) return old;\n          return {\n            ...old,\n            data: old.data.map(exp =>\n              exp.id === id\n                ? { ...exp, ...data, updatedAt: new Date().toISOString() }\n                : exp\n            ),\n          };\n        }\n      );\n\n      return { previousExperiment, previousList };\n    },\n    onError: (error: ApiError, { id }, context) => {\n      // Rollback\n      if (context?.previousExperiment) {\n        queryClient.setQueryData(experimentKeys.detail(id), context.previousExperiment);\n      }\n      if (context?.previousList) {\n        queryClient.setQueryData(experimentKeys.list(filters, pagination), context.previousList);\n      }\n      toast.error(`Failed to update experiment: ${error.message}`);\n    },\n    onSuccess: (response, { id }) => {\n      queryClient.invalidateQueries({ queryKey: experimentKeys.detail(id) });\n      queryClient.invalidateQueries({ queryKey: experimentKeys.lists() });\n      toast.success('Experiment updated successfully');\n    },\n  });\n\n  // Delete experiment mutation\n  const deleteMutation = useMutation({\n    mutationFn: (id: string) => experimentsApi.deleteExperiment(id),\n    onMutate: async (id) => {\n      await queryClient.cancelQueries({ queryKey: experimentKeys.lists() });\n\n      const previousList = queryClient.getQueryData(experimentKeys.list(filters, pagination));\n\n      // Optimistically remove from list\n      queryClient.setQueryData(\n        experimentKeys.list(filters, pagination),\n        (old: PaginatedResponse<Experiment> | undefined) => {\n          if (!old) return old;\n          return {\n            ...old,\n            data: old.data.filter(exp => exp.id !== id),\n            pagination: {\n              ...old.pagination,\n              total: old.pagination.total - 1,\n            },\n          };\n        }\n      );\n\n      return { previousList };\n    },\n    onError: (error: ApiError, id, context) => {\n      // Rollback\n      if (context?.previousList) {\n        queryClient.setQueryData(experimentKeys.list(filters, pagination), context.previousList);\n      }\n      toast.error(`Failed to delete experiment: ${error.message}`);\n    },\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: experimentKeys.lists() });\n      toast.success('Experiment deleted successfully');\n    },\n  });\n\n  // Status change mutation\n  const statusMutation = useMutation({\n    mutationFn: ({ id, status, data }: { id: string; status: ExperimentStatus; data?: any }) =>\n      experimentsApi.updateExperimentStatus(id, status, data),\n    onMutate: async ({ id, status }) => {\n      await queryClient.cancelQueries({ queryKey: experimentKeys.lists() });\n      await queryClient.cancelQueries({ queryKey: experimentKeys.detail(id) });\n\n      const previousList = queryClient.getQueryData(experimentKeys.list(filters, pagination));\n      const previousDetail = queryClient.getQueryData(experimentKeys.detail(id));\n\n      // Optimistically update status\n      const updateStatus = (exp: Experiment) => ({\n        ...exp,\n        status,\n        updatedAt: new Date().toISOString(),\n      });\n\n      queryClient.setQueryData(\n        experimentKeys.list(filters, pagination),\n        (old: PaginatedResponse<Experiment> | undefined) => {\n          if (!old) return old;\n          return {\n            ...old,\n            data: old.data.map(exp => exp.id === id ? updateStatus(exp) : exp),\n          };\n        }\n      );\n\n      queryClient.setQueryData(experimentKeys.detail(id), (old: ApiResponse<Experiment> | undefined) => {\n        if (!old) return old;\n        return {\n          ...old,\n          data: updateStatus(old.data),\n        };\n      });\n\n      return { previousList, previousDetail };\n    },\n    onError: (error: ApiError, { id }, context) => {\n      // Rollback\n      if (context?.previousList) {\n        queryClient.setQueryData(experimentKeys.list(filters, pagination), context.previousList);\n      }\n      if (context?.previousDetail) {\n        queryClient.setQueryData(experimentKeys.detail(id), context.previousDetail);\n      }\n      toast.error(`Failed to update status: ${error.message}`);\n    },\n    onSuccess: (response, { status }) => {\n      queryClient.invalidateQueries({ queryKey: experimentKeys.lists() });\n      toast.success(`Experiment ${status.toLowerCase()} successfully`);\n    },\n  });\n\n  // Duplicate experiment mutation\n  const duplicateMutation = useMutation({\n    mutationFn: (id: string) => experimentsApi.duplicateExperiment(id),\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: experimentKeys.lists() });\n      toast.success('Experiment duplicated successfully');\n    },\n    onError: (error: ApiError) => {\n      toast.error(`Failed to duplicate experiment: ${error.message}`);\n    },\n  });\n\n  // Exposed methods\n  const createExperiment = useCallback(\n    async (data: CreateExperimentForm): Promise<Experiment> => {\n      const result = await createMutation.mutateAsync(data);\n      return result.data;\n    },\n    [createMutation]\n  );\n\n  const updateExperiment = useCallback(\n    async (id: string, data: UpdateExperimentForm): Promise<Experiment> => {\n      const result = await updateMutation.mutateAsync({ id, data });\n      return result.data;\n    },\n    [updateMutation]\n  );\n\n  const deleteExperiment = useCallback(\n    async (id: string): Promise<void> => {\n      await deleteMutation.mutateAsync(id);\n    },\n    [deleteMutation]\n  );\n\n  const changeStatus = useCallback(\n    async (id: string, status: ExperimentStatus, data?: any): Promise<Experiment> => {\n      const result = await statusMutation.mutateAsync({ id, status, data });\n      return result.data;\n    },\n    [statusMutation]\n  );\n\n  const duplicateExperiment = useCallback(\n    async (id: string): Promise<Experiment> => {\n      const result = await duplicateMutation.mutateAsync(id);\n      return result.data;\n    },\n    [duplicateMutation]\n  );\n\n  return {\n    // Data\n    experiments,\n    pagination: pagination_info,\n    \n    // Loading states\n    isLoading,\n    isFetching,\n    isError,\n    error: error?.message,\n    isPreviousData,\n    \n    // Mutation states\n    isCreating: createMutation.isLoading,\n    isUpdating: updateMutation.isLoading,\n    isDeleting: deleteMutation.isLoading,\n    isChangingStatus: statusMutation.isLoading,\n    isDuplicating: duplicateMutation.isLoading,\n    \n    // Methods\n    createExperiment,\n    updateExperiment,\n    deleteExperiment,\n    changeStatus,\n    duplicateExperiment,\n    refetch,\n    \n    // Raw mutations for advanced usage\n    createMutation,\n    updateMutation,\n    deleteMutation,\n    statusMutation,\n    duplicateMutation,\n  };\n};\n"], "mappings": ";AAAA;AACA,SAASA,WAAW,EAAEC,OAAO,QAAmB,OAAO;AACvD,SAASC,QAAQ,EAAEC,WAAW,EAAEC,cAAc,QAAQ,uBAAuB;AAC7E,SAASC,KAAK,QAAQ,iBAAiB;AAYvC,SAASC,cAAc,QAAQ,iBAAiB;AAChD,SAASC,oBAAoB,QAAQ,+BAA+B;;AAEpE;AACA,OAAO,MAAMC,cAAc,GAAG;EAC5BC,GAAG,EAAE,CAAC,aAAa,CAAU;EAC7BC,KAAK,EAAEA,CAAA,KAAM,CAAC,GAAGF,cAAc,CAACC,GAAG,EAAE,MAAM,CAAU;EACrDE,IAAI,EAAEA,CAACC,OAA0B,EAAEC,UAA4B,KAC7D,CAAC,GAAGL,cAAc,CAACE,KAAK,CAAC,CAAC,EAAE;IAAEE,OAAO;IAAEC;EAAW,CAAC,CAAU;EAC/DC,OAAO,EAAEA,CAAA,KAAM,CAAC,GAAGN,cAAc,CAACC,GAAG,EAAE,QAAQ,CAAU;EACzDM,MAAM,EAAGC,EAAU,IAAK,CAAC,GAAGR,cAAc,CAACM,OAAO,CAAC,CAAC,EAAEE,EAAE,CAAU;EAClEC,SAAS,EAAGD,EAAU,IAAK,CAAC,GAAGR,cAAc,CAACC,GAAG,EAAE,WAAW,EAAEO,EAAE;AACpE,CAAC;;AAED;AACA,OAAO,MAAME,cAAc,GAAGA,CAC5BN,OAA0B,GAAG,CAAC,CAAC,EAC/BC,UAA4B,GAAG;EAAEM,IAAI,EAAE,CAAC;EAAEC,KAAK,EAAE;AAAG,CAAC,KAClD;EAAAC,EAAA;EACH,MAAMC,WAAW,GAAGlB,cAAc,CAAC,CAAC;EACpC,MAAM;IAAEmB,KAAK;IAAEC,gBAAgB;IAAEC,mBAAmB;IAAEC;EAAuB,CAAC,GAAGnB,oBAAoB,CAAC,CAAC;;EAEvG;EACA,MAAM;IACJoB,IAAI,EAAEC,mBAAmB;IACzBC,SAAS;IACTC,OAAO;IACPC,KAAK;IACLC,OAAO;IACPC,UAAU;IACVC;EACF,CAAC,GAAGhC,QAAQ,CAAC;IACXiC,QAAQ,EAAE3B,cAAc,CAACG,IAAI,CAACC,OAAO,EAAEC,UAAU,CAAC;IAClDuB,OAAO,EAAEA,CAAA,KAAM9B,cAAc,CAAC+B,cAAc,CAACzB,OAAO,EAAEC,UAAU,CAAC;IACjEyB,eAAe,EAAGC,YAAY,IAAKA,YAAY;IAC/CC,SAAS,EAAEjB,KAAK,CAACkB,WAAW,CAACD,SAAS;IACtCE,MAAM,EAAEnB,KAAK,CAACkB,WAAW,CAACC,MAAM;IAChCC,oBAAoB,EAAEpB,KAAK,CAACkB,WAAW,CAACE,oBAAoB;IAC5DC,cAAc,EAAErB,KAAK,CAACkB,WAAW,CAACG;EACpC,CAAC,CAAC;;EAEF;EACA,MAAMC,WAAW,GAAG5C,OAAO,CAAC,MAAM,CAAA2B,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAED,IAAI,KAAI,EAAE,EAAE,CAACC,mBAAmB,CAAC,CAAC;EACzF,MAAMkB,eAAe,GAAG7C,OAAO,CAAC,MAAM2B,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEf,UAAU,EAAE,CAACe,mBAAmB,CAAC,CAAC;;EAE7F;EACA,MAAMmB,cAAc,GAAG5C,WAAW,CAAC;IACjC6C,UAAU,EAAGrB,IAA0B,IAAKrB,cAAc,CAAC2C,gBAAgB,CAACtB,IAAI,CAAC;IACjFuB,QAAQ,EAAE,MAAOC,aAAa,IAAK;MACjC;MACA,MAAM7B,WAAW,CAAC8B,aAAa,CAAC;QAAEjB,QAAQ,EAAE3B,cAAc,CAACE,KAAK,CAAC;MAAE,CAAC,CAAC;;MAErE;MACA,MAAM2C,mBAAmB,GAAG/B,WAAW,CAACgC,YAAY,CAClD9C,cAAc,CAACG,IAAI,CAACC,OAAO,EAAEC,UAAU,CACzC,CAAC;;MAED;MACA,MAAM0C,oBAAgC,GAAG;QACvCvC,EAAE,EAAE,QAAQwC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;QACxBC,QAAQ,EAAE,gBAAgB;QAC1BC,IAAI,EAAER,aAAa,CAACQ,IAAI;QACxBC,WAAW,EAAET,aAAa,CAACS,WAAW;QACtCC,MAAM,EAAE,OAAO;QACfC,SAAS,EAAE,IAAIN,IAAI,CAAC,CAAC,CAACO,WAAW,CAAC,CAAC;QACnCC,SAAS,EAAE,IAAIR,IAAI,CAAC,CAAC,CAACO,WAAW,CAAC,CAAC;QACnCE,SAAS,EAAE,cAAc;QACzBC,QAAQ,EAAEf,aAAa,CAACe,QAAQ,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,MAAM;UAAE,GAAGD,CAAC;UAAEpD,EAAE,EAAE,gBAAgBqD,CAAC;QAAG,CAAC,CAAC,CAAC;QACnFC,IAAI,EAAEnB,aAAa,CAACmB,IAAI,IAAI,EAAE;QAC9BC,MAAM,EAAE;UAAEC,eAAe,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAE;MAC1C,CAAC;MAEDnD,WAAW,CAACoD,YAAY,CACtBlE,cAAc,CAACG,IAAI,CAACC,OAAO,EAAEC,UAAU,CAAC,EACvC8D,GAA8C,IAAK;QAClD,IAAI,CAACA,GAAG,EAAE,OAAOA,GAAG;QACpB,OAAO;UACL,GAAGA,GAAG;UACNhD,IAAI,EAAE,CAAC4B,oBAAoB,EAAE,GAAGoB,GAAG,CAAChD,IAAI,CAAC;UACzCd,UAAU,EAAE;YACV,GAAG8D,GAAG,CAAC9D,UAAU;YACjB+D,KAAK,EAAED,GAAG,CAAC9D,UAAU,CAAC+D,KAAK,GAAG;UAChC;QACF,CAAC;MACH,CACF,CAAC;;MAED;MACAnD,mBAAmB,CAAC,UAAU8B,oBAAoB,CAACvC,EAAE,EAAE,EAAE;QACvD6D,IAAI,EAAE,QAAQ;QACdlD,IAAI,EAAE4B,oBAAoB;QAC1BuB,QAAQ,EAAEA,CAAA,KAAM;UACdxD,WAAW,CAACoD,YAAY,CACtBlE,cAAc,CAACG,IAAI,CAACC,OAAO,EAAEC,UAAU,CAAC,EACxCwC,mBACF,CAAC;QACH;MACF,CAAC,CAAC;MAEF,OAAO;QAAEA,mBAAmB;QAAEE;MAAqB,CAAC;IACtD,CAAC;IACDwB,OAAO,EAAEA,CAAChD,KAAe,EAAEoB,aAAa,EAAE6B,OAAO,KAAK;MACpD;MACA,IAAIA,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEzB,oBAAoB,EAAE;QACjC7B,sBAAsB,CAAC,UAAUsD,OAAO,CAACzB,oBAAoB,CAACvC,EAAE,EAAE,CAAC;QACnEM,WAAW,CAACoD,YAAY,CACtBlE,cAAc,CAACG,IAAI,CAACC,OAAO,EAAEC,UAAU,CAAC,EACxCmE,OAAO,CAAC3B,mBACV,CAAC;MACH;MACAhD,KAAK,CAAC0B,KAAK,CAAC,gCAAgCA,KAAK,CAACkD,OAAO,EAAE,CAAC;IAC9D,CAAC;IACDC,SAAS,EAAEA,CAACC,QAAQ,EAAEC,SAAS,EAAEJ,OAAO,KAAK;MAC3C;MACA,IAAIA,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEzB,oBAAoB,EAAE;QACjC7B,sBAAsB,CAAC,UAAUsD,OAAO,CAACzB,oBAAoB,CAACvC,EAAE,EAAE,CAAC;MACrE;;MAEA;MACAM,WAAW,CAAC+D,iBAAiB,CAAC;QAAElD,QAAQ,EAAE3B,cAAc,CAACE,KAAK,CAAC;MAAE,CAAC,CAAC;MACnEL,KAAK,CAACiF,OAAO,CAAC,iCAAiC,CAAC;IAClD;EACF,CAAC,CAAC;;EAEF;EACA,MAAMC,cAAc,GAAGpF,WAAW,CAAC;IACjC6C,UAAU,EAAEA,CAAC;MAAEhC,EAAE;MAAEW;IAAiD,CAAC,KACnErB,cAAc,CAACkF,gBAAgB,CAACxE,EAAE,EAAEW,IAAI,CAAC;IAC3CuB,QAAQ,EAAE,MAAAA,CAAO;MAAElC,EAAE;MAAEW;IAAK,CAAC,KAAK;MAChC,MAAML,WAAW,CAAC8B,aAAa,CAAC;QAAEjB,QAAQ,EAAE3B,cAAc,CAACO,MAAM,CAACC,EAAE;MAAE,CAAC,CAAC;MACxE,MAAMM,WAAW,CAAC8B,aAAa,CAAC;QAAEjB,QAAQ,EAAE3B,cAAc,CAACE,KAAK,CAAC;MAAE,CAAC,CAAC;MAErE,MAAM+E,kBAAkB,GAAGnE,WAAW,CAACgC,YAAY,CAAC9C,cAAc,CAACO,MAAM,CAACC,EAAE,CAAC,CAAC;MAC9E,MAAM0E,YAAY,GAAGpE,WAAW,CAACgC,YAAY,CAAC9C,cAAc,CAACG,IAAI,CAACC,OAAO,EAAEC,UAAU,CAAC,CAAC;;MAEvF;MACAS,WAAW,CAACoD,YAAY,CAAClE,cAAc,CAACO,MAAM,CAACC,EAAE,CAAC,EAAG2D,GAAwC,IAAK;QAChG,IAAI,CAACA,GAAG,EAAE,OAAOA,GAAG;QACpB,OAAO;UACL,GAAGA,GAAG;UACNhD,IAAI,EAAE;YAAE,GAAGgD,GAAG,CAAChD,IAAI;YAAE,GAAGA,IAAI;YAAEqC,SAAS,EAAE,IAAIR,IAAI,CAAC,CAAC,CAACO,WAAW,CAAC;UAAE;QACpE,CAAC;MACH,CAAC,CAAC;;MAEF;MACAzC,WAAW,CAACoD,YAAY,CACtBlE,cAAc,CAACG,IAAI,CAACC,OAAO,EAAEC,UAAU,CAAC,EACvC8D,GAA8C,IAAK;QAClD,IAAI,CAACA,GAAG,EAAE,OAAOA,GAAG;QACpB,OAAO;UACL,GAAGA,GAAG;UACNhD,IAAI,EAAEgD,GAAG,CAAChD,IAAI,CAACwC,GAAG,CAACwB,GAAG,IACpBA,GAAG,CAAC3E,EAAE,KAAKA,EAAE,GACT;YAAE,GAAG2E,GAAG;YAAE,GAAGhE,IAAI;YAAEqC,SAAS,EAAE,IAAIR,IAAI,CAAC,CAAC,CAACO,WAAW,CAAC;UAAE,CAAC,GACxD4B,GACN;QACF,CAAC;MACH,CACF,CAAC;MAED,OAAO;QAAEF,kBAAkB;QAAEC;MAAa,CAAC;IAC7C,CAAC;IACDX,OAAO,EAAEA,CAAChD,KAAe,EAAE;MAAEf;IAAG,CAAC,EAAEgE,OAAO,KAAK;MAC7C;MACA,IAAIA,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAES,kBAAkB,EAAE;QAC/BnE,WAAW,CAACoD,YAAY,CAAClE,cAAc,CAACO,MAAM,CAACC,EAAE,CAAC,EAAEgE,OAAO,CAACS,kBAAkB,CAAC;MACjF;MACA,IAAIT,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEU,YAAY,EAAE;QACzBpE,WAAW,CAACoD,YAAY,CAAClE,cAAc,CAACG,IAAI,CAACC,OAAO,EAAEC,UAAU,CAAC,EAAEmE,OAAO,CAACU,YAAY,CAAC;MAC1F;MACArF,KAAK,CAAC0B,KAAK,CAAC,gCAAgCA,KAAK,CAACkD,OAAO,EAAE,CAAC;IAC9D,CAAC;IACDC,SAAS,EAAEA,CAACC,QAAQ,EAAE;MAAEnE;IAAG,CAAC,KAAK;MAC/BM,WAAW,CAAC+D,iBAAiB,CAAC;QAAElD,QAAQ,EAAE3B,cAAc,CAACO,MAAM,CAACC,EAAE;MAAE,CAAC,CAAC;MACtEM,WAAW,CAAC+D,iBAAiB,CAAC;QAAElD,QAAQ,EAAE3B,cAAc,CAACE,KAAK,CAAC;MAAE,CAAC,CAAC;MACnEL,KAAK,CAACiF,OAAO,CAAC,iCAAiC,CAAC;IAClD;EACF,CAAC,CAAC;;EAEF;EACA,MAAMM,cAAc,GAAGzF,WAAW,CAAC;IACjC6C,UAAU,EAAGhC,EAAU,IAAKV,cAAc,CAACuF,gBAAgB,CAAC7E,EAAE,CAAC;IAC/DkC,QAAQ,EAAE,MAAOlC,EAAE,IAAK;MACtB,MAAMM,WAAW,CAAC8B,aAAa,CAAC;QAAEjB,QAAQ,EAAE3B,cAAc,CAACE,KAAK,CAAC;MAAE,CAAC,CAAC;MAErE,MAAMgF,YAAY,GAAGpE,WAAW,CAACgC,YAAY,CAAC9C,cAAc,CAACG,IAAI,CAACC,OAAO,EAAEC,UAAU,CAAC,CAAC;;MAEvF;MACAS,WAAW,CAACoD,YAAY,CACtBlE,cAAc,CAACG,IAAI,CAACC,OAAO,EAAEC,UAAU,CAAC,EACvC8D,GAA8C,IAAK;QAClD,IAAI,CAACA,GAAG,EAAE,OAAOA,GAAG;QACpB,OAAO;UACL,GAAGA,GAAG;UACNhD,IAAI,EAAEgD,GAAG,CAAChD,IAAI,CAACmE,MAAM,CAACH,GAAG,IAAIA,GAAG,CAAC3E,EAAE,KAAKA,EAAE,CAAC;UAC3CH,UAAU,EAAE;YACV,GAAG8D,GAAG,CAAC9D,UAAU;YACjB+D,KAAK,EAAED,GAAG,CAAC9D,UAAU,CAAC+D,KAAK,GAAG;UAChC;QACF,CAAC;MACH,CACF,CAAC;MAED,OAAO;QAAEc;MAAa,CAAC;IACzB,CAAC;IACDX,OAAO,EAAEA,CAAChD,KAAe,EAAEf,EAAE,EAAEgE,OAAO,KAAK;MACzC;MACA,IAAIA,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEU,YAAY,EAAE;QACzBpE,WAAW,CAACoD,YAAY,CAAClE,cAAc,CAACG,IAAI,CAACC,OAAO,EAAEC,UAAU,CAAC,EAAEmE,OAAO,CAACU,YAAY,CAAC;MAC1F;MACArF,KAAK,CAAC0B,KAAK,CAAC,gCAAgCA,KAAK,CAACkD,OAAO,EAAE,CAAC;IAC9D,CAAC;IACDC,SAAS,EAAEA,CAAA,KAAM;MACf5D,WAAW,CAAC+D,iBAAiB,CAAC;QAAElD,QAAQ,EAAE3B,cAAc,CAACE,KAAK,CAAC;MAAE,CAAC,CAAC;MACnEL,KAAK,CAACiF,OAAO,CAAC,iCAAiC,CAAC;IAClD;EACF,CAAC,CAAC;;EAEF;EACA,MAAMS,cAAc,GAAG5F,WAAW,CAAC;IACjC6C,UAAU,EAAEA,CAAC;MAAEhC,EAAE;MAAE6C,MAAM;MAAElC;IAA2D,CAAC,KACrFrB,cAAc,CAAC0F,sBAAsB,CAAChF,EAAE,EAAE6C,MAAM,EAAElC,IAAI,CAAC;IACzDuB,QAAQ,EAAE,MAAAA,CAAO;MAAElC,EAAE;MAAE6C;IAAO,CAAC,KAAK;MAClC,MAAMvC,WAAW,CAAC8B,aAAa,CAAC;QAAEjB,QAAQ,EAAE3B,cAAc,CAACE,KAAK,CAAC;MAAE,CAAC,CAAC;MACrE,MAAMY,WAAW,CAAC8B,aAAa,CAAC;QAAEjB,QAAQ,EAAE3B,cAAc,CAACO,MAAM,CAACC,EAAE;MAAE,CAAC,CAAC;MAExE,MAAM0E,YAAY,GAAGpE,WAAW,CAACgC,YAAY,CAAC9C,cAAc,CAACG,IAAI,CAACC,OAAO,EAAEC,UAAU,CAAC,CAAC;MACvF,MAAMoF,cAAc,GAAG3E,WAAW,CAACgC,YAAY,CAAC9C,cAAc,CAACO,MAAM,CAACC,EAAE,CAAC,CAAC;;MAE1E;MACA,MAAMkF,YAAY,GAAIP,GAAe,KAAM;QACzC,GAAGA,GAAG;QACN9B,MAAM;QACNG,SAAS,EAAE,IAAIR,IAAI,CAAC,CAAC,CAACO,WAAW,CAAC;MACpC,CAAC,CAAC;MAEFzC,WAAW,CAACoD,YAAY,CACtBlE,cAAc,CAACG,IAAI,CAACC,OAAO,EAAEC,UAAU,CAAC,EACvC8D,GAA8C,IAAK;QAClD,IAAI,CAACA,GAAG,EAAE,OAAOA,GAAG;QACpB,OAAO;UACL,GAAGA,GAAG;UACNhD,IAAI,EAAEgD,GAAG,CAAChD,IAAI,CAACwC,GAAG,CAACwB,GAAG,IAAIA,GAAG,CAAC3E,EAAE,KAAKA,EAAE,GAAGkF,YAAY,CAACP,GAAG,CAAC,GAAGA,GAAG;QACnE,CAAC;MACH,CACF,CAAC;MAEDrE,WAAW,CAACoD,YAAY,CAAClE,cAAc,CAACO,MAAM,CAACC,EAAE,CAAC,EAAG2D,GAAwC,IAAK;QAChG,IAAI,CAACA,GAAG,EAAE,OAAOA,GAAG;QACpB,OAAO;UACL,GAAGA,GAAG;UACNhD,IAAI,EAAEuE,YAAY,CAACvB,GAAG,CAAChD,IAAI;QAC7B,CAAC;MACH,CAAC,CAAC;MAEF,OAAO;QAAE+D,YAAY;QAAEO;MAAe,CAAC;IACzC,CAAC;IACDlB,OAAO,EAAEA,CAAChD,KAAe,EAAE;MAAEf;IAAG,CAAC,EAAEgE,OAAO,KAAK;MAC7C;MACA,IAAIA,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEU,YAAY,EAAE;QACzBpE,WAAW,CAACoD,YAAY,CAAClE,cAAc,CAACG,IAAI,CAACC,OAAO,EAAEC,UAAU,CAAC,EAAEmE,OAAO,CAACU,YAAY,CAAC;MAC1F;MACA,IAAIV,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEiB,cAAc,EAAE;QAC3B3E,WAAW,CAACoD,YAAY,CAAClE,cAAc,CAACO,MAAM,CAACC,EAAE,CAAC,EAAEgE,OAAO,CAACiB,cAAc,CAAC;MAC7E;MACA5F,KAAK,CAAC0B,KAAK,CAAC,4BAA4BA,KAAK,CAACkD,OAAO,EAAE,CAAC;IAC1D,CAAC;IACDC,SAAS,EAAEA,CAACC,QAAQ,EAAE;MAAEtB;IAAO,CAAC,KAAK;MACnCvC,WAAW,CAAC+D,iBAAiB,CAAC;QAAElD,QAAQ,EAAE3B,cAAc,CAACE,KAAK,CAAC;MAAE,CAAC,CAAC;MACnEL,KAAK,CAACiF,OAAO,CAAC,cAAczB,MAAM,CAACsC,WAAW,CAAC,CAAC,eAAe,CAAC;IAClE;EACF,CAAC,CAAC;;EAEF;EACA,MAAMC,iBAAiB,GAAGjG,WAAW,CAAC;IACpC6C,UAAU,EAAGhC,EAAU,IAAKV,cAAc,CAAC+F,mBAAmB,CAACrF,EAAE,CAAC;IAClEkE,SAAS,EAAEA,CAAA,KAAM;MACf5D,WAAW,CAAC+D,iBAAiB,CAAC;QAAElD,QAAQ,EAAE3B,cAAc,CAACE,KAAK,CAAC;MAAE,CAAC,CAAC;MACnEL,KAAK,CAACiF,OAAO,CAAC,oCAAoC,CAAC;IACrD,CAAC;IACDP,OAAO,EAAGhD,KAAe,IAAK;MAC5B1B,KAAK,CAAC0B,KAAK,CAAC,mCAAmCA,KAAK,CAACkD,OAAO,EAAE,CAAC;IACjE;EACF,CAAC,CAAC;;EAEF;EACA,MAAMhC,gBAAgB,GAAGjD,WAAW,CAClC,MAAO2B,IAA0B,IAA0B;IACzD,MAAM2E,MAAM,GAAG,MAAMvD,cAAc,CAACwD,WAAW,CAAC5E,IAAI,CAAC;IACrD,OAAO2E,MAAM,CAAC3E,IAAI;EACpB,CAAC,EACD,CAACoB,cAAc,CACjB,CAAC;EAED,MAAMyC,gBAAgB,GAAGxF,WAAW,CAClC,OAAOgB,EAAU,EAAEW,IAA0B,KAA0B;IACrE,MAAM2E,MAAM,GAAG,MAAMf,cAAc,CAACgB,WAAW,CAAC;MAAEvF,EAAE;MAAEW;IAAK,CAAC,CAAC;IAC7D,OAAO2E,MAAM,CAAC3E,IAAI;EACpB,CAAC,EACD,CAAC4D,cAAc,CACjB,CAAC;EAED,MAAMM,gBAAgB,GAAG7F,WAAW,CAClC,MAAOgB,EAAU,IAAoB;IACnC,MAAM4E,cAAc,CAACW,WAAW,CAACvF,EAAE,CAAC;EACtC,CAAC,EACD,CAAC4E,cAAc,CACjB,CAAC;EAED,MAAMY,YAAY,GAAGxG,WAAW,CAC9B,OAAOgB,EAAU,EAAE6C,MAAwB,EAAElC,IAAU,KAA0B;IAC/E,MAAM2E,MAAM,GAAG,MAAMP,cAAc,CAACQ,WAAW,CAAC;MAAEvF,EAAE;MAAE6C,MAAM;MAAElC;IAAK,CAAC,CAAC;IACrE,OAAO2E,MAAM,CAAC3E,IAAI;EACpB,CAAC,EACD,CAACoE,cAAc,CACjB,CAAC;EAED,MAAMM,mBAAmB,GAAGrG,WAAW,CACrC,MAAOgB,EAAU,IAA0B;IACzC,MAAMsF,MAAM,GAAG,MAAMF,iBAAiB,CAACG,WAAW,CAACvF,EAAE,CAAC;IACtD,OAAOsF,MAAM,CAAC3E,IAAI;EACpB,CAAC,EACD,CAACyE,iBAAiB,CACpB,CAAC;EAED,OAAO;IACL;IACAvD,WAAW;IACXhC,UAAU,EAAEiC,eAAe;IAE3B;IACAjB,SAAS;IACTI,UAAU;IACVH,OAAO;IACPC,KAAK,EAAEA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkD,OAAO;IACrBwB,cAAc;IAEd;IACAC,UAAU,EAAE3D,cAAc,CAAClB,SAAS;IACpC8E,UAAU,EAAEpB,cAAc,CAAC1D,SAAS;IACpC+E,UAAU,EAAEhB,cAAc,CAAC/D,SAAS;IACpCgF,gBAAgB,EAAEd,cAAc,CAAClE,SAAS;IAC1CiF,aAAa,EAAEV,iBAAiB,CAACvE,SAAS;IAE1C;IACAoB,gBAAgB;IAChBuC,gBAAgB;IAChBK,gBAAgB;IAChBW,YAAY;IACZH,mBAAmB;IACnBrE,OAAO;IAEP;IACAe,cAAc;IACdwC,cAAc;IACdK,cAAc;IACdG,cAAc;IACdK;EACF,CAAC;AACH,CAAC;AAAC/E,EAAA,CAxVWH,cAAc;EAAA,QAILd,cAAc,EAC+CG,oBAAoB,EAWjGL,QAAQ,EAeWC,WAAW,EA+EXA,WAAW,EAuDXA,WAAW,EAuCXA,WAAW,EAuDRA,WAAW;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
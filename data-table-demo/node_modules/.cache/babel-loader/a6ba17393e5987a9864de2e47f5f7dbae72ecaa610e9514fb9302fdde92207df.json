{"ast": null, "code": "// API service layer for experiment management\n\n// Base API configuration\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';\nconst API_TIMEOUT = 10000; // 10 seconds\n\n// Custom API Error class\nclass ApiError extends Error {\n  constructor(message, code, statusCode, details, fieldErrors) {\n    super(message);\n    this.code = void 0;\n    this.statusCode = void 0;\n    this.details = void 0;\n    this.fieldErrors = void 0;\n    this.name = 'ApiError';\n    this.code = code;\n    this.statusCode = statusCode;\n    this.details = details;\n    this.fieldErrors = fieldErrors;\n  }\n}\n\n// Custom fetch wrapper with error handling\nclass ApiClient {\n  constructor(baseURL, timeout = API_TIMEOUT) {\n    this.baseURL = void 0;\n    this.timeout = void 0;\n    this.baseURL = baseURL;\n    this.timeout = timeout;\n  }\n  async request(endpoint, options = {}) {\n    const url = `${this.baseURL}${endpoint}`;\n\n    // Create abort controller for timeout\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), this.timeout);\n    try {\n      const response = await fetch(url, {\n        ...options,\n        signal: controller.signal,\n        headers: {\n          'Content-Type': 'application/json',\n          ...options.headers\n        }\n      });\n      clearTimeout(timeoutId);\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        throw new ApiError(errorData.message || `HTTP ${response.status}: ${response.statusText}`, errorData.code || 'HTTP_ERROR', response.status, errorData.details, errorData.fieldErrors);\n      }\n      return await response.json();\n    } catch (error) {\n      clearTimeout(timeoutId);\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      if ((error === null || error === void 0 ? void 0 : error.name) === 'AbortError') {\n        throw new ApiError('Request timeout', 'TIMEOUT_ERROR', 408);\n      }\n      throw new ApiError((error === null || error === void 0 ? void 0 : error.message) || 'Network error', 'NETWORK_ERROR', 0);\n    }\n  }\n  async get(endpoint, params) {\n    const url = new URL(endpoint, this.baseURL);\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined && value !== null) {\n          url.searchParams.append(key, String(value));\n        }\n      });\n    }\n    return this.request(url.pathname + url.search);\n  }\n  async post(endpoint, data) {\n    return this.request(endpoint, {\n      method: 'POST',\n      body: data ? JSON.stringify(data) : undefined\n    });\n  }\n  async put(endpoint, data) {\n    return this.request(endpoint, {\n      method: 'PUT',\n      body: data ? JSON.stringify(data) : undefined\n    });\n  }\n  async patch(endpoint, data) {\n    return this.request(endpoint, {\n      method: 'PATCH',\n      body: data ? JSON.stringify(data) : undefined\n    });\n  }\n  async delete(endpoint) {\n    return this.request(endpoint, {\n      method: 'DELETE'\n    });\n  }\n}\n\n// Create API client instance\nconst apiClient = new ApiClient(API_BASE_URL);\n\n// Experiments API service\nexport const experimentsApi = {\n  // Get experiments with filters and pagination\n  async getExperiments(filters = {}, pagination = {\n    page: 1,\n    limit: 20\n  }) {\n    var _filters$status, _filters$tags, _filters$createdBy;\n    const params = {\n      ...pagination,\n      ...filters,\n      // Convert arrays to comma-separated strings for URL params\n      status: (_filters$status = filters.status) === null || _filters$status === void 0 ? void 0 : _filters$status.join(','),\n      tags: (_filters$tags = filters.tags) === null || _filters$tags === void 0 ? void 0 : _filters$tags.join(','),\n      createdBy: (_filters$createdBy = filters.createdBy) === null || _filters$createdBy === void 0 ? void 0 : _filters$createdBy.join(',')\n    };\n    return apiClient.get('/experiments', params);\n  },\n  // Get single experiment by ID\n  async getExperiment(id) {\n    return apiClient.get(`/experiments/${id}`);\n  },\n  // Create new experiment\n  async createExperiment(data) {\n    return apiClient.post('/experiments', data);\n  },\n  // Update experiment\n  async updateExperiment(id, data) {\n    return apiClient.put(`/experiments/${id}`, data);\n  },\n  // Update experiment status\n  async updateExperimentStatus(id, status, data) {\n    return apiClient.patch(`/experiments/${id}/status`, {\n      status,\n      ...data\n    });\n  },\n  // Delete experiment\n  async deleteExperiment(id) {\n    return apiClient.delete(`/experiments/${id}`);\n  },\n  // Duplicate experiment\n  async duplicateExperiment(id) {\n    return apiClient.post(`/experiments/${id}/duplicate`);\n  },\n  // Get experiment analytics\n  async getExperimentAnalytics(id) {\n    return apiClient.get(`/experiments/${id}/analytics`);\n  },\n  // Bulk operations\n  async bulkUpdateStatus(ids, status) {\n    return apiClient.patch('/experiments/bulk/status', {\n      ids,\n      status\n    });\n  },\n  async bulkDelete(ids) {\n    return apiClient.request('/experiments/bulk', {\n      method: 'DELETE',\n      body: JSON.stringify({\n        ids\n      })\n    });\n  }\n};\n\n// Mock data for development\nconst mockExperiments = [{\n  id: '1',\n  tenantId: 'tenant-1',\n  name: 'Homepage Button Color Test',\n  description: 'Testing different button colors to improve conversion rates',\n  status: 'ACTIVE',\n  startDate: '2024-01-15T00:00:00Z',\n  endDate: '2024-02-15T00:00:00Z',\n  createdAt: '2024-01-10T00:00:00Z',\n  updatedAt: '2024-01-15T00:00:00Z',\n  createdBy: 'user-1',\n  variants: [{\n    id: 'v1',\n    name: 'Control (Blue)',\n    trafficWeight: 0.5\n  }, {\n    id: 'v2',\n    name: 'Treatment (Green)',\n    trafficWeight: 0.5\n  }],\n  tags: ['homepage', 'ui', 'conversion'],\n  _count: {\n    userAssignments: 1250,\n    events: 3420\n  }\n}, {\n  id: '2',\n  tenantId: 'tenant-1',\n  name: 'Checkout Flow Optimization',\n  description: 'Testing a simplified checkout process',\n  status: 'DRAFT',\n  createdAt: '2024-01-12T00:00:00Z',\n  updatedAt: '2024-01-12T00:00:00Z',\n  createdBy: 'user-2',\n  variants: [{\n    id: 'v3',\n    name: 'Current Flow',\n    trafficWeight: 0.5\n  }, {\n    id: 'v4',\n    name: 'Simplified Flow',\n    trafficWeight: 0.5\n  }],\n  tags: ['checkout', 'ux', 'conversion'],\n  _count: {\n    userAssignments: 0,\n    events: 0\n  }\n}, {\n  id: '3',\n  tenantId: 'tenant-1',\n  name: 'Email Subject Line Test',\n  description: 'Testing different email subject lines for open rates',\n  status: 'COMPLETED',\n  startDate: '2024-01-01T00:00:00Z',\n  endDate: '2024-01-20T00:00:00Z',\n  createdAt: '2024-01-01T00:00:00Z',\n  updatedAt: '2024-01-20T00:00:00Z',\n  createdBy: 'user-1',\n  variants: [{\n    id: 'v5',\n    name: 'Original Subject',\n    trafficWeight: 0.33\n  }, {\n    id: 'v6',\n    name: 'Personalized Subject',\n    trafficWeight: 0.33\n  }, {\n    id: 'v7',\n    name: 'Urgent Subject',\n    trafficWeight: 0.34\n  }],\n  tags: ['email', 'marketing', 'engagement'],\n  _count: {\n    userAssignments: 5000,\n    events: 12500\n  }\n}, {\n  id: '4',\n  tenantId: 'tenant-1',\n  name: 'Product Page Layout',\n  description: 'Testing different product page layouts',\n  status: 'PAUSED',\n  startDate: '2024-01-08T00:00:00Z',\n  createdAt: '2024-01-05T00:00:00Z',\n  updatedAt: '2024-01-08T00:00:00Z',\n  createdBy: 'user-3',\n  variants: [{\n    id: 'v8',\n    name: 'Current Layout',\n    trafficWeight: 0.5\n  }, {\n    id: 'v9',\n    name: 'New Layout',\n    trafficWeight: 0.5\n  }],\n  tags: ['product', 'layout', 'ui'],\n  _count: {\n    userAssignments: 800,\n    events: 2100\n  }\n}, {\n  id: '5',\n  tenantId: 'tenant-2',\n  name: 'Pricing Page Test',\n  description: 'Testing different pricing displays',\n  status: 'ACTIVE',\n  startDate: '2024-01-20T00:00:00Z',\n  createdAt: '2024-01-18T00:00:00Z',\n  updatedAt: '2024-01-20T00:00:00Z',\n  createdBy: 'user-4',\n  variants: [{\n    id: 'v10',\n    name: 'Monthly Focus',\n    trafficWeight: 0.5\n  }, {\n    id: 'v11',\n    name: 'Annual Focus',\n    trafficWeight: 0.5\n  }],\n  tags: ['pricing', 'conversion', 'revenue'],\n  _count: {\n    userAssignments: 600,\n    events: 1800\n  }\n}];\n\n// Mock API for development (when backend is not available)\nexport const mockExperimentsApi = {\n  async getExperiments(filters = {}, pagination = {\n    page: 1,\n    limit: 20\n  }) {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 500));\n    let filtered = [...mockExperiments];\n\n    // Apply filters\n    if (filters.search) {\n      const searchLower = filters.search.toLowerCase();\n      filtered = filtered.filter(exp => {\n        var _exp$description;\n        return exp.name.toLowerCase().includes(searchLower) || ((_exp$description = exp.description) === null || _exp$description === void 0 ? void 0 : _exp$description.toLowerCase().includes(searchLower)) || exp.tags.some(tag => tag.toLowerCase().includes(searchLower));\n      });\n    }\n    if (filters.status && filters.status.length > 0) {\n      filtered = filtered.filter(exp => filters.status.includes(exp.status));\n    }\n    if (filters.tags && filters.tags.length > 0) {\n      filtered = filtered.filter(exp => filters.tags.some(tag => exp.tags.includes(tag)));\n    }\n\n    // Apply sorting\n    if (pagination.sortBy) {\n      filtered.sort((a, b) => {\n        let aValue, bValue;\n        switch (pagination.sortBy) {\n          case 'variantCount':\n            aValue = a.variants.length;\n            bValue = b.variants.length;\n            break;\n          case 'assignmentCount':\n            aValue = a._count.userAssignments;\n            bValue = b._count.userAssignments;\n            break;\n          case 'eventCount':\n            aValue = a._count.events;\n            bValue = b._count.events;\n            break;\n          default:\n            aValue = pagination.sortBy ? a[pagination.sortBy] : a.createdAt;\n            bValue = pagination.sortBy ? b[pagination.sortBy] : b.createdAt;\n        }\n        if (aValue === bValue) return 0;\n        const comparison = aValue < bValue ? -1 : 1;\n        return pagination.sortOrder === 'desc' ? -comparison : comparison;\n      });\n    }\n\n    // Apply pagination\n    const total = filtered.length;\n    const totalPages = Math.ceil(total / pagination.limit);\n    const startIndex = (pagination.page - 1) * pagination.limit;\n    const paginatedData = filtered.slice(startIndex, startIndex + pagination.limit);\n    return {\n      data: paginatedData,\n      pagination: {\n        page: pagination.page,\n        limit: pagination.limit,\n        total,\n        totalPages\n      }\n    };\n  },\n  async getExperiment(id) {\n    await new Promise(resolve => setTimeout(resolve, 300));\n    const experiment = mockExperiments.find(exp => exp.id === id);\n    if (!experiment) {\n      throw new ApiError('Experiment not found', 'NOT_FOUND', 404);\n    }\n    return {\n      data: experiment,\n      success: true\n    };\n  },\n  async createExperiment(data) {\n    await new Promise(resolve => setTimeout(resolve, 800));\n    const newExperiment = {\n      id: `exp-${Date.now()}`,\n      tenantId: 'tenant-1',\n      name: data.name,\n      description: data.description,\n      status: 'DRAFT',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n      createdBy: 'current-user',\n      variants: data.variants.map((v, i) => ({\n        ...v,\n        id: `var-${Date.now()}-${i}`\n      })),\n      tags: data.tags || [],\n      _count: {\n        userAssignments: 0,\n        events: 0\n      }\n    };\n    mockExperiments.unshift(newExperiment);\n    return {\n      data: newExperiment,\n      success: true,\n      message: 'Experiment created successfully'\n    };\n  },\n  async updateExperiment(id, data) {\n    await new Promise(resolve => setTimeout(resolve, 600));\n    const index = mockExperiments.findIndex(exp => exp.id === id);\n    if (index === -1) {\n      throw new ApiError('Experiment not found', 'NOT_FOUND', 404);\n    }\n    const updatedExperiment = {\n      ...mockExperiments[index],\n      ...data,\n      updatedAt: new Date().toISOString(),\n      variants: data.variants ? data.variants.map((v, i) => ({\n        ...v,\n        id: v.id || `var-${Date.now()}-${i}`\n      })) : mockExperiments[index].variants\n    };\n    mockExperiments[index] = updatedExperiment;\n    return {\n      data: updatedExperiment,\n      success: true,\n      message: 'Experiment updated successfully'\n    };\n  },\n  async updateExperimentStatus(id, status, data) {\n    await new Promise(resolve => setTimeout(resolve, 400));\n    const index = mockExperiments.findIndex(exp => exp.id === id);\n    if (index === -1) {\n      throw new ApiError('Experiment not found', 'NOT_FOUND', 404);\n    }\n    const updatedExperiment = {\n      ...mockExperiments[index],\n      status,\n      updatedAt: new Date().toISOString(),\n      ...(status === 'ACTIVE' && !mockExperiments[index].startDate && {\n        startDate: new Date().toISOString()\n      }),\n      ...(status === 'COMPLETED' && !mockExperiments[index].endDate && {\n        endDate: new Date().toISOString()\n      })\n    };\n    mockExperiments[index] = updatedExperiment;\n    return {\n      data: updatedExperiment,\n      success: true,\n      message: `Experiment ${status.toLowerCase()} successfully`\n    };\n  },\n  async deleteExperiment(id) {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const index = mockExperiments.findIndex(exp => exp.id === id);\n    if (index === -1) {\n      throw new ApiError('Experiment not found', 'NOT_FOUND', 404);\n    }\n    mockExperiments.splice(index, 1);\n    return {\n      data: undefined,\n      success: true,\n      message: 'Experiment deleted successfully'\n    };\n  },\n  async duplicateExperiment(id) {\n    await new Promise(resolve => setTimeout(resolve, 700));\n    const original = mockExperiments.find(exp => exp.id === id);\n    if (!original) {\n      throw new ApiError('Experiment not found', 'NOT_FOUND', 404);\n    }\n    const duplicated = {\n      ...original,\n      id: `exp-${Date.now()}`,\n      name: `${original.name} (Copy)`,\n      status: 'DRAFT',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n      startDate: undefined,\n      endDate: undefined,\n      variants: original.variants.map((v, i) => ({\n        ...v,\n        id: `var-${Date.now()}-${i}`\n      })),\n      _count: {\n        userAssignments: 0,\n        events: 0\n      }\n    };\n    mockExperiments.unshift(duplicated);\n    return {\n      data: duplicated,\n      success: true,\n      message: 'Experiment duplicated successfully'\n    };\n  },\n  async getExperimentAnalytics(id) {\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    const experiment = mockExperiments.find(exp => exp.id === id);\n    if (!experiment) {\n      throw new ApiError('Experiment not found', 'NOT_FOUND', 404);\n    }\n\n    // Mock analytics data\n    const analytics = {\n      experimentId: id,\n      totalAssignments: experiment._count.userAssignments,\n      totalEvents: experiment._count.events,\n      conversionRate: experiment._count.userAssignments > 0 ? experiment._count.events / experiment._count.userAssignments * 100 : 0,\n      variantPerformance: experiment.variants.map(variant => ({\n        variantId: variant.id,\n        variantName: variant.name,\n        assignments: Math.floor(experiment._count.userAssignments * variant.trafficWeight),\n        events: Math.floor(experiment._count.events * variant.trafficWeight),\n        conversionRate: Math.random() * 10 + 2,\n        // Mock conversion rate\n        confidence: Math.random() * 30 + 70 // Mock confidence\n      })),\n      timeSeriesData: Array.from({\n        length: 30\n      }, (_, i) => ({\n        date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],\n        assignments: Math.floor(Math.random() * 100) + 10,\n        events: Math.floor(Math.random() * 50) + 5,\n        conversionRate: Math.random() * 5 + 2\n      })),\n      statisticalSignificance: {\n        isSignificant: Math.random() > 0.5,\n        confidence: Math.random() * 20 + 80,\n        pValue: Math.random() * 0.05,\n        winningVariant: experiment.variants[Math.floor(Math.random() * experiment.variants.length)].id\n      }\n    };\n    return {\n      data: analytics,\n      success: true\n    };\n  },\n  async bulkUpdateStatus(ids, status) {\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    const updatedExperiments = [];\n    for (const id of ids) {\n      const index = mockExperiments.findIndex(exp => exp.id === id);\n      if (index !== -1) {\n        const updatedExperiment = {\n          ...mockExperiments[index],\n          status,\n          updatedAt: new Date().toISOString()\n        };\n        mockExperiments[index] = updatedExperiment;\n        updatedExperiments.push(updatedExperiment);\n      }\n    }\n    return {\n      data: updatedExperiments,\n      success: true,\n      message: `${updatedExperiments.length} experiments updated successfully`\n    };\n  },\n  async bulkDelete(ids) {\n    await new Promise(resolve => setTimeout(resolve, 800));\n    let deletedCount = 0;\n    for (const id of ids) {\n      const index = mockExperiments.findIndex(exp => exp.id === id);\n      if (index !== -1) {\n        mockExperiments.splice(index, 1);\n        deletedCount++;\n      }\n    }\n    return {\n      data: undefined,\n      success: true,\n      message: `${deletedCount} experiments deleted successfully`\n    };\n  }\n};\n\n// Export the appropriate API based on environment\nexport const api = process.env.NODE_ENV === 'development' && !process.env.REACT_APP_USE_REAL_API ? mockExperimentsApi : experimentsApi;", "map": {"version": 3, "names": ["API_BASE_URL", "process", "env", "REACT_APP_API_URL", "API_TIMEOUT", "ApiError", "Error", "constructor", "message", "code", "statusCode", "details", "fieldErrors", "name", "ApiClient", "baseURL", "timeout", "request", "endpoint", "options", "url", "controller", "AbortController", "timeoutId", "setTimeout", "abort", "response", "fetch", "signal", "headers", "clearTimeout", "ok", "errorData", "json", "catch", "status", "statusText", "error", "get", "params", "URL", "Object", "entries", "for<PERSON>ach", "key", "value", "undefined", "searchParams", "append", "String", "pathname", "search", "post", "data", "method", "body", "JSON", "stringify", "put", "patch", "delete", "apiClient", "experimentsApi", "getExperiments", "filters", "pagination", "page", "limit", "_filters$status", "_filters$tags", "_filters$createdBy", "join", "tags", "created<PERSON>y", "getExperiment", "id", "createExperiment", "updateExperiment", "updateExperimentStatus", "deleteExperiment", "duplicateExperiment", "getExperimentAnalytics", "bulkUpdateStatus", "ids", "bulkDelete", "mockExperiments", "tenantId", "description", "startDate", "endDate", "createdAt", "updatedAt", "variants", "trafficWeight", "_count", "userAssignments", "events", "mockExperimentsApi", "Promise", "resolve", "filtered", "searchLower", "toLowerCase", "filter", "exp", "_exp$description", "includes", "some", "tag", "length", "sortBy", "sort", "a", "b", "aValue", "bValue", "comparison", "sortOrder", "total", "totalPages", "Math", "ceil", "startIndex", "paginatedData", "slice", "experiment", "find", "success", "newExperiment", "Date", "now", "toISOString", "map", "v", "i", "unshift", "index", "findIndex", "updatedExperiment", "splice", "original", "duplicated", "analytics", "experimentId", "totalAssignments", "totalEvents", "conversionRate", "variantPerformance", "variant", "variantId", "variantName", "assignments", "floor", "random", "confidence", "timeSeriesData", "Array", "from", "_", "date", "split", "statisticalSignificance", "isSignificant", "pValue", "winning<PERSON><PERSON><PERSON>", "updatedExperiments", "push", "deletedCount", "api", "NODE_ENV", "REACT_APP_USE_REAL_API"], "sources": ["/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/src/services/api.ts"], "sourcesContent": ["// API service layer for experiment management\nimport {\n  Experiment,\n  ExperimentFilters,\n  PaginationParams,\n  PaginatedResponse,\n  ApiResponse,\n  CreateExperimentForm,\n  UpdateExperimentForm,\n  ExperimentStatus,\n  ExperimentAnalytics,\n} from '../types/experiment';\n\n// Base API configuration\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';\nconst API_TIMEOUT = 10000; // 10 seconds\n\n// Custom API Error class\nclass ApiError extends Error {\n  public code: string;\n  public statusCode: number;\n  public details?: any;\n  public fieldErrors?: any[];\n\n  constructor(message: string, code: string, statusCode: number, details?: any, fieldErrors?: any[]) {\n    super(message);\n    this.name = 'ApiError';\n    this.code = code;\n    this.statusCode = statusCode;\n    this.details = details;\n    this.fieldErrors = fieldErrors;\n  }\n}\n\n// Custom fetch wrapper with error handling\nclass ApiClient {\n  private baseURL: string;\n  private timeout: number;\n\n  constructor(baseURL: string, timeout: number = API_TIMEOUT) {\n    this.baseURL = baseURL;\n    this.timeout = timeout;\n  }\n\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<T> {\n    const url = `${this.baseURL}${endpoint}`;\n    \n    // Create abort controller for timeout\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), this.timeout);\n\n    try {\n      const response = await fetch(url, {\n        ...options,\n        signal: controller.signal,\n        headers: {\n          'Content-Type': 'application/json',\n          ...options.headers,\n        },\n      });\n\n      clearTimeout(timeoutId);\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        throw new ApiError(\n          errorData.message || `HTTP ${response.status}: ${response.statusText}`,\n          errorData.code || 'HTTP_ERROR',\n          response.status,\n          errorData.details,\n          errorData.fieldErrors\n        );\n      }\n\n      return await response.json();\n    } catch (error) {\n      clearTimeout(timeoutId);\n      \n      if (error instanceof ApiError) {\n        throw error;\n      }\n\n      if ((error as any)?.name === 'AbortError') {\n        throw new ApiError(\n          'Request timeout',\n          'TIMEOUT_ERROR',\n          408\n        );\n      }\n\n      throw new ApiError(\n        (error as any)?.message || 'Network error',\n        'NETWORK_ERROR',\n        0\n      );\n    }\n  }\n\n  async get<T>(endpoint: string, params?: Record<string, any>): Promise<T> {\n    const url = new URL(endpoint, this.baseURL);\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined && value !== null) {\n          url.searchParams.append(key, String(value));\n        }\n      });\n    }\n\n    return this.request<T>(url.pathname + url.search);\n  }\n\n  async post<T>(endpoint: string, data?: any): Promise<T> {\n    return this.request<T>(endpoint, {\n      method: 'POST',\n      body: data ? JSON.stringify(data) : undefined,\n    });\n  }\n\n  async put<T>(endpoint: string, data?: any): Promise<T> {\n    return this.request<T>(endpoint, {\n      method: 'PUT',\n      body: data ? JSON.stringify(data) : undefined,\n    });\n  }\n\n  async patch<T>(endpoint: string, data?: any): Promise<T> {\n    return this.request<T>(endpoint, {\n      method: 'PATCH',\n      body: data ? JSON.stringify(data) : undefined,\n    });\n  }\n\n  async delete<T>(endpoint: string): Promise<T> {\n    return this.request<T>(endpoint, {\n      method: 'DELETE',\n    });\n  }\n}\n\n// Create API client instance\nconst apiClient = new ApiClient(API_BASE_URL);\n\n// Experiments API service\nexport const experimentsApi = {\n  // Get experiments with filters and pagination\n  async getExperiments(\n    filters: ExperimentFilters = {},\n    pagination: PaginationParams = { page: 1, limit: 20 }\n  ): Promise<PaginatedResponse<Experiment>> {\n    const params = {\n      ...pagination,\n      ...filters,\n      // Convert arrays to comma-separated strings for URL params\n      status: filters.status?.join(','),\n      tags: filters.tags?.join(','),\n      createdBy: filters.createdBy?.join(','),\n    };\n\n    return apiClient.get<PaginatedResponse<Experiment>>('/experiments', params);\n  },\n\n  // Get single experiment by ID\n  async getExperiment(id: string): Promise<ApiResponse<Experiment>> {\n    return apiClient.get<ApiResponse<Experiment>>(`/experiments/${id}`);\n  },\n\n  // Create new experiment\n  async createExperiment(data: CreateExperimentForm): Promise<ApiResponse<Experiment>> {\n    return apiClient.post<ApiResponse<Experiment>>('/experiments', data);\n  },\n\n  // Update experiment\n  async updateExperiment(id: string, data: UpdateExperimentForm): Promise<ApiResponse<Experiment>> {\n    return apiClient.put<ApiResponse<Experiment>>(`/experiments/${id}`, data);\n  },\n\n  // Update experiment status\n  async updateExperimentStatus(\n    id: string,\n    status: ExperimentStatus,\n    data?: any\n  ): Promise<ApiResponse<Experiment>> {\n    return apiClient.patch<ApiResponse<Experiment>>(`/experiments/${id}/status`, {\n      status,\n      ...data,\n    });\n  },\n\n  // Delete experiment\n  async deleteExperiment(id: string): Promise<ApiResponse<void>> {\n    return apiClient.delete<ApiResponse<void>>(`/experiments/${id}`);\n  },\n\n  // Duplicate experiment\n  async duplicateExperiment(id: string): Promise<ApiResponse<Experiment>> {\n    return apiClient.post<ApiResponse<Experiment>>(`/experiments/${id}/duplicate`);\n  },\n\n  // Get experiment analytics\n  async getExperimentAnalytics(id: string): Promise<ApiResponse<ExperimentAnalytics>> {\n    return apiClient.get<ApiResponse<ExperimentAnalytics>>(`/experiments/${id}/analytics`);\n  },\n\n  // Bulk operations\n  async bulkUpdateStatus(\n    ids: string[],\n    status: ExperimentStatus\n  ): Promise<ApiResponse<Experiment[]>> {\n    return apiClient.patch<ApiResponse<Experiment[]>>('/experiments/bulk/status', {\n      ids,\n      status,\n    });\n  },\n\n  async bulkDelete(ids: string[]): Promise<ApiResponse<void>> {\n    return apiClient.request<ApiResponse<void>>('/experiments/bulk', {\n      method: 'DELETE',\n      body: JSON.stringify({ ids }),\n    });\n  },\n};\n\n// Mock data for development\nconst mockExperiments: Experiment[] = [\n  {\n    id: '1',\n    tenantId: 'tenant-1',\n    name: 'Homepage Button Color Test',\n    description: 'Testing different button colors to improve conversion rates',\n    status: 'ACTIVE',\n    startDate: '2024-01-15T00:00:00Z',\n    endDate: '2024-02-15T00:00:00Z',\n    createdAt: '2024-01-10T00:00:00Z',\n    updatedAt: '2024-01-15T00:00:00Z',\n    createdBy: 'user-1',\n    variants: [\n      { id: 'v1', name: 'Control (Blue)', trafficWeight: 0.5 },\n      { id: 'v2', name: 'Treatment (Green)', trafficWeight: 0.5 }\n    ],\n    tags: ['homepage', 'ui', 'conversion'],\n    _count: { userAssignments: 1250, events: 3420 }\n  },\n  {\n    id: '2',\n    tenantId: 'tenant-1',\n    name: 'Checkout Flow Optimization',\n    description: 'Testing a simplified checkout process',\n    status: 'DRAFT',\n    createdAt: '2024-01-12T00:00:00Z',\n    updatedAt: '2024-01-12T00:00:00Z',\n    createdBy: 'user-2',\n    variants: [\n      { id: 'v3', name: 'Current Flow', trafficWeight: 0.5 },\n      { id: 'v4', name: 'Simplified Flow', trafficWeight: 0.5 }\n    ],\n    tags: ['checkout', 'ux', 'conversion'],\n    _count: { userAssignments: 0, events: 0 }\n  },\n  {\n    id: '3',\n    tenantId: 'tenant-1',\n    name: 'Email Subject Line Test',\n    description: 'Testing different email subject lines for open rates',\n    status: 'COMPLETED',\n    startDate: '2024-01-01T00:00:00Z',\n    endDate: '2024-01-20T00:00:00Z',\n    createdAt: '2024-01-01T00:00:00Z',\n    updatedAt: '2024-01-20T00:00:00Z',\n    createdBy: 'user-1',\n    variants: [\n      { id: 'v5', name: 'Original Subject', trafficWeight: 0.33 },\n      { id: 'v6', name: 'Personalized Subject', trafficWeight: 0.33 },\n      { id: 'v7', name: 'Urgent Subject', trafficWeight: 0.34 }\n    ],\n    tags: ['email', 'marketing', 'engagement'],\n    _count: { userAssignments: 5000, events: 12500 }\n  },\n  {\n    id: '4',\n    tenantId: 'tenant-1',\n    name: 'Product Page Layout',\n    description: 'Testing different product page layouts',\n    status: 'PAUSED',\n    startDate: '2024-01-08T00:00:00Z',\n    createdAt: '2024-01-05T00:00:00Z',\n    updatedAt: '2024-01-08T00:00:00Z',\n    createdBy: 'user-3',\n    variants: [\n      { id: 'v8', name: 'Current Layout', trafficWeight: 0.5 },\n      { id: 'v9', name: 'New Layout', trafficWeight: 0.5 }\n    ],\n    tags: ['product', 'layout', 'ui'],\n    _count: { userAssignments: 800, events: 2100 }\n  },\n  {\n    id: '5',\n    tenantId: 'tenant-2',\n    name: 'Pricing Page Test',\n    description: 'Testing different pricing displays',\n    status: 'ACTIVE',\n    startDate: '2024-01-20T00:00:00Z',\n    createdAt: '2024-01-18T00:00:00Z',\n    updatedAt: '2024-01-20T00:00:00Z',\n    createdBy: 'user-4',\n    variants: [\n      { id: 'v10', name: 'Monthly Focus', trafficWeight: 0.5 },\n      { id: 'v11', name: 'Annual Focus', trafficWeight: 0.5 }\n    ],\n    tags: ['pricing', 'conversion', 'revenue'],\n    _count: { userAssignments: 600, events: 1800 }\n  }\n];\n\n// Mock API for development (when backend is not available)\nexport const mockExperimentsApi = {\n  async getExperiments(\n    filters: ExperimentFilters = {},\n    pagination: PaginationParams = { page: 1, limit: 20 }\n  ): Promise<PaginatedResponse<Experiment>> {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 500));\n\n    let filtered = [...mockExperiments];\n\n    // Apply filters\n    if (filters.search) {\n      const searchLower = filters.search.toLowerCase();\n      filtered = filtered.filter(exp =>\n        exp.name.toLowerCase().includes(searchLower) ||\n        exp.description?.toLowerCase().includes(searchLower) ||\n        exp.tags.some(tag => tag.toLowerCase().includes(searchLower))\n      );\n    }\n\n    if (filters.status && filters.status.length > 0) {\n      filtered = filtered.filter(exp => filters.status!.includes(exp.status));\n    }\n\n    if (filters.tags && filters.tags.length > 0) {\n      filtered = filtered.filter(exp =>\n        filters.tags!.some(tag => exp.tags.includes(tag))\n      );\n    }\n\n    // Apply sorting\n    if (pagination.sortBy) {\n      filtered.sort((a, b) => {\n        let aValue: any, bValue: any;\n\n        switch (pagination.sortBy) {\n          case 'variantCount':\n            aValue = a.variants.length;\n            bValue = b.variants.length;\n            break;\n          case 'assignmentCount':\n            aValue = a._count.userAssignments;\n            bValue = b._count.userAssignments;\n            break;\n          case 'eventCount':\n            aValue = a._count.events;\n            bValue = b._count.events;\n            break;\n          default:\n            aValue = pagination.sortBy ? (a as any)[pagination.sortBy] : a.createdAt;\n            bValue = pagination.sortBy ? (b as any)[pagination.sortBy] : b.createdAt;\n        }\n\n        if (aValue === bValue) return 0;\n        const comparison = aValue < bValue ? -1 : 1;\n        return pagination.sortOrder === 'desc' ? -comparison : comparison;\n      });\n    }\n\n    // Apply pagination\n    const total = filtered.length;\n    const totalPages = Math.ceil(total / pagination.limit);\n    const startIndex = (pagination.page - 1) * pagination.limit;\n    const paginatedData = filtered.slice(startIndex, startIndex + pagination.limit);\n\n    return {\n      data: paginatedData,\n      pagination: {\n        page: pagination.page,\n        limit: pagination.limit,\n        total,\n        totalPages,\n      },\n    };\n  },\n\n  async getExperiment(id: string): Promise<ApiResponse<Experiment>> {\n    await new Promise(resolve => setTimeout(resolve, 300));\n\n    const experiment = mockExperiments.find(exp => exp.id === id);\n    if (!experiment) {\n      throw new ApiError('Experiment not found', 'NOT_FOUND', 404);\n    }\n\n    return {\n      data: experiment,\n      success: true,\n    };\n  },\n\n  async createExperiment(data: CreateExperimentForm): Promise<ApiResponse<Experiment>> {\n    await new Promise(resolve => setTimeout(resolve, 800));\n\n    const newExperiment: Experiment = {\n      id: `exp-${Date.now()}`,\n      tenantId: 'tenant-1',\n      name: data.name,\n      description: data.description,\n      status: 'DRAFT',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n      createdBy: 'current-user',\n      variants: data.variants.map((v, i) => ({ ...v, id: `var-${Date.now()}-${i}` })),\n      tags: data.tags || [],\n      _count: { userAssignments: 0, events: 0 },\n    };\n\n    mockExperiments.unshift(newExperiment);\n\n    return {\n      data: newExperiment,\n      success: true,\n      message: 'Experiment created successfully',\n    };\n  },\n\n  async updateExperiment(id: string, data: UpdateExperimentForm): Promise<ApiResponse<Experiment>> {\n    await new Promise(resolve => setTimeout(resolve, 600));\n\n    const index = mockExperiments.findIndex(exp => exp.id === id);\n    if (index === -1) {\n      throw new ApiError('Experiment not found', 'NOT_FOUND', 404);\n    }\n\n    const updatedExperiment: Experiment = {\n      ...mockExperiments[index],\n      ...data,\n      updatedAt: new Date().toISOString(),\n      variants: data.variants ? data.variants.map((v, i) => ({ ...v, id: v.id || `var-${Date.now()}-${i}` })) : mockExperiments[index].variants,\n    };\n\n    mockExperiments[index] = updatedExperiment;\n\n    return {\n      data: updatedExperiment,\n      success: true,\n      message: 'Experiment updated successfully',\n    };\n  },\n\n  async updateExperimentStatus(\n    id: string,\n    status: ExperimentStatus,\n    data?: any\n  ): Promise<ApiResponse<Experiment>> {\n    await new Promise(resolve => setTimeout(resolve, 400));\n\n    const index = mockExperiments.findIndex(exp => exp.id === id);\n    if (index === -1) {\n      throw new ApiError('Experiment not found', 'NOT_FOUND', 404);\n    }\n\n    const updatedExperiment = {\n      ...mockExperiments[index],\n      status,\n      updatedAt: new Date().toISOString(),\n      ...(status === 'ACTIVE' && !mockExperiments[index].startDate && {\n        startDate: new Date().toISOString(),\n      }),\n      ...(status === 'COMPLETED' && !mockExperiments[index].endDate && {\n        endDate: new Date().toISOString(),\n      }),\n    };\n\n    mockExperiments[index] = updatedExperiment;\n\n    return {\n      data: updatedExperiment,\n      success: true,\n      message: `Experiment ${status.toLowerCase()} successfully`,\n    };\n  },\n\n  async deleteExperiment(id: string): Promise<ApiResponse<void>> {\n    await new Promise(resolve => setTimeout(resolve, 500));\n\n    const index = mockExperiments.findIndex(exp => exp.id === id);\n    if (index === -1) {\n      throw new ApiError('Experiment not found', 'NOT_FOUND', 404);\n    }\n\n    mockExperiments.splice(index, 1);\n\n    return {\n      data: undefined as any,\n      success: true,\n      message: 'Experiment deleted successfully',\n    };\n  },\n\n  async duplicateExperiment(id: string): Promise<ApiResponse<Experiment>> {\n    await new Promise(resolve => setTimeout(resolve, 700));\n\n    const original = mockExperiments.find(exp => exp.id === id);\n    if (!original) {\n      throw new ApiError('Experiment not found', 'NOT_FOUND', 404);\n    }\n\n    const duplicated: Experiment = {\n      ...original,\n      id: `exp-${Date.now()}`,\n      name: `${original.name} (Copy)`,\n      status: 'DRAFT',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n      startDate: undefined,\n      endDate: undefined,\n      variants: original.variants.map((v, i) => ({ ...v, id: `var-${Date.now()}-${i}` })),\n      _count: { userAssignments: 0, events: 0 },\n    };\n\n    mockExperiments.unshift(duplicated);\n\n    return {\n      data: duplicated,\n      success: true,\n      message: 'Experiment duplicated successfully',\n    };\n  },\n\n  async getExperimentAnalytics(id: string): Promise<ApiResponse<ExperimentAnalytics>> {\n    await new Promise(resolve => setTimeout(resolve, 1000));\n\n    const experiment = mockExperiments.find(exp => exp.id === id);\n    if (!experiment) {\n      throw new ApiError('Experiment not found', 'NOT_FOUND', 404);\n    }\n\n    // Mock analytics data\n    const analytics: ExperimentAnalytics = {\n      experimentId: id,\n      totalAssignments: experiment._count.userAssignments,\n      totalEvents: experiment._count.events,\n      conversionRate: experiment._count.userAssignments > 0\n        ? (experiment._count.events / experiment._count.userAssignments) * 100\n        : 0,\n      variantPerformance: experiment.variants.map(variant => ({\n        variantId: variant.id,\n        variantName: variant.name,\n        assignments: Math.floor(experiment._count.userAssignments * variant.trafficWeight),\n        events: Math.floor(experiment._count.events * variant.trafficWeight),\n        conversionRate: Math.random() * 10 + 2, // Mock conversion rate\n        confidence: Math.random() * 30 + 70, // Mock confidence\n      })),\n      timeSeriesData: Array.from({ length: 30 }, (_, i) => ({\n        date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],\n        assignments: Math.floor(Math.random() * 100) + 10,\n        events: Math.floor(Math.random() * 50) + 5,\n        conversionRate: Math.random() * 5 + 2,\n      })),\n      statisticalSignificance: {\n        isSignificant: Math.random() > 0.5,\n        confidence: Math.random() * 20 + 80,\n        pValue: Math.random() * 0.05,\n        winningVariant: experiment.variants[Math.floor(Math.random() * experiment.variants.length)].id,\n      },\n    };\n\n    return {\n      data: analytics,\n      success: true,\n    };\n  },\n\n  async bulkUpdateStatus(\n    ids: string[],\n    status: ExperimentStatus\n  ): Promise<ApiResponse<Experiment[]>> {\n    await new Promise(resolve => setTimeout(resolve, 1000));\n\n    const updatedExperiments: Experiment[] = [];\n\n    for (const id of ids) {\n      const index = mockExperiments.findIndex(exp => exp.id === id);\n      if (index !== -1) {\n        const updatedExperiment = {\n          ...mockExperiments[index],\n          status,\n          updatedAt: new Date().toISOString(),\n        };\n        mockExperiments[index] = updatedExperiment;\n        updatedExperiments.push(updatedExperiment);\n      }\n    }\n\n    return {\n      data: updatedExperiments,\n      success: true,\n      message: `${updatedExperiments.length} experiments updated successfully`,\n    };\n  },\n\n  async bulkDelete(ids: string[]): Promise<ApiResponse<void>> {\n    await new Promise(resolve => setTimeout(resolve, 800));\n\n    let deletedCount = 0;\n    for (const id of ids) {\n      const index = mockExperiments.findIndex(exp => exp.id === id);\n      if (index !== -1) {\n        mockExperiments.splice(index, 1);\n        deletedCount++;\n      }\n    }\n\n    return {\n      data: undefined as any,\n      success: true,\n      message: `${deletedCount} experiments deleted successfully`,\n    };\n  },\n};\n\n// Export the appropriate API based on environment\nexport const api = process.env.NODE_ENV === 'development' && !process.env.REACT_APP_USE_REAL_API\n  ? mockExperimentsApi\n  : experimentsApi;\n"], "mappings": "AAAA;;AAaA;AACA,MAAMA,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;AACjF,MAAMC,WAAW,GAAG,KAAK,CAAC,CAAC;;AAE3B;AACA,MAAMC,QAAQ,SAASC,KAAK,CAAC;EAM3BC,WAAWA,CAACC,OAAe,EAAEC,IAAY,EAAEC,UAAkB,EAAEC,OAAa,EAAEC,WAAmB,EAAE;IACjG,KAAK,CAACJ,OAAO,CAAC;IAAC,KANVC,IAAI;IAAA,KACJC,UAAU;IAAA,KACVC,OAAO;IAAA,KACPC,WAAW;IAIhB,IAAI,CAACC,IAAI,GAAG,UAAU;IACtB,IAAI,CAACJ,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,WAAW,GAAGA,WAAW;EAChC;AACF;;AAEA;AACA,MAAME,SAAS,CAAC;EAIdP,WAAWA,CAACQ,OAAe,EAAEC,OAAe,GAAGZ,WAAW,EAAE;IAAA,KAHpDW,OAAO;IAAA,KACPC,OAAO;IAGb,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,OAAO,GAAGA,OAAO;EACxB;EAEA,MAAcC,OAAOA,CACnBC,QAAgB,EAChBC,OAAoB,GAAG,CAAC,CAAC,EACb;IACZ,MAAMC,GAAG,GAAG,GAAG,IAAI,CAACL,OAAO,GAAGG,QAAQ,EAAE;;IAExC;IACA,MAAMG,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;IACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,IAAI,CAACT,OAAO,CAAC;IAEpE,IAAI;MACF,MAAMU,QAAQ,GAAG,MAAMC,KAAK,CAACP,GAAG,EAAE;QAChC,GAAGD,OAAO;QACVS,MAAM,EAAEP,UAAU,CAACO,MAAM;QACzBC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,GAAGV,OAAO,CAACU;QACb;MACF,CAAC,CAAC;MAEFC,YAAY,CAACP,SAAS,CAAC;MAEvB,IAAI,CAACG,QAAQ,CAACK,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACzD,MAAM,IAAI7B,QAAQ,CAChB2B,SAAS,CAACxB,OAAO,IAAI,QAAQkB,QAAQ,CAACS,MAAM,KAAKT,QAAQ,CAACU,UAAU,EAAE,EACtEJ,SAAS,CAACvB,IAAI,IAAI,YAAY,EAC9BiB,QAAQ,CAACS,MAAM,EACfH,SAAS,CAACrB,OAAO,EACjBqB,SAAS,CAACpB,WACZ,CAAC;MACH;MAEA,OAAO,MAAMc,QAAQ,CAACO,IAAI,CAAC,CAAC;IAC9B,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdP,YAAY,CAACP,SAAS,CAAC;MAEvB,IAAIc,KAAK,YAAYhC,QAAQ,EAAE;QAC7B,MAAMgC,KAAK;MACb;MAEA,IAAI,CAACA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAUxB,IAAI,MAAK,YAAY,EAAE;QACzC,MAAM,IAAIR,QAAQ,CAChB,iBAAiB,EACjB,eAAe,EACf,GACF,CAAC;MACH;MAEA,MAAM,IAAIA,QAAQ,CAChB,CAACgC,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAU7B,OAAO,KAAI,eAAe,EAC1C,eAAe,EACf,CACF,CAAC;IACH;EACF;EAEA,MAAM8B,GAAGA,CAAIpB,QAAgB,EAAEqB,MAA4B,EAAc;IACvE,MAAMnB,GAAG,GAAG,IAAIoB,GAAG,CAACtB,QAAQ,EAAE,IAAI,CAACH,OAAO,CAAC;IAC3C,IAAIwB,MAAM,EAAE;MACVE,MAAM,CAACC,OAAO,CAACH,MAAM,CAAC,CAACI,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;QAC/C,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,EAAE;UACzCzB,GAAG,CAAC2B,YAAY,CAACC,MAAM,CAACJ,GAAG,EAAEK,MAAM,CAACJ,KAAK,CAAC,CAAC;QAC7C;MACF,CAAC,CAAC;IACJ;IAEA,OAAO,IAAI,CAAC5B,OAAO,CAAIG,GAAG,CAAC8B,QAAQ,GAAG9B,GAAG,CAAC+B,MAAM,CAAC;EACnD;EAEA,MAAMC,IAAIA,CAAIlC,QAAgB,EAAEmC,IAAU,EAAc;IACtD,OAAO,IAAI,CAACpC,OAAO,CAAIC,QAAQ,EAAE;MAC/BoC,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEF,IAAI,GAAGG,IAAI,CAACC,SAAS,CAACJ,IAAI,CAAC,GAAGP;IACtC,CAAC,CAAC;EACJ;EAEA,MAAMY,GAAGA,CAAIxC,QAAgB,EAAEmC,IAAU,EAAc;IACrD,OAAO,IAAI,CAACpC,OAAO,CAAIC,QAAQ,EAAE;MAC/BoC,MAAM,EAAE,KAAK;MACbC,IAAI,EAAEF,IAAI,GAAGG,IAAI,CAACC,SAAS,CAACJ,IAAI,CAAC,GAAGP;IACtC,CAAC,CAAC;EACJ;EAEA,MAAMa,KAAKA,CAAIzC,QAAgB,EAAEmC,IAAU,EAAc;IACvD,OAAO,IAAI,CAACpC,OAAO,CAAIC,QAAQ,EAAE;MAC/BoC,MAAM,EAAE,OAAO;MACfC,IAAI,EAAEF,IAAI,GAAGG,IAAI,CAACC,SAAS,CAACJ,IAAI,CAAC,GAAGP;IACtC,CAAC,CAAC;EACJ;EAEA,MAAMc,MAAMA,CAAI1C,QAAgB,EAAc;IAC5C,OAAO,IAAI,CAACD,OAAO,CAAIC,QAAQ,EAAE;MAC/BoC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;AACF;;AAEA;AACA,MAAMO,SAAS,GAAG,IAAI/C,SAAS,CAACd,YAAY,CAAC;;AAE7C;AACA,OAAO,MAAM8D,cAAc,GAAG;EAC5B;EACA,MAAMC,cAAcA,CAClBC,OAA0B,GAAG,CAAC,CAAC,EAC/BC,UAA4B,GAAG;IAAEC,IAAI,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAG,CAAC,EACb;IAAA,IAAAC,eAAA,EAAAC,aAAA,EAAAC,kBAAA;IACxC,MAAM/B,MAAM,GAAG;MACb,GAAG0B,UAAU;MACb,GAAGD,OAAO;MACV;MACA7B,MAAM,GAAAiC,eAAA,GAAEJ,OAAO,CAAC7B,MAAM,cAAAiC,eAAA,uBAAdA,eAAA,CAAgBG,IAAI,CAAC,GAAG,CAAC;MACjCC,IAAI,GAAAH,aAAA,GAAEL,OAAO,CAACQ,IAAI,cAAAH,aAAA,uBAAZA,aAAA,CAAcE,IAAI,CAAC,GAAG,CAAC;MAC7BE,SAAS,GAAAH,kBAAA,GAAEN,OAAO,CAACS,SAAS,cAAAH,kBAAA,uBAAjBA,kBAAA,CAAmBC,IAAI,CAAC,GAAG;IACxC,CAAC;IAED,OAAOV,SAAS,CAACvB,GAAG,CAAgC,cAAc,EAAEC,MAAM,CAAC;EAC7E,CAAC;EAED;EACA,MAAMmC,aAAaA,CAACC,EAAU,EAAoC;IAChE,OAAOd,SAAS,CAACvB,GAAG,CAA0B,gBAAgBqC,EAAE,EAAE,CAAC;EACrE,CAAC;EAED;EACA,MAAMC,gBAAgBA,CAACvB,IAA0B,EAAoC;IACnF,OAAOQ,SAAS,CAACT,IAAI,CAA0B,cAAc,EAAEC,IAAI,CAAC;EACtE,CAAC;EAED;EACA,MAAMwB,gBAAgBA,CAACF,EAAU,EAAEtB,IAA0B,EAAoC;IAC/F,OAAOQ,SAAS,CAACH,GAAG,CAA0B,gBAAgBiB,EAAE,EAAE,EAAEtB,IAAI,CAAC;EAC3E,CAAC;EAED;EACA,MAAMyB,sBAAsBA,CAC1BH,EAAU,EACVxC,MAAwB,EACxBkB,IAAU,EACwB;IAClC,OAAOQ,SAAS,CAACF,KAAK,CAA0B,gBAAgBgB,EAAE,SAAS,EAAE;MAC3ExC,MAAM;MACN,GAAGkB;IACL,CAAC,CAAC;EACJ,CAAC;EAED;EACA,MAAM0B,gBAAgBA,CAACJ,EAAU,EAA8B;IAC7D,OAAOd,SAAS,CAACD,MAAM,CAAoB,gBAAgBe,EAAE,EAAE,CAAC;EAClE,CAAC;EAED;EACA,MAAMK,mBAAmBA,CAACL,EAAU,EAAoC;IACtE,OAAOd,SAAS,CAACT,IAAI,CAA0B,gBAAgBuB,EAAE,YAAY,CAAC;EAChF,CAAC;EAED;EACA,MAAMM,sBAAsBA,CAACN,EAAU,EAA6C;IAClF,OAAOd,SAAS,CAACvB,GAAG,CAAmC,gBAAgBqC,EAAE,YAAY,CAAC;EACxF,CAAC;EAED;EACA,MAAMO,gBAAgBA,CACpBC,GAAa,EACbhD,MAAwB,EACY;IACpC,OAAO0B,SAAS,CAACF,KAAK,CAA4B,0BAA0B,EAAE;MAC5EwB,GAAG;MACHhD;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMiD,UAAUA,CAACD,GAAa,EAA8B;IAC1D,OAAOtB,SAAS,CAAC5C,OAAO,CAAoB,mBAAmB,EAAE;MAC/DqC,MAAM,EAAE,QAAQ;MAChBC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAE0B;MAAI,CAAC;IAC9B,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA,MAAME,eAA6B,GAAG,CACpC;EACEV,EAAE,EAAE,GAAG;EACPW,QAAQ,EAAE,UAAU;EACpBzE,IAAI,EAAE,4BAA4B;EAClC0E,WAAW,EAAE,6DAA6D;EAC1EpD,MAAM,EAAE,QAAQ;EAChBqD,SAAS,EAAE,sBAAsB;EACjCC,OAAO,EAAE,sBAAsB;EAC/BC,SAAS,EAAE,sBAAsB;EACjCC,SAAS,EAAE,sBAAsB;EACjClB,SAAS,EAAE,QAAQ;EACnBmB,QAAQ,EAAE,CACR;IAAEjB,EAAE,EAAE,IAAI;IAAE9D,IAAI,EAAE,gBAAgB;IAAEgF,aAAa,EAAE;EAAI,CAAC,EACxD;IAAElB,EAAE,EAAE,IAAI;IAAE9D,IAAI,EAAE,mBAAmB;IAAEgF,aAAa,EAAE;EAAI,CAAC,CAC5D;EACDrB,IAAI,EAAE,CAAC,UAAU,EAAE,IAAI,EAAE,YAAY,CAAC;EACtCsB,MAAM,EAAE;IAAEC,eAAe,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAK;AAChD,CAAC,EACD;EACErB,EAAE,EAAE,GAAG;EACPW,QAAQ,EAAE,UAAU;EACpBzE,IAAI,EAAE,4BAA4B;EAClC0E,WAAW,EAAE,uCAAuC;EACpDpD,MAAM,EAAE,OAAO;EACfuD,SAAS,EAAE,sBAAsB;EACjCC,SAAS,EAAE,sBAAsB;EACjClB,SAAS,EAAE,QAAQ;EACnBmB,QAAQ,EAAE,CACR;IAAEjB,EAAE,EAAE,IAAI;IAAE9D,IAAI,EAAE,cAAc;IAAEgF,aAAa,EAAE;EAAI,CAAC,EACtD;IAAElB,EAAE,EAAE,IAAI;IAAE9D,IAAI,EAAE,iBAAiB;IAAEgF,aAAa,EAAE;EAAI,CAAC,CAC1D;EACDrB,IAAI,EAAE,CAAC,UAAU,EAAE,IAAI,EAAE,YAAY,CAAC;EACtCsB,MAAM,EAAE;IAAEC,eAAe,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE;AAC1C,CAAC,EACD;EACErB,EAAE,EAAE,GAAG;EACPW,QAAQ,EAAE,UAAU;EACpBzE,IAAI,EAAE,yBAAyB;EAC/B0E,WAAW,EAAE,sDAAsD;EACnEpD,MAAM,EAAE,WAAW;EACnBqD,SAAS,EAAE,sBAAsB;EACjCC,OAAO,EAAE,sBAAsB;EAC/BC,SAAS,EAAE,sBAAsB;EACjCC,SAAS,EAAE,sBAAsB;EACjClB,SAAS,EAAE,QAAQ;EACnBmB,QAAQ,EAAE,CACR;IAAEjB,EAAE,EAAE,IAAI;IAAE9D,IAAI,EAAE,kBAAkB;IAAEgF,aAAa,EAAE;EAAK,CAAC,EAC3D;IAAElB,EAAE,EAAE,IAAI;IAAE9D,IAAI,EAAE,sBAAsB;IAAEgF,aAAa,EAAE;EAAK,CAAC,EAC/D;IAAElB,EAAE,EAAE,IAAI;IAAE9D,IAAI,EAAE,gBAAgB;IAAEgF,aAAa,EAAE;EAAK,CAAC,CAC1D;EACDrB,IAAI,EAAE,CAAC,OAAO,EAAE,WAAW,EAAE,YAAY,CAAC;EAC1CsB,MAAM,EAAE;IAAEC,eAAe,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAM;AACjD,CAAC,EACD;EACErB,EAAE,EAAE,GAAG;EACPW,QAAQ,EAAE,UAAU;EACpBzE,IAAI,EAAE,qBAAqB;EAC3B0E,WAAW,EAAE,wCAAwC;EACrDpD,MAAM,EAAE,QAAQ;EAChBqD,SAAS,EAAE,sBAAsB;EACjCE,SAAS,EAAE,sBAAsB;EACjCC,SAAS,EAAE,sBAAsB;EACjClB,SAAS,EAAE,QAAQ;EACnBmB,QAAQ,EAAE,CACR;IAAEjB,EAAE,EAAE,IAAI;IAAE9D,IAAI,EAAE,gBAAgB;IAAEgF,aAAa,EAAE;EAAI,CAAC,EACxD;IAAElB,EAAE,EAAE,IAAI;IAAE9D,IAAI,EAAE,YAAY;IAAEgF,aAAa,EAAE;EAAI,CAAC,CACrD;EACDrB,IAAI,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAC;EACjCsB,MAAM,EAAE;IAAEC,eAAe,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAK;AAC/C,CAAC,EACD;EACErB,EAAE,EAAE,GAAG;EACPW,QAAQ,EAAE,UAAU;EACpBzE,IAAI,EAAE,mBAAmB;EACzB0E,WAAW,EAAE,oCAAoC;EACjDpD,MAAM,EAAE,QAAQ;EAChBqD,SAAS,EAAE,sBAAsB;EACjCE,SAAS,EAAE,sBAAsB;EACjCC,SAAS,EAAE,sBAAsB;EACjClB,SAAS,EAAE,QAAQ;EACnBmB,QAAQ,EAAE,CACR;IAAEjB,EAAE,EAAE,KAAK;IAAE9D,IAAI,EAAE,eAAe;IAAEgF,aAAa,EAAE;EAAI,CAAC,EACxD;IAAElB,EAAE,EAAE,KAAK;IAAE9D,IAAI,EAAE,cAAc;IAAEgF,aAAa,EAAE;EAAI,CAAC,CACxD;EACDrB,IAAI,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,SAAS,CAAC;EAC1CsB,MAAM,EAAE;IAAEC,eAAe,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAK;AAC/C,CAAC,CACF;;AAED;AACA,OAAO,MAAMC,kBAAkB,GAAG;EAChC,MAAMlC,cAAcA,CAClBC,OAA0B,GAAG,CAAC,CAAC,EAC/BC,UAA4B,GAAG;IAAEC,IAAI,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAG,CAAC,EACb;IACxC;IACA,MAAM,IAAI+B,OAAO,CAACC,OAAO,IAAI3E,UAAU,CAAC2E,OAAO,EAAE,GAAG,CAAC,CAAC;IAEtD,IAAIC,QAAQ,GAAG,CAAC,GAAGf,eAAe,CAAC;;IAEnC;IACA,IAAIrB,OAAO,CAACb,MAAM,EAAE;MAClB,MAAMkD,WAAW,GAAGrC,OAAO,CAACb,MAAM,CAACmD,WAAW,CAAC,CAAC;MAChDF,QAAQ,GAAGA,QAAQ,CAACG,MAAM,CAACC,GAAG;QAAA,IAAAC,gBAAA;QAAA,OAC5BD,GAAG,CAAC3F,IAAI,CAACyF,WAAW,CAAC,CAAC,CAACI,QAAQ,CAACL,WAAW,CAAC,MAAAI,gBAAA,GAC5CD,GAAG,CAACjB,WAAW,cAAAkB,gBAAA,uBAAfA,gBAAA,CAAiBH,WAAW,CAAC,CAAC,CAACI,QAAQ,CAACL,WAAW,CAAC,KACpDG,GAAG,CAAChC,IAAI,CAACmC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACN,WAAW,CAAC,CAAC,CAACI,QAAQ,CAACL,WAAW,CAAC,CAAC;MAAA,CAC/D,CAAC;IACH;IAEA,IAAIrC,OAAO,CAAC7B,MAAM,IAAI6B,OAAO,CAAC7B,MAAM,CAAC0E,MAAM,GAAG,CAAC,EAAE;MAC/CT,QAAQ,GAAGA,QAAQ,CAACG,MAAM,CAACC,GAAG,IAAIxC,OAAO,CAAC7B,MAAM,CAAEuE,QAAQ,CAACF,GAAG,CAACrE,MAAM,CAAC,CAAC;IACzE;IAEA,IAAI6B,OAAO,CAACQ,IAAI,IAAIR,OAAO,CAACQ,IAAI,CAACqC,MAAM,GAAG,CAAC,EAAE;MAC3CT,QAAQ,GAAGA,QAAQ,CAACG,MAAM,CAACC,GAAG,IAC5BxC,OAAO,CAACQ,IAAI,CAAEmC,IAAI,CAACC,GAAG,IAAIJ,GAAG,CAAChC,IAAI,CAACkC,QAAQ,CAACE,GAAG,CAAC,CAClD,CAAC;IACH;;IAEA;IACA,IAAI3C,UAAU,CAAC6C,MAAM,EAAE;MACrBV,QAAQ,CAACW,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QACtB,IAAIC,MAAW,EAAEC,MAAW;QAE5B,QAAQlD,UAAU,CAAC6C,MAAM;UACvB,KAAK,cAAc;YACjBI,MAAM,GAAGF,CAAC,CAACpB,QAAQ,CAACiB,MAAM;YAC1BM,MAAM,GAAGF,CAAC,CAACrB,QAAQ,CAACiB,MAAM;YAC1B;UACF,KAAK,iBAAiB;YACpBK,MAAM,GAAGF,CAAC,CAAClB,MAAM,CAACC,eAAe;YACjCoB,MAAM,GAAGF,CAAC,CAACnB,MAAM,CAACC,eAAe;YACjC;UACF,KAAK,YAAY;YACfmB,MAAM,GAAGF,CAAC,CAAClB,MAAM,CAACE,MAAM;YACxBmB,MAAM,GAAGF,CAAC,CAACnB,MAAM,CAACE,MAAM;YACxB;UACF;YACEkB,MAAM,GAAGjD,UAAU,CAAC6C,MAAM,GAAIE,CAAC,CAAS/C,UAAU,CAAC6C,MAAM,CAAC,GAAGE,CAAC,CAACtB,SAAS;YACxEyB,MAAM,GAAGlD,UAAU,CAAC6C,MAAM,GAAIG,CAAC,CAAShD,UAAU,CAAC6C,MAAM,CAAC,GAAGG,CAAC,CAACvB,SAAS;QAC5E;QAEA,IAAIwB,MAAM,KAAKC,MAAM,EAAE,OAAO,CAAC;QAC/B,MAAMC,UAAU,GAAGF,MAAM,GAAGC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC;QAC3C,OAAOlD,UAAU,CAACoD,SAAS,KAAK,MAAM,GAAG,CAACD,UAAU,GAAGA,UAAU;MACnE,CAAC,CAAC;IACJ;;IAEA;IACA,MAAME,KAAK,GAAGlB,QAAQ,CAACS,MAAM;IAC7B,MAAMU,UAAU,GAAGC,IAAI,CAACC,IAAI,CAACH,KAAK,GAAGrD,UAAU,CAACE,KAAK,CAAC;IACtD,MAAMuD,UAAU,GAAG,CAACzD,UAAU,CAACC,IAAI,GAAG,CAAC,IAAID,UAAU,CAACE,KAAK;IAC3D,MAAMwD,aAAa,GAAGvB,QAAQ,CAACwB,KAAK,CAACF,UAAU,EAAEA,UAAU,GAAGzD,UAAU,CAACE,KAAK,CAAC;IAE/E,OAAO;MACLd,IAAI,EAAEsE,aAAa;MACnB1D,UAAU,EAAE;QACVC,IAAI,EAAED,UAAU,CAACC,IAAI;QACrBC,KAAK,EAAEF,UAAU,CAACE,KAAK;QACvBmD,KAAK;QACLC;MACF;IACF,CAAC;EACH,CAAC;EAED,MAAM7C,aAAaA,CAACC,EAAU,EAAoC;IAChE,MAAM,IAAIuB,OAAO,CAACC,OAAO,IAAI3E,UAAU,CAAC2E,OAAO,EAAE,GAAG,CAAC,CAAC;IAEtD,MAAM0B,UAAU,GAAGxC,eAAe,CAACyC,IAAI,CAACtB,GAAG,IAAIA,GAAG,CAAC7B,EAAE,KAAKA,EAAE,CAAC;IAC7D,IAAI,CAACkD,UAAU,EAAE;MACf,MAAM,IAAIxH,QAAQ,CAAC,sBAAsB,EAAE,WAAW,EAAE,GAAG,CAAC;IAC9D;IAEA,OAAO;MACLgD,IAAI,EAAEwE,UAAU;MAChBE,OAAO,EAAE;IACX,CAAC;EACH,CAAC;EAED,MAAMnD,gBAAgBA,CAACvB,IAA0B,EAAoC;IACnF,MAAM,IAAI6C,OAAO,CAACC,OAAO,IAAI3E,UAAU,CAAC2E,OAAO,EAAE,GAAG,CAAC,CAAC;IAEtD,MAAM6B,aAAyB,GAAG;MAChCrD,EAAE,EAAE,OAAOsD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MACvB5C,QAAQ,EAAE,UAAU;MACpBzE,IAAI,EAAEwC,IAAI,CAACxC,IAAI;MACf0E,WAAW,EAAElC,IAAI,CAACkC,WAAW;MAC7BpD,MAAM,EAAE,OAAO;MACfuD,SAAS,EAAE,IAAIuC,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC;MACnCxC,SAAS,EAAE,IAAIsC,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC;MACnC1D,SAAS,EAAE,cAAc;MACzBmB,QAAQ,EAAEvC,IAAI,CAACuC,QAAQ,CAACwC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,MAAM;QAAE,GAAGD,CAAC;QAAE1D,EAAE,EAAE,OAAOsD,IAAI,CAACC,GAAG,CAAC,CAAC,IAAII,CAAC;MAAG,CAAC,CAAC,CAAC;MAC/E9D,IAAI,EAAEnB,IAAI,CAACmB,IAAI,IAAI,EAAE;MACrBsB,MAAM,EAAE;QAAEC,eAAe,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE;IAC1C,CAAC;IAEDX,eAAe,CAACkD,OAAO,CAACP,aAAa,CAAC;IAEtC,OAAO;MACL3E,IAAI,EAAE2E,aAAa;MACnBD,OAAO,EAAE,IAAI;MACbvH,OAAO,EAAE;IACX,CAAC;EACH,CAAC;EAED,MAAMqE,gBAAgBA,CAACF,EAAU,EAAEtB,IAA0B,EAAoC;IAC/F,MAAM,IAAI6C,OAAO,CAACC,OAAO,IAAI3E,UAAU,CAAC2E,OAAO,EAAE,GAAG,CAAC,CAAC;IAEtD,MAAMqC,KAAK,GAAGnD,eAAe,CAACoD,SAAS,CAACjC,GAAG,IAAIA,GAAG,CAAC7B,EAAE,KAAKA,EAAE,CAAC;IAC7D,IAAI6D,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,MAAM,IAAInI,QAAQ,CAAC,sBAAsB,EAAE,WAAW,EAAE,GAAG,CAAC;IAC9D;IAEA,MAAMqI,iBAA6B,GAAG;MACpC,GAAGrD,eAAe,CAACmD,KAAK,CAAC;MACzB,GAAGnF,IAAI;MACPsC,SAAS,EAAE,IAAIsC,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC;MACnCvC,QAAQ,EAAEvC,IAAI,CAACuC,QAAQ,GAAGvC,IAAI,CAACuC,QAAQ,CAACwC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,MAAM;QAAE,GAAGD,CAAC;QAAE1D,EAAE,EAAE0D,CAAC,CAAC1D,EAAE,IAAI,OAAOsD,IAAI,CAACC,GAAG,CAAC,CAAC,IAAII,CAAC;MAAG,CAAC,CAAC,CAAC,GAAGjD,eAAe,CAACmD,KAAK,CAAC,CAAC5C;IACnI,CAAC;IAEDP,eAAe,CAACmD,KAAK,CAAC,GAAGE,iBAAiB;IAE1C,OAAO;MACLrF,IAAI,EAAEqF,iBAAiB;MACvBX,OAAO,EAAE,IAAI;MACbvH,OAAO,EAAE;IACX,CAAC;EACH,CAAC;EAED,MAAMsE,sBAAsBA,CAC1BH,EAAU,EACVxC,MAAwB,EACxBkB,IAAU,EACwB;IAClC,MAAM,IAAI6C,OAAO,CAACC,OAAO,IAAI3E,UAAU,CAAC2E,OAAO,EAAE,GAAG,CAAC,CAAC;IAEtD,MAAMqC,KAAK,GAAGnD,eAAe,CAACoD,SAAS,CAACjC,GAAG,IAAIA,GAAG,CAAC7B,EAAE,KAAKA,EAAE,CAAC;IAC7D,IAAI6D,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,MAAM,IAAInI,QAAQ,CAAC,sBAAsB,EAAE,WAAW,EAAE,GAAG,CAAC;IAC9D;IAEA,MAAMqI,iBAAiB,GAAG;MACxB,GAAGrD,eAAe,CAACmD,KAAK,CAAC;MACzBrG,MAAM;MACNwD,SAAS,EAAE,IAAIsC,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC;MACnC,IAAIhG,MAAM,KAAK,QAAQ,IAAI,CAACkD,eAAe,CAACmD,KAAK,CAAC,CAAChD,SAAS,IAAI;QAC9DA,SAAS,EAAE,IAAIyC,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC;MACpC,CAAC,CAAC;MACF,IAAIhG,MAAM,KAAK,WAAW,IAAI,CAACkD,eAAe,CAACmD,KAAK,CAAC,CAAC/C,OAAO,IAAI;QAC/DA,OAAO,EAAE,IAAIwC,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC;MAClC,CAAC;IACH,CAAC;IAED9C,eAAe,CAACmD,KAAK,CAAC,GAAGE,iBAAiB;IAE1C,OAAO;MACLrF,IAAI,EAAEqF,iBAAiB;MACvBX,OAAO,EAAE,IAAI;MACbvH,OAAO,EAAE,cAAc2B,MAAM,CAACmE,WAAW,CAAC,CAAC;IAC7C,CAAC;EACH,CAAC;EAED,MAAMvB,gBAAgBA,CAACJ,EAAU,EAA8B;IAC7D,MAAM,IAAIuB,OAAO,CAACC,OAAO,IAAI3E,UAAU,CAAC2E,OAAO,EAAE,GAAG,CAAC,CAAC;IAEtD,MAAMqC,KAAK,GAAGnD,eAAe,CAACoD,SAAS,CAACjC,GAAG,IAAIA,GAAG,CAAC7B,EAAE,KAAKA,EAAE,CAAC;IAC7D,IAAI6D,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,MAAM,IAAInI,QAAQ,CAAC,sBAAsB,EAAE,WAAW,EAAE,GAAG,CAAC;IAC9D;IAEAgF,eAAe,CAACsD,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;IAEhC,OAAO;MACLnF,IAAI,EAAEP,SAAgB;MACtBiF,OAAO,EAAE,IAAI;MACbvH,OAAO,EAAE;IACX,CAAC;EACH,CAAC;EAED,MAAMwE,mBAAmBA,CAACL,EAAU,EAAoC;IACtE,MAAM,IAAIuB,OAAO,CAACC,OAAO,IAAI3E,UAAU,CAAC2E,OAAO,EAAE,GAAG,CAAC,CAAC;IAEtD,MAAMyC,QAAQ,GAAGvD,eAAe,CAACyC,IAAI,CAACtB,GAAG,IAAIA,GAAG,CAAC7B,EAAE,KAAKA,EAAE,CAAC;IAC3D,IAAI,CAACiE,QAAQ,EAAE;MACb,MAAM,IAAIvI,QAAQ,CAAC,sBAAsB,EAAE,WAAW,EAAE,GAAG,CAAC;IAC9D;IAEA,MAAMwI,UAAsB,GAAG;MAC7B,GAAGD,QAAQ;MACXjE,EAAE,EAAE,OAAOsD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MACvBrH,IAAI,EAAE,GAAG+H,QAAQ,CAAC/H,IAAI,SAAS;MAC/BsB,MAAM,EAAE,OAAO;MACfuD,SAAS,EAAE,IAAIuC,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC;MACnCxC,SAAS,EAAE,IAAIsC,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC;MACnC3C,SAAS,EAAE1C,SAAS;MACpB2C,OAAO,EAAE3C,SAAS;MAClB8C,QAAQ,EAAEgD,QAAQ,CAAChD,QAAQ,CAACwC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,MAAM;QAAE,GAAGD,CAAC;QAAE1D,EAAE,EAAE,OAAOsD,IAAI,CAACC,GAAG,CAAC,CAAC,IAAII,CAAC;MAAG,CAAC,CAAC,CAAC;MACnFxC,MAAM,EAAE;QAAEC,eAAe,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE;IAC1C,CAAC;IAEDX,eAAe,CAACkD,OAAO,CAACM,UAAU,CAAC;IAEnC,OAAO;MACLxF,IAAI,EAAEwF,UAAU;MAChBd,OAAO,EAAE,IAAI;MACbvH,OAAO,EAAE;IACX,CAAC;EACH,CAAC;EAED,MAAMyE,sBAAsBA,CAACN,EAAU,EAA6C;IAClF,MAAM,IAAIuB,OAAO,CAACC,OAAO,IAAI3E,UAAU,CAAC2E,OAAO,EAAE,IAAI,CAAC,CAAC;IAEvD,MAAM0B,UAAU,GAAGxC,eAAe,CAACyC,IAAI,CAACtB,GAAG,IAAIA,GAAG,CAAC7B,EAAE,KAAKA,EAAE,CAAC;IAC7D,IAAI,CAACkD,UAAU,EAAE;MACf,MAAM,IAAIxH,QAAQ,CAAC,sBAAsB,EAAE,WAAW,EAAE,GAAG,CAAC;IAC9D;;IAEA;IACA,MAAMyI,SAA8B,GAAG;MACrCC,YAAY,EAAEpE,EAAE;MAChBqE,gBAAgB,EAAEnB,UAAU,CAAC/B,MAAM,CAACC,eAAe;MACnDkD,WAAW,EAAEpB,UAAU,CAAC/B,MAAM,CAACE,MAAM;MACrCkD,cAAc,EAAErB,UAAU,CAAC/B,MAAM,CAACC,eAAe,GAAG,CAAC,GAChD8B,UAAU,CAAC/B,MAAM,CAACE,MAAM,GAAG6B,UAAU,CAAC/B,MAAM,CAACC,eAAe,GAAI,GAAG,GACpE,CAAC;MACLoD,kBAAkB,EAAEtB,UAAU,CAACjC,QAAQ,CAACwC,GAAG,CAACgB,OAAO,KAAK;QACtDC,SAAS,EAAED,OAAO,CAACzE,EAAE;QACrB2E,WAAW,EAAEF,OAAO,CAACvI,IAAI;QACzB0I,WAAW,EAAE/B,IAAI,CAACgC,KAAK,CAAC3B,UAAU,CAAC/B,MAAM,CAACC,eAAe,GAAGqD,OAAO,CAACvD,aAAa,CAAC;QAClFG,MAAM,EAAEwB,IAAI,CAACgC,KAAK,CAAC3B,UAAU,CAAC/B,MAAM,CAACE,MAAM,GAAGoD,OAAO,CAACvD,aAAa,CAAC;QACpEqD,cAAc,EAAE1B,IAAI,CAACiC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;QAAE;QACxCC,UAAU,EAAElC,IAAI,CAACiC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAE;MACvC,CAAC,CAAC,CAAC;MACHE,cAAc,EAAEC,KAAK,CAACC,IAAI,CAAC;QAAEhD,MAAM,EAAE;MAAG,CAAC,EAAE,CAACiD,CAAC,EAAExB,CAAC,MAAM;QACpDyB,IAAI,EAAE,IAAI9B,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,GAAGI,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACH,WAAW,CAAC,CAAC,CAAC6B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACvFT,WAAW,EAAE/B,IAAI,CAACgC,KAAK,CAAChC,IAAI,CAACiC,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,EAAE;QACjDzD,MAAM,EAAEwB,IAAI,CAACgC,KAAK,CAAChC,IAAI,CAACiC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC;QAC1CP,cAAc,EAAE1B,IAAI,CAACiC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG;MACtC,CAAC,CAAC,CAAC;MACHQ,uBAAuB,EAAE;QACvBC,aAAa,EAAE1C,IAAI,CAACiC,MAAM,CAAC,CAAC,GAAG,GAAG;QAClCC,UAAU,EAAElC,IAAI,CAACiC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE;QACnCU,MAAM,EAAE3C,IAAI,CAACiC,MAAM,CAAC,CAAC,GAAG,IAAI;QAC5BW,cAAc,EAAEvC,UAAU,CAACjC,QAAQ,CAAC4B,IAAI,CAACgC,KAAK,CAAChC,IAAI,CAACiC,MAAM,CAAC,CAAC,GAAG5B,UAAU,CAACjC,QAAQ,CAACiB,MAAM,CAAC,CAAC,CAAClC;MAC9F;IACF,CAAC;IAED,OAAO;MACLtB,IAAI,EAAEyF,SAAS;MACff,OAAO,EAAE;IACX,CAAC;EACH,CAAC;EAED,MAAM7C,gBAAgBA,CACpBC,GAAa,EACbhD,MAAwB,EACY;IACpC,MAAM,IAAI+D,OAAO,CAACC,OAAO,IAAI3E,UAAU,CAAC2E,OAAO,EAAE,IAAI,CAAC,CAAC;IAEvD,MAAMkE,kBAAgC,GAAG,EAAE;IAE3C,KAAK,MAAM1F,EAAE,IAAIQ,GAAG,EAAE;MACpB,MAAMqD,KAAK,GAAGnD,eAAe,CAACoD,SAAS,CAACjC,GAAG,IAAIA,GAAG,CAAC7B,EAAE,KAAKA,EAAE,CAAC;MAC7D,IAAI6D,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,MAAME,iBAAiB,GAAG;UACxB,GAAGrD,eAAe,CAACmD,KAAK,CAAC;UACzBrG,MAAM;UACNwD,SAAS,EAAE,IAAIsC,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC;QACpC,CAAC;QACD9C,eAAe,CAACmD,KAAK,CAAC,GAAGE,iBAAiB;QAC1C2B,kBAAkB,CAACC,IAAI,CAAC5B,iBAAiB,CAAC;MAC5C;IACF;IAEA,OAAO;MACLrF,IAAI,EAAEgH,kBAAkB;MACxBtC,OAAO,EAAE,IAAI;MACbvH,OAAO,EAAE,GAAG6J,kBAAkB,CAACxD,MAAM;IACvC,CAAC;EACH,CAAC;EAED,MAAMzB,UAAUA,CAACD,GAAa,EAA8B;IAC1D,MAAM,IAAIe,OAAO,CAACC,OAAO,IAAI3E,UAAU,CAAC2E,OAAO,EAAE,GAAG,CAAC,CAAC;IAEtD,IAAIoE,YAAY,GAAG,CAAC;IACpB,KAAK,MAAM5F,EAAE,IAAIQ,GAAG,EAAE;MACpB,MAAMqD,KAAK,GAAGnD,eAAe,CAACoD,SAAS,CAACjC,GAAG,IAAIA,GAAG,CAAC7B,EAAE,KAAKA,EAAE,CAAC;MAC7D,IAAI6D,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBnD,eAAe,CAACsD,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;QAChC+B,YAAY,EAAE;MAChB;IACF;IAEA,OAAO;MACLlH,IAAI,EAAEP,SAAgB;MACtBiF,OAAO,EAAE,IAAI;MACbvH,OAAO,EAAE,GAAG+J,YAAY;IAC1B,CAAC;EACH;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,GAAG,GAAGvK,OAAO,CAACC,GAAG,CAACuK,QAAQ,KAAK,aAAa,IAAI,CAACxK,OAAO,CAACC,GAAG,CAACwK,sBAAsB,GAC5FzE,kBAAkB,GAClBnC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
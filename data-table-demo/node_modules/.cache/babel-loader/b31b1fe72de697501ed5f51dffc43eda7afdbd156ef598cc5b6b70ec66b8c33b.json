{"ast": null, "code": "// src/queriesObserver.ts\nimport { notifyManager } from \"./notifyManager.js\";\nimport { QueryObserver } from \"./queryObserver.js\";\nimport { Subscribable } from \"./subscribable.js\";\nimport { replaceEqualDeep } from \"./utils.js\";\nfunction difference(array1, array2) {\n  const excludeSet = new Set(array2);\n  return array1.filter(x => !excludeSet.has(x));\n}\nfunction replaceAt(array, index, value) {\n  const copy = array.slice(0);\n  copy[index] = value;\n  return copy;\n}\nvar QueriesObserver = class extends Subscribable {\n  #client;\n  #result;\n  #queries;\n  #options;\n  #observers;\n  #combinedResult;\n  #lastCombine;\n  #lastResult;\n  #observerMatches = [];\n  constructor(client, queries, options) {\n    super();\n    this.#client = client;\n    this.#options = options;\n    this.#queries = [];\n    this.#observers = [];\n    this.#result = [];\n    this.setQueries(queries);\n  }\n  onSubscribe() {\n    if (this.listeners.size === 1) {\n      this.#observers.forEach(observer => {\n        observer.subscribe(result => {\n          this.#onUpdate(observer, result);\n        });\n      });\n    }\n  }\n  onUnsubscribe() {\n    if (!this.listeners.size) {\n      this.destroy();\n    }\n  }\n  destroy() {\n    this.listeners = /* @__PURE__ */new Set();\n    this.#observers.forEach(observer => {\n      observer.destroy();\n    });\n  }\n  setQueries(queries, options) {\n    this.#queries = queries;\n    this.#options = options;\n    if (process.env.NODE_ENV !== \"production\") {\n      const queryHashes = queries.map(query => this.#client.defaultQueryOptions(query).queryHash);\n      if (new Set(queryHashes).size !== queryHashes.length) {\n        console.warn(\"[QueriesObserver]: Duplicate Queries found. This might result in unexpected behavior.\");\n      }\n    }\n    notifyManager.batch(() => {\n      const prevObservers = this.#observers;\n      const newObserverMatches = this.#findMatchingObservers(this.#queries);\n      this.#observerMatches = newObserverMatches;\n      newObserverMatches.forEach(match => match.observer.setOptions(match.defaultedQueryOptions));\n      const newObservers = newObserverMatches.map(match => match.observer);\n      const newResult = newObservers.map(observer => observer.getCurrentResult());\n      const hasIndexChange = newObservers.some((observer, index) => observer !== prevObservers[index]);\n      if (prevObservers.length === newObservers.length && !hasIndexChange) {\n        return;\n      }\n      this.#observers = newObservers;\n      this.#result = newResult;\n      if (!this.hasListeners()) {\n        return;\n      }\n      difference(prevObservers, newObservers).forEach(observer => {\n        observer.destroy();\n      });\n      difference(newObservers, prevObservers).forEach(observer => {\n        observer.subscribe(result => {\n          this.#onUpdate(observer, result);\n        });\n      });\n      this.#notify();\n    });\n  }\n  getCurrentResult() {\n    return this.#result;\n  }\n  getQueries() {\n    return this.#observers.map(observer => observer.getCurrentQuery());\n  }\n  getObservers() {\n    return this.#observers;\n  }\n  getOptimisticResult(queries, combine) {\n    const matches = this.#findMatchingObservers(queries);\n    const result = matches.map(match => match.observer.getOptimisticResult(match.defaultedQueryOptions));\n    return [result, r => {\n      return this.#combineResult(r ?? result, combine);\n    }, () => {\n      return this.#trackResult(result, matches);\n    }];\n  }\n  #trackResult(result, matches) {\n    return matches.map((match, index) => {\n      const observerResult = result[index];\n      return !match.defaultedQueryOptions.notifyOnChangeProps ? match.observer.trackResult(observerResult, accessedProp => {\n        matches.forEach(m => {\n          m.observer.trackProp(accessedProp);\n        });\n      }) : observerResult;\n    });\n  }\n  #combineResult(input, combine) {\n    if (combine) {\n      if (!this.#combinedResult || this.#result !== this.#lastResult || combine !== this.#lastCombine) {\n        this.#lastCombine = combine;\n        this.#lastResult = this.#result;\n        this.#combinedResult = replaceEqualDeep(this.#combinedResult, combine(input));\n      }\n      return this.#combinedResult;\n    }\n    return input;\n  }\n  #findMatchingObservers(queries) {\n    const prevObserversMap = new Map(this.#observers.map(observer => [observer.options.queryHash, observer]));\n    const observers = [];\n    queries.forEach(options => {\n      const defaultedOptions = this.#client.defaultQueryOptions(options);\n      const match = prevObserversMap.get(defaultedOptions.queryHash);\n      if (match) {\n        observers.push({\n          defaultedQueryOptions: defaultedOptions,\n          observer: match\n        });\n      } else {\n        observers.push({\n          defaultedQueryOptions: defaultedOptions,\n          observer: new QueryObserver(this.#client, defaultedOptions)\n        });\n      }\n    });\n    return observers;\n  }\n  #onUpdate(observer, result) {\n    const index = this.#observers.indexOf(observer);\n    if (index !== -1) {\n      this.#result = replaceAt(this.#result, index, result);\n      this.#notify();\n    }\n  }\n  #notify() {\n    if (this.hasListeners()) {\n      const previousResult = this.#combinedResult;\n      const newTracked = this.#trackResult(this.#result, this.#observerMatches);\n      const newResult = this.#combineResult(newTracked, this.#options?.combine);\n      if (previousResult !== newResult) {\n        notifyManager.batch(() => {\n          this.listeners.forEach(listener => {\n            listener(this.#result);\n          });\n        });\n      }\n    }\n  }\n};\nexport { QueriesObserver };", "map": {"version": 3, "names": ["notify<PERSON><PERSON>ger", "QueryObserver", "Subscribable", "replaceEqualDeep", "difference", "array1", "array2", "excludeSet", "Set", "filter", "x", "has", "replaceAt", "array", "index", "value", "copy", "slice", "QueriesObserver", "client", "result", "queries", "options", "observers", "combinedResult", "lastCombine", "lastResult", "observerMatches", "constructor", "setQueries", "onSubscribe", "listeners", "size", "for<PERSON>ach", "observer", "subscribe", "onUpdate", "onUnsubscribe", "destroy", "process", "env", "NODE_ENV", "queryHashes", "map", "query", "defaultQueryOptions", "queryHash", "length", "console", "warn", "batch", "prevObservers", "newObserverMatches", "findMatchingObservers", "match", "setOptions", "defaultedQueryOptions", "newObservers", "newResult", "getCurrentResult", "hasIndexChange", "some", "hasListeners", "notify", "getQueries", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getObservers", "getOptimisticResult", "combine", "matches", "r", "combineResult", "trackResult", "#trackResult", "observerResult", "notifyOnChangeProps", "accessedProp", "m", "trackProp", "#combineResult", "input", "#findMatchingObservers", "prevObserversMap", "Map", "defaultedOptions", "get", "push", "#onUpdate", "indexOf", "#notify", "previousResult", "newTracked", "listener"], "sources": ["/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/node_modules/@tanstack/query-core/src/queriesObserver.ts"], "sourcesContent": ["import { notify<PERSON><PERSON><PERSON> } from './notifyManager'\nimport { QueryObserver } from './queryObserver'\nimport { Subscribable } from './subscribable'\nimport { replaceEqualDeep } from './utils'\nimport type {\n  DefaultedQueryObserverOptions,\n  QueryObserverOptions,\n  QueryObserverResult,\n} from './types'\nimport type { QueryClient } from './queryClient'\n\nfunction difference<T>(array1: Array<T>, array2: Array<T>): Array<T> {\n  const excludeSet = new Set(array2)\n  return array1.filter((x) => !excludeSet.has(x))\n}\n\nfunction replaceAt<T>(array: Array<T>, index: number, value: T): Array<T> {\n  const copy = array.slice(0)\n  copy[index] = value\n  return copy\n}\n\ntype QueriesObserverListener = (result: Array<QueryObserverResult>) => void\n\ntype CombineFn<TCombinedResult> = (\n  result: Array<QueryObserverResult>,\n) => TCombinedResult\n\nexport interface QueriesObserverOptions<\n  TCombinedResult = Array<QueryObserverResult>,\n> {\n  combine?: CombineFn<TCombinedResult>\n}\n\nexport class QueriesObserver<\n  TCombinedResult = Array<QueryObserverResult>,\n> extends Subscribable<QueriesObserverListener> {\n  #client: QueryClient\n  #result!: Array<QueryObserverResult>\n  #queries: Array<QueryObserverOptions>\n  #options?: QueriesObserverOptions<TCombinedResult>\n  #observers: Array<QueryObserver>\n  #combinedResult?: TCombinedResult\n  #lastCombine?: CombineFn<TCombinedResult>\n  #lastResult?: Array<QueryObserverResult>\n  #observerMatches: Array<QueryObserverMatch> = []\n\n  constructor(\n    client: QueryClient,\n    queries: Array<QueryObserverOptions<any, any, any, any, any>>,\n    options?: QueriesObserverOptions<TCombinedResult>,\n  ) {\n    super()\n\n    this.#client = client\n    this.#options = options\n    this.#queries = []\n    this.#observers = []\n    this.#result = []\n\n    this.setQueries(queries)\n  }\n\n  protected onSubscribe(): void {\n    if (this.listeners.size === 1) {\n      this.#observers.forEach((observer) => {\n        observer.subscribe((result) => {\n          this.#onUpdate(observer, result)\n        })\n      })\n    }\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.listeners.size) {\n      this.destroy()\n    }\n  }\n\n  destroy(): void {\n    this.listeners = new Set()\n    this.#observers.forEach((observer) => {\n      observer.destroy()\n    })\n  }\n\n  setQueries(\n    queries: Array<QueryObserverOptions>,\n    options?: QueriesObserverOptions<TCombinedResult>,\n  ): void {\n    this.#queries = queries\n    this.#options = options\n\n    if (process.env.NODE_ENV !== 'production') {\n      const queryHashes = queries.map(\n        (query) => this.#client.defaultQueryOptions(query).queryHash,\n      )\n      if (new Set(queryHashes).size !== queryHashes.length) {\n        console.warn(\n          '[QueriesObserver]: Duplicate Queries found. This might result in unexpected behavior.',\n        )\n      }\n    }\n\n    notifyManager.batch(() => {\n      const prevObservers = this.#observers\n\n      const newObserverMatches = this.#findMatchingObservers(this.#queries)\n      this.#observerMatches = newObserverMatches\n\n      // set options for the new observers to notify of changes\n      newObserverMatches.forEach((match) =>\n        match.observer.setOptions(match.defaultedQueryOptions),\n      )\n\n      const newObservers = newObserverMatches.map((match) => match.observer)\n      const newResult = newObservers.map((observer) =>\n        observer.getCurrentResult(),\n      )\n\n      const hasIndexChange = newObservers.some(\n        (observer, index) => observer !== prevObservers[index],\n      )\n\n      if (prevObservers.length === newObservers.length && !hasIndexChange) {\n        return\n      }\n\n      this.#observers = newObservers\n      this.#result = newResult\n\n      if (!this.hasListeners()) {\n        return\n      }\n\n      difference(prevObservers, newObservers).forEach((observer) => {\n        observer.destroy()\n      })\n\n      difference(newObservers, prevObservers).forEach((observer) => {\n        observer.subscribe((result) => {\n          this.#onUpdate(observer, result)\n        })\n      })\n\n      this.#notify()\n    })\n  }\n\n  getCurrentResult(): Array<QueryObserverResult> {\n    return this.#result\n  }\n\n  getQueries() {\n    return this.#observers.map((observer) => observer.getCurrentQuery())\n  }\n\n  getObservers() {\n    return this.#observers\n  }\n\n  getOptimisticResult(\n    queries: Array<QueryObserverOptions>,\n    combine: CombineFn<TCombinedResult> | undefined,\n  ): [\n    rawResult: Array<QueryObserverResult>,\n    combineResult: (r?: Array<QueryObserverResult>) => TCombinedResult,\n    trackResult: () => Array<QueryObserverResult>,\n  ] {\n    const matches = this.#findMatchingObservers(queries)\n    const result = matches.map((match) =>\n      match.observer.getOptimisticResult(match.defaultedQueryOptions),\n    )\n\n    return [\n      result,\n      (r?: Array<QueryObserverResult>) => {\n        return this.#combineResult(r ?? result, combine)\n      },\n      () => {\n        return this.#trackResult(result, matches)\n      },\n    ]\n  }\n\n  #trackResult(\n    result: Array<QueryObserverResult>,\n    matches: Array<QueryObserverMatch>,\n  ) {\n    return matches.map((match, index) => {\n      const observerResult = result[index]!\n      return !match.defaultedQueryOptions.notifyOnChangeProps\n        ? match.observer.trackResult(observerResult, (accessedProp) => {\n            // track property on all observers to ensure proper (synchronized) tracking (#7000)\n            matches.forEach((m) => {\n              m.observer.trackProp(accessedProp)\n            })\n          })\n        : observerResult\n    })\n  }\n\n  #combineResult(\n    input: Array<QueryObserverResult>,\n    combine: CombineFn<TCombinedResult> | undefined,\n  ): TCombinedResult {\n    if (combine) {\n      if (\n        !this.#combinedResult ||\n        this.#result !== this.#lastResult ||\n        combine !== this.#lastCombine\n      ) {\n        this.#lastCombine = combine\n        this.#lastResult = this.#result\n        this.#combinedResult = replaceEqualDeep(\n          this.#combinedResult,\n          combine(input),\n        )\n      }\n\n      return this.#combinedResult\n    }\n    return input as any\n  }\n\n  #findMatchingObservers(\n    queries: Array<QueryObserverOptions>,\n  ): Array<QueryObserverMatch> {\n    const prevObserversMap = new Map(\n      this.#observers.map((observer) => [observer.options.queryHash, observer]),\n    )\n\n    const observers: Array<QueryObserverMatch> = []\n\n    queries.forEach((options) => {\n      const defaultedOptions = this.#client.defaultQueryOptions(options)\n      const match = prevObserversMap.get(defaultedOptions.queryHash)\n      if (match) {\n        observers.push({\n          defaultedQueryOptions: defaultedOptions,\n          observer: match,\n        })\n      } else {\n        observers.push({\n          defaultedQueryOptions: defaultedOptions,\n          observer: new QueryObserver(this.#client, defaultedOptions),\n        })\n      }\n    })\n\n    return observers\n  }\n\n  #onUpdate(observer: QueryObserver, result: QueryObserverResult): void {\n    const index = this.#observers.indexOf(observer)\n    if (index !== -1) {\n      this.#result = replaceAt(this.#result, index, result)\n      this.#notify()\n    }\n  }\n\n  #notify(): void {\n    if (this.hasListeners()) {\n      const previousResult = this.#combinedResult\n      const newTracked = this.#trackResult(this.#result, this.#observerMatches)\n      const newResult = this.#combineResult(newTracked, this.#options?.combine)\n\n      if (previousResult !== newResult) {\n        notifyManager.batch(() => {\n          this.listeners.forEach((listener) => {\n            listener(this.#result)\n          })\n        })\n      }\n    }\n  }\n}\n\ntype QueryObserverMatch = {\n  defaultedQueryOptions: DefaultedQueryObserverOptions\n  observer: QueryObserver\n}\n"], "mappings": ";AAAA,SAASA,aAAA,QAAqB;AAC9B,SAASC,aAAA,QAAqB;AAC9B,SAASC,YAAA,QAAoB;AAC7B,SAASC,gBAAA,QAAwB;AAQjC,SAASC,WAAcC,MAAA,EAAkBC,MAAA,EAA4B;EACnE,MAAMC,UAAA,GAAa,IAAIC,GAAA,CAAIF,MAAM;EACjC,OAAOD,MAAA,CAAOI,MAAA,CAAQC,CAAA,IAAM,CAACH,UAAA,CAAWI,GAAA,CAAID,CAAC,CAAC;AAChD;AAEA,SAASE,UAAaC,KAAA,EAAiBC,KAAA,EAAeC,KAAA,EAAoB;EACxE,MAAMC,IAAA,GAAOH,KAAA,CAAMI,KAAA,CAAM,CAAC;EAC1BD,IAAA,CAAKF,KAAK,IAAIC,KAAA;EACd,OAAOC,IAAA;AACT;AAcO,IAAME,eAAA,GAAN,cAEGhB,YAAA,CAAsC;EAC9C,CAAAiB,MAAA;EACA,CAAAC,MAAA;EACA,CAAAC,OAAA;EACA,CAAAC,OAAA;EACA,CAAAC,SAAA;EACA,CAAAC,cAAA;EACA,CAAAC,WAAA;EACA,CAAAC,UAAA;EACA,CAAAC,eAAA,GAA8C,EAAC;EAE/CC,YACET,MAAA,EACAE,OAAA,EACAC,OAAA,EACA;IACA,MAAM;IAEN,KAAK,CAAAH,MAAA,GAAUA,MAAA;IACf,KAAK,CAAAG,OAAA,GAAWA,OAAA;IAChB,KAAK,CAAAD,OAAA,GAAW,EAAC;IACjB,KAAK,CAAAE,SAAA,GAAa,EAAC;IACnB,KAAK,CAAAH,MAAA,GAAU,EAAC;IAEhB,KAAKS,UAAA,CAAWR,OAAO;EACzB;EAEUS,YAAA,EAAoB;IAC5B,IAAI,KAAKC,SAAA,CAAUC,IAAA,KAAS,GAAG;MAC7B,KAAK,CAAAT,SAAA,CAAWU,OAAA,CAASC,QAAA,IAAa;QACpCA,QAAA,CAASC,SAAA,CAAWf,MAAA,IAAW;UAC7B,KAAK,CAAAgB,QAAA,CAAUF,QAAA,EAAUd,MAAM;QACjC,CAAC;MACH,CAAC;IACH;EACF;EAEUiB,cAAA,EAAsB;IAC9B,IAAI,CAAC,KAAKN,SAAA,CAAUC,IAAA,EAAM;MACxB,KAAKM,OAAA,CAAQ;IACf;EACF;EAEAA,QAAA,EAAgB;IACd,KAAKP,SAAA,GAAY,mBAAIvB,GAAA,CAAI;IACzB,KAAK,CAAAe,SAAA,CAAWU,OAAA,CAASC,QAAA,IAAa;MACpCA,QAAA,CAASI,OAAA,CAAQ;IACnB,CAAC;EACH;EAEAT,WACER,OAAA,EACAC,OAAA,EACM;IACN,KAAK,CAAAD,OAAA,GAAWA,OAAA;IAChB,KAAK,CAAAC,OAAA,GAAWA,OAAA;IAEhB,IAAIiB,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;MACzC,MAAMC,WAAA,GAAcrB,OAAA,CAAQsB,GAAA,CACzBC,KAAA,IAAU,KAAK,CAAAzB,MAAA,CAAQ0B,mBAAA,CAAoBD,KAAK,EAAEE,SACrD;MACA,IAAI,IAAItC,GAAA,CAAIkC,WAAW,EAAEV,IAAA,KAASU,WAAA,CAAYK,MAAA,EAAQ;QACpDC,OAAA,CAAQC,IAAA,CACN,uFACF;MACF;IACF;IAEAjD,aAAA,CAAckD,KAAA,CAAM,MAAM;MACxB,MAAMC,aAAA,GAAgB,KAAK,CAAA5B,SAAA;MAE3B,MAAM6B,kBAAA,GAAqB,KAAK,CAAAC,qBAAA,CAAuB,KAAK,CAAAhC,OAAQ;MACpE,KAAK,CAAAM,eAAA,GAAmByB,kBAAA;MAGxBA,kBAAA,CAAmBnB,OAAA,CAASqB,KAAA,IAC1BA,KAAA,CAAMpB,QAAA,CAASqB,UAAA,CAAWD,KAAA,CAAME,qBAAqB,CACvD;MAEA,MAAMC,YAAA,GAAeL,kBAAA,CAAmBT,GAAA,CAAKW,KAAA,IAAUA,KAAA,CAAMpB,QAAQ;MACrE,MAAMwB,SAAA,GAAYD,YAAA,CAAad,GAAA,CAAKT,QAAA,IAClCA,QAAA,CAASyB,gBAAA,CAAiB,CAC5B;MAEA,MAAMC,cAAA,GAAiBH,YAAA,CAAaI,IAAA,CAClC,CAAC3B,QAAA,EAAUpB,KAAA,KAAUoB,QAAA,KAAaiB,aAAA,CAAcrC,KAAK,CACvD;MAEA,IAAIqC,aAAA,CAAcJ,MAAA,KAAWU,YAAA,CAAaV,MAAA,IAAU,CAACa,cAAA,EAAgB;QACnE;MACF;MAEA,KAAK,CAAArC,SAAA,GAAakC,YAAA;MAClB,KAAK,CAAArC,MAAA,GAAUsC,SAAA;MAEf,IAAI,CAAC,KAAKI,YAAA,CAAa,GAAG;QACxB;MACF;MAEA1D,UAAA,CAAW+C,aAAA,EAAeM,YAAY,EAAExB,OAAA,CAASC,QAAA,IAAa;QAC5DA,QAAA,CAASI,OAAA,CAAQ;MACnB,CAAC;MAEDlC,UAAA,CAAWqD,YAAA,EAAcN,aAAa,EAAElB,OAAA,CAASC,QAAA,IAAa;QAC5DA,QAAA,CAASC,SAAA,CAAWf,MAAA,IAAW;UAC7B,KAAK,CAAAgB,QAAA,CAAUF,QAAA,EAAUd,MAAM;QACjC,CAAC;MACH,CAAC;MAED,KAAK,CAAA2C,MAAA,CAAQ;IACf,CAAC;EACH;EAEAJ,iBAAA,EAA+C;IAC7C,OAAO,KAAK,CAAAvC,MAAA;EACd;EAEA4C,WAAA,EAAa;IACX,OAAO,KAAK,CAAAzC,SAAA,CAAWoB,GAAA,CAAKT,QAAA,IAAaA,QAAA,CAAS+B,eAAA,CAAgB,CAAC;EACrE;EAEAC,aAAA,EAAe;IACb,OAAO,KAAK,CAAA3C,SAAA;EACd;EAEA4C,oBACE9C,OAAA,EACA+C,OAAA,EAKA;IACA,MAAMC,OAAA,GAAU,KAAK,CAAAhB,qBAAA,CAAuBhC,OAAO;IACnD,MAAMD,MAAA,GAASiD,OAAA,CAAQ1B,GAAA,CAAKW,KAAA,IAC1BA,KAAA,CAAMpB,QAAA,CAASiC,mBAAA,CAAoBb,KAAA,CAAME,qBAAqB,CAChE;IAEA,OAAO,CACLpC,MAAA,EACCkD,CAAA,IAAmC;MAClC,OAAO,KAAK,CAAAC,aAAA,CAAeD,CAAA,IAAKlD,MAAA,EAAQgD,OAAO;IACjD,GACA,MAAM;MACJ,OAAO,KAAK,CAAAI,WAAA,CAAapD,MAAA,EAAQiD,OAAO;IAC1C,EACF;EACF;EAEA,CAAAG,WAAAC,CACErD,MAAA,EACAiD,OAAA,EACA;IACA,OAAOA,OAAA,CAAQ1B,GAAA,CAAI,CAACW,KAAA,EAAOxC,KAAA,KAAU;MACnC,MAAM4D,cAAA,GAAiBtD,MAAA,CAAON,KAAK;MACnC,OAAO,CAACwC,KAAA,CAAME,qBAAA,CAAsBmB,mBAAA,GAChCrB,KAAA,CAAMpB,QAAA,CAASsC,WAAA,CAAYE,cAAA,EAAiBE,YAAA,IAAiB;QAE3DP,OAAA,CAAQpC,OAAA,CAAS4C,CAAA,IAAM;UACrBA,CAAA,CAAE3C,QAAA,CAAS4C,SAAA,CAAUF,YAAY;QACnC,CAAC;MACH,CAAC,IACDF,cAAA;IACN,CAAC;EACH;EAEA,CAAAH,aAAAQ,CACEC,KAAA,EACAZ,OAAA,EACiB;IACjB,IAAIA,OAAA,EAAS;MACX,IACE,CAAC,KAAK,CAAA5C,cAAA,IACN,KAAK,CAAAJ,MAAA,KAAY,KAAK,CAAAM,UAAA,IACtB0C,OAAA,KAAY,KAAK,CAAA3C,WAAA,EACjB;QACA,KAAK,CAAAA,WAAA,GAAe2C,OAAA;QACpB,KAAK,CAAA1C,UAAA,GAAc,KAAK,CAAAN,MAAA;QACxB,KAAK,CAAAI,cAAA,GAAkBrB,gBAAA,CACrB,KAAK,CAAAqB,cAAA,EACL4C,OAAA,CAAQY,KAAK,CACf;MACF;MAEA,OAAO,KAAK,CAAAxD,cAAA;IACd;IACA,OAAOwD,KAAA;EACT;EAEA,CAAA3B,qBAAA4B,CACE5D,OAAA,EAC2B;IAC3B,MAAM6D,gBAAA,GAAmB,IAAIC,GAAA,CAC3B,KAAK,CAAA5D,SAAA,CAAWoB,GAAA,CAAKT,QAAA,IAAa,CAACA,QAAA,CAASZ,OAAA,CAAQwB,SAAA,EAAWZ,QAAQ,CAAC,CAC1E;IAEA,MAAMX,SAAA,GAAuC,EAAC;IAE9CF,OAAA,CAAQY,OAAA,CAASX,OAAA,IAAY;MAC3B,MAAM8D,gBAAA,GAAmB,KAAK,CAAAjE,MAAA,CAAQ0B,mBAAA,CAAoBvB,OAAO;MACjE,MAAMgC,KAAA,GAAQ4B,gBAAA,CAAiBG,GAAA,CAAID,gBAAA,CAAiBtC,SAAS;MAC7D,IAAIQ,KAAA,EAAO;QACT/B,SAAA,CAAU+D,IAAA,CAAK;UACb9B,qBAAA,EAAuB4B,gBAAA;UACvBlD,QAAA,EAAUoB;QACZ,CAAC;MACH,OAAO;QACL/B,SAAA,CAAU+D,IAAA,CAAK;UACb9B,qBAAA,EAAuB4B,gBAAA;UACvBlD,QAAA,EAAU,IAAIjC,aAAA,CAAc,KAAK,CAAAkB,MAAA,EAASiE,gBAAgB;QAC5D,CAAC;MACH;IACF,CAAC;IAED,OAAO7D,SAAA;EACT;EAEA,CAAAa,QAAAmD,CAAUrD,QAAA,EAAyBd,MAAA,EAAmC;IACpE,MAAMN,KAAA,GAAQ,KAAK,CAAAS,SAAA,CAAWiE,OAAA,CAAQtD,QAAQ;IAC9C,IAAIpB,KAAA,KAAU,IAAI;MAChB,KAAK,CAAAM,MAAA,GAAUR,SAAA,CAAU,KAAK,CAAAQ,MAAA,EAASN,KAAA,EAAOM,MAAM;MACpD,KAAK,CAAA2C,MAAA,CAAQ;IACf;EACF;EAEA,CAAAA,MAAA0B,CAAA,EAAgB;IACd,IAAI,KAAK3B,YAAA,CAAa,GAAG;MACvB,MAAM4B,cAAA,GAAiB,KAAK,CAAAlE,cAAA;MAC5B,MAAMmE,UAAA,GAAa,KAAK,CAAAnB,WAAA,CAAa,KAAK,CAAApD,MAAA,EAAS,KAAK,CAAAO,eAAgB;MACxE,MAAM+B,SAAA,GAAY,KAAK,CAAAa,aAAA,CAAeoB,UAAA,EAAY,KAAK,CAAArE,OAAA,EAAU8C,OAAO;MAExE,IAAIsB,cAAA,KAAmBhC,SAAA,EAAW;QAChC1D,aAAA,CAAckD,KAAA,CAAM,MAAM;UACxB,KAAKnB,SAAA,CAAUE,OAAA,CAAS2D,QAAA,IAAa;YACnCA,QAAA,CAAS,KAAK,CAAAxE,MAAO;UACvB,CAAC;QACH,CAAC;MACH;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
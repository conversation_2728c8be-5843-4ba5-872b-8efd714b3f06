{"ast": null, "code": "// src/onlineManager.ts\nimport { Subscribable } from \"./subscribable.js\";\nimport { isServer } from \"./utils.js\";\nvar OnlineManager = class extends Subscribable {\n  #online = true;\n  #cleanup;\n  #setup;\n  constructor() {\n    super();\n    this.#setup = onOnline => {\n      if (!isServer && window.addEventListener) {\n        const onlineListener = () => onOnline(true);\n        const offlineListener = () => onOnline(false);\n        window.addEventListener(\"online\", onlineListener, false);\n        window.addEventListener(\"offline\", offlineListener, false);\n        return () => {\n          window.removeEventListener(\"online\", onlineListener);\n          window.removeEventListener(\"offline\", offlineListener);\n        };\n      }\n      return;\n    };\n  }\n  onSubscribe() {\n    if (!this.#cleanup) {\n      this.setEventListener(this.#setup);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#cleanup?.();\n      this.#cleanup = void 0;\n    }\n  }\n  setEventListener(setup) {\n    this.#setup = setup;\n    this.#cleanup?.();\n    this.#cleanup = setup(this.setOnline.bind(this));\n  }\n  setOnline(online) {\n    const changed = this.#online !== online;\n    if (changed) {\n      this.#online = online;\n      this.listeners.forEach(listener => {\n        listener(online);\n      });\n    }\n  }\n  isOnline() {\n    return this.#online;\n  }\n};\nvar onlineManager = new OnlineManager();\nexport { OnlineManager, onlineManager };", "map": {"version": 3, "names": ["Subscribable", "isServer", "OnlineManager", "online", "cleanup", "setup", "constructor", "onOnline", "window", "addEventListener", "onlineListener", "offlineListener", "removeEventListener", "onSubscribe", "setEventListener", "onUnsubscribe", "hasListeners", "setOnline", "bind", "changed", "listeners", "for<PERSON>ach", "listener", "isOnline", "onlineManager"], "sources": ["/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/node_modules/@tanstack/query-core/src/onlineManager.ts"], "sourcesContent": ["import { Subscribable } from './subscribable'\nimport { isServer } from './utils'\n\ntype Listener = (online: boolean) => void\ntype SetupFn = (setOnline: Listener) => (() => void) | undefined\n\nexport class OnlineManager extends Subscribable<Listener> {\n  #online = true\n  #cleanup?: () => void\n\n  #setup: SetupFn\n\n  constructor() {\n    super()\n    this.#setup = (onOnline) => {\n      // addEventListener does not exist in React Native, but window does\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      if (!isServer && window.addEventListener) {\n        const onlineListener = () => onOnline(true)\n        const offlineListener = () => onOnline(false)\n        // Listen to online\n        window.addEventListener('online', onlineListener, false)\n        window.addEventListener('offline', offlineListener, false)\n\n        return () => {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('online', onlineListener)\n          window.removeEventListener('offline', offlineListener)\n        }\n      }\n\n      return\n    }\n  }\n\n  protected onSubscribe(): void {\n    if (!this.#cleanup) {\n      this.setEventListener(this.#setup)\n    }\n  }\n\n  protected onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#cleanup?.()\n      this.#cleanup = undefined\n    }\n  }\n\n  setEventListener(setup: SetupFn): void {\n    this.#setup = setup\n    this.#cleanup?.()\n    this.#cleanup = setup(this.setOnline.bind(this))\n  }\n\n  setOnline(online: boolean): void {\n    const changed = this.#online !== online\n\n    if (changed) {\n      this.#online = online\n      this.listeners.forEach((listener) => {\n        listener(online)\n      })\n    }\n  }\n\n  isOnline(): boolean {\n    return this.#online\n  }\n}\n\nexport const onlineManager = new OnlineManager()\n"], "mappings": ";AAAA,SAASA,YAAA,QAAoB;AAC7B,SAASC,QAAA,QAAgB;AAKlB,IAAMC,aAAA,GAAN,cAA4BF,YAAA,CAAuB;EACxD,CAAAG,MAAA,GAAU;EACV,CAAAC,OAAA;EAEA,CAAAC,KAAA;EAEAC,YAAA,EAAc;IACZ,MAAM;IACN,KAAK,CAAAD,KAAA,GAAUE,QAAA,IAAa;MAG1B,IAAI,CAACN,QAAA,IAAYO,MAAA,CAAOC,gBAAA,EAAkB;QACxC,MAAMC,cAAA,GAAiBA,CAAA,KAAMH,QAAA,CAAS,IAAI;QAC1C,MAAMI,eAAA,GAAkBA,CAAA,KAAMJ,QAAA,CAAS,KAAK;QAE5CC,MAAA,CAAOC,gBAAA,CAAiB,UAAUC,cAAA,EAAgB,KAAK;QACvDF,MAAA,CAAOC,gBAAA,CAAiB,WAAWE,eAAA,EAAiB,KAAK;QAEzD,OAAO,MAAM;UAEXH,MAAA,CAAOI,mBAAA,CAAoB,UAAUF,cAAc;UACnDF,MAAA,CAAOI,mBAAA,CAAoB,WAAWD,eAAe;QACvD;MACF;MAEA;IACF;EACF;EAEUE,YAAA,EAAoB;IAC5B,IAAI,CAAC,KAAK,CAAAT,OAAA,EAAU;MAClB,KAAKU,gBAAA,CAAiB,KAAK,CAAAT,KAAM;IACnC;EACF;EAEUU,cAAA,EAAgB;IACxB,IAAI,CAAC,KAAKC,YAAA,CAAa,GAAG;MACxB,KAAK,CAAAZ,OAAA,GAAW;MAChB,KAAK,CAAAA,OAAA,GAAW;IAClB;EACF;EAEAU,iBAAiBT,KAAA,EAAsB;IACrC,KAAK,CAAAA,KAAA,GAASA,KAAA;IACd,KAAK,CAAAD,OAAA,GAAW;IAChB,KAAK,CAAAA,OAAA,GAAWC,KAAA,CAAM,KAAKY,SAAA,CAAUC,IAAA,CAAK,IAAI,CAAC;EACjD;EAEAD,UAAUd,MAAA,EAAuB;IAC/B,MAAMgB,OAAA,GAAU,KAAK,CAAAhB,MAAA,KAAYA,MAAA;IAEjC,IAAIgB,OAAA,EAAS;MACX,KAAK,CAAAhB,MAAA,GAAUA,MAAA;MACf,KAAKiB,SAAA,CAAUC,OAAA,CAASC,QAAA,IAAa;QACnCA,QAAA,CAASnB,MAAM;MACjB,CAAC;IACH;EACF;EAEAoB,SAAA,EAAoB;IAClB,OAAO,KAAK,CAAApB,MAAA;EACd;AACF;AAEO,IAAMqB,aAAA,GAAgB,IAAItB,aAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
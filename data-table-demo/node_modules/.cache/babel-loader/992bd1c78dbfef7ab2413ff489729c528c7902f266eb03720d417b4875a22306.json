{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/src/contexts/ExperimentContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\n// React context for experiment management\nimport React, { createContext, useContext, useReducer, useCallback } from 'react';\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\nimport { ReactQueryDevtools } from '@tanstack/react-query-devtools';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Query client configuration\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      staleTime: 5 * 60 * 1000,\n      // 5 minutes\n      gcTime: 10 * 60 * 1000,\n      // 10 minutes (renamed from cacheTime)\n      refetchOnWindowFocus: false,\n      refetchOnMount: true,\n      retry: (failureCount, error) => {\n        // Don't retry on 4xx errors\n        if ((error === null || error === void 0 ? void 0 : error.statusCode) >= 400 && (error === null || error === void 0 ? void 0 : error.statusCode) < 500) {\n          return false;\n        }\n        return failureCount < 3;\n      }\n    },\n    mutations: {\n      retry: 1\n    }\n  }\n});\n\n// Context state interface\n\n// Context actions\n\n// Initial state\nconst initialState = {\n  selectedExperiments: new Set(),\n  filters: {},\n  pagination: {\n    page: 1,\n    limit: 20,\n    sortBy: 'createdAt',\n    sortOrder: 'desc'\n  },\n  globalLoading: {\n    isLoading: false,\n    isError: false,\n    error: null\n  },\n  cacheConfig: {\n    staleTime: 5 * 60 * 1000,\n    cacheTime: 10 * 60 * 1000,\n    refetchOnWindowFocus: false,\n    refetchOnMount: true\n  },\n  optimisticUpdates: new Map()\n};\n\n// Reducer function\nfunction experimentReducer(state, action) {\n  switch (action.type) {\n    case 'SET_SELECTED_EXPERIMENTS':\n      return {\n        ...state,\n        selectedExperiments: action.payload\n      };\n    case 'TOGGLE_EXPERIMENT_SELECTION':\n      const newSelection = new Set(state.selectedExperiments);\n      if (newSelection.has(action.payload)) {\n        newSelection.delete(action.payload);\n      } else {\n        newSelection.add(action.payload);\n      }\n      return {\n        ...state,\n        selectedExperiments: newSelection\n      };\n    case 'CLEAR_SELECTION':\n      return {\n        ...state,\n        selectedExperiments: new Set()\n      };\n    case 'SET_FILTERS':\n      return {\n        ...state,\n        filters: action.payload,\n        pagination: {\n          ...state.pagination,\n          page: 1 // Reset to first page when filters change\n        }\n      };\n    case 'SET_PAGINATION':\n      return {\n        ...state,\n        pagination: action.payload\n      };\n    case 'SET_GLOBAL_LOADING':\n      return {\n        ...state,\n        globalLoading: action.payload\n      };\n    case 'SET_CACHE_CONFIG':\n      return {\n        ...state,\n        cacheConfig: action.payload\n      };\n    case 'ADD_OPTIMISTIC_UPDATE':\n      const newUpdates = new Map(state.optimisticUpdates);\n      newUpdates.set(action.payload.id, action.payload.update);\n      return {\n        ...state,\n        optimisticUpdates: newUpdates\n      };\n    case 'REMOVE_OPTIMISTIC_UPDATE':\n      const updatesWithoutRemoved = new Map(state.optimisticUpdates);\n      updatesWithoutRemoved.delete(action.payload);\n      return {\n        ...state,\n        optimisticUpdates: updatesWithoutRemoved\n      };\n    case 'CLEAR_OPTIMISTIC_UPDATES':\n      return {\n        ...state,\n        optimisticUpdates: new Map()\n      };\n    default:\n      return state;\n  }\n}\n\n// Context interface\n\n// Create context\nconst ExperimentContext = /*#__PURE__*/createContext(undefined);\n\n// Context provider component\n\nexport const ExperimentProvider = ({\n  children,\n  initialFilters = {},\n  initialPagination\n}) => {\n  _s();\n  const [state, dispatch] = useReducer(experimentReducer, {\n    ...initialState,\n    filters: {\n      ...initialState.filters,\n      ...initialFilters\n    },\n    pagination: {\n      ...initialState.pagination,\n      ...initialPagination\n    }\n  });\n\n  // Action creators\n  const setSelectedExperiments = useCallback(experiments => {\n    dispatch({\n      type: 'SET_SELECTED_EXPERIMENTS',\n      payload: experiments\n    });\n  }, []);\n  const toggleExperimentSelection = useCallback(experimentId => {\n    dispatch({\n      type: 'TOGGLE_EXPERIMENT_SELECTION',\n      payload: experimentId\n    });\n  }, []);\n  const clearSelection = useCallback(() => {\n    dispatch({\n      type: 'CLEAR_SELECTION'\n    });\n  }, []);\n  const setFilters = useCallback(filters => {\n    dispatch({\n      type: 'SET_FILTERS',\n      payload: filters\n    });\n  }, []);\n  const setPagination = useCallback(pagination => {\n    dispatch({\n      type: 'SET_PAGINATION',\n      payload: pagination\n    });\n  }, []);\n  const setGlobalLoading = useCallback(loading => {\n    dispatch({\n      type: 'SET_GLOBAL_LOADING',\n      payload: loading\n    });\n  }, []);\n  const setCacheConfig = useCallback(config => {\n    dispatch({\n      type: 'SET_CACHE_CONFIG',\n      payload: config\n    });\n  }, []);\n  const addOptimisticUpdate = useCallback((id, update) => {\n    dispatch({\n      type: 'ADD_OPTIMISTIC_UPDATE',\n      payload: {\n        id,\n        update\n      }\n    });\n  }, []);\n  const removeOptimisticUpdate = useCallback(id => {\n    dispatch({\n      type: 'REMOVE_OPTIMISTIC_UPDATE',\n      payload: id\n    });\n  }, []);\n  const clearOptimisticUpdates = useCallback(() => {\n    dispatch({\n      type: 'CLEAR_OPTIMISTIC_UPDATES'\n    });\n  }, []);\n  const contextValue = {\n    state,\n    setSelectedExperiments,\n    toggleExperimentSelection,\n    clearSelection,\n    setFilters,\n    setPagination,\n    setGlobalLoading,\n    setCacheConfig,\n    addOptimisticUpdate,\n    removeOptimisticUpdate,\n    clearOptimisticUpdates,\n    queryClient\n  };\n  return /*#__PURE__*/_jsxDEV(QueryClientProvider, {\n    client: queryClient,\n    children: /*#__PURE__*/_jsxDEV(ExperimentContext.Provider, {\n      value: contextValue,\n      children: [children, process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(ReactQueryDevtools, {\n        initialIsOpen: false\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 276,\n    columnNumber: 5\n  }, this);\n};\n\n// Custom hook to use the experiment context\n_s(ExperimentProvider, \"/fOjsBbi0mHSkbjZ5TV9mfqRDSA=\");\n_c = ExperimentProvider;\nexport const useExperimentContext = () => {\n  _s2();\n  const context = useContext(ExperimentContext);\n  if (!context) {\n    throw new Error('useExperimentContext must be used within an ExperimentProvider');\n  }\n  return context;\n};\n_s2(useExperimentContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"ExperimentProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useReducer", "useCallback", "QueryClient", "QueryClientProvider", "ReactQueryDevtools", "jsxDEV", "_jsxDEV", "queryClient", "defaultOptions", "queries", "staleTime", "gcTime", "refetchOnWindowFocus", "refetchOnMount", "retry", "failureCount", "error", "statusCode", "mutations", "initialState", "selectedExperiments", "Set", "filters", "pagination", "page", "limit", "sortBy", "sortOrder", "globalLoading", "isLoading", "isError", "cacheConfig", "cacheTime", "optimisticUpdates", "Map", "experimentReducer", "state", "action", "type", "payload", "newSelection", "has", "delete", "add", "newUpdates", "set", "id", "update", "updatesWithoutRemoved", "ExperimentContext", "undefined", "ExperimentProvider", "children", "initialFilters", "initialPagination", "_s", "dispatch", "setSelectedExperiments", "experiments", "toggleExperimentSelection", "experimentId", "clearSelection", "setFilters", "setPagination", "setGlobalLoading", "loading", "setCacheConfig", "config", "addOptimisticUpdate", "removeOptimisticUpdate", "clearOptimisticUpdates", "contextValue", "client", "Provider", "value", "process", "env", "NODE_ENV", "initialIsOpen", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useExperimentContext", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/src/contexts/ExperimentContext.tsx"], "sourcesContent": ["// React context for experiment management\nimport React, { createContext, useContext, useReducer, useCallback, ReactNode } from 'react';\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\nimport { ReactQueryDevtools } from '@tanstack/react-query-devtools';\nimport {\n  Experiment,\n  ExperimentFilters,\n  PaginationParams,\n  LoadingState,\n  CacheConfig,\n  OptimisticUpdate,\n} from '../types/experiment';\n\n// Query client configuration\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      staleTime: 5 * 60 * 1000, // 5 minutes\n      gcTime: 10 * 60 * 1000, // 10 minutes (renamed from cacheTime)\n      refetchOnWindowFocus: false,\n      refetchOnMount: true,\n      retry: (failureCount, error: any) => {\n        // Don't retry on 4xx errors\n        if (error?.statusCode >= 400 && error?.statusCode < 500) {\n          return false;\n        }\n        return failureCount < 3;\n      },\n    },\n    mutations: {\n      retry: 1,\n    },\n  },\n});\n\n// Context state interface\ninterface ExperimentContextState {\n  // UI State\n  selectedExperiments: Set<string>;\n  filters: ExperimentFilters;\n  pagination: PaginationParams;\n  \n  // Loading states\n  globalLoading: LoadingState;\n  \n  // Cache configuration\n  cacheConfig: CacheConfig;\n  \n  // Optimistic updates\n  optimisticUpdates: Map<string, OptimisticUpdate<any>>;\n}\n\n// Context actions\ntype ExperimentAction =\n  | { type: 'SET_SELECTED_EXPERIMENTS'; payload: Set<string> }\n  | { type: 'TOGGLE_EXPERIMENT_SELECTION'; payload: string }\n  | { type: 'CLEAR_SELECTION' }\n  | { type: 'SET_FILTERS'; payload: ExperimentFilters }\n  | { type: 'SET_PAGINATION'; payload: PaginationParams }\n  | { type: 'SET_GLOBAL_LOADING'; payload: LoadingState }\n  | { type: 'SET_CACHE_CONFIG'; payload: CacheConfig }\n  | { type: 'ADD_OPTIMISTIC_UPDATE'; payload: { id: string; update: OptimisticUpdate<any> } }\n  | { type: 'REMOVE_OPTIMISTIC_UPDATE'; payload: string }\n  | { type: 'CLEAR_OPTIMISTIC_UPDATES' };\n\n// Initial state\nconst initialState: ExperimentContextState = {\n  selectedExperiments: new Set(),\n  filters: {},\n  pagination: {\n    page: 1,\n    limit: 20,\n    sortBy: 'createdAt',\n    sortOrder: 'desc',\n  },\n  globalLoading: {\n    isLoading: false,\n    isError: false,\n    error: null,\n  },\n  cacheConfig: {\n    staleTime: 5 * 60 * 1000,\n    cacheTime: 10 * 60 * 1000,\n    refetchOnWindowFocus: false,\n    refetchOnMount: true,\n  },\n  optimisticUpdates: new Map(),\n};\n\n// Reducer function\nfunction experimentReducer(\n  state: ExperimentContextState,\n  action: ExperimentAction\n): ExperimentContextState {\n  switch (action.type) {\n    case 'SET_SELECTED_EXPERIMENTS':\n      return {\n        ...state,\n        selectedExperiments: action.payload,\n      };\n\n    case 'TOGGLE_EXPERIMENT_SELECTION':\n      const newSelection = new Set(state.selectedExperiments);\n      if (newSelection.has(action.payload)) {\n        newSelection.delete(action.payload);\n      } else {\n        newSelection.add(action.payload);\n      }\n      return {\n        ...state,\n        selectedExperiments: newSelection,\n      };\n\n    case 'CLEAR_SELECTION':\n      return {\n        ...state,\n        selectedExperiments: new Set(),\n      };\n\n    case 'SET_FILTERS':\n      return {\n        ...state,\n        filters: action.payload,\n        pagination: {\n          ...state.pagination,\n          page: 1, // Reset to first page when filters change\n        },\n      };\n\n    case 'SET_PAGINATION':\n      return {\n        ...state,\n        pagination: action.payload,\n      };\n\n    case 'SET_GLOBAL_LOADING':\n      return {\n        ...state,\n        globalLoading: action.payload,\n      };\n\n    case 'SET_CACHE_CONFIG':\n      return {\n        ...state,\n        cacheConfig: action.payload,\n      };\n\n    case 'ADD_OPTIMISTIC_UPDATE':\n      const newUpdates = new Map(state.optimisticUpdates);\n      newUpdates.set(action.payload.id, action.payload.update);\n      return {\n        ...state,\n        optimisticUpdates: newUpdates,\n      };\n\n    case 'REMOVE_OPTIMISTIC_UPDATE':\n      const updatesWithoutRemoved = new Map(state.optimisticUpdates);\n      updatesWithoutRemoved.delete(action.payload);\n      return {\n        ...state,\n        optimisticUpdates: updatesWithoutRemoved,\n      };\n\n    case 'CLEAR_OPTIMISTIC_UPDATES':\n      return {\n        ...state,\n        optimisticUpdates: new Map(),\n      };\n\n    default:\n      return state;\n  }\n}\n\n// Context interface\ninterface ExperimentContextValue {\n  // State\n  state: ExperimentContextState;\n  \n  // Actions\n  setSelectedExperiments: (experiments: Set<string>) => void;\n  toggleExperimentSelection: (experimentId: string) => void;\n  clearSelection: () => void;\n  setFilters: (filters: ExperimentFilters) => void;\n  setPagination: (pagination: PaginationParams) => void;\n  setGlobalLoading: (loading: LoadingState) => void;\n  setCacheConfig: (config: CacheConfig) => void;\n  \n  // Optimistic updates\n  addOptimisticUpdate: (id: string, update: OptimisticUpdate<any>) => void;\n  removeOptimisticUpdate: (id: string) => void;\n  clearOptimisticUpdates: () => void;\n  \n  // Query client access\n  queryClient: QueryClient;\n}\n\n// Create context\nconst ExperimentContext = createContext<ExperimentContextValue | undefined>(undefined);\n\n// Context provider component\ninterface ExperimentProviderProps {\n  children: ReactNode;\n  initialFilters?: ExperimentFilters;\n  initialPagination?: PaginationParams;\n}\n\nexport const ExperimentProvider: React.FC<ExperimentProviderProps> = ({\n  children,\n  initialFilters = {},\n  initialPagination,\n}) => {\n  const [state, dispatch] = useReducer(experimentReducer, {\n    ...initialState,\n    filters: { ...initialState.filters, ...initialFilters },\n    pagination: { ...initialState.pagination, ...initialPagination },\n  });\n\n  // Action creators\n  const setSelectedExperiments = useCallback((experiments: Set<string>) => {\n    dispatch({ type: 'SET_SELECTED_EXPERIMENTS', payload: experiments });\n  }, []);\n\n  const toggleExperimentSelection = useCallback((experimentId: string) => {\n    dispatch({ type: 'TOGGLE_EXPERIMENT_SELECTION', payload: experimentId });\n  }, []);\n\n  const clearSelection = useCallback(() => {\n    dispatch({ type: 'CLEAR_SELECTION' });\n  }, []);\n\n  const setFilters = useCallback((filters: ExperimentFilters) => {\n    dispatch({ type: 'SET_FILTERS', payload: filters });\n  }, []);\n\n  const setPagination = useCallback((pagination: PaginationParams) => {\n    dispatch({ type: 'SET_PAGINATION', payload: pagination });\n  }, []);\n\n  const setGlobalLoading = useCallback((loading: LoadingState) => {\n    dispatch({ type: 'SET_GLOBAL_LOADING', payload: loading });\n  }, []);\n\n  const setCacheConfig = useCallback((config: CacheConfig) => {\n    dispatch({ type: 'SET_CACHE_CONFIG', payload: config });\n  }, []);\n\n  const addOptimisticUpdate = useCallback((id: string, update: OptimisticUpdate<any>) => {\n    dispatch({ type: 'ADD_OPTIMISTIC_UPDATE', payload: { id, update } });\n  }, []);\n\n  const removeOptimisticUpdate = useCallback((id: string) => {\n    dispatch({ type: 'REMOVE_OPTIMISTIC_UPDATE', payload: id });\n  }, []);\n\n  const clearOptimisticUpdates = useCallback(() => {\n    dispatch({ type: 'CLEAR_OPTIMISTIC_UPDATES' });\n  }, []);\n\n  const contextValue: ExperimentContextValue = {\n    state,\n    setSelectedExperiments,\n    toggleExperimentSelection,\n    clearSelection,\n    setFilters,\n    setPagination,\n    setGlobalLoading,\n    setCacheConfig,\n    addOptimisticUpdate,\n    removeOptimisticUpdate,\n    clearOptimisticUpdates,\n    queryClient,\n  };\n\n  return (\n    <QueryClientProvider client={queryClient}>\n      <ExperimentContext.Provider value={contextValue}>\n        {children}\n        {process.env.NODE_ENV === 'development' && (\n          <ReactQueryDevtools initialIsOpen={false} />\n        )}\n      </ExperimentContext.Provider>\n    </QueryClientProvider>\n  );\n};\n\n// Custom hook to use the experiment context\nexport const useExperimentContext = (): ExperimentContextValue => {\n  const context = useContext(ExperimentContext);\n  if (!context) {\n    throw new Error('useExperimentContext must be used within an ExperimentProvider');\n  }\n  return context;\n};\n"], "mappings": ";;;AAAA;AACA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEC,WAAW,QAAmB,OAAO;AAC5F,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,uBAAuB;AACxE,SAASC,kBAAkB,QAAQ,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAUpE;AACA,MAAMC,WAAW,GAAG,IAAIL,WAAW,CAAC;EAClCM,cAAc,EAAE;IACdC,OAAO,EAAE;MACPC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;MAAE;MAC1BC,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;MAAE;MACxBC,oBAAoB,EAAE,KAAK;MAC3BC,cAAc,EAAE,IAAI;MACpBC,KAAK,EAAEA,CAACC,YAAY,EAAEC,KAAU,KAAK;QACnC;QACA,IAAI,CAAAA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,UAAU,KAAI,GAAG,IAAI,CAAAD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,UAAU,IAAG,GAAG,EAAE;UACvD,OAAO,KAAK;QACd;QACA,OAAOF,YAAY,GAAG,CAAC;MACzB;IACF,CAAC;IACDG,SAAS,EAAE;MACTJ,KAAK,EAAE;IACT;EACF;AACF,CAAC,CAAC;;AAEF;;AAiBA;;AAaA;AACA,MAAMK,YAAoC,GAAG;EAC3CC,mBAAmB,EAAE,IAAIC,GAAG,CAAC,CAAC;EAC9BC,OAAO,EAAE,CAAC,CAAC;EACXC,UAAU,EAAE;IACVC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,WAAW;IACnBC,SAAS,EAAE;EACb,CAAC;EACDC,aAAa,EAAE;IACbC,SAAS,EAAE,KAAK;IAChBC,OAAO,EAAE,KAAK;IACdd,KAAK,EAAE;EACT,CAAC;EACDe,WAAW,EAAE;IACXrB,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;IACxBsB,SAAS,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IACzBpB,oBAAoB,EAAE,KAAK;IAC3BC,cAAc,EAAE;EAClB,CAAC;EACDoB,iBAAiB,EAAE,IAAIC,GAAG,CAAC;AAC7B,CAAC;;AAED;AACA,SAASC,iBAAiBA,CACxBC,KAA6B,EAC7BC,MAAwB,EACA;EACxB,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK,0BAA0B;MAC7B,OAAO;QACL,GAAGF,KAAK;QACRhB,mBAAmB,EAAEiB,MAAM,CAACE;MAC9B,CAAC;IAEH,KAAK,6BAA6B;MAChC,MAAMC,YAAY,GAAG,IAAInB,GAAG,CAACe,KAAK,CAAChB,mBAAmB,CAAC;MACvD,IAAIoB,YAAY,CAACC,GAAG,CAACJ,MAAM,CAACE,OAAO,CAAC,EAAE;QACpCC,YAAY,CAACE,MAAM,CAACL,MAAM,CAACE,OAAO,CAAC;MACrC,CAAC,MAAM;QACLC,YAAY,CAACG,GAAG,CAACN,MAAM,CAACE,OAAO,CAAC;MAClC;MACA,OAAO;QACL,GAAGH,KAAK;QACRhB,mBAAmB,EAAEoB;MACvB,CAAC;IAEH,KAAK,iBAAiB;MACpB,OAAO;QACL,GAAGJ,KAAK;QACRhB,mBAAmB,EAAE,IAAIC,GAAG,CAAC;MAC/B,CAAC;IAEH,KAAK,aAAa;MAChB,OAAO;QACL,GAAGe,KAAK;QACRd,OAAO,EAAEe,MAAM,CAACE,OAAO;QACvBhB,UAAU,EAAE;UACV,GAAGa,KAAK,CAACb,UAAU;UACnBC,IAAI,EAAE,CAAC,CAAE;QACX;MACF,CAAC;IAEH,KAAK,gBAAgB;MACnB,OAAO;QACL,GAAGY,KAAK;QACRb,UAAU,EAAEc,MAAM,CAACE;MACrB,CAAC;IAEH,KAAK,oBAAoB;MACvB,OAAO;QACL,GAAGH,KAAK;QACRR,aAAa,EAAES,MAAM,CAACE;MACxB,CAAC;IAEH,KAAK,kBAAkB;MACrB,OAAO;QACL,GAAGH,KAAK;QACRL,WAAW,EAAEM,MAAM,CAACE;MACtB,CAAC;IAEH,KAAK,uBAAuB;MAC1B,MAAMK,UAAU,GAAG,IAAIV,GAAG,CAACE,KAAK,CAACH,iBAAiB,CAAC;MACnDW,UAAU,CAACC,GAAG,CAACR,MAAM,CAACE,OAAO,CAACO,EAAE,EAAET,MAAM,CAACE,OAAO,CAACQ,MAAM,CAAC;MACxD,OAAO;QACL,GAAGX,KAAK;QACRH,iBAAiB,EAAEW;MACrB,CAAC;IAEH,KAAK,0BAA0B;MAC7B,MAAMI,qBAAqB,GAAG,IAAId,GAAG,CAACE,KAAK,CAACH,iBAAiB,CAAC;MAC9De,qBAAqB,CAACN,MAAM,CAACL,MAAM,CAACE,OAAO,CAAC;MAC5C,OAAO;QACL,GAAGH,KAAK;QACRH,iBAAiB,EAAEe;MACrB,CAAC;IAEH,KAAK,0BAA0B;MAC7B,OAAO;QACL,GAAGZ,KAAK;QACRH,iBAAiB,EAAE,IAAIC,GAAG,CAAC;MAC7B,CAAC;IAEH;MACE,OAAOE,KAAK;EAChB;AACF;;AAEA;;AAuBA;AACA,MAAMa,iBAAiB,gBAAGnD,aAAa,CAAqCoD,SAAS,CAAC;;AAEtF;;AAOA,OAAO,MAAMC,kBAAqD,GAAGA,CAAC;EACpEC,QAAQ;EACRC,cAAc,GAAG,CAAC,CAAC;EACnBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACnB,KAAK,EAAEoB,QAAQ,CAAC,GAAGxD,UAAU,CAACmC,iBAAiB,EAAE;IACtD,GAAGhB,YAAY;IACfG,OAAO,EAAE;MAAE,GAAGH,YAAY,CAACG,OAAO;MAAE,GAAG+B;IAAe,CAAC;IACvD9B,UAAU,EAAE;MAAE,GAAGJ,YAAY,CAACI,UAAU;MAAE,GAAG+B;IAAkB;EACjE,CAAC,CAAC;;EAEF;EACA,MAAMG,sBAAsB,GAAGxD,WAAW,CAAEyD,WAAwB,IAAK;IACvEF,QAAQ,CAAC;MAAElB,IAAI,EAAE,0BAA0B;MAAEC,OAAO,EAAEmB;IAAY,CAAC,CAAC;EACtE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,yBAAyB,GAAG1D,WAAW,CAAE2D,YAAoB,IAAK;IACtEJ,QAAQ,CAAC;MAAElB,IAAI,EAAE,6BAA6B;MAAEC,OAAO,EAAEqB;IAAa,CAAC,CAAC;EAC1E,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,cAAc,GAAG5D,WAAW,CAAC,MAAM;IACvCuD,QAAQ,CAAC;MAAElB,IAAI,EAAE;IAAkB,CAAC,CAAC;EACvC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMwB,UAAU,GAAG7D,WAAW,CAAEqB,OAA0B,IAAK;IAC7DkC,QAAQ,CAAC;MAAElB,IAAI,EAAE,aAAa;MAAEC,OAAO,EAAEjB;IAAQ,CAAC,CAAC;EACrD,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMyC,aAAa,GAAG9D,WAAW,CAAEsB,UAA4B,IAAK;IAClEiC,QAAQ,CAAC;MAAElB,IAAI,EAAE,gBAAgB;MAAEC,OAAO,EAAEhB;IAAW,CAAC,CAAC;EAC3D,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMyC,gBAAgB,GAAG/D,WAAW,CAAEgE,OAAqB,IAAK;IAC9DT,QAAQ,CAAC;MAAElB,IAAI,EAAE,oBAAoB;MAAEC,OAAO,EAAE0B;IAAQ,CAAC,CAAC;EAC5D,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,cAAc,GAAGjE,WAAW,CAAEkE,MAAmB,IAAK;IAC1DX,QAAQ,CAAC;MAAElB,IAAI,EAAE,kBAAkB;MAAEC,OAAO,EAAE4B;IAAO,CAAC,CAAC;EACzD,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,mBAAmB,GAAGnE,WAAW,CAAC,CAAC6C,EAAU,EAAEC,MAA6B,KAAK;IACrFS,QAAQ,CAAC;MAAElB,IAAI,EAAE,uBAAuB;MAAEC,OAAO,EAAE;QAAEO,EAAE;QAAEC;MAAO;IAAE,CAAC,CAAC;EACtE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMsB,sBAAsB,GAAGpE,WAAW,CAAE6C,EAAU,IAAK;IACzDU,QAAQ,CAAC;MAAElB,IAAI,EAAE,0BAA0B;MAAEC,OAAO,EAAEO;IAAG,CAAC,CAAC;EAC7D,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMwB,sBAAsB,GAAGrE,WAAW,CAAC,MAAM;IAC/CuD,QAAQ,CAAC;MAAElB,IAAI,EAAE;IAA2B,CAAC,CAAC;EAChD,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMiC,YAAoC,GAAG;IAC3CnC,KAAK;IACLqB,sBAAsB;IACtBE,yBAAyB;IACzBE,cAAc;IACdC,UAAU;IACVC,aAAa;IACbC,gBAAgB;IAChBE,cAAc;IACdE,mBAAmB;IACnBC,sBAAsB;IACtBC,sBAAsB;IACtB/D;EACF,CAAC;EAED,oBACED,OAAA,CAACH,mBAAmB;IAACqE,MAAM,EAAEjE,WAAY;IAAA6C,QAAA,eACvC9C,OAAA,CAAC2C,iBAAiB,CAACwB,QAAQ;MAACC,KAAK,EAAEH,YAAa;MAAAnB,QAAA,GAC7CA,QAAQ,EACRuB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBACrCvE,OAAA,CAACF,kBAAkB;QAAC0E,aAAa,EAAE;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAC5C;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACyB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAE1B,CAAC;;AAED;AAAA3B,EAAA,CA/EaJ,kBAAqD;AAAAgC,EAAA,GAArDhC,kBAAqD;AAgFlE,OAAO,MAAMiC,oBAAoB,GAAGA,CAAA,KAA8B;EAAAC,GAAA;EAChE,MAAMC,OAAO,GAAGvF,UAAU,CAACkD,iBAAiB,CAAC;EAC7C,IAAI,CAACqC,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,gEAAgE,CAAC;EACnF;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,oBAAoB;AAAA,IAAAD,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
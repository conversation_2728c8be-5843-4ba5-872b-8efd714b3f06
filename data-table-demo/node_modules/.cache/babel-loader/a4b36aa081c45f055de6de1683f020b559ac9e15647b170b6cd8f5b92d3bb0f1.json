{"ast": null, "code": "\"use client\";\n\n// src/IsRestoringProvider.ts\nimport * as React from \"react\";\nvar IsRestoringContext = React.createContext(false);\nvar useIsRestoring = () => React.useContext(IsRestoringContext);\nvar IsRestoringProvider = IsRestoringContext.Provider;\nexport { IsRestoringProvider, useIsRestoring };", "map": {"version": 3, "names": ["React", "IsRestoringContext", "createContext", "useIsRestoring", "useContext", "IsRestoringProvider", "Provider"], "sources": ["/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/node_modules/@tanstack/react-query/src/IsRestoringProvider.ts"], "sourcesContent": ["'use client'\nimport * as React from 'react'\n\nconst IsRestoringContext = React.createContext(false)\n\nexport const useIsRestoring = () => React.useContext(IsRestoringContext)\nexport const IsRestoringProvider = IsRestoringContext.Provider\n"], "mappings": ";;;AACA,YAAYA,KAAA,MAAW;AAEvB,IAAMC,kBAAA,GAA2BD,KAAA,CAAAE,aAAA,CAAc,KAAK;AAE7C,IAAMC,cAAA,GAAiBA,CAAA,KAAYH,KAAA,CAAAI,UAAA,CAAWH,kBAAkB;AAChE,IAAMI,mBAAA,GAAsBJ,kBAAA,CAAmBK,QAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
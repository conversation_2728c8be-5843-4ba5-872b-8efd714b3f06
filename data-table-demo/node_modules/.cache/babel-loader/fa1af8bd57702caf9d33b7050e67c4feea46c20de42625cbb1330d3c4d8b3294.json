{"ast": null, "code": "\"use client\";\n\n// src/ReactQueryDevtools.tsx\nimport * as React from \"react\";\nimport { onlineManager, useQueryClient } from \"@tanstack/react-query\";\nimport { TanstackQueryDevtools } from \"@tanstack/query-devtools\";\nimport { jsx } from \"react/jsx-runtime\";\nfunction ReactQueryDevtools(props) {\n  const queryClient = useQueryClient(props.client);\n  const ref = React.useRef(null);\n  const {\n    buttonPosition,\n    position,\n    initialIsOpen,\n    errorTypes,\n    styleNonce,\n    shadowDOMTarget\n  } = props;\n  const [devtools] = React.useState(new TanstackQueryDevtools({\n    client: queryClient,\n    queryFlavor: \"React Query\",\n    version: \"5\",\n    onlineManager,\n    buttonPosition,\n    position,\n    initialIsOpen,\n    errorTypes,\n    styleNonce,\n    shadowDOMTarget\n  }));\n  React.useEffect(() => {\n    devtools.setClient(queryClient);\n  }, [queryClient, devtools]);\n  React.useEffect(() => {\n    if (buttonPosition) {\n      devtools.setButtonPosition(buttonPosition);\n    }\n  }, [buttonPosition, devtools]);\n  React.useEffect(() => {\n    if (position) {\n      devtools.setPosition(position);\n    }\n  }, [position, devtools]);\n  React.useEffect(() => {\n    devtools.setInitialIsOpen(initialIsOpen || false);\n  }, [initialIsOpen, devtools]);\n  React.useEffect(() => {\n    devtools.setErrorTypes(errorTypes || []);\n  }, [errorTypes, devtools]);\n  React.useEffect(() => {\n    if (ref.current) {\n      devtools.mount(ref.current);\n    }\n    return () => {\n      devtools.unmount();\n    };\n  }, [devtools]);\n  return /* @__PURE__ */jsx(\"div\", {\n    dir: \"ltr\",\n    className: \"tsqd-parent-container\",\n    ref\n  });\n}\nexport { ReactQueryDevtools };", "map": {"version": 3, "names": ["React", "onlineManager", "useQueryClient", "TanstackQueryDevtools", "jsx", "ReactQueryDevtools", "props", "queryClient", "client", "ref", "useRef", "buttonPosition", "position", "initialIsOpen", "errorTypes", "styleNonce", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "devtools", "useState", "queryFlavor", "version", "useEffect", "setClient", "setButtonPosition", "setPosition", "setInitialIsOpen", "setErrorTypes", "current", "mount", "unmount", "dir", "className"], "sources": ["/Users/<USER>/Documents/augment-projects/Curation/data-table-demo/node_modules/@tanstack/react-query-devtools/src/ReactQueryDevtools.tsx"], "sourcesContent": ["'use client'\nimport * as React from 'react'\nimport { onlineManager, useQueryClient } from '@tanstack/react-query'\nimport { TanstackQueryDevtools } from '@tanstack/query-devtools'\nimport type {\n  DevtoolsButtonPosition,\n  DevtoolsErrorType,\n  DevtoolsPosition,\n} from '@tanstack/query-devtools'\nimport type { QueryClient } from '@tanstack/react-query'\n\nexport interface DevtoolsOptions {\n  /**\n   * Set this true if you want the dev tools to default to being open\n   */\n  initialIsOpen?: boolean\n  /**\n   * The position of the React Query logo to open and close the devtools panel.\n   * 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'\n   * Defaults to 'bottom-right'.\n   */\n  buttonPosition?: DevtoolsButtonPosition\n  /**\n   * The position of the React Query devtools panel.\n   * 'top' | 'bottom' | 'left' | 'right'\n   * Defaults to 'bottom'.\n   */\n  position?: DevtoolsPosition\n  /**\n   * Custom instance of QueryClient\n   */\n  client?: QueryClient\n  /**\n   * Use this so you can define custom errors that can be shown in the devtools.\n   */\n  errorTypes?: Array<DevtoolsErrorType>\n  /**\n   * Use this to pass a nonce to the style tag that is added to the document head. This is useful if you are using a Content Security Policy (CSP) nonce to allow inline styles.\n   */\n  styleNonce?: string\n  /**\n   * Use this so you can attach the devtool's styles to specific element in the DOM.\n   */\n  shadowDOMTarget?: ShadowRoot\n}\n\nexport function ReactQueryDevtools(\n  props: DevtoolsOptions,\n): React.ReactElement | null {\n  const queryClient = useQueryClient(props.client)\n  const ref = React.useRef<HTMLDivElement>(null)\n  const {\n    buttonPosition,\n    position,\n    initialIsOpen,\n    errorTypes,\n    styleNonce,\n    shadowDOMTarget,\n  } = props\n  const [devtools] = React.useState(\n    new TanstackQueryDevtools({\n      client: queryClient,\n      queryFlavor: 'React Query',\n      version: '5',\n      onlineManager,\n      buttonPosition,\n      position,\n      initialIsOpen,\n      errorTypes,\n      styleNonce,\n      shadowDOMTarget,\n    }),\n  )\n\n  React.useEffect(() => {\n    devtools.setClient(queryClient)\n  }, [queryClient, devtools])\n\n  React.useEffect(() => {\n    if (buttonPosition) {\n      devtools.setButtonPosition(buttonPosition)\n    }\n  }, [buttonPosition, devtools])\n\n  React.useEffect(() => {\n    if (position) {\n      devtools.setPosition(position)\n    }\n  }, [position, devtools])\n\n  React.useEffect(() => {\n    devtools.setInitialIsOpen(initialIsOpen || false)\n  }, [initialIsOpen, devtools])\n\n  React.useEffect(() => {\n    devtools.setErrorTypes(errorTypes || [])\n  }, [errorTypes, devtools])\n\n  React.useEffect(() => {\n    if (ref.current) {\n      devtools.mount(ref.current)\n    }\n\n    return () => {\n      devtools.unmount()\n    }\n  }, [devtools])\n\n  return <div dir=\"ltr\" className=\"tsqd-parent-container\" ref={ref}></div>\n}\n"], "mappings": ";;;AACA,YAAYA,KAAA,MAAW;AACvB,SAASC,aAAA,EAAeC,cAAA,QAAsB;AAC9C,SAASC,qBAAA,QAA6B;AAyG7B,SAAAC,GAAA;AA9DF,SAASC,mBACdC,KAAA,EAC2B;EAC3B,MAAMC,WAAA,GAAcL,cAAA,CAAeI,KAAA,CAAME,MAAM;EAC/C,MAAMC,GAAA,GAAYT,KAAA,CAAAU,MAAA,CAAuB,IAAI;EAC7C,MAAM;IACJC,cAAA;IACAC,QAAA;IACAC,aAAA;IACAC,UAAA;IACAC,UAAA;IACAC;EACF,IAAIV,KAAA;EACJ,MAAM,CAACW,QAAQ,IAAUjB,KAAA,CAAAkB,QAAA,CACvB,IAAIf,qBAAA,CAAsB;IACxBK,MAAA,EAAQD,WAAA;IACRY,WAAA,EAAa;IACbC,OAAA,EAAS;IACTnB,aAAA;IACAU,cAAA;IACAC,QAAA;IACAC,aAAA;IACAC,UAAA;IACAC,UAAA;IACAC;EACF,CAAC,CACH;EAEMhB,KAAA,CAAAqB,SAAA,CAAU,MAAM;IACpBJ,QAAA,CAASK,SAAA,CAAUf,WAAW;EAChC,GAAG,CAACA,WAAA,EAAaU,QAAQ,CAAC;EAEpBjB,KAAA,CAAAqB,SAAA,CAAU,MAAM;IACpB,IAAIV,cAAA,EAAgB;MAClBM,QAAA,CAASM,iBAAA,CAAkBZ,cAAc;IAC3C;EACF,GAAG,CAACA,cAAA,EAAgBM,QAAQ,CAAC;EAEvBjB,KAAA,CAAAqB,SAAA,CAAU,MAAM;IACpB,IAAIT,QAAA,EAAU;MACZK,QAAA,CAASO,WAAA,CAAYZ,QAAQ;IAC/B;EACF,GAAG,CAACA,QAAA,EAAUK,QAAQ,CAAC;EAEjBjB,KAAA,CAAAqB,SAAA,CAAU,MAAM;IACpBJ,QAAA,CAASQ,gBAAA,CAAiBZ,aAAA,IAAiB,KAAK;EAClD,GAAG,CAACA,aAAA,EAAeI,QAAQ,CAAC;EAEtBjB,KAAA,CAAAqB,SAAA,CAAU,MAAM;IACpBJ,QAAA,CAASS,aAAA,CAAcZ,UAAA,IAAc,EAAE;EACzC,GAAG,CAACA,UAAA,EAAYG,QAAQ,CAAC;EAEnBjB,KAAA,CAAAqB,SAAA,CAAU,MAAM;IACpB,IAAIZ,GAAA,CAAIkB,OAAA,EAAS;MACfV,QAAA,CAASW,KAAA,CAAMnB,GAAA,CAAIkB,OAAO;IAC5B;IAEA,OAAO,MAAM;MACXV,QAAA,CAASY,OAAA,CAAQ;IACnB;EACF,GAAG,CAACZ,QAAQ,CAAC;EAEb,OAAO,eAAAb,GAAA,CAAC;IAAI0B,GAAA,EAAI;IAAMC,SAAA,EAAU;IAAwBtB;EAAA,CAAU;AACpE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
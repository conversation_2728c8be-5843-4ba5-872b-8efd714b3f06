"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var genericRole = {
  abstract: false,
  accessibleNameRequired: false,
  baseConcepts: [],
  childrenPresentational: false,
  nameFrom: ['prohibited'],
  prohibitedProps: ['aria-label', 'aria-labelledby'],
  props: {},
  relatedConcepts: [{
    concept: {
      name: 'a'
    },
    module: 'HTML'
  }, {
    concept: {
      name: 'area'
    },
    module: 'HTML'
  }, {
    concept: {
      name: 'aside'
    },
    module: 'HTML'
  }, {
    concept: {
      name: 'b'
    },
    module: 'HTML'
  }, {
    concept: {
      name: 'bdo'
    },
    module: 'HTML'
  }, {
    concept: {
      name: 'body'
    },
    module: 'HTML'
  }, {
    concept: {
      name: 'data'
    },
    module: 'HTML'
  }, {
    concept: {
      name: 'div'
    },
    module: 'HTML'
  }, {
    concept: {
      constraints: ['scoped to the main element', 'scoped to a sectioning content element', 'scoped to a sectioning root element other than body'],
      name: 'footer'
    },
    module: 'HTML'
  }, {
    concept: {
      constraints: ['scoped to the main element', 'scoped to a sectioning content element', 'scoped to a sectioning root element other than body'],
      name: 'header'
    },
    module: 'HTML'
  }, {
    concept: {
      name: 'hgroup'
    },
    module: 'HTML'
  }, {
    concept: {
      name: 'i'
    },
    module: 'HTML'
  }, {
    concept: {
      name: 'pre'
    },
    module: 'HTML'
  }, {
    concept: {
      name: 'q'
    },
    module: 'HTML'
  }, {
    concept: {
      name: 'samp'
    },
    module: 'HTML'
  }, {
    concept: {
      name: 'section'
    },
    module: 'HTML'
  }, {
    concept: {
      name: 'small'
    },
    module: 'HTML'
  }, {
    concept: {
      name: 'span'
    },
    module: 'HTML'
  }, {
    concept: {
      name: 'u'
    },
    module: 'HTML'
  }],
  requireContextRole: [],
  requiredContextRole: [],
  requiredOwnedElements: [],
  requiredProps: {},
  superClass: [['roletype', 'structure']]
};
var _default = genericRole;
exports.default = _default;
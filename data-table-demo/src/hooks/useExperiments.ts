// Custom hook for experiment management with API communication, caching, and error handling
import { useCallback, useMemo, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import {
  Experiment,
  ExperimentFilters,
  PaginationParams,
  CreateExperimentForm,
  UpdateExperimentForm,
  ExperimentStatus,
  PaginatedResponse,
  ApiResponse,
  ApiError,
} from '../types/experiment';
import { mockExperimentsApi as experimentsApi } from '../services/api';
import { useExperimentContext } from '../contexts/ExperimentContext';

// Query keys factory
export const experimentKeys = {
  all: ['experiments'] as const,
  lists: () => [...experimentKeys.all, 'list'] as const,
  list: (filters: ExperimentFilters, pagination: PaginationParams) =>
    [...experimentKeys.lists(), { filters, pagination }] as const,
  details: () => [...experimentKeys.all, 'detail'] as const,
  detail: (id: string) => [...experimentKeys.details(), id] as const,
  analytics: (id: string) => [...experimentKeys.all, 'analytics', id] as const,
};

// Main experiments hook
export const useExperiments = (
  filters: ExperimentFilters = {},
  pagination: PaginationParams = { page: 1, limit: 20 }
) => {
  const queryClient = useQueryClient();
  const { state, setGlobalLoading, addOptimisticUpdate, removeOptimisticUpdate } = useExperimentContext();

  // Fetch experiments query
  const {
    data: experimentsResponse,
    isLoading,
    isError,
    error,
    refetch,
    isFetching,
    isPlaceholderData,
  } = useQuery({
    queryKey: experimentKeys.list(filters, pagination),
    queryFn: () => experimentsApi.getExperiments(filters, pagination),
    placeholderData: (previousData) => previousData,
    staleTime: state.cacheConfig.staleTime,
    gcTime: state.cacheConfig.gcTime,
    refetchOnWindowFocus: state.cacheConfig.refetchOnWindowFocus,
    refetchOnMount: state.cacheConfig.refetchOnMount,
  });

  // Handle loading states and errors
  useEffect(() => {
    if (isError && error) {
      setGlobalLoading({
        isLoading: false,
        isError: true,
        error: error.message,
      });
      toast.error(`Failed to load experiments: ${error.message}`);
    } else if (isLoading) {
      setGlobalLoading({
        isLoading: true,
        isError: false,
        error: null,
      });
    } else {
      setGlobalLoading({
        isLoading: false,
        isError: false,
        error: null,
      });
    }
  }, [isLoading, isError, error, setGlobalLoading]);

  // Extract data with fallbacks
  const experiments = useMemo(() => experimentsResponse?.data || [], [experimentsResponse]);
  const pagination_info = useMemo(() => experimentsResponse?.pagination, [experimentsResponse]);

  // Create experiment mutation
  const createMutation = useMutation({
    mutationFn: (data: CreateExperimentForm) => experimentsApi.createExperiment(data),
    onMutate: async (newExperiment) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: experimentKeys.lists() });

      // Snapshot previous value
      const previousExperiments = queryClient.getQueryData(
        experimentKeys.list(filters, pagination)
      );

      // Optimistically update
      const optimisticExperiment: Experiment = {
        id: `temp-${Date.now()}`,
        tenantId: 'current-tenant',
        name: newExperiment.name,
        description: newExperiment.description,
        status: 'DRAFT',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'current-user',
        variants: newExperiment.variants.map((v, i) => ({ ...v, id: `temp-variant-${i}` })),
        tags: newExperiment.tags || [],
        _count: { userAssignments: 0, events: 0 },
      };

      queryClient.setQueryData(
        experimentKeys.list(filters, pagination),
        (old: PaginatedResponse<Experiment> | undefined) => {
          if (!old) return old;
          return {
            ...old,
            data: [optimisticExperiment, ...old.data],
            pagination: {
              ...old.pagination,
              total: old.pagination.total + 1,
            },
          };
        }
      );

      // Store rollback function
      addOptimisticUpdate(`create-${optimisticExperiment.id}`, {
        type: 'create',
        data: optimisticExperiment,
        rollback: () => {
          queryClient.setQueryData(
            experimentKeys.list(filters, pagination),
            previousExperiments
          );
        },
      });

      return { previousExperiments, optimisticExperiment };
    },
    onError: (error: ApiError, newExperiment, context) => {
      // Rollback optimistic update
      if (context?.optimisticExperiment) {
        removeOptimisticUpdate(`create-${context.optimisticExperiment.id}`);
        queryClient.setQueryData(
          experimentKeys.list(filters, pagination),
          context.previousExperiments
        );
      }
      toast.error(`Failed to create experiment: ${error.message}`);
    },
    onSuccess: (response, variables, context) => {
      // Remove optimistic update
      if (context?.optimisticExperiment) {
        removeOptimisticUpdate(`create-${context.optimisticExperiment.id}`);
      }
      
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: experimentKeys.lists() });
      toast.success('Experiment created successfully');
    },
  });

  // Update experiment mutation
  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateExperimentForm }) =>
      experimentsApi.updateExperiment(id, data),
    onMutate: async ({ id, data }) => {
      await queryClient.cancelQueries({ queryKey: experimentKeys.detail(id) });
      await queryClient.cancelQueries({ queryKey: experimentKeys.lists() });

      const previousExperiment = queryClient.getQueryData(experimentKeys.detail(id));
      const previousList = queryClient.getQueryData(experimentKeys.list(filters, pagination));

      // Optimistically update detail
      queryClient.setQueryData(experimentKeys.detail(id), (old: ApiResponse<Experiment> | undefined) => {
        if (!old) return old;
        return {
          ...old,
          data: { ...old.data, ...data, updatedAt: new Date().toISOString() },
        };
      });

      // Optimistically update list
      queryClient.setQueryData(
        experimentKeys.list(filters, pagination),
        (old: PaginatedResponse<Experiment> | undefined) => {
          if (!old) return old;
          return {
            ...old,
            data: old.data.map(exp =>
              exp.id === id
                ? { ...exp, ...data, updatedAt: new Date().toISOString() }
                : exp
            ),
          };
        }
      );

      return { previousExperiment, previousList };
    },
    onError: (error: ApiError, { id }, context) => {
      // Rollback
      if (context?.previousExperiment) {
        queryClient.setQueryData(experimentKeys.detail(id), context.previousExperiment);
      }
      if (context?.previousList) {
        queryClient.setQueryData(experimentKeys.list(filters, pagination), context.previousList);
      }
      toast.error(`Failed to update experiment: ${error.message}`);
    },
    onSuccess: (response, { id }) => {
      queryClient.invalidateQueries({ queryKey: experimentKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: experimentKeys.lists() });
      toast.success('Experiment updated successfully');
    },
  });

  // Delete experiment mutation
  const deleteMutation = useMutation({
    mutationFn: (id: string) => experimentsApi.deleteExperiment(id),
    onMutate: async (id) => {
      await queryClient.cancelQueries({ queryKey: experimentKeys.lists() });

      const previousList = queryClient.getQueryData(experimentKeys.list(filters, pagination));

      // Optimistically remove from list
      queryClient.setQueryData(
        experimentKeys.list(filters, pagination),
        (old: PaginatedResponse<Experiment> | undefined) => {
          if (!old) return old;
          return {
            ...old,
            data: old.data.filter(exp => exp.id !== id),
            pagination: {
              ...old.pagination,
              total: old.pagination.total - 1,
            },
          };
        }
      );

      return { previousList };
    },
    onError: (error: ApiError, id, context) => {
      // Rollback
      if (context?.previousList) {
        queryClient.setQueryData(experimentKeys.list(filters, pagination), context.previousList);
      }
      toast.error(`Failed to delete experiment: ${error.message}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: experimentKeys.lists() });
      toast.success('Experiment deleted successfully');
    },
  });

  // Status change mutation
  const statusMutation = useMutation({
    mutationFn: ({ id, status, data }: { id: string; status: ExperimentStatus; data?: any }) =>
      experimentsApi.updateExperimentStatus(id, status, data),
    onMutate: async ({ id, status }) => {
      await queryClient.cancelQueries({ queryKey: experimentKeys.lists() });
      await queryClient.cancelQueries({ queryKey: experimentKeys.detail(id) });

      const previousList = queryClient.getQueryData(experimentKeys.list(filters, pagination));
      const previousDetail = queryClient.getQueryData(experimentKeys.detail(id));

      // Optimistically update status
      const updateStatus = (exp: Experiment) => ({
        ...exp,
        status,
        updatedAt: new Date().toISOString(),
      });

      queryClient.setQueryData(
        experimentKeys.list(filters, pagination),
        (old: PaginatedResponse<Experiment> | undefined) => {
          if (!old) return old;
          return {
            ...old,
            data: old.data.map(exp => exp.id === id ? updateStatus(exp) : exp),
          };
        }
      );

      queryClient.setQueryData(experimentKeys.detail(id), (old: ApiResponse<Experiment> | undefined) => {
        if (!old) return old;
        return {
          ...old,
          data: updateStatus(old.data),
        };
      });

      return { previousList, previousDetail };
    },
    onError: (error: ApiError, { id }, context) => {
      // Rollback
      if (context?.previousList) {
        queryClient.setQueryData(experimentKeys.list(filters, pagination), context.previousList);
      }
      if (context?.previousDetail) {
        queryClient.setQueryData(experimentKeys.detail(id), context.previousDetail);
      }
      toast.error(`Failed to update status: ${error.message}`);
    },
    onSuccess: (response, { status }) => {
      queryClient.invalidateQueries({ queryKey: experimentKeys.lists() });
      toast.success(`Experiment ${status.toLowerCase()} successfully`);
    },
  });

  // Duplicate experiment mutation
  const duplicateMutation = useMutation({
    mutationFn: (id: string) => experimentsApi.duplicateExperiment(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: experimentKeys.lists() });
      toast.success('Experiment duplicated successfully');
    },
    onError: (error: ApiError) => {
      toast.error(`Failed to duplicate experiment: ${error.message}`);
    },
  });

  // Exposed methods
  const createExperiment = useCallback(
    async (data: CreateExperimentForm): Promise<Experiment> => {
      const result = await createMutation.mutateAsync(data);
      return result.data;
    },
    [createMutation]
  );

  const updateExperiment = useCallback(
    async (id: string, data: UpdateExperimentForm): Promise<Experiment> => {
      const result = await updateMutation.mutateAsync({ id, data });
      return result.data;
    },
    [updateMutation]
  );

  const deleteExperiment = useCallback(
    async (id: string): Promise<void> => {
      await deleteMutation.mutateAsync(id);
    },
    [deleteMutation]
  );

  const changeStatus = useCallback(
    async (id: string, status: ExperimentStatus, data?: any): Promise<Experiment> => {
      const result = await statusMutation.mutateAsync({ id, status, data });
      return result.data;
    },
    [statusMutation]
  );

  const duplicateExperiment = useCallback(
    async (id: string): Promise<Experiment> => {
      const result = await duplicateMutation.mutateAsync(id);
      return result.data;
    },
    [duplicateMutation]
  );

  return {
    // Data
    experiments,
    pagination: pagination_info,
    
    // Loading states
    isLoading,
    isFetching,
    isError,
    error: error?.message,
    isPlaceholderData,
    
    // Mutation states
    isCreating: createMutation.isPending,
    isUpdating: updateMutation.isPending,
    isDeleting: deleteMutation.isPending,
    isChangingStatus: statusMutation.isPending,
    isDuplicating: duplicateMutation.isPending,
    
    // Methods
    createExperiment,
    updateExperiment,
    deleteExperiment,
    changeStatus,
    duplicateExperiment,
    refetch,
    
    // Raw mutations for advanced usage
    createMutation,
    updateMutation,
    deleteMutation,
    statusMutation,
    duplicateMutation,
  };
};

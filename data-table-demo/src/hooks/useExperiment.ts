// Custom hook for single experiment management
import { useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import {
  Experiment,
  ExperimentAnalytics,
  UpdateExperimentForm,
  ExperimentStatus,
  ApiResponse,
  ApiError,
} from '../types/experiment';
import { experimentsApi } from '../services/api';
import { experimentKeys } from './useExperiments';
import { useExperimentContext } from '../contexts/ExperimentContext';

// Hook for managing a single experiment
export const useExperiment = (experimentId: string) => {
  const queryClient = useQueryClient();
  const { state } = useExperimentContext();

  // Fetch single experiment
  const {
    data: experimentResponse,
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery({
    queryKey: experimentKeys.detail(experimentId),
    queryFn: () => experimentsApi.getExperiment(experimentId),
    enabled: !!experimentId,
    staleTime: state.cacheConfig.staleTime,
    gcTime: state.cacheConfig.gcTime,
  });

  // Fetch experiment analytics
  const {
    data: analyticsResponse,
    isLoading: isLoadingAnalytics,
    isError: isAnalyticsError,
    error: analyticsError,
    refetch: refetchAnalytics,
  } = useQuery({
    queryKey: experimentKeys.analytics(experimentId),
    queryFn: () => experimentsApi.getExperimentAnalytics(experimentId),
    enabled: !!experimentId && experimentResponse?.data?.status === 'ACTIVE',
    staleTime: 2 * 60 * 1000, // 2 minutes for analytics
  });

  // Update experiment mutation
  const updateMutation = useMutation({
    mutationFn: (data: UpdateExperimentForm) =>
      experimentsApi.updateExperiment(experimentId, data),
    onMutate: async (data) => {
      await queryClient.cancelQueries({ queryKey: experimentKeys.detail(experimentId) });

      const previousExperiment = queryClient.getQueryData(experimentKeys.detail(experimentId));

      // Optimistically update
      queryClient.setQueryData(
        experimentKeys.detail(experimentId),
        (old: ApiResponse<Experiment> | undefined) => {
          if (!old) return old;
          return {
            ...old,
            data: { ...old.data, ...data, updatedAt: new Date().toISOString() },
          };
        }
      );

      return { previousExperiment };
    },
    onError: (error: ApiError, data, context) => {
      // Rollback
      if (context?.previousExperiment) {
        queryClient.setQueryData(experimentKeys.detail(experimentId), context.previousExperiment);
      }
      toast.error(`Failed to update experiment: ${error.message}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: experimentKeys.detail(experimentId) });
      queryClient.invalidateQueries({ queryKey: experimentKeys.lists() });
      toast.success('Experiment updated successfully');
    },
  });

  // Status change mutation
  const statusMutation = useMutation({
    mutationFn: ({ status, data }: { status: ExperimentStatus; data?: any }) =>
      experimentsApi.updateExperimentStatus(experimentId, status, data),
    onMutate: async ({ status }) => {
      await queryClient.cancelQueries({ queryKey: experimentKeys.detail(experimentId) });

      const previousExperiment = queryClient.getQueryData(experimentKeys.detail(experimentId));

      // Optimistically update status
      queryClient.setQueryData(
        experimentKeys.detail(experimentId),
        (old: ApiResponse<Experiment> | undefined) => {
          if (!old) return old;
          return {
            ...old,
            data: {
              ...old.data,
              status,
              updatedAt: new Date().toISOString(),
            },
          };
        }
      );

      return { previousExperiment };
    },
    onError: (error: ApiError, { status }, context) => {
      // Rollback
      if (context?.previousExperiment) {
        queryClient.setQueryData(experimentKeys.detail(experimentId), context.previousExperiment);
      }
      toast.error(`Failed to update status: ${error.message}`);
    },
    onSuccess: (response, { status }) => {
      queryClient.invalidateQueries({ queryKey: experimentKeys.detail(experimentId) });
      queryClient.invalidateQueries({ queryKey: experimentKeys.lists() });
      
      // Refetch analytics if experiment becomes active
      if (status === 'ACTIVE') {
        queryClient.invalidateQueries({ queryKey: experimentKeys.analytics(experimentId) });
      }
      
      toast.success(`Experiment ${status.toLowerCase()} successfully`);
    },
  });

  // Delete experiment mutation
  const deleteMutation = useMutation({
    mutationFn: () => experimentsApi.deleteExperiment(experimentId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: experimentKeys.lists() });
      queryClient.removeQueries({ queryKey: experimentKeys.detail(experimentId) });
      queryClient.removeQueries({ queryKey: experimentKeys.analytics(experimentId) });
      toast.success('Experiment deleted successfully');
    },
    onError: (error: ApiError) => {
      toast.error(`Failed to delete experiment: ${error.message}`);
    },
  });

  // Duplicate experiment mutation
  const duplicateMutation = useMutation({
    mutationFn: () => experimentsApi.duplicateExperiment(experimentId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: experimentKeys.lists() });
      toast.success('Experiment duplicated successfully');
    },
    onError: (error: ApiError) => {
      toast.error(`Failed to duplicate experiment: ${error.message}`);
    },
  });

  // Exposed methods
  const updateExperiment = useCallback(
    async (data: UpdateExperimentForm): Promise<Experiment> => {
      const result = await updateMutation.mutateAsync(data);
      return result.data;
    },
    [updateMutation]
  );

  const changeStatus = useCallback(
    async (status: ExperimentStatus, data?: any): Promise<Experiment> => {
      const result = await statusMutation.mutateAsync({ status, data });
      return result.data;
    },
    [statusMutation]
  );

  const deleteExperiment = useCallback(async (): Promise<void> => {
    await deleteMutation.mutateAsync();
  }, [deleteMutation]);

  const duplicateExperiment = useCallback(async (): Promise<Experiment> => {
    const result = await duplicateMutation.mutateAsync();
    return result.data;
  }, [duplicateMutation]);

  // Convenience methods for status changes
  const startExperiment = useCallback(
    (data?: any) => changeStatus('ACTIVE', data),
    [changeStatus]
  );

  const pauseExperiment = useCallback(
    (data?: any) => changeStatus('PAUSED', data),
    [changeStatus]
  );

  const completeExperiment = useCallback(
    (data?: any) => changeStatus('COMPLETED', data),
    [changeStatus]
  );

  const archiveExperiment = useCallback(
    (data?: any) => changeStatus('ARCHIVED', data),
    [changeStatus]
  );

  return {
    // Data
    experiment: experimentResponse?.data,
    analytics: analyticsResponse?.data,
    
    // Loading states
    isLoading,
    isLoadingAnalytics,
    isError,
    isAnalyticsError,
    error: error?.message,
    analyticsError: analyticsError?.message,
    
    // Mutation states
    isUpdating: updateMutation.isPending,
    isChangingStatus: statusMutation.isPending,
    isDeleting: deleteMutation.isPending,
    isDuplicating: duplicateMutation.isPending,
    
    // Methods
    updateExperiment,
    changeStatus,
    deleteExperiment,
    duplicateExperiment,
    refetch,
    refetchAnalytics,
    
    // Convenience methods
    startExperiment,
    pauseExperiment,
    completeExperiment,
    archiveExperiment,
    
    // Raw mutations for advanced usage
    updateMutation,
    statusMutation,
    deleteMutation,
    duplicateMutation,
  };
};

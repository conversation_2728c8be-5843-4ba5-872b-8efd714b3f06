// Custom hook for bulk experiment operations
import { useCallback } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import {
  Experiment,
  ExperimentStatus,
  PaginatedResponse,
  ApiError,
} from '../types/experiment';
import { mockExperimentsApi as experimentsApi } from '../services/api';
import { experimentKeys } from './useExperiments';
import { useExperimentContext } from '../contexts/ExperimentContext';

export const useBulkOperations = () => {
  const queryClient = useQueryClient();
  const { clearSelection } = useExperimentContext();

  // Bulk status update mutation
  const bulkStatusMutation = useMutation({
    mutationFn: ({ ids, status }: { ids: string[]; status: ExperimentStatus }) =>
      experimentsApi.bulkUpdateStatus(ids, status),
    onMutate: async ({ ids, status }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: experimentKeys.lists() });

      // Snapshot previous values
      const previousQueries = new Map();
      
      // Get all list queries and store their previous values
      const queryCache = queryClient.getQueryCache();
      const listQueries = queryCache.findAll({ queryKey: experimentKeys.lists() });
      
      listQueries.forEach(query => {
        if (query.state.data) {
          previousQueries.set(query.queryKey, query.state.data);
        }
      });

      // Optimistically update all list queries
      listQueries.forEach(query => {
        queryClient.setQueryData(
          query.queryKey,
          (old: PaginatedResponse<Experiment> | undefined) => {
            if (!old) return old;
            return {
              ...old,
              data: old.data.map(exp =>
                ids.includes(exp.id)
                  ? { ...exp, status, updatedAt: new Date().toISOString() }
                  : exp
              ),
            };
          }
        );
      });

      // Also update individual experiment queries
      ids.forEach(id => {
        queryClient.setQueryData(
          experimentKeys.detail(id),
          (old: any) => {
            if (!old) return old;
            return {
              ...old,
              data: {
                ...old.data,
                status,
                updatedAt: new Date().toISOString(),
              },
            };
          }
        );
      });

      return { previousQueries };
    },
    onError: (error: ApiError, { ids, status }, context) => {
      // Rollback all optimistic updates
      if (context?.previousQueries) {
        context.previousQueries.forEach((data, queryKey) => {
          queryClient.setQueryData(queryKey, data);
        });
      }
      
      toast.error(`Failed to update ${ids.length} experiments: ${error.message}`);
    },
    onSuccess: (response, { ids, status }) => {
      // Invalidate all list queries
      queryClient.invalidateQueries({ queryKey: experimentKeys.lists() });
      
      // Invalidate individual experiment queries
      ids.forEach(id => {
        queryClient.invalidateQueries({ queryKey: experimentKeys.detail(id) });
      });
      
      // Clear selection
      clearSelection();
      
      toast.success(`Successfully updated ${ids.length} experiments to ${status.toLowerCase()}`);
    },
  });

  // Bulk delete mutation
  const bulkDeleteMutation = useMutation({
    mutationFn: (ids: string[]) => experimentsApi.bulkDelete(ids),
    onMutate: async (ids) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: experimentKeys.lists() });

      // Snapshot previous values
      const previousQueries = new Map();
      
      // Get all list queries and store their previous values
      const queryCache = queryClient.getQueryCache();
      const listQueries = queryCache.findAll({ queryKey: experimentKeys.lists() });
      
      listQueries.forEach(query => {
        if (query.state.data) {
          previousQueries.set(query.queryKey, query.state.data);
        }
      });

      // Optimistically remove from all list queries
      listQueries.forEach(query => {
        queryClient.setQueryData(
          query.queryKey,
          (old: PaginatedResponse<Experiment> | undefined) => {
            if (!old) return old;
            const filteredData = old.data.filter(exp => !ids.includes(exp.id));
            return {
              ...old,
              data: filteredData,
              pagination: {
                ...old.pagination,
                total: old.pagination.total - (old.data.length - filteredData.length),
              },
            };
          }
        );
      });

      return { previousQueries };
    },
    onError: (error: ApiError, ids, context) => {
      // Rollback all optimistic updates
      if (context?.previousQueries) {
        context.previousQueries.forEach((data, queryKey) => {
          queryClient.setQueryData(queryKey, data);
        });
      }
      
      toast.error(`Failed to delete ${ids.length} experiments: ${error.message}`);
    },
    onSuccess: (response, ids) => {
      // Invalidate all list queries
      queryClient.invalidateQueries({ queryKey: experimentKeys.lists() });
      
      // Remove individual experiment queries from cache
      ids.forEach(id => {
        queryClient.removeQueries({ queryKey: experimentKeys.detail(id) });
        queryClient.removeQueries({ queryKey: experimentKeys.analytics(id) });
      });
      
      // Clear selection
      clearSelection();
      
      toast.success(`Successfully deleted ${ids.length} experiments`);
    },
  });

  // Exposed methods
  const bulkUpdateStatus = useCallback(
    async (ids: string[], status: ExperimentStatus): Promise<Experiment[]> => {
      if (ids.length === 0) {
        throw new Error('No experiments selected');
      }
      
      const result = await bulkStatusMutation.mutateAsync({ ids, status });
      return result.data;
    },
    [bulkStatusMutation]
  );

  const bulkDelete = useCallback(
    async (ids: string[]): Promise<void> => {
      if (ids.length === 0) {
        throw new Error('No experiments selected');
      }
      
      // Show confirmation for bulk delete
      const confirmed = window.confirm(
        `Are you sure you want to delete ${ids.length} experiment${ids.length > 1 ? 's' : ''}? This action cannot be undone.`
      );
      
      if (!confirmed) {
        return;
      }
      
      await bulkDeleteMutation.mutateAsync(ids);
    },
    [bulkDeleteMutation]
  );

  // Convenience methods for specific status changes
  const bulkStart = useCallback(
    (ids: string[]) => bulkUpdateStatus(ids, 'ACTIVE'),
    [bulkUpdateStatus]
  );

  const bulkPause = useCallback(
    (ids: string[]) => bulkUpdateStatus(ids, 'PAUSED'),
    [bulkUpdateStatus]
  );

  const bulkComplete = useCallback(
    (ids: string[]) => bulkUpdateStatus(ids, 'COMPLETED'),
    [bulkUpdateStatus]
  );

  const bulkArchive = useCallback(
    (ids: string[]) => bulkUpdateStatus(ids, 'ARCHIVED'),
    [bulkUpdateStatus]
  );

  // Bulk operation with progress tracking
  const bulkOperationWithProgress = useCallback(
    async (
      ids: string[],
      operation: (id: string) => Promise<any>,
      operationName: string
    ): Promise<{ successful: string[]; failed: { id: string; error: string }[] }> => {
      const results = {
        successful: [] as string[],
        failed: [] as { id: string; error: string }[],
      };

      // Show progress toast
      const progressToast = toast.loading(`${operationName} 0/${ids.length} experiments...`);

      try {
        for (let i = 0; i < ids.length; i++) {
          const id = ids[i];
          
          try {
            await operation(id);
            results.successful.push(id);
          } catch (error: any) {
            results.failed.push({
              id,
              error: error.message || 'Unknown error',
            });
          }

          // Update progress
          toast.loading(
            `${operationName} ${i + 1}/${ids.length} experiments...`,
            { id: progressToast }
          );
        }

        // Show final result
        toast.dismiss(progressToast);
        
        if (results.failed.length === 0) {
          toast.success(`Successfully ${operationName.toLowerCase()} ${results.successful.length} experiments`);
        } else if (results.successful.length === 0) {
          toast.error(`Failed to ${operationName.toLowerCase()} all experiments`);
        } else {
          toast.success(
            `${operationName} completed: ${results.successful.length} successful, ${results.failed.length} failed`
          );
        }

        return results;
      } catch (error) {
        toast.dismiss(progressToast);
        toast.error(`Bulk operation failed: ${(error as any)?.message || 'Unknown error'}`);
        throw error;
      }
    },
    []
  );

  return {
    // Loading states
    isBulkUpdating: bulkStatusMutation.isPending,
    isBulkDeleting: bulkDeleteMutation.isPending,
    
    // Methods
    bulkUpdateStatus,
    bulkDelete,
    bulkOperationWithProgress,
    
    // Convenience methods
    bulkStart,
    bulkPause,
    bulkComplete,
    bulkArchive,
    
    // Raw mutations for advanced usage
    bulkStatusMutation,
    bulkDeleteMutation,
  };
};

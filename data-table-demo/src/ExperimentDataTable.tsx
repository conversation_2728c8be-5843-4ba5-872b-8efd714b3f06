import React, { useState, useMemo, useCallback } from 'react';

// Types
interface Experiment {
  id: string;
  tenantId: string;
  name: string;
  description?: string;
  status: 'DRAFT' | 'ACTIVE' | 'PAUSED' | 'COMPLETED' | 'ARCHIVED';
  startDate?: string;
  endDate?: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  variants: Array<{
    id: string;
    name: string;
    trafficWeight: number;
  }>;
  tags: string[];
  _count: {
    userAssignments: number;
    events: number;
  };
}

interface SortConfig {
  key: string;
  direction: 'asc' | 'desc';
}

interface FilterConfig {
  status: string[];
  tags: string[];
  search: string;
  dateRange: Record<string, any>;
  createdBy: string[];
}

interface ExperimentDataTableProps {
  experiments?: Experiment[];
  currentTenant?: string;
  onExperimentClick?: (experiment: Experiment) => void;
  onStatusChange?: (experiment: Experiment, newStatus: string) => void;
  onEdit?: (experiment: Experiment) => void;
  onDuplicate?: (experiment: Experiment) => void;
  onDelete?: (experiment: Experiment) => void;
  onBulkAction?: (action: string, experiments: Experiment[]) => void;
}

// Mock data
const mockExperiments: Experiment[] = [
  {
    id: '1',
    tenantId: 'tenant-1',
    name: 'Homepage Button Color Test',
    description: 'Testing different button colors to improve conversion rates',
    status: 'ACTIVE',
    startDate: '2024-01-15T00:00:00Z',
    endDate: '2024-02-15T00:00:00Z',
    createdAt: '2024-01-10T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z',
    createdBy: 'user-1',
    variants: [
      { id: 'v1', name: 'Control (Blue)', trafficWeight: 0.5 },
      { id: 'v2', name: 'Treatment (Green)', trafficWeight: 0.5 }
    ],
    tags: ['homepage', 'ui', 'conversion'],
    _count: { userAssignments: 1250, events: 3420 }
  },
  {
    id: '2',
    tenantId: 'tenant-1',
    name: 'Checkout Flow Optimization',
    description: 'Testing a simplified checkout process',
    status: 'DRAFT',
    createdAt: '2024-01-12T00:00:00Z',
    updatedAt: '2024-01-12T00:00:00Z',
    createdBy: 'user-2',
    variants: [
      { id: 'v3', name: 'Current Flow', trafficWeight: 0.5 },
      { id: 'v4', name: 'Simplified Flow', trafficWeight: 0.5 }
    ],
    tags: ['checkout', 'ux', 'conversion'],
    _count: { userAssignments: 0, events: 0 }
  },
  {
    id: '3',
    tenantId: 'tenant-1',
    name: 'Email Subject Line Test',
    description: 'Testing different email subject lines for open rates',
    status: 'COMPLETED',
    startDate: '2024-01-01T00:00:00Z',
    endDate: '2024-01-20T00:00:00Z',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-20T00:00:00Z',
    createdBy: 'user-1',
    variants: [
      { id: 'v5', name: 'Original Subject', trafficWeight: 0.33 },
      { id: 'v6', name: 'Personalized Subject', trafficWeight: 0.33 },
      { id: 'v7', name: 'Urgent Subject', trafficWeight: 0.34 }
    ],
    tags: ['email', 'marketing', 'engagement'],
    _count: { userAssignments: 5000, events: 12500 }
  },
  {
    id: '4',
    tenantId: 'tenant-1',
    name: 'Product Page Layout',
    description: 'Testing different product page layouts',
    status: 'PAUSED',
    startDate: '2024-01-08T00:00:00Z',
    createdAt: '2024-01-05T00:00:00Z',
    updatedAt: '2024-01-08T00:00:00Z',
    createdBy: 'user-3',
    variants: [
      { id: 'v8', name: 'Current Layout', trafficWeight: 0.5 },
      { id: 'v9', name: 'New Layout', trafficWeight: 0.5 }
    ],
    tags: ['product', 'layout', 'ui'],
    _count: { userAssignments: 800, events: 2100 }
  },
  {
    id: '5',
    tenantId: 'tenant-2',
    name: 'Pricing Page Test',
    description: 'Testing different pricing displays',
    status: 'ACTIVE',
    startDate: '2024-01-20T00:00:00Z',
    createdAt: '2024-01-18T00:00:00Z',
    updatedAt: '2024-01-20T00:00:00Z',
    createdBy: 'user-4',
    variants: [
      { id: 'v10', name: 'Monthly Focus', trafficWeight: 0.5 },
      { id: 'v11', name: 'Annual Focus', trafficWeight: 0.5 }
    ],
    tags: ['pricing', 'conversion', 'revenue'],
    _count: { userAssignments: 600, events: 1800 }
  }
];

// Status configuration
const statusConfig = {
  DRAFT: {
    label: 'Draft',
    color: 'bg-gray-100 text-gray-800',
    actions: ['start']
  },
  ACTIVE: {
    label: 'Active',
    color: 'bg-green-100 text-green-800',
    actions: ['pause', 'complete']
  },
  PAUSED: {
    label: 'Paused',
    color: 'bg-yellow-100 text-yellow-800',
    actions: ['resume', 'complete']
  },
  COMPLETED: {
    label: 'Completed',
    color: 'bg-blue-100 text-blue-800',
    actions: ['archive']
  },
  ARCHIVED: {
    label: 'Archived',
    color: 'bg-gray-100 text-gray-600',
    actions: []
  }
};

const actionConfig = {
  start: { label: 'Start', status: 'ACTIVE' },
  pause: { label: 'Pause', status: 'PAUSED' },
  resume: { label: 'Resume', status: 'ACTIVE' },
  complete: { label: 'Complete', status: 'COMPLETED' },
  archive: { label: 'Archive', status: 'ARCHIVED' }
};

// Icons
const ChevronUpIcon: React.FC<{ className?: string }> = ({ className = "h-4 w-4" }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 15l7-7 7 7" />
  </svg>
);

const ChevronDownIcon: React.FC<{ className?: string }> = ({ className = "h-4 w-4" }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
  </svg>
);

const MagnifyingGlassIcon: React.FC<{ className?: string }> = ({ className = "h-5 w-5" }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
  </svg>
);

const FunnelIcon: React.FC<{ className?: string }> = ({ className = "h-4 w-4" }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z" />
  </svg>
);

const UserGroupIcon: React.FC<{ className?: string }> = ({ className = "h-4 w-4" }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
  </svg>
);

const ChartBarIcon: React.FC<{ className?: string }> = ({ className = "h-4 w-4" }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
  </svg>
);

const CalendarIcon: React.FC<{ className?: string }> = ({ className = "h-4 w-4" }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
  </svg>
);

// Utility functions
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'short', 
    day: 'numeric' 
  });
};

const formatRelativeDate = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - date.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays === 1) return '1 day ago';
  if (diffDays < 30) return `${diffDays} days ago`;
  if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
  return `${Math.floor(diffDays / 365)} years ago`;
};

// Sortable Header Component
const SortableHeader: React.FC<{
  sortKey: string;
  children: React.ReactNode;
  sortConfig: SortConfig;
  onSort: (key: string) => void;
}> = ({ sortKey, children, sortConfig, onSort }) => (
  <th
    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
    onClick={() => onSort(sortKey)}
  >
    <div className="flex items-center space-x-1">
      <span>{children}</span>
      {sortConfig.key === sortKey && (
        sortConfig.direction === 'asc' ? <ChevronUpIcon /> : <ChevronDownIcon />
      )}
    </div>
  </th>
);

// Main ExperimentDataTable Component
export const ExperimentDataTable: React.FC<ExperimentDataTableProps> = ({
  experiments = mockExperiments,
  currentTenant = 'tenant-1',
  onExperimentClick = (exp) => console.log('View experiment:', exp.id),
  onStatusChange = (exp, status) => console.log('Status change:', exp.id, status),
  onEdit = (exp) => console.log('Edit experiment:', exp.id),
  onDuplicate = (exp) => console.log('Duplicate experiment:', exp.id),
  onDelete = (exp) => console.log('Delete experiment:', exp.id),
  onBulkAction = (action, exps) => console.log('Bulk action:', action, exps.map(e => e.id))
}) => {
  const [sortConfig, setSortConfig] = useState<SortConfig>({ key: 'createdAt', direction: 'desc' });
  const [filters, setFilters] = useState<FilterConfig>({
    status: [],
    tags: [],
    search: '',
    dateRange: {},
    createdBy: []
  });
  const [showFilters, setShowFilters] = useState(false);
  const [selectedExperiments, setSelectedExperiments] = useState<Set<string>>(new Set());
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  // Filter and sort experiments
  const filteredAndSortedExperiments = useMemo(() => {
    let filtered = experiments;

    // Apply tenant filter
    if (currentTenant) {
      filtered = filtered.filter(exp => exp.tenantId === currentTenant);
    }

    // Apply search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(exp =>
        exp.name.toLowerCase().includes(searchLower) ||
        exp.description?.toLowerCase().includes(searchLower) ||
        exp.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    // Apply status filter
    if (filters.status.length > 0) {
      filtered = filtered.filter(exp => filters.status.includes(exp.status));
    }

    // Apply tags filter
    if (filters.tags.length > 0) {
      filtered = filtered.filter(exp =>
        filters.tags.some(tag => exp.tags.includes(tag))
      );
    }

    // Sort experiments
    const sorted = [...filtered].sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortConfig.key) {
        case 'variantCount':
          aValue = a.variants.length;
          bValue = b.variants.length;
          break;
        case 'assignmentCount':
          aValue = a._count?.userAssignments || 0;
          bValue = b._count?.userAssignments || 0;
          break;
        case 'eventCount':
          aValue = a._count?.events || 0;
          bValue = b._count?.events || 0;
          break;
        default:
          aValue = (a as any)[sortConfig.key];
          bValue = (b as any)[sortConfig.key];
      }

      if (aValue === bValue) return 0;
      const comparison = aValue < bValue ? -1 : 1;
      return sortConfig.direction === 'desc' ? -comparison : comparison;
    });

    return sorted;
  }, [experiments, filters, sortConfig, currentTenant]);

  // Pagination
  const paginatedExperiments = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    return filteredAndSortedExperiments.slice(startIndex, startIndex + pageSize);
  }, [filteredAndSortedExperiments, currentPage, pageSize]);

  const totalPages = Math.ceil(filteredAndSortedExperiments.length / pageSize);

  // Handlers
  const handleSort = useCallback((key: string) => {
    setSortConfig(prev => ({
      key,
      direction: prev.key === key && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  }, []);

  const handleFilterChange = useCallback((newFilters: Partial<FilterConfig>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setCurrentPage(1);
  }, []);

  const handleSelectExperiment = useCallback((experimentId: string, selected: boolean) => {
    setSelectedExperiments(prev => {
      const newSet = new Set(prev);
      if (selected) {
        newSet.add(experimentId);
      } else {
        newSet.delete(experimentId);
      }
      return newSet;
    });
  }, []);

  const handleSelectAll = useCallback((selected: boolean) => {
    if (selected) {
      setSelectedExperiments(new Set(paginatedExperiments.map(exp => exp.id)));
    } else {
      setSelectedExperiments(new Set());
    }
  }, [paginatedExperiments]);

  const getAvailableActions = (experiment: Experiment) => {
    return statusConfig[experiment.status].actions.map(action => (actionConfig as any)[action]);
  };

  return (
    <div className="space-y-4">
      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search experiments..."
              value={filters.search}
              onChange={(e) => handleFilterChange({ search: e.target.value })}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
            />
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`inline-flex items-center px-4 py-2 border rounded-md shadow-sm text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${
              showFilters
                ? 'border-indigo-300 text-indigo-700 bg-indigo-50 hover:bg-indigo-100'
                : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
            }`}
          >
            <FunnelIcon className="h-4 w-4 mr-2" />
            Filters
            {(filters.status.length > 0 || filters.tags.length > 0) && (
              <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                {filters.status.length + filters.tags.length}
              </span>
            )}
          </button>

          {selectedExperiments.size > 0 && (
            <button
              onClick={() => onBulkAction('archive', Array.from(selectedExperiments).map(id => experiments.find(e => e.id === id)!).filter(Boolean))}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              Bulk Actions ({selectedExperiments.size})
            </button>
          )}
        </div>
      </div>

      {/* Filter Panel */}
      {showFilters && (
        <div className="bg-gray-50 p-4 rounded-lg border">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
              <div className="space-y-2">
                {Object.entries(statusConfig).map(([status, config]) => (
                  <label key={status} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={filters.status.includes(status)}
                      onChange={(e) => {
                        const newStatus = e.target.checked
                          ? [...filters.status, status]
                          : filters.status.filter(s => s !== status);
                        handleFilterChange({ status: newStatus });
                      }}
                      className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">{config.label}</span>
                  </label>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Tags</label>
              <input
                type="text"
                placeholder="Enter tags..."
                onChange={(e) => {
                  const tags = e.target.value.split(',').map(tag => tag.trim()).filter(Boolean);
                  handleFilterChange({ tags });
                }}
                className="block w-full text-sm border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Tenant</label>
              <div className="text-sm text-indigo-600 font-medium">{currentTenant}</div>
            </div>

            <div className="flex items-end">
              <button
                onClick={() => {
                  setFilters({
                    status: [],
                    tags: [],
                    search: '',
                    dateRange: {},
                    createdBy: []
                  });
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Clear Filters
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Data Table */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left">
                <input
                  type="checkbox"
                  checked={selectedExperiments.size === paginatedExperiments.length && paginatedExperiments.length > 0}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                  className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                />
              </th>
              <SortableHeader sortKey="name" sortConfig={sortConfig} onSort={handleSort}>Name</SortableHeader>
              <SortableHeader sortKey="status" sortConfig={sortConfig} onSort={handleSort}>Status</SortableHeader>
              <SortableHeader sortKey="variantCount" sortConfig={sortConfig} onSort={handleSort}>Variants</SortableHeader>
              <SortableHeader sortKey="assignmentCount" sortConfig={sortConfig} onSort={handleSort}>Assignments</SortableHeader>
              <SortableHeader sortKey="eventCount" sortConfig={sortConfig} onSort={handleSort}>Events</SortableHeader>
              <SortableHeader sortKey="startDate" sortConfig={sortConfig} onSort={handleSort}>Start Date</SortableHeader>
              <SortableHeader sortKey="createdAt" sortConfig={sortConfig} onSort={handleSort}>Created</SortableHeader>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {paginatedExperiments.map((experiment) => {
              const config = statusConfig[experiment.status];
              const availableActions = getAvailableActions(experiment);

              return (
                <tr
                  key={experiment.id}
                  className="hover:bg-gray-50 cursor-pointer"
                  onClick={() => onExperimentClick(experiment)}
                >
                  <td className="px-6 py-4 whitespace-nowrap" onClick={(e) => e.stopPropagation()}>
                    <input
                      type="checkbox"
                      checked={selectedExperiments.has(experiment.id)}
                      onChange={(e) => handleSelectExperiment(experiment.id, e.target.checked)}
                      className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                    />
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex flex-col">
                      <div className="text-sm font-medium text-gray-900 truncate max-w-xs">
                        {experiment.name}
                      </div>
                      {experiment.description && (
                        <div className="text-sm text-gray-500 truncate max-w-xs">
                          {experiment.description}
                        </div>
                      )}
                      {experiment.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-1">
                          {experiment.tags.slice(0, 3).map(tag => (
                            <span
                              key={tag}
                              className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800"
                            >
                              {tag}
                            </span>
                          ))}
                          {experiment.tags.length > 3 && (
                            <span className="text-xs text-gray-500">
                              +{experiment.tags.length - 3} more
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
                      {config.label}
                    </span>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="flex items-center">
                      <ChartBarIcon className="h-4 w-4 text-gray-400 mr-1" />
                      {experiment.variants.length}
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="flex items-center">
                      <UserGroupIcon className="h-4 w-4 text-gray-400 mr-1" />
                      {(experiment._count?.userAssignments || 0).toLocaleString()}
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="flex items-center">
                      <ChartBarIcon className="h-4 w-4 text-gray-400 mr-1" />
                      {(experiment._count?.events || 0).toLocaleString()}
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {experiment.startDate ? (
                      <div className="flex items-center">
                        <CalendarIcon className="h-4 w-4 text-gray-400 mr-1" />
                        <div>
                          <div>{formatDate(experiment.startDate)}</div>
                          <div className="text-xs text-gray-400">
                            {formatRelativeDate(experiment.startDate)}
                          </div>
                        </div>
                      </div>
                    ) : (
                      <span className="text-gray-400">Not started</span>
                    )}
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="flex items-center">
                      <CalendarIcon className="h-4 w-4 text-gray-400 mr-1" />
                      <div>
                        <div>{formatDate(experiment.createdAt)}</div>
                        <div className="text-xs text-gray-400">
                          {formatRelativeDate(experiment.createdAt)}
                        </div>
                      </div>
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium" onClick={(e) => e.stopPropagation()}>
                    <div className="flex items-center space-x-2">
                      {availableActions.slice(0, 1).map((action: any) => (
                        <button
                          key={action.label}
                          onClick={() => onStatusChange(experiment, action.status)}
                          className="inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                        >
                          {action.label}
                        </button>
                      ))}

                      <button
                        onClick={() => onEdit(experiment)}
                        className="inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                      >
                        Edit
                      </button>
                    </div>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>

        {/* Empty State */}
        {filteredAndSortedExperiments.length === 0 && (
          <div className="text-center py-12">
            <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No experiments found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {filters.search || filters.status.length > 0 || filters.tags.length > 0
                ? 'Try adjusting your search or filter criteria.'
                : 'Get started by creating your first experiment.'}
            </p>
          </div>
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
          <div className="flex flex-1 justify-between sm:hidden">
            <button
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
              className="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button
              onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
              disabled={currentPage === totalPages}
              className="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
            <div className="flex items-center space-x-2">
              <p className="text-sm text-gray-700">
                Showing{' '}
                <span className="font-medium">{(currentPage - 1) * pageSize + 1}</span>
                {' '}to{' '}
                <span className="font-medium">
                  {Math.min(currentPage * pageSize, filteredAndSortedExperiments.length)}
                </span>
                {' '}of{' '}
                <span className="font-medium">{filteredAndSortedExperiments.length}</span>
                {' '}results
              </p>
              <select
                value={pageSize}
                onChange={(e) => {
                  setPageSize(Number(e.target.value));
                  setCurrentPage(1);
                }}
                className="border-gray-300 rounded-md text-sm"
              >
                <option value={10}>10 per page</option>
                <option value={20}>20 per page</option>
                <option value={50}>50 per page</option>
              </select>
            </div>
            <div>
              <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>

                {[...Array(Math.min(5, totalPages))].map((_, i) => {
                  const pageNum = i + 1;
                  return (
                    <button
                      key={pageNum}
                      onClick={() => setCurrentPage(pageNum)}
                      className={`relative inline-flex items-center px-4 py-2 text-sm font-semibold ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 ${
                        currentPage === pageNum
                          ? 'bg-indigo-600 text-white ring-indigo-600'
                          : 'text-gray-900'
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}

                <button
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                  className="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

import React, { useState, useMemo, useCallback } from 'react';
import { useExperiments } from './hooks/useExperiments';
import { useBulkOperations } from './hooks/useBulkOperations';
import { useExperimentContext } from './contexts/ExperimentContext';
import { Experiment } from './types/experiment';

// Types
interface Experiment {
  id: string;
  tenantId: string;
  name: string;
  description?: string;
  status: 'DRAFT' | 'ACTIVE' | 'PAUSED' | 'COMPLETED' | 'ARCHIVED';
  startDate?: string;
  endDate?: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  variants: Array<{
    id: string;
    name: string;
    trafficWeight: number;
  }>;
  tags: string[];
  _count: {
    userAssignments: number;
    events: number;
  };
}

interface SortConfig {
  key: string;
  direction: 'asc' | 'desc';
}

interface FilterConfig {
  status: string[];
  tags: string[];
  search: string;
  dateRange: Record<string, any>;
  createdBy: string[];
}

interface ExperimentDataTableProps {
  currentTenant?: string;
  onExperimentClick?: (experiment: Experiment) => void;
}

// Mock data
const mockExperiments: Experiment[] = [
  {
    id: '1',
    tenantId: 'tenant-1',
    name: 'Homepage Button Color Test',
    description: 'Testing different button colors to improve conversion rates',
    status: 'ACTIVE',
    startDate: '2024-01-15T00:00:00Z',
    endDate: '2024-02-15T00:00:00Z',
    createdAt: '2024-01-10T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z',
    createdBy: 'user-1',
    variants: [
      { id: 'v1', name: 'Control (Blue)', trafficWeight: 0.5 },
      { id: 'v2', name: 'Treatment (Green)', trafficWeight: 0.5 }
    ],
    tags: ['homepage', 'ui', 'conversion'],
    _count: { userAssignments: 1250, events: 3420 }
  },
  {
    id: '2',
    tenantId: 'tenant-1',
    name: 'Checkout Flow Optimization',
    description: 'Testing a simplified checkout process',
    status: 'DRAFT',
    createdAt: '2024-01-12T00:00:00Z',
    updatedAt: '2024-01-12T00:00:00Z',
    createdBy: 'user-2',
    variants: [
      { id: 'v3', name: 'Current Flow', trafficWeight: 0.5 },
      { id: 'v4', name: 'Simplified Flow', trafficWeight: 0.5 }
    ],
    tags: ['checkout', 'ux', 'conversion'],
    _count: { userAssignments: 0, events: 0 }
  },
  {
    id: '3',
    tenantId: 'tenant-1',
    name: 'Email Subject Line Test',
    description: 'Testing different email subject lines for open rates',
    status: 'COMPLETED',
    startDate: '2024-01-01T00:00:00Z',
    endDate: '2024-01-20T00:00:00Z',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-20T00:00:00Z',
    createdBy: 'user-1',
    variants: [
      { id: 'v5', name: 'Original Subject', trafficWeight: 0.33 },
      { id: 'v6', name: 'Personalized Subject', trafficWeight: 0.33 },
      { id: 'v7', name: 'Urgent Subject', trafficWeight: 0.34 }
    ],
    tags: ['email', 'marketing', 'engagement'],
    _count: { userAssignments: 5000, events: 12500 }
  },
  {
    id: '4',
    tenantId: 'tenant-1',
    name: 'Product Page Layout',
    description: 'Testing different product page layouts',
    status: 'PAUSED',
    startDate: '2024-01-08T00:00:00Z',
    createdAt: '2024-01-05T00:00:00Z',
    updatedAt: '2024-01-08T00:00:00Z',
    createdBy: 'user-3',
    variants: [
      { id: 'v8', name: 'Current Layout', trafficWeight: 0.5 },
      { id: 'v9', name: 'New Layout', trafficWeight: 0.5 }
    ],
    tags: ['product', 'layout', 'ui'],
    _count: { userAssignments: 800, events: 2100 }
  },
  {
    id: '5',
    tenantId: 'tenant-2',
    name: 'Pricing Page Test',
    description: 'Testing different pricing displays',
    status: 'ACTIVE',
    startDate: '2024-01-20T00:00:00Z',
    createdAt: '2024-01-18T00:00:00Z',
    updatedAt: '2024-01-20T00:00:00Z',
    createdBy: 'user-4',
    variants: [
      { id: 'v10', name: 'Monthly Focus', trafficWeight: 0.5 },
      { id: 'v11', name: 'Annual Focus', trafficWeight: 0.5 }
    ],
    tags: ['pricing', 'conversion', 'revenue'],
    _count: { userAssignments: 600, events: 1800 }
  }
];

// Status configuration
const statusConfig = {
  DRAFT: {
    label: 'Draft',
    color: 'bg-gray-100 text-gray-800',
    actions: ['start']
  },
  ACTIVE: {
    label: 'Active',
    color: 'bg-green-100 text-green-800',
    actions: ['pause', 'complete']
  },
  PAUSED: {
    label: 'Paused',
    color: 'bg-yellow-100 text-yellow-800',
    actions: ['resume', 'complete']
  },
  COMPLETED: {
    label: 'Completed',
    color: 'bg-blue-100 text-blue-800',
    actions: ['archive']
  },
  ARCHIVED: {
    label: 'Archived',
    color: 'bg-gray-100 text-gray-600',
    actions: []
  }
};

const actionConfig = {
  start: { label: 'Start', status: 'ACTIVE' },
  pause: { label: 'Pause', status: 'PAUSED' },
  resume: { label: 'Resume', status: 'ACTIVE' },
  complete: { label: 'Complete', status: 'COMPLETED' },
  archive: { label: 'Archive', status: 'ARCHIVED' }
};

// Icons
const ChevronUpIcon: React.FC<{ className?: string }> = ({ className = "h-4 w-4" }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 15l7-7 7 7" />
  </svg>
);

const ChevronDownIcon: React.FC<{ className?: string }> = ({ className = "h-4 w-4" }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
  </svg>
);

const MagnifyingGlassIcon: React.FC<{ className?: string }> = ({ className = "h-5 w-5" }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
  </svg>
);

const FunnelIcon: React.FC<{ className?: string }> = ({ className = "h-4 w-4" }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z" />
  </svg>
);

const UserGroupIcon: React.FC<{ className?: string }> = ({ className = "h-4 w-4" }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
  </svg>
);

const ChartBarIcon: React.FC<{ className?: string }> = ({ className = "h-4 w-4" }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
  </svg>
);

const CalendarIcon: React.FC<{ className?: string }> = ({ className = "h-4 w-4" }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
  </svg>
);

// Utility functions
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'short', 
    day: 'numeric' 
  });
};

const formatRelativeDate = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - date.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays === 1) return '1 day ago';
  if (diffDays < 30) return `${diffDays} days ago`;
  if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
  return `${Math.floor(diffDays / 365)} years ago`;
};

// Sortable Header Component
const SortableHeader: React.FC<{
  sortKey: string;
  children: React.ReactNode;
  sortConfig: SortConfig;
  onSort: (key: string) => void;
}> = ({ sortKey, children, sortConfig, onSort }) => (
  <th
    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
    onClick={() => onSort(sortKey)}
  >
    <div className="flex items-center space-x-1">
      <span>{children}</span>
      {sortConfig.key === sortKey && (
        sortConfig.direction === 'asc' ? <ChevronUpIcon /> : <ChevronDownIcon />
      )}
    </div>
  </th>
);

// Main ExperimentDataTable Component
export const ExperimentDataTable: React.FC<ExperimentDataTableProps> = ({
  currentTenant = 'tenant-1',
  onExperimentClick = (exp) => console.log('View experiment:', exp.id),
}) => {
  // Get context state and actions
  const {
    state: { selectedExperiments, filters, pagination },
    setFilters,
    setPagination,
    toggleExperimentSelection,
    setSelectedExperiments,
    clearSelection,
  } = useExperimentContext();

  // Local UI state
  const [showFilters, setShowFilters] = useState(false);

  // Get experiments data using our custom hook
  const {
    experiments,
    pagination: paginationInfo,
    isLoading,
    isFetching,
    isError,
    error,
    changeStatus,
    duplicateExperiment,
    deleteExperiment,
    isChangingStatus,
    isDuplicating,
    isDeleting,
  } = useExperiments(filters, pagination);

  // Get bulk operations
  const {
    bulkUpdateStatus,
    bulkDelete,
    bulkStart,
    bulkPause,
    bulkComplete,
    bulkArchive,
    isBulkUpdating,
    isBulkDeleting,
  } = useBulkOperations();

  // Experiments are already filtered and sorted by the API/hook
  const filteredAndSortedExperiments = experiments;

  // Handlers
  const handleSort = useCallback((key: string) => {
    const newSortOrder = pagination.sortBy === key && pagination.sortOrder === 'asc' ? 'desc' : 'asc';
    setPagination({
      ...pagination,
      sortBy: key,
      sortOrder: newSortOrder,
      page: 1, // Reset to first page when sorting
    });
  }, [pagination, setPagination]);

  const handleFilterChange = useCallback((newFilters: Partial<typeof filters>) => {
    setFilters({ ...filters, ...newFilters });
  }, [filters, setFilters]);

  const handleSelectExperiment = useCallback((experimentId: string, selected: boolean) => {
    if (selected) {
      toggleExperimentSelection(experimentId);
    } else {
      toggleExperimentSelection(experimentId);
    }
  }, [toggleExperimentSelection]);

  const handleSelectAll = useCallback((selected: boolean) => {
    if (selected) {
      setSelectedExperiments(new Set(filteredAndSortedExperiments.map(exp => exp.id)));
    } else {
      clearSelection();
    }
  }, [filteredAndSortedExperiments, setSelectedExperiments, clearSelection]);

  const handleBulkAction = useCallback(async (action: string) => {
    const selectedIds = Array.from(selectedExperiments);

    try {
      switch (action) {
        case 'start':
          await bulkStart(selectedIds);
          break;
        case 'pause':
          await bulkPause(selectedIds);
          break;
        case 'complete':
          await bulkComplete(selectedIds);
          break;
        case 'archive':
          await bulkArchive(selectedIds);
          break;
        case 'delete':
          await bulkDelete(selectedIds);
          break;
        default:
          console.warn('Unknown bulk action:', action);
      }
    } catch (error) {
      console.error('Bulk action failed:', error);
    }
  }, [selectedExperiments, bulkStart, bulkPause, bulkComplete, bulkArchive, bulkDelete]);

  const handleStatusChange = useCallback(async (experiment: Experiment, newStatus: string) => {
    try {
      await changeStatus(experiment.id, newStatus as any);
    } catch (error) {
      console.error('Status change failed:', error);
    }
  }, [changeStatus]);

  const handleDuplicate = useCallback(async (experiment: Experiment) => {
    try {
      await duplicateExperiment(experiment.id);
    } catch (error) {
      console.error('Duplicate failed:', error);
    }
  }, [duplicateExperiment]);

  const handleDelete = useCallback(async (experiment: Experiment) => {
    try {
      const confirmed = window.confirm(`Are you sure you want to delete "${experiment.name}"? This action cannot be undone.`);
      if (confirmed) {
        await deleteExperiment(experiment.id);
      }
    } catch (error) {
      console.error('Delete failed:', error);
    }
  }, [deleteExperiment]);

  const getAvailableActions = (experiment: Experiment) => {
    return statusConfig[experiment.status].actions.map(action => (actionConfig as any)[action]);
  };

  return (
    <div className="space-y-4">
      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search experiments..."
              value={filters.search}
              onChange={(e) => handleFilterChange({ search: e.target.value })}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
            />
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`inline-flex items-center px-4 py-2 border rounded-md shadow-sm text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${
              showFilters
                ? 'border-indigo-300 text-indigo-700 bg-indigo-50 hover:bg-indigo-100'
                : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
            }`}
          >
            <FunnelIcon className="h-4 w-4 mr-2" />
            Filters
            {((filters.status && filters.status.length > 0) || (filters.tags && filters.tags.length > 0)) && (
              <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                {(filters.status?.length || 0) + (filters.tags?.length || 0)}
              </span>
            )}
          </button>

          {selectedExperiments.size > 0 && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-500">
                {selectedExperiments.size} selected
              </span>
              <select
                onChange={(e) => {
                  if (e.target.value) {
                    handleBulkAction(e.target.value);
                    e.target.value = '';
                  }
                }}
                disabled={isBulkUpdating || isBulkDeleting}
                className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md disabled:opacity-50"
              >
                <option value="">
                  {isBulkUpdating || isBulkDeleting ? 'Processing...' : 'Bulk Actions'}
                </option>
                <option value="start">Start Selected</option>
                <option value="pause">Pause Selected</option>
                <option value="complete">Complete Selected</option>
                <option value="archive">Archive Selected</option>
                <option value="delete">Delete Selected</option>
              </select>
            </div>
          )}
        </div>
      </div>

      {/* Filter Panel */}
      {showFilters && (
        <div className="bg-gray-50 p-4 rounded-lg border">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
              <div className="space-y-2">
                {Object.entries(statusConfig).map(([status, config]) => (
                  <label key={status} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={filters.status.includes(status)}
                      onChange={(e) => {
                        const newStatus = e.target.checked
                          ? [...filters.status, status]
                          : filters.status.filter(s => s !== status);
                        handleFilterChange({ status: newStatus });
                      }}
                      className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">{config.label}</span>
                  </label>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Tags</label>
              <input
                type="text"
                placeholder="Enter tags..."
                onChange={(e) => {
                  const tags = e.target.value.split(',').map(tag => tag.trim()).filter(Boolean);
                  handleFilterChange({ tags });
                }}
                className="block w-full text-sm border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Tenant</label>
              <div className="text-sm text-indigo-600 font-medium">{currentTenant}</div>
            </div>

            <div className="flex items-end">
              <button
                onClick={() => {
                  setFilters({
                    status: [],
                    tags: [],
                    search: '',
                    dateRange: {},
                    createdBy: []
                  });
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Clear Filters
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Data Table */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left">
                <input
                  type="checkbox"
                  checked={selectedExperiments.size === paginatedExperiments.length && paginatedExperiments.length > 0}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                  className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                />
              </th>
              <SortableHeader sortKey="name" sortConfig={sortConfig} onSort={handleSort}>Name</SortableHeader>
              <SortableHeader sortKey="status" sortConfig={sortConfig} onSort={handleSort}>Status</SortableHeader>
              <SortableHeader sortKey="variantCount" sortConfig={sortConfig} onSort={handleSort}>Variants</SortableHeader>
              <SortableHeader sortKey="assignmentCount" sortConfig={sortConfig} onSort={handleSort}>Assignments</SortableHeader>
              <SortableHeader sortKey="eventCount" sortConfig={sortConfig} onSort={handleSort}>Events</SortableHeader>
              <SortableHeader sortKey="startDate" sortConfig={sortConfig} onSort={handleSort}>Start Date</SortableHeader>
              <SortableHeader sortKey="createdAt" sortConfig={sortConfig} onSort={handleSort}>Created</SortableHeader>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {paginatedExperiments.map((experiment) => {
              const config = statusConfig[experiment.status];
              const availableActions = getAvailableActions(experiment);

              return (
                <tr
                  key={experiment.id}
                  className="hover:bg-gray-50 cursor-pointer"
                  onClick={() => onExperimentClick(experiment)}
                >
                  <td className="px-6 py-4 whitespace-nowrap" onClick={(e) => e.stopPropagation()}>
                    <input
                      type="checkbox"
                      checked={selectedExperiments.has(experiment.id)}
                      onChange={(e) => handleSelectExperiment(experiment.id, e.target.checked)}
                      className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                    />
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex flex-col">
                      <div className="text-sm font-medium text-gray-900 truncate max-w-xs">
                        {experiment.name}
                      </div>
                      {experiment.description && (
                        <div className="text-sm text-gray-500 truncate max-w-xs">
                          {experiment.description}
                        </div>
                      )}
                      {experiment.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-1">
                          {experiment.tags.slice(0, 3).map(tag => (
                            <span
                              key={tag}
                              className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800"
                            >
                              {tag}
                            </span>
                          ))}
                          {experiment.tags.length > 3 && (
                            <span className="text-xs text-gray-500">
                              +{experiment.tags.length - 3} more
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
                      {config.label}
                    </span>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="flex items-center">
                      <ChartBarIcon className="h-4 w-4 text-gray-400 mr-1" />
                      {experiment.variants.length}
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="flex items-center">
                      <UserGroupIcon className="h-4 w-4 text-gray-400 mr-1" />
                      {(experiment._count?.userAssignments || 0).toLocaleString()}
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="flex items-center">
                      <ChartBarIcon className="h-4 w-4 text-gray-400 mr-1" />
                      {(experiment._count?.events || 0).toLocaleString()}
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {experiment.startDate ? (
                      <div className="flex items-center">
                        <CalendarIcon className="h-4 w-4 text-gray-400 mr-1" />
                        <div>
                          <div>{formatDate(experiment.startDate)}</div>
                          <div className="text-xs text-gray-400">
                            {formatRelativeDate(experiment.startDate)}
                          </div>
                        </div>
                      </div>
                    ) : (
                      <span className="text-gray-400">Not started</span>
                    )}
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="flex items-center">
                      <CalendarIcon className="h-4 w-4 text-gray-400 mr-1" />
                      <div>
                        <div>{formatDate(experiment.createdAt)}</div>
                        <div className="text-xs text-gray-400">
                          {formatRelativeDate(experiment.createdAt)}
                        </div>
                      </div>
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium" onClick={(e) => e.stopPropagation()}>
                    <div className="flex items-center space-x-2">
                      {availableActions.slice(0, 1).map((action: any) => (
                        <button
                          key={action.label}
                          onClick={() => handleStatusChange(experiment, action.status)}
                          disabled={isChangingStatus}
                          className="inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                        >
                          {isChangingStatus ? 'Updating...' : action.label}
                        </button>
                      ))}

                      <button
                        onClick={() => handleDuplicate(experiment)}
                        disabled={isDuplicating}
                        className="inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                      >
                        {isDuplicating ? 'Duplicating...' : 'Duplicate'}
                      </button>

                      <button
                        onClick={() => handleDelete(experiment)}
                        disabled={isDeleting}
                        className="inline-flex items-center px-2 py-1 border border-red-300 shadow-sm text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50 disabled:opacity-50"
                      >
                        {isDeleting ? 'Deleting...' : 'Delete'}
                      </button>
                    </div>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>

        {/* Empty State */}
        {filteredAndSortedExperiments.length === 0 && (
          <div className="text-center py-12">
            <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No experiments found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {filters.search || (filters.status && filters.status.length > 0) || (filters.tags && filters.tags.length > 0)
                ? 'Try adjusting your search or filter criteria.'
                : 'Get started by creating your first experiment.'}
            </p>
          </div>
        )}
      </div>

      {/* Pagination */}
      {paginationInfo && paginationInfo.totalPages > 1 && (
        <div className="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
          <div className="flex flex-1 justify-between sm:hidden">
            <button
              onClick={() => setPagination({ ...pagination, page: Math.max(1, pagination.page - 1) })}
              disabled={pagination.page === 1 || isLoading}
              className="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button
              onClick={() => setPagination({ ...pagination, page: Math.min(paginationInfo.totalPages, pagination.page + 1) })}
              disabled={pagination.page === paginationInfo.totalPages || isLoading}
              className="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
            <div className="flex items-center space-x-2">
              <p className="text-sm text-gray-700">
                Showing{' '}
                <span className="font-medium">{(pagination.page - 1) * pagination.limit + 1}</span>
                {' '}to{' '}
                <span className="font-medium">
                  {Math.min(pagination.page * pagination.limit, paginationInfo.total)}
                </span>
                {' '}of{' '}
                <span className="font-medium">{paginationInfo.total}</span>
                {' '}results
              </p>
              <select
                value={pagination.limit}
                onChange={(e) => setPagination({ ...pagination, limit: Number(e.target.value), page: 1 })}
                disabled={isLoading}
                className="border-gray-300 rounded-md text-sm disabled:opacity-50"
              >
                <option value={10}>10 per page</option>
                <option value={20}>20 per page</option>
                <option value={50}>50 per page</option>
              </select>
            </div>
            <div>
              <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm">
                <button
                  onClick={() => setPagination({ ...pagination, page: Math.max(1, pagination.page - 1) })}
                  disabled={pagination.page === 1 || isLoading}
                  className="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>

                {[...Array(Math.min(5, paginationInfo.totalPages))].map((_, i) => {
                  const pageNum = i + 1;
                  return (
                    <button
                      key={pageNum}
                      onClick={() => setPagination({ ...pagination, page: pageNum })}
                      disabled={isLoading}
                      className={`relative inline-flex items-center px-4 py-2 text-sm font-semibold ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 ${
                        pagination.page === pageNum
                          ? 'bg-indigo-600 text-white ring-indigo-600'
                          : 'text-gray-900'
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}

                <button
                  onClick={() => setPagination({ ...pagination, page: Math.min(paginationInfo.totalPages, pagination.page + 1) })}
                  disabled={pagination.page === paginationInfo.totalPages || isLoading}
                  className="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}

      {/* Loading overlay */}
      {(isLoading || isFetching) && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"></div>
            <span className="text-sm text-gray-600">
              {isLoading ? 'Loading experiments...' : 'Updating...'}
            </span>
          </div>
        </div>
      )}

      {/* Error state */}
      {isError && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                Error loading experiments
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

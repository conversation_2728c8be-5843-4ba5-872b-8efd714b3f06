// Experiment types and interfaces
export interface Experiment {
  id: string;
  tenantId: string;
  name: string;
  description?: string;
  status: ExperimentStatus;
  startDate?: string;
  endDate?: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  variants: ExperimentVariant[];
  tags: string[];
  targetingRules?: TargetingRule[];
  _count: {
    userAssignments: number;
    events: number;
  };
}

export interface ExperimentVariant {
  id: string;
  name: string;
  trafficWeight: number;
  config?: Record<string, any>;
}

export interface TargetingRule {
  id: string;
  field: string;
  operator: string;
  value: any;
  type: 'include' | 'exclude';
}

export type ExperimentStatus = 'DRAFT' | 'ACTIVE' | 'PAUSED' | 'COMPLETED' | 'ARCHIVED';

export interface ExperimentFilters {
  status?: ExperimentStatus[];
  tags?: string[];
  search?: string;
  createdBy?: string[];
  dateRange?: {
    start?: string;
    end?: string;
  };
}

export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface CreateExperimentForm {
  name: string;
  description?: string;
  variants: Omit<ExperimentVariant, 'id'>[];
  tags?: string[];
  targetingRules?: Omit<TargetingRule, 'id'>[];
}

export interface UpdateExperimentForm extends Partial<CreateExperimentForm> {
  id: string;
}

export interface ExperimentAnalytics {
  experimentId: string;
  totalAssignments: number;
  totalEvents: number;
  conversionRate: number;
  variantPerformance: VariantPerformance[];
  timeSeriesData: TimeSeriesPoint[];
  statisticalSignificance?: StatisticalSignificance;
}

export interface VariantPerformance {
  variantId: string;
  variantName: string;
  assignments: number;
  events: number;
  conversionRate: number;
  confidence?: number;
}

export interface TimeSeriesPoint {
  date: string;
  assignments: number;
  events: number;
  conversionRate: number;
}

export interface StatisticalSignificance {
  isSignificant: boolean;
  confidence: number;
  pValue: number;
  winningVariant?: string;
}

// Error types
export interface ApiError {
  message: string;
  code: string;
  statusCode: number;
  details?: any;
  fieldErrors?: FieldError[];
}

export interface FieldError {
  field: string;
  message: string;
  code: string;
}

// Loading states
export interface LoadingState {
  isLoading: boolean;
  isError: boolean;
  error?: string | null;
}

// Cache configuration
export interface CacheConfig {
  staleTime: number;
  gcTime: number; // renamed from cacheTime in React Query v5
  refetchOnWindowFocus: boolean;
  refetchOnMount: boolean;
}

// Optimistic update types
export interface OptimisticUpdate<T> {
  type: 'create' | 'update' | 'delete';
  data: T;
  rollback: () => void;
}

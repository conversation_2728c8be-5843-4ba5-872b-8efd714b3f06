// React context for experiment management
import React, { createContext, useContext, useReducer, useCallback, ReactNode } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import {
  Experiment,
  ExperimentFilters,
  PaginationParams,
  LoadingState,
  CacheConfig,
  OptimisticUpdate,
} from '../types/experiment';

// Query client configuration
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes (renamed from cacheTime)
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      retry: (failureCount, error: any) => {
        // Don't retry on 4xx errors
        if (error?.statusCode >= 400 && error?.statusCode < 500) {
          return false;
        }
        return failureCount < 3;
      },
    },
    mutations: {
      retry: 1,
    },
  },
});

// Context state interface
interface ExperimentContextState {
  // UI State
  selectedExperiments: Set<string>;
  filters: ExperimentFilters;
  pagination: PaginationParams;
  
  // Loading states
  globalLoading: LoadingState;
  
  // Cache configuration
  cacheConfig: CacheConfig;
  
  // Optimistic updates
  optimisticUpdates: Map<string, OptimisticUpdate<any>>;
}

// Context actions
type ExperimentAction =
  | { type: 'SET_SELECTED_EXPERIMENTS'; payload: Set<string> }
  | { type: 'TOGGLE_EXPERIMENT_SELECTION'; payload: string }
  | { type: 'CLEAR_SELECTION' }
  | { type: 'SET_FILTERS'; payload: ExperimentFilters }
  | { type: 'SET_PAGINATION'; payload: PaginationParams }
  | { type: 'SET_GLOBAL_LOADING'; payload: LoadingState }
  | { type: 'SET_CACHE_CONFIG'; payload: CacheConfig }
  | { type: 'ADD_OPTIMISTIC_UPDATE'; payload: { id: string; update: OptimisticUpdate<any> } }
  | { type: 'REMOVE_OPTIMISTIC_UPDATE'; payload: string }
  | { type: 'CLEAR_OPTIMISTIC_UPDATES' };

// Initial state
const initialState: ExperimentContextState = {
  selectedExperiments: new Set(),
  filters: {},
  pagination: {
    page: 1,
    limit: 20,
    sortBy: 'createdAt',
    sortOrder: 'desc',
  },
  globalLoading: {
    isLoading: false,
    isError: false,
    error: null,
  },
  cacheConfig: {
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
    refetchOnWindowFocus: false,
    refetchOnMount: true,
  },
  optimisticUpdates: new Map(),
};

// Reducer function
function experimentReducer(
  state: ExperimentContextState,
  action: ExperimentAction
): ExperimentContextState {
  switch (action.type) {
    case 'SET_SELECTED_EXPERIMENTS':
      return {
        ...state,
        selectedExperiments: action.payload,
      };

    case 'TOGGLE_EXPERIMENT_SELECTION':
      const newSelection = new Set(state.selectedExperiments);
      if (newSelection.has(action.payload)) {
        newSelection.delete(action.payload);
      } else {
        newSelection.add(action.payload);
      }
      return {
        ...state,
        selectedExperiments: newSelection,
      };

    case 'CLEAR_SELECTION':
      return {
        ...state,
        selectedExperiments: new Set(),
      };

    case 'SET_FILTERS':
      return {
        ...state,
        filters: action.payload,
        pagination: {
          ...state.pagination,
          page: 1, // Reset to first page when filters change
        },
      };

    case 'SET_PAGINATION':
      return {
        ...state,
        pagination: action.payload,
      };

    case 'SET_GLOBAL_LOADING':
      return {
        ...state,
        globalLoading: action.payload,
      };

    case 'SET_CACHE_CONFIG':
      return {
        ...state,
        cacheConfig: action.payload,
      };

    case 'ADD_OPTIMISTIC_UPDATE':
      const newUpdates = new Map(state.optimisticUpdates);
      newUpdates.set(action.payload.id, action.payload.update);
      return {
        ...state,
        optimisticUpdates: newUpdates,
      };

    case 'REMOVE_OPTIMISTIC_UPDATE':
      const updatesWithoutRemoved = new Map(state.optimisticUpdates);
      updatesWithoutRemoved.delete(action.payload);
      return {
        ...state,
        optimisticUpdates: updatesWithoutRemoved,
      };

    case 'CLEAR_OPTIMISTIC_UPDATES':
      return {
        ...state,
        optimisticUpdates: new Map(),
      };

    default:
      return state;
  }
}

// Context interface
interface ExperimentContextValue {
  // State
  state: ExperimentContextState;
  
  // Actions
  setSelectedExperiments: (experiments: Set<string>) => void;
  toggleExperimentSelection: (experimentId: string) => void;
  clearSelection: () => void;
  setFilters: (filters: ExperimentFilters) => void;
  setPagination: (pagination: PaginationParams) => void;
  setGlobalLoading: (loading: LoadingState) => void;
  setCacheConfig: (config: CacheConfig) => void;
  
  // Optimistic updates
  addOptimisticUpdate: (id: string, update: OptimisticUpdate<any>) => void;
  removeOptimisticUpdate: (id: string) => void;
  clearOptimisticUpdates: () => void;
  
  // Query client access
  queryClient: QueryClient;
}

// Create context
const ExperimentContext = createContext<ExperimentContextValue | undefined>(undefined);

// Context provider component
interface ExperimentProviderProps {
  children: ReactNode;
  initialFilters?: ExperimentFilters;
  initialPagination?: PaginationParams;
}

export const ExperimentProvider: React.FC<ExperimentProviderProps> = ({
  children,
  initialFilters = {},
  initialPagination,
}) => {
  const [state, dispatch] = useReducer(experimentReducer, {
    ...initialState,
    filters: { ...initialState.filters, ...initialFilters },
    pagination: { ...initialState.pagination, ...initialPagination },
  });

  // Action creators
  const setSelectedExperiments = useCallback((experiments: Set<string>) => {
    dispatch({ type: 'SET_SELECTED_EXPERIMENTS', payload: experiments });
  }, []);

  const toggleExperimentSelection = useCallback((experimentId: string) => {
    dispatch({ type: 'TOGGLE_EXPERIMENT_SELECTION', payload: experimentId });
  }, []);

  const clearSelection = useCallback(() => {
    dispatch({ type: 'CLEAR_SELECTION' });
  }, []);

  const setFilters = useCallback((filters: ExperimentFilters) => {
    dispatch({ type: 'SET_FILTERS', payload: filters });
  }, []);

  const setPagination = useCallback((pagination: PaginationParams) => {
    dispatch({ type: 'SET_PAGINATION', payload: pagination });
  }, []);

  const setGlobalLoading = useCallback((loading: LoadingState) => {
    dispatch({ type: 'SET_GLOBAL_LOADING', payload: loading });
  }, []);

  const setCacheConfig = useCallback((config: CacheConfig) => {
    dispatch({ type: 'SET_CACHE_CONFIG', payload: config });
  }, []);

  const addOptimisticUpdate = useCallback((id: string, update: OptimisticUpdate<any>) => {
    dispatch({ type: 'ADD_OPTIMISTIC_UPDATE', payload: { id, update } });
  }, []);

  const removeOptimisticUpdate = useCallback((id: string) => {
    dispatch({ type: 'REMOVE_OPTIMISTIC_UPDATE', payload: id });
  }, []);

  const clearOptimisticUpdates = useCallback(() => {
    dispatch({ type: 'CLEAR_OPTIMISTIC_UPDATES' });
  }, []);

  const contextValue: ExperimentContextValue = {
    state,
    setSelectedExperiments,
    toggleExperimentSelection,
    clearSelection,
    setFilters,
    setPagination,
    setGlobalLoading,
    setCacheConfig,
    addOptimisticUpdate,
    removeOptimisticUpdate,
    clearOptimisticUpdates,
    queryClient,
  };

  return (
    <QueryClientProvider client={queryClient}>
      <ExperimentContext.Provider value={contextValue}>
        {children}
      </ExperimentContext.Provider>
    </QueryClientProvider>
  );
};

// Custom hook to use the experiment context
export const useExperimentContext = (): ExperimentContextValue => {
  const context = useContext(ExperimentContext);
  if (!context) {
    throw new Error('useExperimentContext must be used within an ExperimentProvider');
  }
  return context;
};

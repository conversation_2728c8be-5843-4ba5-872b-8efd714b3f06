import React, { useState } from 'react';
import { ExperimentDataTable } from './ExperimentDataTable';

function App() {
  const [experiments, setExperiments] = useState([
    {
      id: '1',
      tenantId: 'tenant-1',
      name: 'Homepage Button Color Test',
      description: 'Testing different button colors to improve conversion rates',
      status: 'ACTIVE' as const,
      startDate: '2024-01-15T00:00:00Z',
      endDate: '2024-02-15T00:00:00Z',
      createdAt: '2024-01-10T00:00:00Z',
      updatedAt: '2024-01-15T00:00:00Z',
      createdBy: 'user-1',
      variants: [
        { id: 'v1', name: 'Control (Blue)', trafficWeight: 0.5 },
        { id: 'v2', name: 'Treatment (Green)', trafficWeight: 0.5 }
      ],
      tags: ['homepage', 'ui', 'conversion'],
      _count: { userAssignments: 1250, events: 3420 }
    },
    {
      id: '2',
      tenantId: 'tenant-1',
      name: 'Checkout Flow Optimization',
      description: 'Testing a simplified checkout process',
      status: 'DRAFT' as const,
      createdAt: '2024-01-12T00:00:00Z',
      updatedAt: '2024-01-12T00:00:00Z',
      createdBy: 'user-2',
      variants: [
        { id: 'v3', name: 'Current Flow', trafficWeight: 0.5 },
        { id: 'v4', name: 'Simplified Flow', trafficWeight: 0.5 }
      ],
      tags: ['checkout', 'ux', 'conversion'],
      _count: { userAssignments: 0, events: 0 }
    },
    {
      id: '3',
      tenantId: 'tenant-1',
      name: 'Email Subject Line Test',
      description: 'Testing different email subject lines for open rates',
      status: 'COMPLETED' as const,
      startDate: '2024-01-01T00:00:00Z',
      endDate: '2024-01-20T00:00:00Z',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-20T00:00:00Z',
      createdBy: 'user-1',
      variants: [
        { id: 'v5', name: 'Original Subject', trafficWeight: 0.33 },
        { id: 'v6', name: 'Personalized Subject', trafficWeight: 0.33 },
        { id: 'v7', name: 'Urgent Subject', trafficWeight: 0.34 }
      ],
      tags: ['email', 'marketing', 'engagement'],
      _count: { userAssignments: 5000, events: 12500 }
    }
  ]);

  const handleStatusChange = (experiment: any, newStatus: string) => {
    setExperiments(prev =>
      prev.map(exp =>
        exp.id === experiment.id
          ? { ...exp, status: newStatus as any, updatedAt: new Date().toISOString() }
          : exp
      )
    );
    alert(`Changed ${experiment.name} status to ${newStatus}`);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">🎉 React Experiment Data Table</h1>
            <p className="text-gray-500 mt-1">
              Complete data table with sorting, filtering, pagination, and quick actions
            </p>
          </div>
          <div className="text-sm text-gray-500">
            <div>Current Tenant: <span className="font-medium text-indigo-600">tenant-1</span></div>
            <div className="mt-1">Total Experiments: <span className="font-medium">{experiments.length}</span></div>
          </div>
        </div>

        {/* Features List */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">✨ Interactive Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
            <div className="flex items-center space-x-2">
              <span className="text-green-500">✓</span>
              <span>Click column headers to sort</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-green-500">✓</span>
              <span>Real-time search filtering</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-green-500">✓</span>
              <span>Multi-status filtering</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-green-500">✓</span>
              <span>Bulk selection & actions</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-green-500">✓</span>
              <span>Quick status changes</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-green-500">✓</span>
              <span>Responsive pagination</span>
            </div>
          </div>
        </div>

        {/* Data Table */}
        <ExperimentDataTable
          experiments={experiments}
          onStatusChange={handleStatusChange}
        />
      </div>
    </div>
  );
}

export default App;

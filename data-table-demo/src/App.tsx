import React from 'react';
import { Toaster } from 'react-hot-toast';
import { ExperimentProvider } from './contexts/ExperimentContext';
import { ExperimentDataTable } from './ExperimentDataTable';

// Main App component with context providers
const AppContent: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">🚀 React Experiment Management</h1>
            <p className="text-gray-500 mt-1">
              Advanced experiment management with React Context, custom hooks, and API integration
            </p>
          </div>
          <div className="text-sm text-gray-500">
            <div>Current Tenant: <span className="font-medium text-indigo-600">tenant-1</span></div>
            <div className="mt-1">Environment: <span className="font-medium text-green-600">Development</span></div>
          </div>
        </div>

        {/* Features List */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">🎯 Advanced Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
            <div className="flex items-center space-x-2">
              <span className="text-green-500">✓</span>
              <span>React Context State Management</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-green-500">✓</span>
              <span>Custom Hooks for API Communication</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-green-500">✓</span>
              <span>Optimistic Updates</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-green-500">✓</span>
              <span>Error Handling & Recovery</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-green-500">✓</span>
              <span>Data Caching with React Query</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-green-500">✓</span>
              <span>Loading States Management</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-green-500">✓</span>
              <span>Bulk Operations</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-green-500">✓</span>
              <span>Real-time Toast Notifications</span>
            </div>
          </div>
        </div>

        {/* Data Table with Context */}
        <ExperimentDataTable />
      </div>
    </div>
  );
};

function App() {
  return (
    <ExperimentProvider>
      <AppContent />

      {/* Toast notifications */}
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#363636',
            color: '#fff',
          },
          success: {
            duration: 3000,
            iconTheme: {
              primary: '#4ade80',
              secondary: '#fff',
            },
          },
          error: {
            duration: 5000,
            iconTheme: {
              primary: '#ef4444',
              secondary: '#fff',
            },
          },
          loading: {
            duration: Infinity,
          },
        }}
      />
    </ExperimentProvider>
  );
}

export default App;

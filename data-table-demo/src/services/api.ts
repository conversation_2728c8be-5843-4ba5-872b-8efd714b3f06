// API service layer for experiment management
import {
  Experiment,
  ExperimentFilters,
  PaginationParams,
  PaginatedResponse,
  ApiResponse,
  CreateExperimentForm,
  UpdateExperimentForm,
  ExperimentStatus,
  ExperimentAnalytics,
} from '../types/experiment';

// Base API configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';
const API_TIMEOUT = 10000; // 10 seconds

// Custom API Error class
class ApiError extends Error {
  public code: string;
  public statusCode: number;
  public details?: any;
  public fieldErrors?: any[];

  constructor(message: string, code: string, statusCode: number, details?: any, fieldErrors?: any[]) {
    super(message);
    this.name = 'ApiError';
    this.code = code;
    this.statusCode = statusCode;
    this.details = details;
    this.fieldErrors = fieldErrors;
  }
}

// Custom fetch wrapper with error handling
class ApiClient {
  private baseURL: string;
  private timeout: number;

  constructor(baseURL: string, timeout: number = API_TIMEOUT) {
    this.baseURL = baseURL;
    this.timeout = timeout;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    
    // Create abort controller for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new ApiError(
          errorData.message || `HTTP ${response.status}: ${response.statusText}`,
          errorData.code || 'HTTP_ERROR',
          response.status,
          errorData.details,
          errorData.fieldErrors
        );
      }

      return await response.json();
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof ApiError) {
        throw error;
      }

      if ((error as any)?.name === 'AbortError') {
        throw new ApiError(
          'Request timeout',
          'TIMEOUT_ERROR',
          408
        );
      }

      throw new ApiError(
        (error as any)?.message || 'Network error',
        'NETWORK_ERROR',
        0
      );
    }
  }

  async get<T>(endpoint: string, params?: Record<string, any>): Promise<T> {
    const url = new URL(endpoint, this.baseURL);
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, String(value));
        }
      });
    }

    return this.request<T>(url.pathname + url.search);
  }

  async post<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async patch<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'DELETE',
    });
  }
}

// Create API client instance
const apiClient = new ApiClient(API_BASE_URL);

// Experiments API service
export const experimentsApi = {
  // Get experiments with filters and pagination
  async getExperiments(
    filters: ExperimentFilters = {},
    pagination: PaginationParams = { page: 1, limit: 20 }
  ): Promise<PaginatedResponse<Experiment>> {
    const params = {
      ...pagination,
      ...filters,
      // Convert arrays to comma-separated strings for URL params
      status: filters.status?.join(','),
      tags: filters.tags?.join(','),
      createdBy: filters.createdBy?.join(','),
    };

    return apiClient.get<PaginatedResponse<Experiment>>('/experiments', params);
  },

  // Get single experiment by ID
  async getExperiment(id: string): Promise<ApiResponse<Experiment>> {
    return apiClient.get<ApiResponse<Experiment>>(`/experiments/${id}`);
  },

  // Create new experiment
  async createExperiment(data: CreateExperimentForm): Promise<ApiResponse<Experiment>> {
    return apiClient.post<ApiResponse<Experiment>>('/experiments', data);
  },

  // Update experiment
  async updateExperiment(id: string, data: UpdateExperimentForm): Promise<ApiResponse<Experiment>> {
    return apiClient.put<ApiResponse<Experiment>>(`/experiments/${id}`, data);
  },

  // Update experiment status
  async updateExperimentStatus(
    id: string,
    status: ExperimentStatus,
    data?: any
  ): Promise<ApiResponse<Experiment>> {
    return apiClient.patch<ApiResponse<Experiment>>(`/experiments/${id}/status`, {
      status,
      ...data,
    });
  },

  // Delete experiment
  async deleteExperiment(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<ApiResponse<void>>(`/experiments/${id}`);
  },

  // Duplicate experiment
  async duplicateExperiment(id: string): Promise<ApiResponse<Experiment>> {
    return apiClient.post<ApiResponse<Experiment>>(`/experiments/${id}/duplicate`);
  },

  // Get experiment analytics
  async getExperimentAnalytics(id: string): Promise<ApiResponse<ExperimentAnalytics>> {
    return apiClient.get<ApiResponse<ExperimentAnalytics>>(`/experiments/${id}/analytics`);
  },

  // Bulk operations
  async bulkUpdateStatus(
    ids: string[],
    status: ExperimentStatus
  ): Promise<ApiResponse<Experiment[]>> {
    return apiClient.patch<ApiResponse<Experiment[]>>('/experiments/bulk/status', {
      ids,
      status,
    });
  },

  async bulkDelete(ids: string[]): Promise<ApiResponse<void>> {
    return apiClient.delete<ApiResponse<void>>('/experiments/bulk');
  },
};

// Mock data for development
const mockExperiments: Experiment[] = [
  {
    id: '1',
    tenantId: 'tenant-1',
    name: 'Homepage Button Color Test',
    description: 'Testing different button colors to improve conversion rates',
    status: 'ACTIVE',
    startDate: '2024-01-15T00:00:00Z',
    endDate: '2024-02-15T00:00:00Z',
    createdAt: '2024-01-10T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z',
    createdBy: 'user-1',
    variants: [
      { id: 'v1', name: 'Control (Blue)', trafficWeight: 0.5 },
      { id: 'v2', name: 'Treatment (Green)', trafficWeight: 0.5 }
    ],
    tags: ['homepage', 'ui', 'conversion'],
    _count: { userAssignments: 1250, events: 3420 }
  },
  {
    id: '2',
    tenantId: 'tenant-1',
    name: 'Checkout Flow Optimization',
    description: 'Testing a simplified checkout process',
    status: 'DRAFT',
    createdAt: '2024-01-12T00:00:00Z',
    updatedAt: '2024-01-12T00:00:00Z',
    createdBy: 'user-2',
    variants: [
      { id: 'v3', name: 'Current Flow', trafficWeight: 0.5 },
      { id: 'v4', name: 'Simplified Flow', trafficWeight: 0.5 }
    ],
    tags: ['checkout', 'ux', 'conversion'],
    _count: { userAssignments: 0, events: 0 }
  },
  {
    id: '3',
    tenantId: 'tenant-1',
    name: 'Email Subject Line Test',
    description: 'Testing different email subject lines for open rates',
    status: 'COMPLETED',
    startDate: '2024-01-01T00:00:00Z',
    endDate: '2024-01-20T00:00:00Z',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-20T00:00:00Z',
    createdBy: 'user-1',
    variants: [
      { id: 'v5', name: 'Original Subject', trafficWeight: 0.33 },
      { id: 'v6', name: 'Personalized Subject', trafficWeight: 0.33 },
      { id: 'v7', name: 'Urgent Subject', trafficWeight: 0.34 }
    ],
    tags: ['email', 'marketing', 'engagement'],
    _count: { userAssignments: 5000, events: 12500 }
  },
  {
    id: '4',
    tenantId: 'tenant-1',
    name: 'Product Page Layout',
    description: 'Testing different product page layouts',
    status: 'PAUSED',
    startDate: '2024-01-08T00:00:00Z',
    createdAt: '2024-01-05T00:00:00Z',
    updatedAt: '2024-01-08T00:00:00Z',
    createdBy: 'user-3',
    variants: [
      { id: 'v8', name: 'Current Layout', trafficWeight: 0.5 },
      { id: 'v9', name: 'New Layout', trafficWeight: 0.5 }
    ],
    tags: ['product', 'layout', 'ui'],
    _count: { userAssignments: 800, events: 2100 }
  },
  {
    id: '5',
    tenantId: 'tenant-2',
    name: 'Pricing Page Test',
    description: 'Testing different pricing displays',
    status: 'ACTIVE',
    startDate: '2024-01-20T00:00:00Z',
    createdAt: '2024-01-18T00:00:00Z',
    updatedAt: '2024-01-20T00:00:00Z',
    createdBy: 'user-4',
    variants: [
      { id: 'v10', name: 'Monthly Focus', trafficWeight: 0.5 },
      { id: 'v11', name: 'Annual Focus', trafficWeight: 0.5 }
    ],
    tags: ['pricing', 'conversion', 'revenue'],
    _count: { userAssignments: 600, events: 1800 }
  }
];

// Mock API for development (when backend is not available)
export const mockExperimentsApi = {
  async getExperiments(
    filters: ExperimentFilters = {},
    pagination: PaginationParams = { page: 1, limit: 20 }
  ): Promise<PaginatedResponse<Experiment>> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    let filtered = [...mockExperiments];

    // Apply filters
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(exp =>
        exp.name.toLowerCase().includes(searchLower) ||
        exp.description?.toLowerCase().includes(searchLower) ||
        exp.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    if (filters.status && filters.status.length > 0) {
      filtered = filtered.filter(exp => filters.status!.includes(exp.status));
    }

    if (filters.tags && filters.tags.length > 0) {
      filtered = filtered.filter(exp =>
        filters.tags!.some(tag => exp.tags.includes(tag))
      );
    }

    // Apply sorting
    if (pagination.sortBy) {
      filtered.sort((a, b) => {
        let aValue: any, bValue: any;

        switch (pagination.sortBy) {
          case 'variantCount':
            aValue = a.variants.length;
            bValue = b.variants.length;
            break;
          case 'assignmentCount':
            aValue = a._count.userAssignments;
            bValue = b._count.userAssignments;
            break;
          case 'eventCount':
            aValue = a._count.events;
            bValue = b._count.events;
            break;
          default:
            aValue = pagination.sortBy ? (a as any)[pagination.sortBy] : a.createdAt;
            bValue = pagination.sortBy ? (b as any)[pagination.sortBy] : b.createdAt;
        }

        if (aValue === bValue) return 0;
        const comparison = aValue < bValue ? -1 : 1;
        return pagination.sortOrder === 'desc' ? -comparison : comparison;
      });
    }

    // Apply pagination
    const total = filtered.length;
    const totalPages = Math.ceil(total / pagination.limit);
    const startIndex = (pagination.page - 1) * pagination.limit;
    const paginatedData = filtered.slice(startIndex, startIndex + pagination.limit);

    return {
      data: paginatedData,
      pagination: {
        page: pagination.page,
        limit: pagination.limit,
        total,
        totalPages,
      },
    };
  },

  async getExperiment(id: string): Promise<ApiResponse<Experiment>> {
    await new Promise(resolve => setTimeout(resolve, 300));

    const experiment = mockExperiments.find(exp => exp.id === id);
    if (!experiment) {
      throw new ApiError('Experiment not found', 'NOT_FOUND', 404);
    }

    return {
      data: experiment,
      success: true,
    };
  },

  async createExperiment(data: CreateExperimentForm): Promise<ApiResponse<Experiment>> {
    await new Promise(resolve => setTimeout(resolve, 800));

    const newExperiment: Experiment = {
      id: `exp-${Date.now()}`,
      tenantId: 'tenant-1',
      name: data.name,
      description: data.description,
      status: 'DRAFT',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: 'current-user',
      variants: data.variants.map((v, i) => ({ ...v, id: `var-${Date.now()}-${i}` })),
      tags: data.tags || [],
      _count: { userAssignments: 0, events: 0 },
    };

    mockExperiments.unshift(newExperiment);

    return {
      data: newExperiment,
      success: true,
      message: 'Experiment created successfully',
    };
  },

  async updateExperiment(id: string, data: UpdateExperimentForm): Promise<ApiResponse<Experiment>> {
    await new Promise(resolve => setTimeout(resolve, 600));

    const index = mockExperiments.findIndex(exp => exp.id === id);
    if (index === -1) {
      throw new ApiError('Experiment not found', 'NOT_FOUND', 404);
    }

    const updatedExperiment: Experiment = {
      ...mockExperiments[index],
      ...data,
      updatedAt: new Date().toISOString(),
      variants: data.variants ? data.variants.map((v, i) => ({
        id: `var-${Date.now()}-${i}`,
        name: v.name,
        trafficWeight: v.trafficWeight,
        config: v.config
      })) : mockExperiments[index].variants,
      targetingRules: data.targetingRules ? data.targetingRules.map((rule, i) => ({
        ...rule,
        id: `rule-${Date.now()}-${i}`
      })) : mockExperiments[index].targetingRules,
    };

    mockExperiments[index] = updatedExperiment;

    return {
      data: updatedExperiment,
      success: true,
      message: 'Experiment updated successfully',
    };
  },

  async updateExperimentStatus(
    id: string,
    status: ExperimentStatus,
    data?: any
  ): Promise<ApiResponse<Experiment>> {
    await new Promise(resolve => setTimeout(resolve, 400));

    const index = mockExperiments.findIndex(exp => exp.id === id);
    if (index === -1) {
      throw new ApiError('Experiment not found', 'NOT_FOUND', 404);
    }

    const updatedExperiment = {
      ...mockExperiments[index],
      status,
      updatedAt: new Date().toISOString(),
      ...(status === 'ACTIVE' && !mockExperiments[index].startDate && {
        startDate: new Date().toISOString(),
      }),
      ...(status === 'COMPLETED' && !mockExperiments[index].endDate && {
        endDate: new Date().toISOString(),
      }),
    };

    mockExperiments[index] = updatedExperiment;

    return {
      data: updatedExperiment,
      success: true,
      message: `Experiment ${status.toLowerCase()} successfully`,
    };
  },

  async deleteExperiment(id: string): Promise<ApiResponse<void>> {
    await new Promise(resolve => setTimeout(resolve, 500));

    const index = mockExperiments.findIndex(exp => exp.id === id);
    if (index === -1) {
      throw new ApiError('Experiment not found', 'NOT_FOUND', 404);
    }

    mockExperiments.splice(index, 1);

    return {
      data: undefined as any,
      success: true,
      message: 'Experiment deleted successfully',
    };
  },

  async duplicateExperiment(id: string): Promise<ApiResponse<Experiment>> {
    await new Promise(resolve => setTimeout(resolve, 700));

    const original = mockExperiments.find(exp => exp.id === id);
    if (!original) {
      throw new ApiError('Experiment not found', 'NOT_FOUND', 404);
    }

    const duplicated: Experiment = {
      ...original,
      id: `exp-${Date.now()}`,
      name: `${original.name} (Copy)`,
      status: 'DRAFT',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      startDate: undefined,
      endDate: undefined,
      variants: original.variants.map((v, i) => ({ ...v, id: `var-${Date.now()}-${i}` })),
      _count: { userAssignments: 0, events: 0 },
    };

    mockExperiments.unshift(duplicated);

    return {
      data: duplicated,
      success: true,
      message: 'Experiment duplicated successfully',
    };
  },

  async getExperimentAnalytics(id: string): Promise<ApiResponse<ExperimentAnalytics>> {
    await new Promise(resolve => setTimeout(resolve, 1000));

    const experiment = mockExperiments.find(exp => exp.id === id);
    if (!experiment) {
      throw new ApiError('Experiment not found', 'NOT_FOUND', 404);
    }

    // Mock analytics data
    const analytics: ExperimentAnalytics = {
      experimentId: id,
      totalAssignments: experiment._count.userAssignments,
      totalEvents: experiment._count.events,
      conversionRate: experiment._count.userAssignments > 0
        ? (experiment._count.events / experiment._count.userAssignments) * 100
        : 0,
      variantPerformance: experiment.variants.map(variant => ({
        variantId: variant.id,
        variantName: variant.name,
        assignments: Math.floor(experiment._count.userAssignments * variant.trafficWeight),
        events: Math.floor(experiment._count.events * variant.trafficWeight),
        conversionRate: Math.random() * 10 + 2, // Mock conversion rate
        confidence: Math.random() * 30 + 70, // Mock confidence
      })),
      timeSeriesData: Array.from({ length: 30 }, (_, i) => ({
        date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        assignments: Math.floor(Math.random() * 100) + 10,
        events: Math.floor(Math.random() * 50) + 5,
        conversionRate: Math.random() * 5 + 2,
      })),
      statisticalSignificance: {
        isSignificant: Math.random() > 0.5,
        confidence: Math.random() * 20 + 80,
        pValue: Math.random() * 0.05,
        winningVariant: experiment.variants[Math.floor(Math.random() * experiment.variants.length)].id,
      },
    };

    return {
      data: analytics,
      success: true,
    };
  },

  async bulkUpdateStatus(
    ids: string[],
    status: ExperimentStatus
  ): Promise<ApiResponse<Experiment[]>> {
    await new Promise(resolve => setTimeout(resolve, 1000));

    const updatedExperiments: Experiment[] = [];

    for (const id of ids) {
      const index = mockExperiments.findIndex(exp => exp.id === id);
      if (index !== -1) {
        const updatedExperiment = {
          ...mockExperiments[index],
          status,
          updatedAt: new Date().toISOString(),
        };
        mockExperiments[index] = updatedExperiment;
        updatedExperiments.push(updatedExperiment);
      }
    }

    return {
      data: updatedExperiments,
      success: true,
      message: `${updatedExperiments.length} experiments updated successfully`,
    };
  },

  async bulkDelete(ids: string[]): Promise<ApiResponse<void>> {
    await new Promise(resolve => setTimeout(resolve, 800));

    let deletedCount = 0;
    for (const id of ids) {
      const index = mockExperiments.findIndex(exp => exp.id === id);
      if (index !== -1) {
        mockExperiments.splice(index, 1);
        deletedCount++;
      }
    }

    return {
      data: undefined as any,
      success: true,
      message: `${deletedCount} experiments deleted successfully`,
    };
  },
};

// Export the appropriate API based on environment
export const api = process.env.NODE_ENV === 'development' && !process.env.REACT_APP_USE_REAL_API
  ? mockExperimentsApi
  : experimentsApi;

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stock Graph - Real-time Stock Market Visualization</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>📈 Stock Graph</h1>
            <p>Real-time stock market visualization</p>
        </header>

        <div class="controls">
            <div class="search-section">
                <input
                    type="text"
                    id="stockSymbol"
                    placeholder="Enter stock symbol (e.g., MGTX, AAPL, GOOGL)"
                    value="MGTX"
                >
                <button id="searchBtn">Search</button>
            </div>

            <div class="time-range-section">
                <div class="time-buttons">
                    <button class="time-btn active" data-range="1D">1D</button>
                    <button class="time-btn" data-range="1W">1W</button>
                    <button class="time-btn" data-range="1M">1M</button>
                    <button class="time-btn" data-range="YTD">YTD</button>
                    <button class="time-btn" data-range="1Y">1Y</button>
                    <button class="time-btn" data-range="Max">Max</button>
                    <button class="time-btn" data-range="Future">Future</button>
                </div>
            </div>
        </div>

        <div class="stock-info" id="stockInfo">
            <div class="stock-header">
                <div class="stock-title">
                    <span class="exchange">Nasdaq</span>
                    <h2 id="stockName">MeiraGTx Holdings (MGTX)</h2>
                </div>
                <div class="stock-price">
                    <span id="currentPrice">$7.04</span>
                    <span id="priceChange" class="positive">+0.23 (+3.38%)</span>
                </div>
            </div>
        </div>

        <div class="chart-container">
            <canvas id="stockChart"></canvas>
        </div>

        <div class="chart-controls">
            <div class="toggle-section">
                <label class="toggle-item">
                    <input type="checkbox" id="togglePrice" checked>
                    <span class="checkmark"></span>
                    Price
                </label>
                <label class="toggle-item">
                    <input type="checkbox" id="toggleVolume" checked>
                    <span class="checkmark"></span>
                    Volume
                </label>
                <label class="toggle-item">
                    <input type="checkbox" id="toggleEvents" checked>
                    <span class="checkmark"></span>
                    Events
                </label>
                <label class="toggle-item">
                    <input type="checkbox" id="toggleForecast">
                    <span class="checkmark"></span>
                    Forecast
                </label>
            </div>
        </div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Loading stock data...</p>
        </div>

        <div class="error" id="error" style="display: none;">
            <p>Error loading stock data. Please try again.</p>
        </div>

        <div class="demo-notice">
            <p><strong>Demo Mode:</strong> This application uses simulated data for demonstration. 
            To use real stock data, you'll need to sign up for a free API key from 
            <a href="https://www.alphavantage.co/support/#api-key" target="_blank">Alpha Vantage</a> 
            and update the API configuration in script.js.</p>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>

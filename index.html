<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stock Graph - Real-time Stock Market Visualization</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>📈 Stock Graph</h1>
            <p>Real-time stock market visualization</p>
        </header>

        <div class="controls">
            <div class="search-section">
                <input 
                    type="text" 
                    id="stockSymbol" 
                    placeholder="Enter stock symbol (e.g., AAPL, GOOGL, MSFT)"
                    value="AAPL"
                >
                <button id="searchBtn">Search</button>
            </div>

            <div class="time-range-section">
                <label>Time Range:</label>
                <div class="time-buttons">
                    <button class="time-btn active" data-range="1D">1D</button>
                    <button class="time-btn" data-range="1W">1W</button>
                    <button class="time-btn" data-range="1M">1M</button>
                    <button class="time-btn" data-range="3M">3M</button>
                    <button class="time-btn" data-range="1Y">1Y</button>
                </div>
            </div>
        </div>

        <div class="stock-info" id="stockInfo">
            <div class="stock-header">
                <h2 id="stockName">Apple Inc. (AAPL)</h2>
                <div class="stock-price">
                    <span id="currentPrice">$150.00</span>
                    <span id="priceChange" class="positive">+2.50 (+1.69%)</span>
                </div>
            </div>
        </div>

        <div class="chart-container">
            <canvas id="stockChart"></canvas>
        </div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Loading stock data...</p>
        </div>

        <div class="error" id="error" style="display: none;">
            <p>Error loading stock data. Please try again.</p>
        </div>

        <div class="demo-notice">
            <p><strong>Demo Mode:</strong> This application uses simulated data for demonstration. 
            To use real stock data, you'll need to sign up for a free API key from 
            <a href="https://www.alphavantage.co/support/#api-key" target="_blank">Alpha Vantage</a> 
            and update the API configuration in script.js.</p>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>

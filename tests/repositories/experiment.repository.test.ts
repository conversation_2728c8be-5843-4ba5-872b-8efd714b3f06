// Test file for ExperimentRepository
import { ExperimentRepository } from '../../src/repositories/experiment.repository';
import { createDatabaseClient } from '../../src/database/client';
import { ExperimentStatus } from '@prisma/client';

describe('ExperimentRepository', () => {
  let repository: ExperimentRepository;
  let tenantId: string;

  beforeAll(async () => {
    // Initialize database client for testing
    const dbClient = createDatabaseClient({
      url: process.env.TEST_DATABASE_URL || 'postgresql://test:test@localhost:5432/ab_testing_test',
    });
    await dbClient.connect();

    repository = new ExperimentRepository();
    
    // Create a test tenant
    const tenant = await dbClient.getClient().tenant.create({
      data: {
        name: 'Test Tenant',
        slug: 'test-tenant',
      },
    });
    tenantId = tenant.id;
  });

  afterAll(async () => {
    // Cleanup test data
    const dbClient = createDatabaseClient({
      url: process.env.TEST_DATABASE_URL || 'postgresql://test:test@localhost:5432/ab_testing_test',
    });
    
    await dbClient.getClient().tenant.deleteMany({
      where: { slug: 'test-tenant' },
    });
    
    await dbClient.disconnect();
  });

  describe('create', () => {
    it('should create a new experiment', async () => {
      const experimentData = {
        name: 'Test Experiment',
        description: 'A test experiment',
        hypothesis: 'This is a test hypothesis',
        status: ExperimentStatus.DRAFT,
        primaryMetric: 'conversion_rate',
      };

      const experiment = await repository.create(experimentData, tenantId);

      expect(experiment).toBeDefined();
      expect(experiment.name).toBe(experimentData.name);
      expect(experiment.tenantId).toBe(tenantId);
      expect(experiment.status).toBe(ExperimentStatus.DRAFT);
    });

    it('should throw error for invalid tenant', async () => {
      const experimentData = {
        name: 'Test Experiment',
        description: 'A test experiment',
      };

      await expect(
        repository.create(experimentData, 'invalid-tenant-id')
      ).rejects.toThrow();
    });
  });

  describe('findByStatus', () => {
    it('should find experiments by status', async () => {
      // Create test experiments
      await repository.create({
        name: 'Active Experiment',
        status: ExperimentStatus.ACTIVE,
      }, tenantId);

      await repository.create({
        name: 'Draft Experiment',
        status: ExperimentStatus.DRAFT,
      }, tenantId);

      const activeExperiments = await repository.findByStatus(
        tenantId,
        [ExperimentStatus.ACTIVE]
      );

      expect(activeExperiments).toBeDefined();
      expect(activeExperiments.length).toBeGreaterThan(0);
      expect(activeExperiments.every(exp => exp.status === ExperimentStatus.ACTIVE)).toBe(true);
    });
  });

  describe('updateStatus', () => {
    it('should update experiment status', async () => {
      const experiment = await repository.create({
        name: 'Status Test Experiment',
        status: ExperimentStatus.DRAFT,
      }, tenantId);

      const updatedExperiment = await repository.updateStatus(
        experiment.id,
        ExperimentStatus.ACTIVE,
        tenantId
      );

      expect(updatedExperiment.status).toBe(ExperimentStatus.ACTIVE);
      expect(updatedExperiment.startDate).toBeDefined();
    });

    it('should throw error for invalid status transition', async () => {
      const experiment = await repository.create({
        name: 'Invalid Transition Test',
        status: ExperimentStatus.COMPLETED,
      }, tenantId);

      await expect(
        repository.updateStatus(experiment.id, ExperimentStatus.DRAFT, tenantId)
      ).rejects.toThrow('Invalid status transition');
    });
  });

  describe('getStatistics', () => {
    it('should return experiment statistics', async () => {
      const experiment = await repository.create({
        name: 'Stats Test Experiment',
        status: ExperimentStatus.ACTIVE,
      }, tenantId);

      const stats = await repository.getStatistics(experiment.id, tenantId);

      expect(stats).toBeDefined();
      expect(stats.experimentId).toBe(experiment.id);
      expect(stats.experimentName).toBe(experiment.name);
      expect(stats.totalAssignments).toBe(0);
      expect(stats.variants).toEqual([]);
    });
  });
});

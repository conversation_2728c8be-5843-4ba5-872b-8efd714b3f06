// Prisma Schema for A/B Testing Platform
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enums
enum ExperimentStatus {
  DRAFT
  ACTIVE
  PAUSED
  COMPLETED
  ARCHIVED
}

enum TargetingOperator {
  EQUALS
  NOT_EQUALS
  IN
  NOT_IN
  GREATER_THAN
  LESS_THAN
  CONTAINS
  REGEX
}

enum AssignmentMethod {
  RANDOM
  STICKY
  DETERMINISTIC
}

// Models
model Tenant {
  id        String   @id @default(uuid()) @db.Uuid
  name      String   @db.VarChar(255)
  slug      String   @unique @db.VarChar(100)
  settings  Json     @default("{}")
  isActive  Boolean  @default(true) @map("is_active")
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamptz
  updatedAt DateTime @updatedAt @map("updated_at") @db.Timestamptz

  // Relations
  experiments     Experiment[]
  variants        Variant[]
  targetingRules  TargetingRule[]
  userAssignments UserAssignment[]
  events          Event[]
  results         ExperimentResult[]

  @@map("tenants")
}

model Experiment {
  id                       String           @id @default(uuid()) @db.Uuid
  tenantId                 String           @map("tenant_id") @db.Uuid
  name                     String           @db.VarChar(255)
  description              String?          @db.Text
  hypothesis               String?          @db.Text
  status                   ExperimentStatus @default(DRAFT)
  trafficAllocation        Decimal          @default(1.0000) @map("traffic_allocation") @db.Decimal(5, 4)
  assignmentMethod         AssignmentMethod @default(RANDOM) @map("assignment_method")
  startDate                DateTime?        @map("start_date") @db.Timestamptz
  endDate                  DateTime?        @map("end_date") @db.Timestamptz
  sampleSize               Int?             @map("sample_size")
  confidenceLevel          Decimal?         @default(0.95) @map("confidence_level") @db.Decimal(3, 2)
  minimumDetectableEffect  Decimal?         @map("minimum_detectable_effect") @db.Decimal(5, 4)
  primaryMetric            String?          @map("primary_metric") @db.VarChar(100)
  secondaryMetrics         String[]         @map("secondary_metrics")
  tags                     String[]
  metadata                 Json             @default("{}")
  createdBy                String?          @map("created_by") @db.Uuid
  createdAt                DateTime         @default(now()) @map("created_at") @db.Timestamptz
  updatedAt                DateTime         @updatedAt @map("updated_at") @db.Timestamptz

  // Relations
  tenant          Tenant             @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  variants        Variant[]
  targetingRules  TargetingRule[]
  userAssignments UserAssignment[]
  events          Event[]
  results         ExperimentResult[]

  @@map("experiments")
  @@index([tenantId])
  @@index([status])
  @@index([tenantId, status])
  @@index([startDate, endDate])
}

model Variant {
  id               String  @id @default(uuid()) @db.Uuid
  tenantId         String  @map("tenant_id") @db.Uuid
  experimentId     String  @map("experiment_id") @db.Uuid
  name             String  @db.VarChar(255)
  description      String? @db.Text
  isControl        Boolean @default(false) @map("is_control")
  trafficWeight    Decimal @default(0.5000) @map("traffic_weight") @db.Decimal(5, 4)
  configuration    Json    @default("{}")
  createdAt        DateTime @default(now()) @map("created_at") @db.Timestamptz
  updatedAt        DateTime @updatedAt @map("updated_at") @db.Timestamptz

  // Relations
  tenant          Tenant             @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  experiment      Experiment         @relation(fields: [experimentId], references: [id], onDelete: Cascade)
  userAssignments UserAssignment[]
  events          Event[]
  results         ExperimentResult[]

  @@unique([experimentId, name])
  @@map("variants")
  @@index([tenantId])
  @@index([experimentId])
}

model TargetingRule {
  id            String            @id @default(uuid()) @db.Uuid
  tenantId      String            @map("tenant_id") @db.Uuid
  experimentId  String            @map("experiment_id") @db.Uuid
  name          String            @db.VarChar(255)
  attributeName String            @map("attribute_name") @db.VarChar(100)
  operator      TargetingOperator
  valueText     String?           @map("value_text") @db.Text
  valueNumber   Decimal?          @map("value_number") @db.Decimal
  valueBoolean  Boolean?          @map("value_boolean")
  valueList     String[]          @map("value_list")
  isActive      Boolean           @default(true) @map("is_active")
  priority      Int               @default(0)
  createdAt     DateTime          @default(now()) @map("created_at") @db.Timestamptz
  updatedAt     DateTime          @updatedAt @map("updated_at") @db.Timestamptz

  // Relations
  tenant     Tenant     @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  experiment Experiment @relation(fields: [experimentId], references: [id], onDelete: Cascade)

  @@map("targeting_rules")
  @@index([tenantId])
  @@index([experimentId])
  @@index([attributeName])
}

model UserAssignment {
  id                  String   @id @default(uuid()) @db.Uuid
  tenantId            String   @map("tenant_id") @db.Uuid
  experimentId        String   @map("experiment_id") @db.Uuid
  variantId           String?  @map("variant_id") @db.Uuid
  userId              String   @map("user_id") @db.VarChar(255)
  sessionId           String?  @map("session_id") @db.VarChar(255)
  assignmentTimestamp DateTime @default(now()) @map("assignment_timestamp") @db.Timestamptz
  bucketingKey        String?  @map("bucketing_key") @db.VarChar(255)
  userAttributes      Json     @default("{}") @map("user_attributes")
  isExcluded          Boolean  @default(false) @map("is_excluded")
  exclusionReason     String?  @map("exclusion_reason") @db.Text
  createdAt           DateTime @default(now()) @map("created_at") @db.Timestamptz
  updatedAt           DateTime @updatedAt @map("updated_at") @db.Timestamptz

  // Relations
  tenant     Tenant      @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  experiment Experiment  @relation(fields: [experimentId], references: [id], onDelete: Cascade)
  variant    Variant?    @relation(fields: [variantId], references: [id], onDelete: Cascade)

  @@unique([tenantId, experimentId, userId])
  @@map("user_assignments")
  @@index([tenantId])
  @@index([experimentId])
  @@index([userId])
  @@index([tenantId, userId])
}

model Event {
  id              String   @id @default(uuid()) @db.Uuid
  tenantId        String   @map("tenant_id") @db.Uuid
  experimentId    String?  @map("experiment_id") @db.Uuid
  variantId       String?  @map("variant_id") @db.Uuid
  userId          String   @map("user_id") @db.VarChar(255)
  sessionId       String?  @map("session_id") @db.VarChar(255)
  eventName       String   @map("event_name") @db.VarChar(100)
  eventValue      Decimal? @map("event_value") @db.Decimal(15, 4)
  eventProperties Json     @default("{}") @map("event_properties")
  timestamp       DateTime @default(now()) @db.Timestamptz
  createdAt       DateTime @default(now()) @map("created_at") @db.Timestamptz

  // Relations
  tenant     Tenant      @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  experiment Experiment? @relation(fields: [experimentId], references: [id], onDelete: Cascade)
  variant    Variant?    @relation(fields: [variantId], references: [id], onDelete: Cascade)

  @@map("events")
  @@index([tenantId])
  @@index([experimentId])
  @@index([userId])
  @@index([eventName])
  @@index([timestamp])
}

model ExperimentResult {
  id                        String   @id @default(uuid()) @db.Uuid
  tenantId                  String   @map("tenant_id") @db.Uuid
  experimentId              String   @map("experiment_id") @db.Uuid
  variantId                 String   @map("variant_id") @db.Uuid
  metricName                String   @map("metric_name") @db.VarChar(100)
  sampleSize                Int      @map("sample_size")
  conversionRate            Decimal? @map("conversion_rate") @db.Decimal(8, 6)
  meanValue                 Decimal? @map("mean_value") @db.Decimal(15, 4)
  standardDeviation         Decimal? @map("standard_deviation") @db.Decimal(15, 4)
  confidenceIntervalLower   Decimal? @map("confidence_interval_lower") @db.Decimal(15, 4)
  confidenceIntervalUpper   Decimal? @map("confidence_interval_upper") @db.Decimal(15, 4)
  pValue                    Decimal? @map("p_value") @db.Decimal(10, 8)
  statisticalSignificance   Boolean? @map("statistical_significance")
  calculatedAt              DateTime @default(now()) @map("calculated_at") @db.Timestamptz
  createdAt                 DateTime @default(now()) @map("created_at") @db.Timestamptz
  updatedAt                 DateTime @updatedAt @map("updated_at") @db.Timestamptz

  // Relations
  tenant     Tenant     @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  experiment Experiment @relation(fields: [experimentId], references: [id], onDelete: Cascade)
  variant    Variant    @relation(fields: [variantId], references: [id], onDelete: Cascade)

  @@unique([experimentId, variantId, metricName, calculatedAt])
  @@map("experiment_results")
  @@index([tenantId])
  @@index([experimentId])
  @@index([variantId])
}

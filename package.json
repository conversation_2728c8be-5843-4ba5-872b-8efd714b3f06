{"name": "ab-testing-platform", "version": "1.0.0", "description": "Complete A/B testing platform with PostgreSQL schema, JSON configuration, and TypeScript data access layer", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "ts-node src/index.ts", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:migrate:deploy": "prisma migrate deploy", "db:migrate:reset": "prisma migrate reset", "db:seed": "ts-node prisma/seed.ts", "db:studio": "prisma studio", "type-check": "tsc --noEmit", "validate": "node validate_experiment.js", "validate-examples": "node validate_examples.js", "lint-schema": "ajv compile -s experiment_config_schema.json", "docker:start": "./scripts/start.sh", "docker:start:prod": "./scripts/start.sh production", "docker:stop": "./scripts/stop.sh", "docker:stop:clean": "./scripts/stop.sh --volumes", "docker:backup": "./scripts/backup.sh", "docker:logs": "docker-compose logs -f", "docker:logs:app": "docker-compose logs -f app", "docker:ps": "docker-compose ps", "docker:build": "docker-compose build", "docker:pull": "docker-compose pull", "docker:restart": "docker-compose restart", "docker:health": "curl -f http://localhost:3003/health && echo ' ✅ Application is healthy'"}, "keywords": ["ab-testing", "prisma", "typescript", "orm", "data-access-layer", "repository-pattern", "multi-tenant", "json-schema", "experiment", "validation", "mixpanel"], "author": "Your Name", "license": "MIT", "dependencies": {"@prisma/client": "^5.7.0", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/pg": "^8.15.4", "ajv": "^8.12.0", "ajv-formats": "^2.1.1", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "helmet": "^8.1.0", "pg": "^8.16.0", "prisma": "^5.7.0", "winston": "^3.17.0"}, "devDependencies": {"@types/jest": "^29.5.8", "@types/node": "^20.19.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "prettier": "^3.1.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/ab-testing-platform"}, "files": ["dist", "prisma/schema.prisma", "experiment_config_schema.json", "validate_experiment.js", "example_experiments.json"]}
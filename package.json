{"name": "ab-testing-platform", "version": "1.0.0", "description": "Complete A/B testing platform with PostgreSQL schema, JSON configuration, and TypeScript data access layer", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:migrate:deploy": "prisma migrate deploy", "db:migrate:reset": "prisma migrate reset", "db:seed": "ts-node prisma/seed.ts", "db:studio": "prisma studio", "type-check": "tsc --noEmit", "validate": "node validate_experiment.js", "validate-examples": "node validate_examples.js", "lint-schema": "ajv compile -s experiment_config_schema.json"}, "keywords": ["ab-testing", "prisma", "typescript", "orm", "data-access-layer", "repository-pattern", "multi-tenant", "json-schema", "experiment", "validation", "mixpanel"], "author": "Your Name", "license": "MIT", "dependencies": {"@prisma/client": "^5.7.0", "prisma": "^5.7.0", "ajv": "^8.12.0", "ajv-formats": "^2.1.1"}, "devDependencies": {"@types/jest": "^29.5.8", "@types/node": "^20.9.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "prettier": "^3.1.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "typescript": "^5.3.2"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/ab-testing-platform"}, "files": ["dist", "prisma/schema.prisma", "experiment_config_schema.json", "validate_experiment.js", "example_experiments.json"]}
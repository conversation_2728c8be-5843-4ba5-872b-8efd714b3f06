{"name": "ab-testing-experiment-config", "version": "1.0.0", "description": "JSON schema and validation tools for A/B testing experiment configuration", "main": "validate_experiment.js", "scripts": {"validate": "node validate_experiment.js", "test": "node test_validation.js", "lint-schema": "ajv compile -s experiment_config_schema.json", "validate-examples": "node validate_examples.js"}, "keywords": ["ab-testing", "json-schema", "experiment", "validation", "mixpanel"], "author": "Your Name", "license": "MIT", "dependencies": {"ajv": "^8.12.0", "ajv-formats": "^2.1.1"}, "devDependencies": {"jest": "^29.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/ab-testing-config"}, "files": ["experiment_config_schema.json", "validate_experiment.js", "example_experiments.json"]}
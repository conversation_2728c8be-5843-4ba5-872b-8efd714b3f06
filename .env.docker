# Docker Environment Configuration for A/B Testing Platform

# Application Configuration
NODE_ENV=production
PORT=3003

# Database Configuration
DB_HOST=postgres
DB_PORT=5432
DB_NAME=ab_testing_platform
DB_USER=ab_testing_user
DB_PASSWORD=ab_testing_password
DB_SSL=false
DB_POOL_MIN=2
DB_POOL_MAX=20

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=redis_password
REDIS_DB=0

# Authentication & Security
JWT_SECRET=your_super_secure_jwt_secret_key_change_in_production_12345
JWT_EXPIRES_IN=24h
BCRYPT_ROUNDS=12
SESSION_SECRET=your_super_secure_session_secret_change_in_production_67890

# API Configuration
API_RATE_LIMIT_WINDOW=900000
API_RATE_LIMIT_MAX=100
API_CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:8080,http://localhost

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json
LOG_FILE_ENABLED=true
LOG_FILE_PATH=/app/logs/application.log

# Feature Flags
ENABLE_AUDIT_LOGGING=true
ENABLE_METRICS=true
ENABLE_HEALTH_CHECKS=true
ENABLE_SWAGGER_DOCS=true

# External Services (Optional - replace with your actual keys)
MIXPANEL_TOKEN=your_mixpanel_token_here
SEGMENT_WRITE_KEY=your_segment_write_key_here

# Email Configuration (Optional - for user invitations)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM=A/B Testing Platform <<EMAIL>>

# Monitoring & Observability (Optional)
SENTRY_DSN=your_sentry_dsn_here
NEW_RELIC_LICENSE_KEY=your_new_relic_key_here

# Demo Mode Settings
DEMO_MODE=true
ENABLE_MOCK_AUTH=true

# PostgreSQL Settings
POSTGRES_DB=ab_testing_platform
POSTGRES_USER=ab_testing_user
POSTGRES_PASSWORD=ab_testing_password

# Redis Settings
REDIS_PASSWORD=redis_password

# Grafana Settings
GF_SECURITY_ADMIN_USER=admin
GF_SECURITY_ADMIN_PASSWORD=admin_password

# pgAdmin Settings
PGADMIN_DEFAULT_EMAIL=<EMAIL>
PGADMIN_DEFAULT_PASSWORD=admin_password

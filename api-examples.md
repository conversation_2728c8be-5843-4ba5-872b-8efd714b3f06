# A/B Testing API Examples

This document provides practical examples of using the A/B Testing Experiment Management API.

## Authentication

All API requests require a Bearer token in the Authorization header:

```bash
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## Base URL

```
Production: https://api.example.com/v1
Staging: https://staging-api.example.com/v1
Development: http://localhost:3000/v1
```

## Complete Experiment Workflow

### 1. Create an Experiment

```bash
curl -X POST "https://api.example.com/v1/experiments" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Homepage Button Color Test",
    "description": "Testing different button colors to improve conversion rates",
    "hypothesis": "A red CTA button will increase click-through rates by 15%",
    "traffic_allocation": 1.0,
    "assignment_method": "STICKY",
    "sample_size": 5000,
    "confidence_level": 0.95,
    "minimum_detectable_effect": 0.15,
    "primary_metric": "button_click",
    "secondary_metrics": ["page_view", "bounce_rate"],
    "tags": ["homepage", "cta", "ui"],
    "variants": [
      {
        "name": "Control (Blue Button)",
        "description": "Original blue CTA button",
        "is_control": true,
        "traffic_weight": 0.5,
        "configuration": {
          "button_color": "#007bff",
          "button_text": "Get Started"
        }
      },
      {
        "name": "Red Button",
        "description": "Red CTA button variant",
        "is_control": false,
        "traffic_weight": 0.5,
        "configuration": {
          "button_color": "#dc3545",
          "button_text": "Get Started"
        }
      }
    ],
    "targeting_rules": [
      {
        "name": "US and Canada Only",
        "attribute_name": "country",
        "operator": "IN",
        "value": ["US", "CA"],
        "is_active": true,
        "priority": 1
      },
      {
        "name": "Desktop Users",
        "attribute_name": "device_type",
        "operator": "EQUALS",
        "value": "desktop",
        "is_active": true,
        "priority": 2
      }
    ]
  }'
```

**Response:**
```json
{
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "name": "Homepage Button Color Test",
    "status": "DRAFT",
    "created_at": "2024-01-15T10:00:00Z",
    "variants": [
      {
        "id": "variant-1",
        "name": "Control (Blue Button)",
        "is_control": true,
        "traffic_weight": 0.5
      },
      {
        "id": "variant-2",
        "name": "Red Button",
        "is_control": false,
        "traffic_weight": 0.5
      }
    ]
  },
  "meta": {
    "timestamp": "2024-01-15T10:00:00Z",
    "request_id": "req_123",
    "tenant_id": "tenant_456"
  }
}
```

### 2. Start the Experiment

```bash
curl -X POST "https://api.example.com/v1/experiments/123e4567-e89b-12d3-a456-426614174000/start" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "start_date": "2024-01-15T12:00:00Z",
    "end_date": "2024-02-15T12:00:00Z"
  }'
```

### 3. Assign Users to Experiment

```bash
curl -X POST "https://api.example.com/v1/experiments/123e4567-e89b-12d3-a456-426614174000/assignments" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user_12345",
    "session_id": "session_abc123",
    "user_attributes": {
      "country": "US",
      "device_type": "desktop",
      "subscription_tier": "premium"
    }
  }'
```

**Response:**
```json
{
  "data": {
    "variant_id": "variant-2",
    "is_excluded": false,
    "exclusion_reason": null,
    "assignment": {
      "id": "assignment-789",
      "experiment_id": "123e4567-e89b-12d3-a456-426614174000",
      "variant_id": "variant-2",
      "variant": {
        "name": "Red Button",
        "configuration": {
          "button_color": "#dc3545",
          "button_text": "Get Started"
        }
      }
    }
  }
}
```

### 4. Track Events

```bash
curl -X POST "https://api.example.com/v1/experiments/123e4567-e89b-12d3-a456-426614174000/events" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user_12345",
    "session_id": "session_abc123",
    "event_name": "button_click",
    "event_value": 1,
    "event_properties": {
      "button_color": "red",
      "page": "homepage",
      "position": "hero"
    }
  }'
```

### 5. Get Analytics

```bash
curl -X GET "https://api.example.com/v1/experiments/123e4567-e89b-12d3-a456-426614174000/analytics" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**Response:**
```json
{
  "data": {
    "experiment_id": "123e4567-e89b-12d3-a456-426614174000",
    "experiment_name": "Homepage Button Color Test",
    "status": "ACTIVE",
    "total_users": 2500,
    "start_date": "2024-01-15T12:00:00Z",
    "end_date": "2024-02-15T12:00:00Z",
    "duration_days": 31,
    "variants": [
      {
        "variant_id": "variant-1",
        "variant_name": "Control (Blue Button)",
        "is_control": true,
        "metrics": {
          "total_users": 1250,
          "converted_users": 125,
          "conversion_rate": 0.10,
          "average_value": 1.0,
          "total_value": 125.0
        },
        "statistical_significance": false,
        "p_value": 0.12
      },
      {
        "variant_id": "variant-2",
        "variant_name": "Red Button",
        "is_control": false,
        "metrics": {
          "total_users": 1250,
          "converted_users": 158,
          "conversion_rate": 0.1264,
          "average_value": 1.0,
          "total_value": 158.0
        },
        "statistical_significance": true,
        "p_value": 0.045,
        "confidence_interval": {
          "lower": 0.002,
          "upper": 0.051
        }
      }
    ]
  }
}
```

## Experiment Management Examples

### List Experiments with Filtering

```bash
# Get active experiments with pagination
curl -X GET "https://api.example.com/v1/experiments?status=ACTIVE&page=1&limit=10&sort_by=created_at&sort_order=desc" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Search experiments by name
curl -X GET "https://api.example.com/v1/experiments?search=button&tags=homepage,ui" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Filter by date range
curl -X GET "https://api.example.com/v1/experiments?start_date_from=2024-01-01T00:00:00Z&start_date_to=2024-01-31T23:59:59Z" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Update Experiment (Draft Only)

```bash
curl -X PUT "https://api.example.com/v1/experiments/123e4567-e89b-12d3-a456-426614174000" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "description": "Updated description with more details",
    "sample_size": 10000,
    "tags": ["homepage", "cta", "ui", "conversion"]
  }'
```

### Pause Experiment

```bash
curl -X POST "https://api.example.com/v1/experiments/123e4567-e89b-12d3-a456-426614174000/pause" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "reason": "Unexpected technical issues with red button implementation"
  }'
```

### Complete Experiment

```bash
curl -X POST "https://api.example.com/v1/experiments/123e4567-e89b-12d3-a456-426614174000/complete" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "end_date": "2024-02-10T12:00:00Z",
    "results_summary": "Red button variant showed 26% improvement in conversion rate with statistical significance (p=0.045)"
  }'
```

## Error Handling Examples

### Validation Error Response

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Request validation failed",
    "field_errors": [
      {
        "field": "variants",
        "message": "At least 2 variants are required"
      },
      {
        "field": "traffic_allocation",
        "message": "Must be between 0.0 and 1.0"
      }
    ]
  }
}
```

### Status Transition Error

```json
{
  "error": {
    "code": "INVALID_STATUS_TRANSITION",
    "message": "Cannot transition from COMPLETED to ACTIVE status",
    "details": {
      "current_status": "COMPLETED",
      "requested_status": "ACTIVE",
      "valid_transitions": ["ARCHIVED"]
    }
  }
}
```

## Rate Limiting

The API implements rate limiting with the following limits:

- **Experiment Management**: 1000 requests per hour
- **User Assignment**: 10000 requests per hour  
- **Event Tracking**: 10000 requests per hour

Rate limit headers are included in responses:

```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1642694400
```

## Pagination

All list endpoints support pagination:

```bash
curl -X GET "https://api.example.com/v1/experiments?page=2&limit=50" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

Response includes pagination metadata:

```json
{
  "data": [...],
  "pagination": {
    "page": 2,
    "limit": 50,
    "total": 150,
    "total_pages": 3,
    "has_next": true,
    "has_prev": true
  }
}
```

{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "https://example.com/schemas/experiment-config.json", "title": "A/B Testing Experiment Configuration", "description": "JSON schema for defining A/B testing experiments with Mixpanel integration", "type": "object", "required": ["name", "description", "hypothesis", "status", "variants", "metrics"], "properties": {"name": {"type": "string", "minLength": 1, "maxLength": 255, "description": "Human-readable name for the experiment", "examples": ["Homepage Hero Button Color Test", "Checkout Flow Simplification"]}, "description": {"type": "string", "minLength": 1, "maxLength": 1000, "description": "Detailed description of what the experiment tests", "examples": ["Testing different button colors on the homepage hero section to improve click-through rates"]}, "hypothesis": {"type": "string", "minLength": 1, "maxLength": 1000, "description": "The hypothesis being tested in this experiment", "examples": ["A red CTA button will increase click-through rates by 15% compared to the current blue button"]}, "startDate": {"type": "string", "format": "date-time", "description": "ISO 8601 datetime when the experiment should start", "examples": ["2024-01-15T00:00:00Z"]}, "endDate": {"type": "string", "format": "date-time", "description": "ISO 8601 datetime when the experiment should end", "examples": ["2024-02-15T23:59:59Z"]}, "status": {"type": "string", "enum": ["draft", "running", "paused", "completed"], "description": "Current status of the experiment", "default": "draft"}, "variants": {"type": "array", "minItems": 2, "maxItems": 10, "description": "List of experiment variants being tested", "items": {"type": "object", "required": ["name", "description", "allocationPercentage"], "properties": {"name": {"type": "string", "minLength": 1, "maxLength": 255, "description": "Name of the variant", "examples": ["Control (<PERSON> Button)", "<PERSON>", "<PERSON>ton"]}, "description": {"type": "string", "minLength": 1, "maxLength": 500, "description": "Description of what this variant contains", "examples": ["Original blue CTA button", "Red CTA button variant"]}, "isControl": {"type": "boolean", "description": "Whether this variant is the control group", "default": false}, "allocationPercentage": {"type": "number", "minimum": 0, "maximum": 100, "multipleOf": 0.01, "description": "Percentage of traffic allocated to this variant (0-100)", "examples": [50, 25, 33.33]}, "configuration": {"type": "object", "description": "Variant-specific configuration parameters", "additionalProperties": true, "examples": [{"buttonColor": "#007bff", "buttonText": "Get Started"}, {"checkoutSteps": 2, "flow": ["cart", "shipping_payment"]}]}}, "additionalProperties": false}}, "targetingRules": {"type": "array", "description": "Rules that determine which users are included in the experiment", "items": {"type": "object", "required": ["name", "attribute", "operator", "value"], "properties": {"name": {"type": "string", "minLength": 1, "maxLength": 255, "description": "Human-readable name for this targeting rule", "examples": ["US and Canada Only", "Desktop Users", "Premium Subscribers"]}, "attribute": {"type": "string", "minLength": 1, "maxLength": 100, "description": "User attribute to evaluate", "examples": ["country", "device_type", "subscription_tier", "user_segment"]}, "operator": {"type": "string", "enum": ["equals", "not_equals", "in", "not_in", "greater_than", "less_than", "contains", "regex"], "description": "Comparison operator for the rule"}, "value": {"oneOf": [{"type": "string"}, {"type": "number"}, {"type": "boolean"}, {"type": "array", "items": {"type": "string"}}], "description": "Value(s) to compare against the user attribute"}, "isActive": {"type": "boolean", "description": "Whether this targeting rule is currently active", "default": true}, "priority": {"type": "integer", "minimum": 0, "description": "Priority order for rule evaluation (lower numbers = higher priority)", "default": 0}}, "additionalProperties": false}}, "metrics": {"type": "object", "required": ["primary"], "properties": {"primary": {"type": "object", "required": ["name", "mixpanelEvent"], "description": "Primary metric for measuring experiment success", "properties": {"name": {"type": "string", "minLength": 1, "maxLength": 100, "description": "Human-readable name for the metric", "examples": ["Click-through Rate", "Conversion Rate", "Revenue per User"]}, "mixpanelEvent": {"type": "string", "minLength": 1, "maxLength": 100, "description": "Mixpanel event name to track for this metric", "examples": ["<PERSON><PERSON> Clicked", "Purchase Completed", "Sign Up"]}, "mixpanelProperties": {"type": "object", "description": "Additional Mixpanel event properties to filter on", "additionalProperties": true, "examples": [{"button_location": "hero"}, {"product_category": "premium"}]}, "aggregationType": {"type": "string", "enum": ["count", "unique_count", "sum", "average"], "description": "How to aggregate the metric values", "default": "count"}, "valueProperty": {"type": "string", "description": "Mixpanel property name for numeric values (required for sum/average)", "examples": ["revenue", "order_value", "session_duration"]}}, "additionalProperties": false}, "secondary": {"type": "array", "description": "Secondary metrics to track for additional insights", "items": {"type": "object", "required": ["name", "mixpanelEvent"], "properties": {"name": {"type": "string", "minLength": 1, "maxLength": 100, "description": "Human-readable name for the secondary metric"}, "mixpanelEvent": {"type": "string", "minLength": 1, "maxLength": 100, "description": "Mixpanel event name to track"}, "mixpanelProperties": {"type": "object", "description": "Additional Mixpanel event properties to filter on", "additionalProperties": true}, "aggregationType": {"type": "string", "enum": ["count", "unique_count", "sum", "average"], "default": "count"}, "valueProperty": {"type": "string", "description": "Mixpanel property name for numeric values"}}, "additionalProperties": false}}}, "additionalProperties": false}, "trafficAllocation": {"type": "number", "minimum": 0, "maximum": 1, "multipleOf": 0.0001, "description": "Percentage of total traffic to include in experiment (0.0 to 1.0)", "default": 1.0, "examples": [1.0, 0.5, 0.1]}, "assignmentMethod": {"type": "string", "enum": ["random", "sticky", "deterministic"], "description": "Method for assigning users to variants", "default": "sticky"}, "sampleSize": {"type": "integer", "minimum": 1, "description": "Target sample size for statistical significance", "examples": [1000, 5000, 10000]}, "confidenceLevel": {"type": "number", "minimum": 0.8, "maximum": 0.99, "description": "Statistical confidence level for significance testing", "default": 0.95, "examples": [0.9, 0.95, 0.99]}, "minimumDetectableEffect": {"type": "number", "minimum": 0.01, "maximum": 1.0, "description": "Minimum effect size to detect (as a decimal)", "examples": [0.05, 0.1, 0.15]}, "tags": {"type": "array", "description": "Tags for categorizing and filtering experiments", "items": {"type": "string", "minLength": 1, "maxLength": 50}, "uniqueItems": true, "examples": [["homepage", "cta", "ui"], ["checkout", "conversion", "mobile"]]}, "mixpanelConfig": {"type": "object", "description": "Mixpanel-specific configuration for tracking", "properties": {"projectId": {"type": "string", "description": "Mixpanel project ID for this experiment"}, "experimentProperty": {"type": "string", "description": "Mixpanel property name to store experiment assignment", "default": "experiment_variant", "examples": ["ab_test_variant", "experiment_group"]}, "cohortTracking": {"type": "boolean", "description": "Whether to create Mixpanel cohorts for experiment participants", "default": false}, "customProperties": {"type": "object", "description": "Additional custom properties to send with all experiment events", "additionalProperties": true, "examples": [{"experiment_id": "homepage_button_test", "experiment_version": "v1"}]}}, "additionalProperties": false}, "metadata": {"type": "object", "description": "Additional metadata for the experiment", "properties": {"owner": {"type": "string", "description": "Person or team responsible for the experiment"}, "stakeholders": {"type": "array", "items": {"type": "string"}, "description": "List of stakeholders interested in results"}, "businessImpact": {"type": "string", "description": "Expected business impact of the experiment"}, "technicalNotes": {"type": "string", "description": "Technical implementation notes"}}, "additionalProperties": true}}, "additionalProperties": false, "allOf": [{"description": "Variant allocation percentages must sum to 100", "properties": {"variants": {"type": "array", "items": {"type": "object", "properties": {"allocationPercentage": {"type": "number"}}}}}}, {"description": "End date must be after start date", "if": {"properties": {"startDate": {"type": "string"}, "endDate": {"type": "string"}}, "required": ["startDate", "endDate"]}, "then": {"properties": {"endDate": {"formatMinimum": {"$data": "1/startDate"}}}}}, {"description": "Value property required for sum/average aggregation types", "if": {"properties": {"metrics": {"type": "object", "properties": {"primary": {"type": "object", "properties": {"aggregationType": {"enum": ["sum", "average"]}}}}}}}, "then": {"properties": {"metrics": {"properties": {"primary": {"required": ["valueProperty"]}}}}}}]}
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.controls {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-bottom: 25px;
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: center;
    justify-content: space-between;
}

.search-section {
    display: flex;
    gap: 10px;
    flex: 1;
    min-width: 300px;
}

#stockSymbol {
    flex: 1;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

#stockSymbol:focus {
    outline: none;
    border-color: #667eea;
}

#searchBtn {
    padding: 12px 25px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

#searchBtn:hover {
    background: #5a6fd8;
}

.time-range-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.time-range-section label {
    font-weight: 600;
    color: #555;
}

.time-buttons {
    display: flex;
    gap: 5px;
}

.time-btn {
    padding: 8px 16px;
    border: 2px solid #e1e5e9;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.time-btn:hover {
    border-color: #667eea;
    color: #667eea;
}

.time-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.stock-info {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-bottom: 25px;
}

.stock-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

#stockName {
    font-size: 1.5rem;
    color: #333;
}

.stock-price {
    text-align: right;
}

#currentPrice {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
    display: block;
}

#priceChange {
    font-size: 1.1rem;
    font-weight: 600;
}

.positive {
    color: #10b981;
}

.negative {
    color: #ef4444;
}

.chart-container {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-bottom: 25px;
    height: 500px;
    position: relative;
}

#stockChart {
    width: 100% !important;
    height: 100% !important;
}

.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error {
    background: #fee2e2;
    color: #dc2626;
    padding: 20px;
    border-radius: 15px;
    text-align: center;
    border: 1px solid #fecaca;
}

.demo-notice {
    background: #fef3c7;
    color: #92400e;
    padding: 20px;
    border-radius: 15px;
    border: 1px solid #fde68a;
    text-align: center;
}

.demo-notice a {
    color: #92400e;
    text-decoration: underline;
}

@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-section {
        min-width: auto;
    }
    
    .time-range-section {
        justify-content: center;
    }
    
    .stock-header {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
    
    .chart-container {
        height: 400px;
        padding: 15px;
    }
}

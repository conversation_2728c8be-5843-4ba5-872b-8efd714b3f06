* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON><PERSON> UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: #1a1a2e;
    min-height: 100vh;
    color: #ffffff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.controls {
    background: #16213e;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    margin-bottom: 25px;
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: center;
    justify-content: space-between;
    border: 1px solid #2a3f5f;
}

.search-section {
    display: flex;
    gap: 10px;
    flex: 1;
    min-width: 300px;
}

#stockSymbol {
    flex: 1;
    padding: 12px 15px;
    border: 2px solid #2a3f5f;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
    background: #1a1a2e;
    color: #ffffff;
}

#stockSymbol:focus {
    outline: none;
    border-color: #8b5cf6;
}

#searchBtn {
    padding: 12px 25px;
    background: #8b5cf6;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

#searchBtn:hover {
    background: #7c3aed;
}

.time-range-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.time-range-section label {
    font-weight: 600;
    color: #ffffff;
}

.time-buttons {
    display: flex;
    gap: 5px;
}

.time-btn {
    padding: 8px 16px;
    border: 2px solid #2a3f5f;
    background: #1a1a2e;
    color: #ffffff;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.time-btn:hover {
    border-color: #8b5cf6;
    color: #8b5cf6;
}

.time-btn.active {
    background: #8b5cf6;
    color: white;
    border-color: #8b5cf6;
}

.stock-info {
    background: #16213e;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    margin-bottom: 25px;
    border: 1px solid #2a3f5f;
}

.stock-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 15px;
}

.stock-title {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.exchange {
    font-size: 0.9rem;
    color: #9ca3af;
    font-weight: 500;
}

#stockName {
    font-size: 1.5rem;
    color: #ffffff;
    margin: 0;
}

.stock-price {
    text-align: right;
}

#currentPrice {
    font-size: 2rem;
    font-weight: bold;
    color: #ffffff;
    display: block;
}

#priceChange {
    font-size: 1.1rem;
    font-weight: 600;
}

.positive {
    color: #8b5cf6;
}

.negative {
    color: #ef4444;
}

.chart-container {
    background: #16213e;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    margin-bottom: 25px;
    height: 500px;
    position: relative;
    border: 1px solid #2a3f5f;
}

#stockChart {
    width: 100% !important;
    height: 100% !important;
}

.chart-controls {
    background: #16213e;
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    margin-bottom: 25px;
    border: 1px solid #2a3f5f;
}

.toggle-section {
    display: flex;
    gap: 30px;
    align-items: center;
    flex-wrap: wrap;
}

.toggle-item {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    color: #ffffff;
    font-weight: 500;
    user-select: none;
}

.toggle-item input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #8b5cf6;
    cursor: pointer;
}

.checkmark {
    position: relative;
}

.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50px;
    background: #16213e;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    border: 1px solid #2a3f5f;
    color: #ffffff;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #2a3f5f;
    border-top: 4px solid #8b5cf6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error {
    background: #fee2e2;
    color: #dc2626;
    padding: 20px;
    border-radius: 15px;
    text-align: center;
    border: 1px solid #fecaca;
}

.demo-notice {
    background: #fef3c7;
    color: #92400e;
    padding: 20px;
    border-radius: 15px;
    border: 1px solid #fde68a;
    text-align: center;
}

.demo-notice a {
    color: #92400e;
    text-decoration: underline;
}

@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-section {
        min-width: auto;
    }
    
    .time-range-section {
        justify-content: center;
    }
    
    .stock-header {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
    
    .chart-container {
        height: 400px;
        padding: 15px;
    }
}

# A/B Testing Platform - Complete Schema & Configuration

A comprehensive solution for A/B testing platforms including PostgreSQL database schema and JSON configuration schema with Mixpanel integration.

## 🏗️ Project Overview

This project provides everything needed to build a production-ready A/B testing platform:

### Database Layer
- **PostgreSQL schema** with multi-tenant architecture
- **Advanced targeting rules** with flexible operators
- **Statistical analysis** functions and views
- **Performance optimized** with proper indexing

### Configuration Layer
- **JSON schema** for experiment configuration
- **Mixpanel integration** for event tracking
- **Validation tools** with business logic checks
- **Example configurations** for common use cases

## 📁 Project Structure

```
├── prisma/
│   └── schema.prisma              # Prisma ORM schema
├── src/
│   ├── types/
│   │   └── index.ts               # TypeScript type definitions
│   ├── database/
│   │   └── client.ts              # Database client with middleware
│   ├── repositories/
│   │   ├── base.repository.ts     # Base repository with tenant isolation
│   │   ├── experiment.repository.ts
│   │   ├── variant.repository.ts
│   │   ├── targeting-rule.repository.ts
│   │   ├── user-assignment.repository.ts
│   │   └── event.repository.ts
│   ├── services/
│   │   └── experiment.service.ts  # Business logic orchestration
│   └── index.ts                   # Main entry point
├── tests/                         # Test files
├── ab_testing_schema.sql          # PostgreSQL database schema
├── sample_data.sql                # Example database data
├── helper_functions.sql           # Utility functions and views
├── experiment_config_schema.json  # JSON schema for experiments
├── example_experiments.json       # Example experiment configurations
├── validate_experiment.js         # Validation script
├── tsconfig.json                  # TypeScript configuration
├── jest.config.js                 # Test configuration
└── README.md                      # This documentation
```

## 🚀 Quick Start

### 1. Database Setup

```sql
-- Create the database schema
\i ab_testing_schema.sql

-- Load sample data (optional)
\i sample_data.sql

-- Add helper functions
\i helper_functions.sql
```

### 2. TypeScript Data Access Layer

```bash
# Install dependencies
npm install

# Generate Prisma client
npm run db:generate

# Run database migrations
npm run db:migrate

# Build TypeScript
npm run build

# Run tests
npm test
```

### 3. Configuration Validation

```bash
# Validate an experiment configuration
node validate_experiment.js example_experiments.json

# Validate all examples
npm run validate-examples
```

## 📋 Usage Examples

### TypeScript Usage

#### Initialize the Platform

```typescript
import ABTestingPlatform from 'ab-testing-platform';

const platform = new ABTestingPlatform({
  url: process.env.DATABASE_URL!,
  logLevel: 'info',
});

await platform.initialize();
```

#### Create an Experiment

```typescript
const context = { tenantId: 'tenant-123' };

const experimentResult = await platform.experiments.createExperiment(
  {
    name: 'Button Color Test',
    description: 'Testing red vs blue button colors',
    hypothesis: 'Red buttons will increase clicks by 15%',
    status: 'DRAFT',
    primaryMetric: 'button_clicks',
  },
  [
    {
      name: 'Control (Blue)',
      description: 'Original blue button',
      isControl: true,
      trafficWeight: 0.5,
      configuration: { buttonColor: '#007bff' },
    },
    {
      name: 'Red Button',
      description: 'Red button variant',
      isControl: false,
      trafficWeight: 0.5,
      configuration: { buttonColor: '#dc3545' },
    },
  ],
  [
    {
      name: 'US Users Only',
      attributeName: 'country',
      operator: 'EQUALS',
      value: 'US',
    },
  ],
  context
);
```

#### Assign User to Experiment

```typescript
const assignment = await platform.experiments.assignUserToExperiment(
  'experiment-id',
  {
    userId: 'user-123',
    sessionId: 'session-456',
    userAttributes: {
      country: 'US',
      deviceType: 'desktop',
    },
  },
  context
);

if (!assignment.isExcluded) {
  console.log(`User assigned to variant: ${assignment.variantId}`);
}
```

#### Track Events

```typescript
await platform.experiments.trackEvent(
  {
    userId: 'user-123',
    eventName: 'button_clicks',
    eventValue: 1,
    eventProperties: {
      buttonColor: 'red',
      page: 'homepage',
    },
  },
  context
);
```

### Database Operations

#### Raw SQL Operations

```sql
-- Insert a new tenant
INSERT INTO tenants (name, slug)
VALUES ('My Company', 'my-company');

-- Create an experiment
INSERT INTO experiments (tenant_id, name, description, status, primary_metric)
VALUES (
    (SELECT id FROM tenants WHERE slug = 'my-company'),
    'Button Color Test',
    'Testing red vs blue button colors',
    'ACTIVE',
    'button_clicks'
);
```

### JSON Configuration

#### Basic Experiment Configuration

```json
{
  "name": "Homepage Button Test",
  "description": "Testing button colors for conversion optimization",
  "hypothesis": "Red buttons will increase clicks by 15%",
  "status": "running",
  "variants": [
    {
      "name": "Control (Blue)",
      "description": "Original blue button",
      "isControl": true,
      "allocationPercentage": 50,
      "configuration": {"buttonColor": "#007bff"}
    },
    {
      "name": "Red Button",
      "description": "Red button variant",
      "isControl": false,
      "allocationPercentage": 50,
      "configuration": {"buttonColor": "#dc3545"}
    }
  ],
  "metrics": {
    "primary": {
      "name": "Click Rate",
      "mixpanelEvent": "Button Clicked",
      "aggregationType": "unique_count"
    }
  }
}
```

## 🏗️ Architecture Features

### TypeScript Data Access Layer

- **Repository Pattern** - Clean separation of data access logic
- **Tenant Isolation** - Multi-tenant support with automatic filtering
- **Transaction Support** - ACID transactions for complex operations
- **Error Handling** - Comprehensive error types and handling
- **Type Safety** - Full TypeScript support with Prisma client
- **Performance** - Optimized queries with proper indexing
- **Testing** - Unit tests with Jest and test database setup

### Core Configuration Elements

- **Experiment Metadata** - Name, description, hypothesis, dates, status
- **Variants** - Multiple test variations with allocation percentages
- **Targeting Rules** - Flexible user targeting with various operators
- **Metrics Integration** - Primary and secondary metrics with Mixpanel events
- **Statistical Configuration** - Sample size, confidence levels, effect sizes

### Mixpanel Integration

```json
{
  "metrics": {
    "primary": {
      "name": "Conversion Rate",
      "mixpanelEvent": "Purchase Completed",
      "mixpanelProperties": {"product_type": "premium"},
      "aggregationType": "unique_count"
    }
  },
  "mixpanelConfig": {
    "projectId": "abc123",
    "experimentProperty": "experiment_variant",
    "cohortTracking": true,
    "customProperties": {
      "experiment_id": "checkout_test_2024"
    }
  }
}
```

### Validation and Business Logic

The validation script checks for:
- ✅ **Schema compliance** - All required fields and correct types
- ✅ **Allocation totals** - Variant percentages sum to 100%
- ✅ **Date logic** - End date after start date
- ✅ **Control variants** - Exactly one control variant
- ✅ **Metric configuration** - Proper aggregation type setup
- ⚠️ **Statistical warnings** - Sample size recommendations

## 📈 Analytics and Reporting

### Database Analytics

```sql
-- Get conversion rates for all experiments
SELECT * FROM conversion_rates
WHERE tenant_id = (SELECT id FROM tenants WHERE slug = 'my-company');

-- Calculate statistical significance
SELECT * FROM calculate_statistical_significance(
    125,  -- control conversions
    1000, -- control sample size
    158,  -- variant conversions
    1000, -- variant sample size
    0.95  -- confidence level
);
```

## 🔧 Advanced Features

### Multi-Tenant Isolation

Every table includes a `tenant_id` column with proper foreign key constraints to ensure complete data isolation between tenants.

### Flexible Targeting

The targeting rules system supports multiple operators:
- `equals`, `not_equals` - Exact matching
- `in`, `not_in` - List membership
- `greater_than`, `less_than` - Numeric comparisons
- `contains` - String pattern matching
- `regex` - Regular expression matching

### Assignment Methods

- **Random** - Pure random assignment
- **Sticky** - Consistent assignment based on user ID
- **Deterministic** - Hash-based assignment for consistency

### Event Tracking

Comprehensive event tracking with:
- Automatic experiment context association
- Flexible event properties via JSONB
- Support for both conversion events and general analytics

## 🔍 Indexes and Performance

The schema includes comprehensive indexing for optimal performance:

- **Tenant isolation** - All queries can efficiently filter by tenant
- **Time-based queries** - Optimized for date range filtering
- **User lookups** - Fast user assignment and event retrieval
- **Analytics queries** - Efficient aggregation and reporting

## 🛡️ Data Integrity

- **Foreign key constraints** prevent orphaned records
- **Check constraints** validate data ranges and formats
- **Unique constraints** prevent duplicate assignments
- **NOT NULL constraints** ensure required fields are populated

## 📝 Schema Maintenance

### Automatic Timestamps

All tables with `updated_at` columns have triggers that automatically update the timestamp on record modification.

### Data Cleanup

Consider implementing regular cleanup jobs for:
- Completed experiments older than retention period
- Anonymous event data beyond legal requirements
- Archived experiment data

## 🔄 Migration Considerations

When deploying this schema:

1. **Create extensions first** - Ensure `uuid-ossp` extension is available
2. **Run in transaction** - Use transactions for atomic schema deployment
3. **Test with sample data** - Validate functionality before production use
4. **Monitor performance** - Adjust indexes based on actual query patterns

## 📚 Additional Resources

- [PostgreSQL UUID Documentation](https://www.postgresql.org/docs/current/datatype-uuid.html)
- [JSONB Best Practices](https://www.postgresql.org/docs/current/datatype-json.html)
- [Statistical Significance in A/B Testing](https://en.wikipedia.org/wiki/Statistical_significance)

## 🤝 Contributing

This schema is designed to be extensible. Common extensions include:

- **Segment integration** - Add user segment tables
- **Feature flags** - Extend for feature flag management
- **Advanced analytics** - Add cohort analysis tables
- **Audit logging** - Track all schema changes

## 📄 License

This schema is provided as-is for educational and commercial use. Adapt as needed for your specific requirements.

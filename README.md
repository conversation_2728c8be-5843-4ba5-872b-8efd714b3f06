# Stock Graph - Real-time Stock Market Visualization

A modern, interactive stock graph application that displays real-time stock market data with beautiful charts and an intuitive user interface.

## Features

- 📈 **Interactive Stock Charts** - Smooth, responsive charts with hover effects
- 🔍 **Stock Symbol Search** - Search for any stock by symbol (AAPL, GOOGL, MSFT, etc.)
- ⏰ **Multiple Time Ranges** - View data for 1D, 1W, 1M, 3M, and 1Y periods
- 📱 **Responsive Design** - Works perfectly on desktop, tablet, and mobile devices
- 🎨 **Modern UI** - Clean, professional interface with smooth animations
- 💰 **Real-time Price Updates** - Current price and change indicators
- 🎯 **Demo Mode** - Works out of the box with simulated data

## Quick Start

1. **Clone or download** this repository
2. **Open `index.html`** in your web browser
3. **Start exploring** - The app loads with Apple (AAPL) stock data by default

That's it! No installation or setup required.

## How to Use

### Search for Stocks
- Enter any stock symbol in the search box (e.g., AAPL, GOOGL, MSFT, AMZN, TSLA)
- Click "Search" or press Enter
- The chart will update with the new stock data

### Change Time Ranges
- Click any of the time range buttons: 1D, 1W, 1M, 3M, 1Y
- The chart will automatically update to show data for the selected period

### Interactive Chart
- Hover over the chart to see detailed price information
- The chart automatically adjusts colors based on stock performance (green for gains, red for losses)

## Demo vs Real Data

### Demo Mode (Default)
The application runs in demo mode by default, generating realistic simulated stock data. This allows you to:
- Test all features without API setup
- See how the application works with different stocks
- Experience the full functionality immediately

### Real Data Mode
To use real stock market data:

1. **Get a free API key** from [Alpha Vantage](https://www.alphavantage.co/support/#api-key)
2. **Edit `script.js`**:
   ```javascript
   this.apiKey = 'YOUR_API_KEY_HERE'; // Replace 'demo' with your actual API key
   this.isDemo = false; // Change to false
   ```
3. **Save and refresh** the page

## Supported Stock Symbols

The demo mode includes data for popular stocks:
- **AAPL** - Apple Inc.
- **GOOGL** - Alphabet Inc.
- **MSFT** - Microsoft Corporation
- **AMZN** - Amazon.com Inc.
- **TSLA** - Tesla Inc.
- **META** - Meta Platforms Inc.
- **NVDA** - NVIDIA Corporation
- **NFLX** - Netflix Inc.

You can enter any symbol - the app will generate appropriate demo data.

## Technical Details

### Built With
- **HTML5** - Semantic markup
- **CSS3** - Modern styling with flexbox and grid
- **Vanilla JavaScript** - No frameworks, pure JS
- **Chart.js** - Professional charting library
- **Alpha Vantage API** - Real stock market data (when enabled)

### Browser Compatibility
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### File Structure
```
stock-graph/
├── index.html          # Main HTML file
├── style.css           # Styling and responsive design
├── script.js           # Application logic and chart rendering
└── README.md           # This file
```

## Customization

### Colors and Styling
Edit `style.css` to customize:
- Color scheme (currently blue gradient theme)
- Chart colors and styling
- Layout and spacing
- Responsive breakpoints

### Chart Configuration
Edit `script.js` to modify:
- Chart appearance and behavior
- Data generation logic
- API integration
- Time range options

## API Rate Limits

When using real data with Alpha Vantage:
- **Free tier**: 5 API requests per minute, 500 per day
- **Premium tiers**: Higher limits available

The demo mode has no rate limits.

## Troubleshooting

### Chart Not Loading
- Check browser console for errors
- Ensure internet connection for Chart.js CDN
- Verify API key if using real data mode

### Styling Issues
- Clear browser cache
- Check if CSS file is loading properly
- Verify responsive design on different screen sizes

## License

This project is open source and available under the MIT License.

## Contributing

Feel free to submit issues, fork the repository, and create pull requests for any improvements.

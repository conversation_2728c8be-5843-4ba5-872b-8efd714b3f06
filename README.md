# Experiment Management API - Authorization Middleware

A comprehensive experiment management system with enterprise-grade authorization middleware for A/B testing platforms. Features role-based access control, ownership verification, multi-tenant isolation, and comprehensive audit logging.

## 🚀 Quick Demo

**The authorization middleware is currently running at http://localhost:3003**

Try these commands to see the authorization system in action:

```bash
# ✅ <PERSON><PERSON> can list experiments
curl -H "Authorization: Bearer admin_token" http://localhost:3003/api/experiments

# ❌ Viewer cannot access user management
curl -H "Authorization: Bearer viewer_token" http://localhost:3003/api/users

# ❌ Cross-tenant access denied
curl -H "Authorization: Bearer other_tenant_token" http://localhost:3003/api/experiments

# ✅ Create experiment with proper permissions
curl -X POST -H "Authorization: Bearer experimenter_token" \
     -H "Content-Type: application/json" \
     -d '{"name":"Demo Test","description":"Testing authorization"}' \
     http://localhost:3003/api/experiments
```

## 📚 Documentation

### Core Documentation
- **[API Documentation](docs/API_DOCUMENTATION.md)** - Comprehensive API reference with examples
- **[Developer Guide](docs/DEVELOPER_GUIDE.md)** - Architecture, data model, and extension points
- **[Authentication Guide](docs/AUTHENTICATION_GUIDE.md)** - Security model and authorization patterns
- **[Testing Examples](docs/TESTING_EXAMPLES.md)** - Test scenarios and automation scripts

### Quick Links
- [Health Check](http://localhost:3003/health) - Server status
- [Setup Guide](README-SETUP.md) - Local installation instructions

## 🛡️ Key Features

### Multi-Layer Authorization
- **Role-Based Access Control (RBAC)**: Hierarchical permission system
- **Ownership Verification**: Resource-level access control
- **Multi-Tenant Isolation**: Complete data separation between tenants
- **Custom Permission Logic**: Extensible authorization rules

### Security & Monitoring
- **Comprehensive Audit Logging**: Every authorization attempt tracked
- **Security Event Detection**: Automatic threat detection and alerting
- **Rate Limiting**: Protection against abuse
- **Real-time Analytics**: Authorization metrics and insights

### Developer Experience
- **Clear Error Messages**: Detailed error responses with context
- **TypeScript Support**: Full type safety and IntelliSense
- **Extensive Testing**: Unit, integration, and E2E test suites
- **Comprehensive Documentation**: API docs, guides, and examples

## 📁 Project Structure

```
├── prisma/
│   └── schema.prisma              # Prisma ORM schema
├── src/
│   ├── types/
│   │   └── index.ts               # TypeScript type definitions
│   ├── database/
│   │   └── client.ts              # Database client with middleware
│   ├── repositories/
│   │   ├── base.repository.ts     # Base repository with tenant isolation
│   │   ├── experiment.repository.ts
│   │   ├── variant.repository.ts
│   │   ├── targeting-rule.repository.ts
│   │   ├── user-assignment.repository.ts
│   │   └── event.repository.ts
│   ├── services/
│   │   └── experiment.service.ts  # Business logic orchestration
│   └── index.ts                   # Main entry point
├── tests/                         # Test files
├── openapi.yaml                   # OpenAPI 3.0 API specification
├── api-examples.md                # API usage examples
├── api-integration-guide.md       # Integration patterns and best practices
├── API_README.md                  # API documentation overview
├── ab_testing_schema.sql          # PostgreSQL database schema
├── sample_data.sql                # Example database data
├── helper_functions.sql           # Utility functions and views
├── experiment_config_schema.json  # JSON schema for experiments
├── example_experiments.json       # Example experiment configurations
├── validate_experiment.js         # Validation script
├── tsconfig.json                  # TypeScript configuration
├── jest.config.js                 # Test configuration
└── README.md                      # This documentation
```

## 🚀 Quick Start

### 1. Database Setup

```sql
-- Create the database schema
\i ab_testing_schema.sql

-- Load sample data (optional)
\i sample_data.sql

-- Add helper functions
\i helper_functions.sql
```

### 2. TypeScript Data Access Layer

```bash
# Install dependencies
npm install

# Generate Prisma client
npm run db:generate

# Run database migrations
npm run db:migrate

# Build TypeScript
npm run build

# Run tests
npm test
```

### 3. RESTful API

The platform includes a comprehensive RESTful API for experiment management:

```bash
# View the OpenAPI specification
cat openapi.yaml

# Start API server (implementation required)
npm run start:api

# View API documentation
open API_README.md
```

**API Features:**
- Complete experiment lifecycle management (CRUD + start/pause/complete/archive)
- Real-time user assignment with targeting rules
- Event tracking and analytics
- Multi-tenant support with proper isolation
- Comprehensive error handling and validation
- Rate limiting and pagination
- OpenAPI 3.0 specification with examples

### 4. Configuration Validation

```bash
# Validate an experiment configuration
node validate_experiment.js example_experiments.json

# Validate all examples
npm run validate-examples
```

## 📋 Usage Examples

### TypeScript Usage

#### Initialize the Platform

```typescript
import ABTestingPlatform from 'ab-testing-platform';

const platform = new ABTestingPlatform({
  url: process.env.DATABASE_URL!,
  logLevel: 'info',
});

await platform.initialize();
```

#### Create an Experiment

```typescript
const context = { tenantId: 'tenant-123' };

const experimentResult = await platform.experiments.createExperiment(
  {
    name: 'Button Color Test',
    description: 'Testing red vs blue button colors',
    hypothesis: 'Red buttons will increase clicks by 15%',
    status: 'DRAFT',
    primaryMetric: 'button_clicks',
  },
  [
    {
      name: 'Control (Blue)',
      description: 'Original blue button',
      isControl: true,
      trafficWeight: 0.5,
      configuration: { buttonColor: '#007bff' },
    },
    {
      name: 'Red Button',
      description: 'Red button variant',
      isControl: false,
      trafficWeight: 0.5,
      configuration: { buttonColor: '#dc3545' },
    },
  ],
  [
    {
      name: 'US Users Only',
      attributeName: 'country',
      operator: 'EQUALS',
      value: 'US',
    },
  ],
  context
);
```

#### Assign User to Experiment

```typescript
const assignment = await platform.experiments.assignUserToExperiment(
  'experiment-id',
  {
    userId: 'user-123',
    sessionId: 'session-456',
    userAttributes: {
      country: 'US',
      deviceType: 'desktop',
    },
  },
  context
);

if (!assignment.isExcluded) {
  console.log(`User assigned to variant: ${assignment.variantId}`);
}
```

#### Track Events

```typescript
await platform.experiments.trackEvent(
  {
    userId: 'user-123',
    eventName: 'button_clicks',
    eventValue: 1,
    eventProperties: {
      buttonColor: 'red',
      page: 'homepage',
    },
  },
  context
);
```

### Database Operations

#### Raw SQL Operations

```sql
-- Insert a new tenant
INSERT INTO tenants (name, slug)
VALUES ('My Company', 'my-company');

-- Create an experiment
INSERT INTO experiments (tenant_id, name, description, status, primary_metric)
VALUES (
    (SELECT id FROM tenants WHERE slug = 'my-company'),
    'Button Color Test',
    'Testing red vs blue button colors',
    'ACTIVE',
    'button_clicks'
);
```

### JSON Configuration

#### Basic Experiment Configuration

```json
{
  "name": "Homepage Button Test",
  "description": "Testing button colors for conversion optimization",
  "hypothesis": "Red buttons will increase clicks by 15%",
  "status": "running",
  "variants": [
    {
      "name": "Control (Blue)",
      "description": "Original blue button",
      "isControl": true,
      "allocationPercentage": 50,
      "configuration": {"buttonColor": "#007bff"}
    },
    {
      "name": "Red Button",
      "description": "Red button variant",
      "isControl": false,
      "allocationPercentage": 50,
      "configuration": {"buttonColor": "#dc3545"}
    }
  ],
  "metrics": {
    "primary": {
      "name": "Click Rate",
      "mixpanelEvent": "Button Clicked",
      "aggregationType": "unique_count"
    }
  }
}
```

## 🏗️ Architecture Features

### RESTful API Layer

- **OpenAPI 3.0 Specification** - Complete API documentation with examples
- **Multi-Tenant Architecture** - Secure tenant isolation with JWT authentication
- **Real-Time Experiment Control** - Start, pause, resume, complete, and archive experiments
- **Intelligent User Assignment** - Hash-based consistent bucketing with targeting rules
- **Event Tracking & Analytics** - Comprehensive conversion tracking with statistical analysis
- **Rate Limiting** - Configurable limits for different endpoint categories
- **Error Handling** - Detailed error responses with field-level validation
- **Pagination & Filtering** - Advanced filtering and sorting capabilities

### TypeScript Data Access Layer

- **Repository Pattern** - Clean separation of data access logic
- **Tenant Isolation** - Multi-tenant support with automatic filtering
- **Transaction Support** - ACID transactions for complex operations
- **Error Handling** - Comprehensive error types and handling
- **Type Safety** - Full TypeScript support with Prisma client
- **Performance** - Optimized queries with proper indexing
- **Testing** - Unit tests with Jest and test database setup

### Core Configuration Elements

- **Experiment Metadata** - Name, description, hypothesis, dates, status
- **Variants** - Multiple test variations with allocation percentages
- **Targeting Rules** - Flexible user targeting with various operators
- **Metrics Integration** - Primary and secondary metrics with Mixpanel events
- **Statistical Configuration** - Sample size, confidence levels, effect sizes

### Mixpanel Integration

```json
{
  "metrics": {
    "primary": {
      "name": "Conversion Rate",
      "mixpanelEvent": "Purchase Completed",
      "mixpanelProperties": {"product_type": "premium"},
      "aggregationType": "unique_count"
    }
  },
  "mixpanelConfig": {
    "projectId": "abc123",
    "experimentProperty": "experiment_variant",
    "cohortTracking": true,
    "customProperties": {
      "experiment_id": "checkout_test_2024"
    }
  }
}
```

### Validation and Business Logic

The validation script checks for:
- ✅ **Schema compliance** - All required fields and correct types
- ✅ **Allocation totals** - Variant percentages sum to 100%
- ✅ **Date logic** - End date after start date
- ✅ **Control variants** - Exactly one control variant
- ✅ **Metric configuration** - Proper aggregation type setup
- ⚠️ **Statistical warnings** - Sample size recommendations

## 📈 Analytics and Reporting

### Database Analytics

```sql
-- Get conversion rates for all experiments
SELECT * FROM conversion_rates
WHERE tenant_id = (SELECT id FROM tenants WHERE slug = 'my-company');

-- Calculate statistical significance
SELECT * FROM calculate_statistical_significance(
    125,  -- control conversions
    1000, -- control sample size
    158,  -- variant conversions
    1000, -- variant sample size
    0.95  -- confidence level
);
```

## 🔧 Advanced Features

### Multi-Tenant Isolation

Every table includes a `tenant_id` column with proper foreign key constraints to ensure complete data isolation between tenants.

### Flexible Targeting

The targeting rules system supports multiple operators:
- `equals`, `not_equals` - Exact matching
- `in`, `not_in` - List membership
- `greater_than`, `less_than` - Numeric comparisons
- `contains` - String pattern matching
- `regex` - Regular expression matching

### Assignment Methods

- **Random** - Pure random assignment
- **Sticky** - Consistent assignment based on user ID
- **Deterministic** - Hash-based assignment for consistency

### Event Tracking

Comprehensive event tracking with:
- Automatic experiment context association
- Flexible event properties via JSONB
- Support for both conversion events and general analytics

## 🔍 Indexes and Performance

The schema includes comprehensive indexing for optimal performance:

- **Tenant isolation** - All queries can efficiently filter by tenant
- **Time-based queries** - Optimized for date range filtering
- **User lookups** - Fast user assignment and event retrieval
- **Analytics queries** - Efficient aggregation and reporting

## 🛡️ Data Integrity

- **Foreign key constraints** prevent orphaned records
- **Check constraints** validate data ranges and formats
- **Unique constraints** prevent duplicate assignments
- **NOT NULL constraints** ensure required fields are populated

## 📝 Schema Maintenance

### Automatic Timestamps

All tables with `updated_at` columns have triggers that automatically update the timestamp on record modification.

### Data Cleanup

Consider implementing regular cleanup jobs for:
- Completed experiments older than retention period
- Anonymous event data beyond legal requirements
- Archived experiment data

## 🔄 Migration Considerations

When deploying this schema:

1. **Create extensions first** - Ensure `uuid-ossp` extension is available
2. **Run in transaction** - Use transactions for atomic schema deployment
3. **Test with sample data** - Validate functionality before production use
4. **Monitor performance** - Adjust indexes based on actual query patterns

## 📚 Additional Resources

- [PostgreSQL UUID Documentation](https://www.postgresql.org/docs/current/datatype-uuid.html)
- [JSONB Best Practices](https://www.postgresql.org/docs/current/datatype-json.html)
- [Statistical Significance in A/B Testing](https://en.wikipedia.org/wiki/Statistical_significance)

## 🤝 Contributing

This schema is designed to be extensible. Common extensions include:

- **Segment integration** - Add user segment tables
- **Feature flags** - Extend for feature flag management
- **Advanced analytics** - Add cohort analysis tables
- **Audit logging** - Track all schema changes

## 📄 License

This schema is provided as-is for educational and commercial use. Adapt as needed for your specific requirements.

# A/B Testing Platform - PostgreSQL Schema

A comprehensive PostgreSQL database schema for a multi-tenant A/B testing platform with advanced features for experiment management, user targeting, and statistical analysis.

## 🏗️ Schema Overview

This schema provides a complete foundation for building an A/B testing platform with the following capabilities:

- **Multi-tenant architecture** with proper data isolation
- **Flexible experiment configuration** with multiple variants
- **Advanced targeting rules** with various operators and data types
- **User assignment tracking** with bucketing and exclusion logic
- **Event tracking** for conversion measurement
- **Statistical analysis** with confidence intervals and significance testing

## 📊 Database Tables

### Core Tables

1. **`tenants`** - Multi-tenant isolation and configuration
2. **`experiments`** - A/B test definitions and metadata
3. **`variants`** - Different versions being tested in experiments
4. **`targeting_rules`** - Conditions for user inclusion/exclusion
5. **`user_assignments`** - Tracks which users see which variants
6. **`events`** - User interactions and conversion tracking
7. **`experiment_results`** - Statistical analysis results

### Key Features

- **UUID primary keys** for better security and distribution
- **Comprehensive indexing** for optimal query performance
- **Foreign key constraints** ensuring data integrity
- **Automatic timestamps** with triggers for `updated_at` fields
- **JSONB columns** for flexible metadata and configuration storage
- **Custom ENUM types** for better data validation

## 🚀 Quick Start

### 1. Create the Database Schema

```sql
-- Run the main schema file
\i ab_testing_schema.sql
```

### 2. Load Sample Data (Optional)

```sql
-- Load example data to test the schema
\i sample_data.sql
```

### 3. Add Helper Functions

```sql
-- Add utility functions and views
\i helper_functions.sql
```

## 📋 Usage Examples

### Creating a New Experiment

```sql
-- Insert a new tenant
INSERT INTO tenants (name, slug) 
VALUES ('My Company', 'my-company');

-- Create an experiment
INSERT INTO experiments (tenant_id, name, description, status, primary_metric)
VALUES (
    (SELECT id FROM tenants WHERE slug = 'my-company'),
    'Button Color Test',
    'Testing red vs blue button colors',
    'active',
    'button_clicks'
);

-- Add variants
INSERT INTO variants (tenant_id, experiment_id, name, is_control, traffic_weight)
VALUES 
    ((SELECT id FROM tenants WHERE slug = 'my-company'),
     (SELECT id FROM experiments WHERE name = 'Button Color Test'),
     'Blue Button (Control)', true, 0.5),
    ((SELECT id FROM tenants WHERE slug = 'my-company'),
     (SELECT id FROM experiments WHERE name = 'Button Color Test'),
     'Red Button', false, 0.5);
```

### Adding Targeting Rules

```sql
-- Target only US users
INSERT INTO targeting_rules (tenant_id, experiment_id, name, attribute_name, operator, value_text)
VALUES (
    (SELECT id FROM tenants WHERE slug = 'my-company'),
    (SELECT id FROM experiments WHERE name = 'Button Color Test'),
    'US Users Only',
    'country',
    'equals',
    'US'
);
```

### Assigning Users to Experiments

```sql
-- Use the helper function to assign a user
SELECT assign_user_to_experiment(
    (SELECT id FROM tenants WHERE slug = 'my-company'),
    (SELECT id FROM experiments WHERE name = 'Button Color Test'),
    'user_12345',
    '{"country": "US", "device": "desktop"}'::jsonb
);
```

### Tracking Events

```sql
-- Track a conversion event
SELECT track_event(
    (SELECT id FROM tenants WHERE slug = 'my-company'),
    'user_12345',
    'button_clicks',
    1,
    '{"button_color": "red", "page": "homepage"}'::jsonb
);
```

## 📈 Analytics and Reporting

### View Experiment Performance

```sql
-- Get conversion rates for all experiments
SELECT * FROM conversion_rates 
WHERE tenant_id = (SELECT id FROM tenants WHERE slug = 'my-company');
```

### Calculate Statistical Significance

```sql
-- Compare control vs variant
SELECT * FROM calculate_statistical_significance(
    125,  -- control conversions
    1000, -- control sample size
    158,  -- variant conversions
    1000, -- variant sample size
    0.95  -- confidence level
);
```

### Active Experiments Summary

```sql
-- View all active experiments
SELECT * FROM active_experiments_summary
WHERE tenant_name = 'My Company';
```

## 🔧 Advanced Features

### Multi-Tenant Isolation

Every table includes a `tenant_id` column with proper foreign key constraints to ensure complete data isolation between tenants.

### Flexible Targeting

The targeting rules system supports multiple operators:
- `equals`, `not_equals` - Exact matching
- `in`, `not_in` - List membership
- `greater_than`, `less_than` - Numeric comparisons
- `contains` - String pattern matching
- `regex` - Regular expression matching

### Assignment Methods

- **Random** - Pure random assignment
- **Sticky** - Consistent assignment based on user ID
- **Deterministic** - Hash-based assignment for consistency

### Event Tracking

Comprehensive event tracking with:
- Automatic experiment context association
- Flexible event properties via JSONB
- Support for both conversion events and general analytics

## 🔍 Indexes and Performance

The schema includes comprehensive indexing for optimal performance:

- **Tenant isolation** - All queries can efficiently filter by tenant
- **Time-based queries** - Optimized for date range filtering
- **User lookups** - Fast user assignment and event retrieval
- **Analytics queries** - Efficient aggregation and reporting

## 🛡️ Data Integrity

- **Foreign key constraints** prevent orphaned records
- **Check constraints** validate data ranges and formats
- **Unique constraints** prevent duplicate assignments
- **NOT NULL constraints** ensure required fields are populated

## 📝 Schema Maintenance

### Automatic Timestamps

All tables with `updated_at` columns have triggers that automatically update the timestamp on record modification.

### Data Cleanup

Consider implementing regular cleanup jobs for:
- Completed experiments older than retention period
- Anonymous event data beyond legal requirements
- Archived experiment data

## 🔄 Migration Considerations

When deploying this schema:

1. **Create extensions first** - Ensure `uuid-ossp` extension is available
2. **Run in transaction** - Use transactions for atomic schema deployment
3. **Test with sample data** - Validate functionality before production use
4. **Monitor performance** - Adjust indexes based on actual query patterns

## 📚 Additional Resources

- [PostgreSQL UUID Documentation](https://www.postgresql.org/docs/current/datatype-uuid.html)
- [JSONB Best Practices](https://www.postgresql.org/docs/current/datatype-json.html)
- [Statistical Significance in A/B Testing](https://en.wikipedia.org/wiki/Statistical_significance)

## 🤝 Contributing

This schema is designed to be extensible. Common extensions include:

- **Segment integration** - Add user segment tables
- **Feature flags** - Extend for feature flag management
- **Advanced analytics** - Add cohort analysis tables
- **Audit logging** - Track all schema changes

## 📄 License

This schema is provided as-is for educational and commercial use. Adapt as needed for your specific requirements.

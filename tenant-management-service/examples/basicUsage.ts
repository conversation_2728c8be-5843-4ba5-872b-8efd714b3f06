import { Pool } from 'pg';
import {
  createTenantManagementService,
  TenantTier,
  TenantStatus,
  TenantProvisioningRequest
} from '../src/index';

// Database connection
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432'),
  database: process.env.DB_NAME || 'tenant_management',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'password'
});

async function main() {
  // Create tenant management service
  const tenantService = createTenantManagementService({
    database: pool,
    config: {
      tenantManagement: {
        defaultTier: TenantTier.FREE,
        autoActivate: true,
        enableTrials: true,
        trialDurationDays: 14
      },
      mixpanel: {
        apiBaseUrl: 'https://mixpanel.com/api',
        serviceAccountUsername: process.env.MIXPANEL_USERNAME!,
        serviceAccountPassword: process.env.MIXPANEL_PASSWORD!,
        defaultTimezone: 'UTC',
        defaultRetentionDays: 365,
        organizationId: process.env.MIXPANEL_ORG_ID!
      },
      billing: {
        stripe: {
          secretKey: process.env.STRIPE_SECRET_KEY!,
          webhookSecret: process.env.STRIPE_WEBHOOK_SECRET!,
          defaultCurrency: 'USD'
        },
        plans: {
          [TenantTier.FREE]: {
            priceId: '',
            monthlyPrice: 0,
            yearlyPrice: 0,
            features: []
          },
          [TenantTier.STARTER]: {
            priceId: 'price_starter_monthly',
            monthlyPrice: 29,
            yearlyPrice: 290,
            features: ['analytics', 'api_access']
          },
          [TenantTier.PRO]: {
            priceId: 'price_pro_monthly',
            monthlyPrice: 99,
            yearlyPrice: 990,
            features: ['analytics', 'api_access', 'custom_domain']
          },
          [TenantTier.ENTERPRISE]: {
            priceId: 'price_enterprise_monthly',
            monthlyPrice: 299,
            yearlyPrice: 2990,
            features: ['all']
          },
          [TenantTier.CUSTOM]: {
            priceId: '',
            monthlyPrice: 0,
            yearlyPrice: 0,
            features: ['all']
          }
        },
        trialDays: 14
      }
    }
  });

  try {
    console.log('🚀 Starting tenant management demo...\n');

    // 1. Create a new tenant
    console.log('1. Creating a new tenant...');
    const tenantRequest: TenantProvisioningRequest = {
      name: 'Acme Corporation',
      slug: 'acme-corp',
      displayName: 'Acme Corp',
      description: 'A sample corporation for testing',
      domain: 'acme.example.com',
      tier: TenantTier.PRO,
      owner: {
        userId: 'user_123',
        email: '<EMAIL>',
        name: 'John Doe'
      },
      billing: {
        plan: 'pro',
        billingCycle: 'monthly',
        currency: 'USD',
        isTrialActive: true
      },
      integrations: {
        mixpanel: {
          enabled: true,
          projectToken: '', // Will be generated
          dataResidency: 'us',
          retentionDays: 365,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      },
      settings: {
        timezone: 'America/New_York',
        locale: 'en-US',
        dateFormat: 'MM/DD/YYYY',
        currency: 'USD',
        theme: 'light',
        notifications: {
          email: true,
          slack: false,
          webhook: false,
          inApp: true,
          frequency: 'immediate',
          types: ['system', 'billing']
        },
        security: {
          passwordPolicy: {
            minLength: 8,
            requireUppercase: true,
            requireLowercase: true,
            requireNumbers: true,
            requireSymbols: false,
            maxAge: 90,
            preventReuse: 5
          },
          sessionTimeout: 3600,
          mfaRequired: false,
          auditLogging: true,
          dataEncryption: true
        },
        privacy: {
          dataRetentionDays: 365,
          anonymizeData: false,
          cookieConsent: true,
          gdprCompliant: true,
          ccpaCompliant: true
        },
        customization: {
          primaryColor: '#007bff',
          brandName: 'Acme Corp'
        }
      },
      metadata: {
        source: 'api',
        campaign: 'demo'
      }
    };

    const newTenant = await tenantService.createTenant(tenantRequest);
    console.log(`✅ Tenant created: ${newTenant.id} (${newTenant.slug})\n`);

    // 2. Get tenant information
    console.log('2. Retrieving tenant information...');
    const tenant = await tenantService.getTenant(newTenant.id);
    if (tenant) {
      console.log(`✅ Tenant found: ${tenant.displayName}`);
      console.log(`   Status: ${tenant.status}`);
      console.log(`   Tier: ${tenant.tier}`);
      console.log(`   Features: ${Object.entries(tenant.features).filter(([_, enabled]) => enabled).map(([feature]) => feature).join(', ')}\n`);
    }

    // 3. Update tenant settings
    console.log('3. Updating tenant settings...');
    const updatedTenant = await tenantService.updateTenant(newTenant.id, {
      displayName: 'Acme Corporation Ltd',
      settings: {
        ...tenant!.settings,
        theme: 'dark',
        customization: {
          ...tenant!.settings.customization,
          primaryColor: '#28a745'
        }
      }
    });
    console.log(`✅ Tenant updated: ${updatedTenant.displayName}\n`);

    // 4. Enable a feature
    console.log('4. Enabling SSO feature...');
    const tenantWithSSO = await tenantService.enableTenantFeature(newTenant.id, 'sso');
    console.log(`✅ SSO enabled: ${tenantWithSSO.features.sso}\n`);

    // 5. Check resource usage
    console.log('5. Checking resource usage...');
    const resourceUsage = await tenantService.getTenantResourceUsage(newTenant.id);
    console.log(`✅ Resource usage retrieved:`);
    console.log(`   Storage: ${resourceUsage.resources.storage} MB`);
    console.log(`   API Calls: ${resourceUsage.resources.apiCalls}`);
    console.log(`   Users: ${resourceUsage.resources.users}\n`);

    // 6. List all tenants
    console.log('6. Listing all tenants...');
    const tenantList = await tenantService.listTenants({
      status: [TenantStatus.ACTIVE],
      page: 1,
      limit: 10
    });
    console.log(`✅ Found ${tenantList.total} active tenants:`);
    tenantList.tenants.forEach(t => {
      console.log(`   - ${t.displayName} (${t.slug}) - ${t.tier}`);
    });
    console.log();

    // 7. Suspend tenant
    console.log('7. Suspending tenant...');
    const suspendedTenant = await tenantService.suspendTenant(newTenant.id, 'Demo suspension');
    console.log(`✅ Tenant suspended: ${suspendedTenant.status}\n`);

    // 8. Activate tenant
    console.log('8. Reactivating tenant...');
    const activatedTenant = await tenantService.activateTenant(newTenant.id);
    console.log(`✅ Tenant reactivated: ${activatedTenant.status}\n`);

    // 9. Demonstrate event handling
    console.log('9. Setting up event listeners...');
    tenantService.on('tenantEvent', (event) => {
      console.log(`📧 Event received: ${event.type} for tenant ${event.tenantId}`);
    });

    // Trigger an event by updating the tenant
    await tenantService.updateTenant(newTenant.id, {
      metadata: {
        ...tenant!.metadata,
        lastDemo: new Date().toISOString()
      }
    });

    console.log('\n🎉 Demo completed successfully!');

    // Cleanup (optional)
    console.log('\n🧹 Cleaning up demo tenant...');
    await tenantService.deleteTenant(newTenant.id, false); // Soft delete
    console.log('✅ Demo tenant deleted');

  } catch (error) {
    console.error('❌ Demo failed:', error);
  } finally {
    await pool.end();
  }
}

// Run the demo
if (require.main === module) {
  main().catch(console.error);
}

export default main;

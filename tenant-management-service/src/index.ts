// Main exports
export { TenantManagementService } from './services/TenantManagementService';
export { MixpanelService } from './services/MixpanelService';
export { ResourceManager } from './services/ResourceManager';
export { BillingService } from './services/BillingService';
export { NotificationService } from './services/NotificationService';

// Repository
export { TenantRepository } from './repositories/TenantRepository';

// Types
export {
  TenantConfig,
  TenantStatus,
  TenantTier,
  TenantProvisioningRequest,
  TenantUpdateRequest,
  TenantEvent,
  TenantEventType,
  TenantResourceUsage,
  ResourceAllocation,
  TenantFeatures,
  MixpanelConfig,
  BillingConfig,
  TenantSettings
} from './types/tenant';

// Utilities
export { Logger, ConsoleLogger } from './utils/Logger';

// Factory function for easy setup
import { Pool } from 'pg';
import { TenantManagementService, TenantManagementServiceConfig } from './services/TenantManagementService';
import { TenantRepository } from './repositories/TenantRepository';
import { ResourceManager, ResourceManagerConfig } from './services/ResourceManager';
import { MixpanelService, MixpanelServiceConfig } from './services/MixpanelService';
import { BillingService, BillingServiceConfig } from './services/BillingService';
import { NotificationService, NotificationServiceConfig } from './services/NotificationService';
import { Logger, ConsoleLogger } from './utils/Logger';
import { TenantTier, TenantFeatures, ResourceAllocation } from './types/tenant';

export interface TenantManagementOptions {
  database: Pool;
  logger?: Logger;
  config?: {
    tenantManagement?: Partial<TenantManagementServiceConfig>;
    resourceManager?: Partial<ResourceManagerConfig>;
    mixpanel?: MixpanelServiceConfig;
    billing?: BillingServiceConfig;
    notifications?: NotificationServiceConfig;
  };
}

/**
 * Create a complete tenant management service with all dependencies
 */
export function createTenantManagementService(options: TenantManagementOptions): TenantManagementService {
  const logger = options.logger || new ConsoleLogger();

  // Create repository
  const repository = new TenantRepository(options.database, logger);

  // Default configurations
  const defaultTenantConfig: TenantManagementServiceConfig = {
    defaultTier: TenantTier.FREE,
    autoActivate: true,
    enableTrials: true,
    trialDurationDays: 14,
    resourceQuotas: getDefaultResourceQuotas(),
    featureMatrix: getDefaultFeatureMatrix()
  };

  const defaultResourceConfig: ResourceManagerConfig = {
    quotas: getDefaultResourceQuotas(),
    monitoring: {
      enabled: true,
      alertThresholds: {
        storage: 80,
        bandwidth: 80,
        apiCalls: 80,
        cpu: 80,
        memory: 80
      }
    },
    provisioning: {
      autoScale: false,
      scaleThresholds: {
        cpu: 80,
        memory: 80
      }
    }
  };

  // Merge with provided configurations
  const tenantConfig = { ...defaultTenantConfig, ...options.config?.tenantManagement };
  const resourceConfig = { ...defaultResourceConfig, ...options.config?.resourceManager };

  // Create services
  const resourceManager = new ResourceManager(resourceConfig, logger);
  
  const mixpanelService = options.config?.mixpanel 
    ? new MixpanelService(options.config.mixpanel, logger)
    : new MixpanelService(getDefaultMixpanelConfig(), logger);

  const billingService = options.config?.billing
    ? new BillingService(options.config.billing, logger)
    : new BillingService(getDefaultBillingConfig(), logger);

  const notificationService = options.config?.notifications
    ? new NotificationService(options.config.notifications, logger)
    : new NotificationService(getDefaultNotificationConfig(), logger);

  // Create main service
  return new TenantManagementService(
    repository,
    resourceManager,
    mixpanelService,
    billingService,
    notificationService,
    logger,
    tenantConfig
  );
}

/**
 * Default configurations
 */
function getDefaultResourceQuotas(): Record<TenantTier, ResourceAllocation> {
  return {
    [TenantTier.FREE]: {
      storage: { limit: 1000, used: 0, unit: 'MB' },
      bandwidth: { limit: 10000, used: 0, unit: 'MB' },
      apiCalls: { limit: 1000, used: 0, unit: 'requests', resetPeriod: 'monthly' },
      users: { limit: 5, used: 0, unit: 'users' },
      projects: { limit: 1, used: 0, unit: 'projects' },
      databases: { limit: 1, used: 0, unit: 'databases' },
      compute: {
        cpu: { limit: 1, used: 0, unit: 'cores' },
        memory: { limit: 512, used: 0, unit: 'MB' },
        instances: { limit: 1, used: 0, unit: 'instances' }
      }
    },
    [TenantTier.STARTER]: {
      storage: { limit: 10000, used: 0, unit: 'MB' },
      bandwidth: { limit: 100000, used: 0, unit: 'MB' },
      apiCalls: { limit: 10000, used: 0, unit: 'requests', resetPeriod: 'monthly' },
      users: { limit: 25, used: 0, unit: 'users' },
      projects: { limit: 5, used: 0, unit: 'projects' },
      databases: { limit: 2, used: 0, unit: 'databases' },
      compute: {
        cpu: { limit: 2, used: 0, unit: 'cores' },
        memory: { limit: 2048, used: 0, unit: 'MB' },
        instances: { limit: 2, used: 0, unit: 'instances' }
      }
    },
    [TenantTier.PRO]: {
      storage: { limit: 100000, used: 0, unit: 'MB' },
      bandwidth: { limit: 1000000, used: 0, unit: 'MB' },
      apiCalls: { limit: 100000, used: 0, unit: 'requests', resetPeriod: 'monthly' },
      users: { limit: 100, used: 0, unit: 'users' },
      projects: { limit: 25, used: 0, unit: 'projects' },
      databases: { limit: 5, used: 0, unit: 'databases' },
      compute: {
        cpu: { limit: 4, used: 0, unit: 'cores' },
        memory: { limit: 8192, used: 0, unit: 'MB' },
        instances: { limit: 5, used: 0, unit: 'instances' }
      }
    },
    [TenantTier.ENTERPRISE]: {
      storage: { limit: -1, used: 0, unit: 'MB' }, // Unlimited
      bandwidth: { limit: -1, used: 0, unit: 'MB' },
      apiCalls: { limit: -1, used: 0, unit: 'requests', resetPeriod: 'monthly' },
      users: { limit: -1, used: 0, unit: 'users' },
      projects: { limit: -1, used: 0, unit: 'projects' },
      databases: { limit: -1, used: 0, unit: 'databases' },
      compute: {
        cpu: { limit: -1, used: 0, unit: 'cores' },
        memory: { limit: -1, used: 0, unit: 'MB' },
        instances: { limit: -1, used: 0, unit: 'instances' }
      }
    },
    [TenantTier.CUSTOM]: {
      storage: { limit: -1, used: 0, unit: 'MB' },
      bandwidth: { limit: -1, used: 0, unit: 'MB' },
      apiCalls: { limit: -1, used: 0, unit: 'requests', resetPeriod: 'monthly' },
      users: { limit: -1, used: 0, unit: 'users' },
      projects: { limit: -1, used: 0, unit: 'projects' },
      databases: { limit: -1, used: 0, unit: 'databases' },
      compute: {
        cpu: { limit: -1, used: 0, unit: 'cores' },
        memory: { limit: -1, used: 0, unit: 'MB' },
        instances: { limit: -1, used: 0, unit: 'instances' }
      }
    }
  };
}

function getDefaultFeatureMatrix(): Record<TenantTier, TenantFeatures> {
  return {
    [TenantTier.FREE]: {
      analytics: true,
      apiAccess: false,
      customDomain: false,
      sso: false,
      whiteLabel: false,
      advancedReporting: false,
      multiUser: false,
      customIntegrations: false,
      prioritySupport: false,
      auditLogs: false,
      dataExport: false,
      customFields: false
    },
    [TenantTier.STARTER]: {
      analytics: true,
      apiAccess: true,
      customDomain: false,
      sso: false,
      whiteLabel: false,
      advancedReporting: true,
      multiUser: true,
      customIntegrations: false,
      prioritySupport: false,
      auditLogs: false,
      dataExport: true,
      customFields: false
    },
    [TenantTier.PRO]: {
      analytics: true,
      apiAccess: true,
      customDomain: true,
      sso: false,
      whiteLabel: false,
      advancedReporting: true,
      multiUser: true,
      customIntegrations: true,
      prioritySupport: true,
      auditLogs: true,
      dataExport: true,
      customFields: true
    },
    [TenantTier.ENTERPRISE]: {
      analytics: true,
      apiAccess: true,
      customDomain: true,
      sso: true,
      whiteLabel: true,
      advancedReporting: true,
      multiUser: true,
      customIntegrations: true,
      prioritySupport: true,
      auditLogs: true,
      dataExport: true,
      customFields: true
    },
    [TenantTier.CUSTOM]: {
      analytics: true,
      apiAccess: true,
      customDomain: true,
      sso: true,
      whiteLabel: true,
      advancedReporting: true,
      multiUser: true,
      customIntegrations: true,
      prioritySupport: true,
      auditLogs: true,
      dataExport: true,
      customFields: true
    }
  };
}

function getDefaultMixpanelConfig(): MixpanelServiceConfig {
  return {
    apiBaseUrl: 'https://mixpanel.com/api',
    serviceAccountUsername: process.env.MIXPANEL_USERNAME || '',
    serviceAccountPassword: process.env.MIXPANEL_PASSWORD || '',
    defaultTimezone: 'UTC',
    defaultRetentionDays: 365,
    organizationId: process.env.MIXPANEL_ORG_ID || ''
  };
}

function getDefaultBillingConfig(): BillingServiceConfig {
  return {
    stripe: {
      secretKey: process.env.STRIPE_SECRET_KEY || '',
      webhookSecret: process.env.STRIPE_WEBHOOK_SECRET || '',
      defaultCurrency: 'USD'
    },
    plans: {
      [TenantTier.FREE]: {
        priceId: '',
        monthlyPrice: 0,
        yearlyPrice: 0,
        features: []
      },
      [TenantTier.STARTER]: {
        priceId: process.env.STRIPE_STARTER_PRICE_ID || '',
        monthlyPrice: 29,
        yearlyPrice: 290,
        features: ['analytics', 'api_access']
      },
      [TenantTier.PRO]: {
        priceId: process.env.STRIPE_PRO_PRICE_ID || '',
        monthlyPrice: 99,
        yearlyPrice: 990,
        features: ['analytics', 'api_access', 'custom_domain']
      },
      [TenantTier.ENTERPRISE]: {
        priceId: process.env.STRIPE_ENTERPRISE_PRICE_ID || '',
        monthlyPrice: 299,
        yearlyPrice: 2990,
        features: ['all']
      },
      [TenantTier.CUSTOM]: {
        priceId: '',
        monthlyPrice: 0,
        yearlyPrice: 0,
        features: ['all']
      }
    },
    trialDays: 14
  };
}

function getDefaultNotificationConfig(): NotificationServiceConfig {
  return {
    email: {
      enabled: true,
      provider: 'sendgrid',
      apiKey: process.env.SENDGRID_API_KEY || '',
      fromEmail: process.env.FROM_EMAIL || '<EMAIL>',
      fromName: process.env.FROM_NAME || 'Tenant Management'
    },
    slack: {
      enabled: false,
      webhookUrl: process.env.SLACK_WEBHOOK_URL
    },
    webhook: {
      enabled: false,
      endpoints: []
    },
    templates: {
      tenantCreated: 'tenant-created',
      tenantUpdated: 'tenant-updated',
      tenantDeleted: 'tenant-deleted',
      billingUpdated: 'billing-updated',
      resourceLimitExceeded: 'resource-limit-exceeded'
    }
  };
}

import { Pool, PoolClient } from 'pg';
import {
  TenantConfig,
  TenantStatus,
  TenantTier,
  TenantUpdateRequest,
  TenantEvent
} from '../types/tenant';
import { Logger } from '../utils/Logger';

export interface TenantQueryOptions {
  status?: TenantStatus[];
  tier?: TenantTier[];
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface TenantListResult {
  tenants: TenantConfig[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export class TenantRepository {
  private pool: Pool;
  private logger: Logger;

  constructor(pool: Pool, logger: Logger) {
    this.pool = pool;
    this.logger = logger;
  }

  /**
   * Create a new tenant
   */
  async create(tenant: Omit<TenantConfig, 'id' | 'createdAt' | 'updatedAt'>): Promise<TenantConfig> {
    const client = await this.pool.connect();
    
    try {
      await client.query('BEGIN');

      const insertQuery = `
        INSERT INTO tenants (
          slug, name, display_name, description, domain, subdomain,
          status, tier, owner, billing, features, resources,
          integrations, settings, metadata
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
        RETURNING id, created_at, updated_at
      `;

      const values = [
        tenant.slug,
        tenant.name,
        tenant.displayName,
        tenant.description,
        tenant.domain,
        tenant.subdomain,
        tenant.status,
        tenant.tier,
        JSON.stringify(tenant.owner),
        JSON.stringify(tenant.billing),
        JSON.stringify(tenant.features),
        JSON.stringify(tenant.resources),
        JSON.stringify(tenant.integrations),
        JSON.stringify(tenant.settings),
        JSON.stringify(tenant.metadata)
      ];

      const result = await client.query(insertQuery, values);
      const row = result.rows[0];

      await client.query('COMMIT');

      const createdTenant: TenantConfig = {
        id: row.id,
        ...tenant,
        createdAt: row.created_at,
        updatedAt: row.updated_at
      };

      this.logger.info('Tenant created in database', {
        tenantId: createdTenant.id,
        slug: createdTenant.slug
      });

      return createdTenant;
    } catch (error) {
      await client.query('ROLLBACK');
      this.logger.error('Failed to create tenant in database', {
        slug: tenant.slug,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Find tenant by ID
   */
  async findById(id: string): Promise<TenantConfig | null> {
    try {
      const query = `
        SELECT * FROM tenants 
        WHERE id = $1 AND deleted_at IS NULL
      `;

      const result = await this.pool.query(query, [id]);
      
      if (result.rows.length === 0) {
        return null;
      }

      return this.mapRowToTenant(result.rows[0]);
    } catch (error) {
      this.logger.error('Failed to find tenant by ID', {
        tenantId: id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Find tenant by slug
   */
  async findBySlug(slug: string): Promise<TenantConfig | null> {
    try {
      const query = `
        SELECT * FROM tenants 
        WHERE slug = $1 AND deleted_at IS NULL
      `;

      const result = await this.pool.query(query, [slug]);
      
      if (result.rows.length === 0) {
        return null;
      }

      return this.mapRowToTenant(result.rows[0]);
    } catch (error) {
      this.logger.error('Failed to find tenant by slug', {
        slug: slug,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Find tenant by domain
   */
  async findByDomain(domain: string): Promise<TenantConfig | null> {
    try {
      const query = `
        SELECT * FROM tenants 
        WHERE domain = $1 AND deleted_at IS NULL
      `;

      const result = await this.pool.query(query, [domain]);
      
      if (result.rows.length === 0) {
        return null;
      }

      return this.mapRowToTenant(result.rows[0]);
    } catch (error) {
      this.logger.error('Failed to find tenant by domain', {
        domain: domain,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Update tenant
   */
  async update(id: string, updates: TenantUpdateRequest): Promise<TenantConfig> {
    const client = await this.pool.connect();
    
    try {
      await client.query('BEGIN');

      // Build dynamic update query
      const updateFields: string[] = [];
      const values: any[] = [];
      let paramIndex = 1;

      if (updates.name !== undefined) {
        updateFields.push(`name = $${paramIndex++}`);
        values.push(updates.name);
      }

      if (updates.displayName !== undefined) {
        updateFields.push(`display_name = $${paramIndex++}`);
        values.push(updates.displayName);
      }

      if (updates.description !== undefined) {
        updateFields.push(`description = $${paramIndex++}`);
        values.push(updates.description);
      }

      if (updates.domain !== undefined) {
        updateFields.push(`domain = $${paramIndex++}`);
        values.push(updates.domain);
      }

      if (updates.status !== undefined) {
        updateFields.push(`status = $${paramIndex++}`);
        values.push(updates.status);
      }

      if (updates.tier !== undefined) {
        updateFields.push(`tier = $${paramIndex++}`);
        values.push(updates.tier);
      }

      if (updates.billing !== undefined) {
        updateFields.push(`billing = $${paramIndex++}`);
        values.push(JSON.stringify(updates.billing));
      }

      if (updates.features !== undefined) {
        updateFields.push(`features = $${paramIndex++}`);
        values.push(JSON.stringify(updates.features));
      }

      if (updates.settings !== undefined) {
        updateFields.push(`settings = $${paramIndex++}`);
        values.push(JSON.stringify(updates.settings));
      }

      if (updates.integrations !== undefined) {
        updateFields.push(`integrations = $${paramIndex++}`);
        values.push(JSON.stringify(updates.integrations));
      }

      if (updates.metadata !== undefined) {
        updateFields.push(`metadata = $${paramIndex++}`);
        values.push(JSON.stringify(updates.metadata));
      }

      if (updateFields.length === 0) {
        throw new Error('No fields to update');
      }

      updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
      values.push(id);

      const updateQuery = `
        UPDATE tenants 
        SET ${updateFields.join(', ')}
        WHERE id = $${paramIndex} AND deleted_at IS NULL
        RETURNING *
      `;

      const result = await client.query(updateQuery, values);
      
      if (result.rows.length === 0) {
        throw new Error(`Tenant not found: ${id}`);
      }

      await client.query('COMMIT');

      const updatedTenant = this.mapRowToTenant(result.rows[0]);

      this.logger.info('Tenant updated in database', {
        tenantId: id,
        updatedFields: Object.keys(updates)
      });

      return updatedTenant;
    } catch (error) {
      await client.query('ROLLBACK');
      this.logger.error('Failed to update tenant in database', {
        tenantId: id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Soft delete tenant
   */
  async delete(id: string): Promise<void> {
    try {
      const query = `
        UPDATE tenants 
        SET deleted_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
        WHERE id = $1 AND deleted_at IS NULL
      `;

      const result = await this.pool.query(query, [id]);
      
      if (result.rowCount === 0) {
        throw new Error(`Tenant not found: ${id}`);
      }

      this.logger.info('Tenant soft deleted', { tenantId: id });
    } catch (error) {
      this.logger.error('Failed to soft delete tenant', {
        tenantId: id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Hard delete tenant
   */
  async hardDelete(id: string): Promise<void> {
    const client = await this.pool.connect();
    
    try {
      await client.query('BEGIN');

      // Delete related records first
      await client.query('DELETE FROM tenant_events WHERE tenant_id = $1', [id]);
      await client.query('DELETE FROM tenant_usage WHERE tenant_id = $1', [id]);
      
      // Delete tenant
      const result = await client.query('DELETE FROM tenants WHERE id = $1', [id]);
      
      if (result.rowCount === 0) {
        throw new Error(`Tenant not found: ${id}`);
      }

      await client.query('COMMIT');

      this.logger.info('Tenant hard deleted', { tenantId: id });
    } catch (error) {
      await client.query('ROLLBACK');
      this.logger.error('Failed to hard delete tenant', {
        tenantId: id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Find multiple tenants with filtering and pagination
   */
  async findMany(options: TenantQueryOptions = {}): Promise<TenantListResult> {
    try {
      const {
        status,
        tier,
        search,
        page = 1,
        limit = 20,
        sortBy = 'created_at',
        sortOrder = 'desc'
      } = options;

      // Build WHERE clause
      const conditions: string[] = ['deleted_at IS NULL'];
      const values: any[] = [];
      let paramIndex = 1;

      if (status && status.length > 0) {
        conditions.push(`status = ANY($${paramIndex++})`);
        values.push(status);
      }

      if (tier && tier.length > 0) {
        conditions.push(`tier = ANY($${paramIndex++})`);
        values.push(tier);
      }

      if (search) {
        conditions.push(`(name ILIKE $${paramIndex} OR display_name ILIKE $${paramIndex} OR slug ILIKE $${paramIndex})`);
        values.push(`%${search}%`);
        paramIndex++;
      }

      const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

      // Count total records
      const countQuery = `SELECT COUNT(*) FROM tenants ${whereClause}`;
      const countResult = await this.pool.query(countQuery, values);
      const total = parseInt(countResult.rows[0].count, 10);

      // Calculate pagination
      const offset = (page - 1) * limit;
      const totalPages = Math.ceil(total / limit);

      // Get paginated results
      const dataQuery = `
        SELECT * FROM tenants 
        ${whereClause}
        ORDER BY ${sortBy} ${sortOrder.toUpperCase()}
        LIMIT $${paramIndex++} OFFSET $${paramIndex++}
      `;

      values.push(limit, offset);
      const dataResult = await this.pool.query(dataQuery, values);

      const tenants = dataResult.rows.map(row => this.mapRowToTenant(row));

      return {
        tenants,
        total,
        page,
        limit,
        totalPages
      };
    } catch (error) {
      this.logger.error('Failed to find tenants', {
        options: options,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Store tenant event
   */
  async storeEvent(event: TenantEvent): Promise<void> {
    try {
      const query = `
        INSERT INTO tenant_events (id, tenant_id, type, data, timestamp, user_id, source)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
      `;

      const values = [
        event.id,
        event.tenantId,
        event.type,
        JSON.stringify(event.data),
        event.timestamp,
        event.userId,
        event.source
      ];

      await this.pool.query(query, values);

      this.logger.debug('Tenant event stored', {
        eventId: event.id,
        tenantId: event.tenantId,
        type: event.type
      });
    } catch (error) {
      this.logger.error('Failed to store tenant event', {
        eventId: event.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get tenant events
   */
  async getEvents(tenantId: string, options: {
    types?: string[];
    limit?: number;
    offset?: number;
  } = {}): Promise<TenantEvent[]> {
    try {
      const { types, limit = 50, offset = 0 } = options;

      let query = `
        SELECT * FROM tenant_events 
        WHERE tenant_id = $1
      `;
      const values: any[] = [tenantId];
      let paramIndex = 2;

      if (types && types.length > 0) {
        query += ` AND type = ANY($${paramIndex++})`;
        values.push(types);
      }

      query += ` ORDER BY timestamp DESC LIMIT $${paramIndex++} OFFSET $${paramIndex++}`;
      values.push(limit, offset);

      const result = await this.pool.query(query, values);

      return result.rows.map(row => ({
        id: row.id,
        tenantId: row.tenant_id,
        type: row.type,
        data: row.data,
        timestamp: row.timestamp,
        userId: row.user_id,
        source: row.source
      }));
    } catch (error) {
      this.logger.error('Failed to get tenant events', {
        tenantId: tenantId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Map database row to TenantConfig
   */
  private mapRowToTenant(row: any): TenantConfig {
    return {
      id: row.id,
      slug: row.slug,
      name: row.name,
      displayName: row.display_name,
      description: row.description,
      domain: row.domain,
      subdomain: row.subdomain,
      status: row.status,
      tier: row.tier,
      owner: row.owner,
      billing: row.billing,
      features: row.features,
      resources: row.resources,
      integrations: row.integrations,
      settings: row.settings,
      metadata: row.metadata,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      deletedAt: row.deleted_at
    };
  }
}

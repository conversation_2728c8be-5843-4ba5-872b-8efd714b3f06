export interface TenantConfig {
  id: string;
  slug: string;
  name: string;
  displayName: string;
  description?: string;
  domain?: string;
  subdomain?: string;
  status: TenantStatus;
  tier: TenantTier;
  owner: TenantOwner;
  billing: BillingConfig;
  features: TenantFeatures;
  resources: ResourceAllocation;
  integrations: TenantIntegrations;
  settings: TenantSettings;
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
}

export enum TenantStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  PENDING = 'pending',
  DELETED = 'deleted'
}

export enum TenantTier {
  FREE = 'free',
  STARTER = 'starter',
  PRO = 'pro',
  ENTERPRISE = 'enterprise',
  CUSTOM = 'custom'
}

export interface TenantOwner {
  userId: string;
  email: string;
  name: string;
  role: string;
}

export interface BillingConfig {
  plan: string;
  billingCycle: 'monthly' | 'yearly';
  currency: string;
  paymentMethod?: string;
  subscriptionId?: string;
  trialEndsAt?: Date;
  nextBillingDate?: Date;
  isTrialActive: boolean;
}

export interface TenantFeatures {
  analytics: boolean;
  apiAccess: boolean;
  customDomain: boolean;
  sso: boolean;
  whiteLabel: boolean;
  advancedReporting: boolean;
  multiUser: boolean;
  customIntegrations: boolean;
  prioritySupport: boolean;
  auditLogs: boolean;
  dataExport: boolean;
  customFields: boolean;
}

export interface ResourceAllocation {
  storage: ResourceLimit;
  bandwidth: ResourceLimit;
  apiCalls: ResourceLimit;
  users: ResourceLimit;
  projects: ResourceLimit;
  databases: ResourceLimit;
  compute: ComputeResources;
}

export interface ResourceLimit {
  limit: number; // -1 for unlimited
  used: number;
  unit: string;
  resetPeriod?: 'daily' | 'monthly' | 'yearly';
}

export interface ComputeResources {
  cpu: ResourceLimit;
  memory: ResourceLimit;
  instances: ResourceLimit;
}

export interface TenantIntegrations {
  mixpanel?: MixpanelConfig;
  stripe?: StripeConfig;
  slack?: SlackConfig;
  webhook?: WebhookConfig;
  sso?: SSOConfig;
  email?: EmailConfig;
}

export interface MixpanelConfig {
  enabled: boolean;
  projectToken: string;
  apiSecret?: string;
  serviceAccountUsername?: string;
  serviceAccountSecret?: string;
  dataResidency?: 'us' | 'eu';
  customProperties?: Record<string, any>;
  eventFilters?: string[];
  retentionDays?: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface StripeConfig {
  enabled: boolean;
  customerId: string;
  subscriptionId?: string;
  webhookEndpointId?: string;
  publishableKey?: string;
}

export interface SlackConfig {
  enabled: boolean;
  workspaceId: string;
  botToken: string;
  webhookUrl?: string;
  channels: string[];
}

export interface WebhookConfig {
  enabled: boolean;
  endpoints: WebhookEndpoint[];
}

export interface WebhookEndpoint {
  id: string;
  url: string;
  events: string[];
  secret: string;
  active: boolean;
}

export interface SSOConfig {
  enabled: boolean;
  provider: 'saml' | 'oidc' | 'oauth2';
  entityId?: string;
  ssoUrl?: string;
  certificate?: string;
  clientId?: string;
  clientSecret?: string;
  issuer?: string;
}

export interface EmailConfig {
  enabled: boolean;
  provider: 'sendgrid' | 'mailgun' | 'ses' | 'smtp';
  apiKey?: string;
  domain?: string;
  fromEmail: string;
  fromName: string;
  templates: Record<string, string>;
}

export interface TenantSettings {
  timezone: string;
  locale: string;
  dateFormat: string;
  currency: string;
  theme: 'light' | 'dark' | 'auto';
  notifications: NotificationSettings;
  security: SecuritySettings;
  privacy: PrivacySettings;
  customization: CustomizationSettings;
}

export interface NotificationSettings {
  email: boolean;
  slack: boolean;
  webhook: boolean;
  inApp: boolean;
  frequency: 'immediate' | 'hourly' | 'daily' | 'weekly';
  types: string[];
}

export interface SecuritySettings {
  passwordPolicy: PasswordPolicy;
  sessionTimeout: number;
  ipWhitelist?: string[];
  mfaRequired: boolean;
  auditLogging: boolean;
  dataEncryption: boolean;
}

export interface PasswordPolicy {
  minLength: number;
  requireUppercase: boolean;
  requireLowercase: boolean;
  requireNumbers: boolean;
  requireSymbols: boolean;
  maxAge: number;
  preventReuse: number;
}

export interface PrivacySettings {
  dataRetentionDays: number;
  anonymizeData: boolean;
  cookieConsent: boolean;
  gdprCompliant: boolean;
  ccpaCompliant: boolean;
}

export interface CustomizationSettings {
  logo?: string;
  favicon?: string;
  primaryColor?: string;
  secondaryColor?: string;
  customCss?: string;
  customJs?: string;
  brandName?: string;
}

export interface TenantProvisioningRequest {
  name: string;
  slug: string;
  displayName: string;
  description?: string;
  domain?: string;
  tier: TenantTier;
  owner: Omit<TenantOwner, 'role'>;
  billing?: Partial<BillingConfig>;
  features?: Partial<TenantFeatures>;
  settings?: Partial<TenantSettings>;
  integrations?: Partial<TenantIntegrations>;
  metadata?: Record<string, any>;
}

export interface TenantUpdateRequest {
  name?: string;
  displayName?: string;
  description?: string;
  domain?: string;
  status?: TenantStatus;
  tier?: TenantTier;
  billing?: Partial<BillingConfig>;
  features?: Partial<TenantFeatures>;
  settings?: Partial<TenantSettings>;
  integrations?: Partial<TenantIntegrations>;
  metadata?: Record<string, any>;
}

export interface TenantResourceUsage {
  tenantId: string;
  timestamp: Date;
  resources: {
    storage: number;
    bandwidth: number;
    apiCalls: number;
    users: number;
    projects: number;
    databases: number;
    cpu: number;
    memory: number;
  };
}

export interface TenantEvent {
  id: string;
  tenantId: string;
  type: TenantEventType;
  data: Record<string, any>;
  timestamp: Date;
  userId?: string;
  source: string;
}

export enum TenantEventType {
  CREATED = 'tenant.created',
  UPDATED = 'tenant.updated',
  DELETED = 'tenant.deleted',
  ACTIVATED = 'tenant.activated',
  SUSPENDED = 'tenant.suspended',
  TIER_CHANGED = 'tenant.tier_changed',
  FEATURE_ENABLED = 'tenant.feature_enabled',
  FEATURE_DISABLED = 'tenant.feature_disabled',
  INTEGRATION_ADDED = 'tenant.integration_added',
  INTEGRATION_UPDATED = 'tenant.integration_updated',
  INTEGRATION_REMOVED = 'tenant.integration_removed',
  RESOURCE_LIMIT_EXCEEDED = 'tenant.resource_limit_exceeded',
  BILLING_UPDATED = 'tenant.billing_updated'
}

import Stripe from 'stripe';
import { TenantConfig, BillingConfig, TenantTier } from '../types/tenant';
import { Logger } from '../utils/Logger';

export interface BillingServiceConfig {
  stripe: {
    secretKey: string;
    webhookSecret: string;
    defaultCurrency: string;
  };
  plans: Record<TenantTier, {
    priceId: string;
    monthlyPrice: number;
    yearlyPrice: number;
    features: string[];
  }>;
  trialDays: number;
}

export interface SubscriptionInfo {
  id: string;
  status: string;
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  cancelAtPeriodEnd: boolean;
  trialEnd?: Date;
  plan: {
    id: string;
    amount: number;
    currency: string;
    interval: string;
  };
}

export interface InvoiceInfo {
  id: string;
  amount: number;
  currency: string;
  status: string;
  dueDate: Date;
  paidAt?: Date;
  hostedInvoiceUrl?: string;
}

export class BillingService {
  private stripe: Stripe;
  private config: BillingServiceConfig;
  private logger: Logger;

  constructor(config: BillingServiceConfig, logger: Logger) {
    this.config = config;
    this.logger = logger;
    this.stripe = new Stripe(config.stripe.secretKey, {
      apiVersion: '2023-10-16'
    });
  }

  /**
   * Setup billing for a new tenant
   */
  async setupBilling(tenant: TenantConfig): Promise<void> {
    this.logger.info('Setting up billing for tenant', {
      tenantId: tenant.id,
      tier: tenant.tier
    });

    try {
      // Create Stripe customer
      const customer = await this.createCustomer(tenant);

      // Create subscription if not free tier
      let subscription = null;
      if (tenant.tier !== TenantTier.FREE) {
        subscription = await this.createSubscription(customer.id, tenant);
      }

      // Update tenant billing configuration
      const billingConfig: Partial<BillingConfig> = {
        ...tenant.billing,
        subscriptionId: subscription?.id,
        nextBillingDate: subscription ? new Date(subscription.current_period_end * 1000) : undefined
      };

      this.logger.info('Billing setup completed', {
        tenantId: tenant.id,
        customerId: customer.id,
        subscriptionId: subscription?.id
      });
    } catch (error) {
      this.logger.error('Failed to setup billing', {
        tenantId: tenant.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Update billing for tenant
   */
  async updateBilling(tenant: TenantConfig): Promise<void> {
    this.logger.info('Updating billing for tenant', {
      tenantId: tenant.id,
      tier: tenant.tier
    });

    try {
      const customerId = await this.getOrCreateCustomerId(tenant);

      // Update subscription if tier changed
      if (tenant.billing.subscriptionId) {
        await this.updateSubscription(tenant.billing.subscriptionId, tenant);
      } else if (tenant.tier !== TenantTier.FREE) {
        // Create new subscription
        await this.createSubscription(customerId, tenant);
      }

      this.logger.info('Billing updated successfully', {
        tenantId: tenant.id
      });
    } catch (error) {
      this.logger.error('Failed to update billing', {
        tenantId: tenant.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Cancel billing for tenant
   */
  async cancelBilling(tenant: TenantConfig): Promise<void> {
    this.logger.info('Canceling billing for tenant', {
      tenantId: tenant.id
    });

    try {
      if (tenant.billing.subscriptionId) {
        await this.stripe.subscriptions.cancel(tenant.billing.subscriptionId);
      }

      // Optionally delete customer
      // await this.stripe.customers.del(customerId);

      this.logger.info('Billing canceled successfully', {
        tenantId: tenant.id
      });
    } catch (error) {
      this.logger.error('Failed to cancel billing', {
        tenantId: tenant.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get subscription information
   */
  async getSubscriptionInfo(tenant: TenantConfig): Promise<SubscriptionInfo | null> {
    if (!tenant.billing.subscriptionId) {
      return null;
    }

    try {
      const subscription = await this.stripe.subscriptions.retrieve(tenant.billing.subscriptionId);

      return {
        id: subscription.id,
        status: subscription.status,
        currentPeriodStart: new Date(subscription.current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000),
        cancelAtPeriodEnd: subscription.cancel_at_period_end,
        trialEnd: subscription.trial_end ? new Date(subscription.trial_end * 1000) : undefined,
        plan: {
          id: subscription.items.data[0].price.id,
          amount: subscription.items.data[0].price.unit_amount || 0,
          currency: subscription.items.data[0].price.currency,
          interval: subscription.items.data[0].price.recurring?.interval || 'month'
        }
      };
    } catch (error) {
      this.logger.error('Failed to get subscription info', {
        tenantId: tenant.id,
        subscriptionId: tenant.billing.subscriptionId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get upcoming invoice
   */
  async getUpcomingInvoice(tenant: TenantConfig): Promise<InvoiceInfo | null> {
    const customerId = await this.getCustomerId(tenant);
    if (!customerId) {
      return null;
    }

    try {
      const invoice = await this.stripe.invoices.retrieveUpcoming({
        customer: customerId
      });

      return {
        id: invoice.id || 'upcoming',
        amount: invoice.amount_due,
        currency: invoice.currency,
        status: invoice.status || 'draft',
        dueDate: new Date((invoice.due_date || invoice.created) * 1000),
        hostedInvoiceUrl: invoice.hosted_invoice_url || undefined
      };
    } catch (error) {
      if (error instanceof Stripe.errors.StripeError && error.code === 'invoice_upcoming_none') {
        return null;
      }

      this.logger.error('Failed to get upcoming invoice', {
        tenantId: tenant.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get invoice history
   */
  async getInvoiceHistory(tenant: TenantConfig, limit: number = 10): Promise<InvoiceInfo[]> {
    const customerId = await this.getCustomerId(tenant);
    if (!customerId) {
      return [];
    }

    try {
      const invoices = await this.stripe.invoices.list({
        customer: customerId,
        limit: limit
      });

      return invoices.data.map(invoice => ({
        id: invoice.id,
        amount: invoice.amount_paid,
        currency: invoice.currency,
        status: invoice.status || 'draft',
        dueDate: new Date((invoice.due_date || invoice.created) * 1000),
        paidAt: invoice.status_transitions.paid_at ? new Date(invoice.status_transitions.paid_at * 1000) : undefined,
        hostedInvoiceUrl: invoice.hosted_invoice_url || undefined
      }));
    } catch (error) {
      this.logger.error('Failed to get invoice history', {
        tenantId: tenant.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Create payment method setup intent
   */
  async createSetupIntent(tenant: TenantConfig): Promise<{ clientSecret: string }> {
    const customerId = await this.getOrCreateCustomerId(tenant);

    try {
      const setupIntent = await this.stripe.setupIntents.create({
        customer: customerId,
        payment_method_types: ['card'],
        usage: 'off_session'
      });

      return {
        clientSecret: setupIntent.client_secret!
      };
    } catch (error) {
      this.logger.error('Failed to create setup intent', {
        tenantId: tenant.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Handle Stripe webhook
   */
  async handleWebhook(payload: string, signature: string): Promise<void> {
    try {
      const event = this.stripe.webhooks.constructEvent(
        payload,
        signature,
        this.config.stripe.webhookSecret
      );

      this.logger.info('Processing Stripe webhook', {
        type: event.type,
        id: event.id
      });

      switch (event.type) {
        case 'customer.subscription.created':
          await this.handleSubscriptionCreated(event.data.object as Stripe.Subscription);
          break;

        case 'customer.subscription.updated':
          await this.handleSubscriptionUpdated(event.data.object as Stripe.Subscription);
          break;

        case 'customer.subscription.deleted':
          await this.handleSubscriptionDeleted(event.data.object as Stripe.Subscription);
          break;

        case 'invoice.payment_succeeded':
          await this.handlePaymentSucceeded(event.data.object as Stripe.Invoice);
          break;

        case 'invoice.payment_failed':
          await this.handlePaymentFailed(event.data.object as Stripe.Invoice);
          break;

        default:
          this.logger.debug('Unhandled webhook event type', { type: event.type });
      }
    } catch (error) {
      this.logger.error('Failed to handle webhook', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Private helper methods
   */
  private async createCustomer(tenant: TenantConfig): Promise<Stripe.Customer> {
    return this.stripe.customers.create({
      email: tenant.owner.email,
      name: tenant.owner.name,
      metadata: {
        tenantId: tenant.id,
        tenantSlug: tenant.slug
      }
    });
  }

  private async createSubscription(customerId: string, tenant: TenantConfig): Promise<Stripe.Subscription> {
    const plan = this.config.plans[tenant.tier];
    if (!plan) {
      throw new Error(`No billing plan configured for tier: ${tenant.tier}`);
    }

    const priceId = tenant.billing.billingCycle === 'yearly' ? plan.priceId + '_yearly' : plan.priceId;

    const subscriptionParams: Stripe.SubscriptionCreateParams = {
      customer: customerId,
      items: [{ price: priceId }],
      metadata: {
        tenantId: tenant.id,
        tenantSlug: tenant.slug,
        tier: tenant.tier
      }
    };

    // Add trial if enabled
    if (tenant.billing.isTrialActive && tenant.billing.trialEndsAt) {
      subscriptionParams.trial_end = Math.floor(tenant.billing.trialEndsAt.getTime() / 1000);
    }

    return this.stripe.subscriptions.create(subscriptionParams);
  }

  private async updateSubscription(subscriptionId: string, tenant: TenantConfig): Promise<Stripe.Subscription> {
    const plan = this.config.plans[tenant.tier];
    if (!plan) {
      throw new Error(`No billing plan configured for tier: ${tenant.tier}`);
    }

    const subscription = await this.stripe.subscriptions.retrieve(subscriptionId);
    const priceId = tenant.billing.billingCycle === 'yearly' ? plan.priceId + '_yearly' : plan.priceId;

    return this.stripe.subscriptions.update(subscriptionId, {
      items: [{
        id: subscription.items.data[0].id,
        price: priceId
      }],
      proration_behavior: 'create_prorations'
    });
  }

  private async getCustomerId(tenant: TenantConfig): Promise<string | null> {
    // This would typically be stored in the tenant's billing configuration
    // For now, we'll search by metadata
    const customers = await this.stripe.customers.list({
      limit: 1,
      metadata: { tenantId: tenant.id }
    });

    return customers.data.length > 0 ? customers.data[0].id : null;
  }

  private async getOrCreateCustomerId(tenant: TenantConfig): Promise<string> {
    let customerId = await this.getCustomerId(tenant);
    
    if (!customerId) {
      const customer = await this.createCustomer(tenant);
      customerId = customer.id;
    }

    return customerId;
  }

  private async handleSubscriptionCreated(subscription: Stripe.Subscription): Promise<void> {
    this.logger.info('Subscription created', {
      subscriptionId: subscription.id,
      customerId: subscription.customer
    });
    // Update tenant billing status
  }

  private async handleSubscriptionUpdated(subscription: Stripe.Subscription): Promise<void> {
    this.logger.info('Subscription updated', {
      subscriptionId: subscription.id,
      status: subscription.status
    });
    // Update tenant billing status
  }

  private async handleSubscriptionDeleted(subscription: Stripe.Subscription): Promise<void> {
    this.logger.info('Subscription deleted', {
      subscriptionId: subscription.id
    });
    // Update tenant status to suspended or downgrade to free tier
  }

  private async handlePaymentSucceeded(invoice: Stripe.Invoice): Promise<void> {
    this.logger.info('Payment succeeded', {
      invoiceId: invoice.id,
      amount: invoice.amount_paid,
      customerId: invoice.customer
    });
    // Send payment confirmation notification
  }

  private async handlePaymentFailed(invoice: Stripe.Invoice): Promise<void> {
    this.logger.warn('Payment failed', {
      invoiceId: invoice.id,
      amount: invoice.amount_due,
      customerId: invoice.customer
    });
    // Send payment failure notification and handle dunning
  }
}

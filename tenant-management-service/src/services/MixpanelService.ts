import axios, { AxiosInstance } from 'axios';
import { TenantConfig, MixpanelConfig } from '../types/tenant';
import { Logger } from '../utils/Logger';

export interface MixpanelProjectConfig {
  name: string;
  timezone: string;
  currency?: string;
  dataResidency: 'us' | 'eu';
  retentionDays?: number;
  customProperties?: Record<string, any>;
}

export interface MixpanelProjectResponse {
  projectId: string;
  projectToken: string;
  apiSecret: string;
  name: string;
  timezone: string;
  created: string;
}

export interface MixpanelServiceConfig {
  apiBaseUrl: string;
  serviceAccountUsername: string;
  serviceAccountPassword: string;
  defaultTimezone: string;
  defaultRetentionDays: number;
  organizationId: string;
}

export class MixpanelService {
  private config: MixpanelServiceConfig;
  private logger: Logger;
  private apiClient: AxiosInstance;

  constructor(config: MixpanelServiceConfig, logger: Logger) {
    this.config = config;
    this.logger = logger;
    
    this.apiClient = axios.create({
      baseURL: config.apiBaseUrl,
      auth: {
        username: config.serviceAccountUsername,
        password: config.serviceAccountPassword
      },
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      timeout: 30000
    });

    // Add request/response interceptors for logging
    this.setupInterceptors();
  }

  /**
   * Setup Mixpanel project for a tenant
   */
  async setupTenantProject(tenant: TenantConfig): Promise<MixpanelProjectResponse> {
    this.logger.info('Setting up Mixpanel project for tenant', {
      tenantId: tenant.id,
      tenantSlug: tenant.slug
    });

    try {
      const projectConfig: MixpanelProjectConfig = {
        name: `${tenant.displayName} (${tenant.slug})`,
        timezone: tenant.settings.timezone || this.config.defaultTimezone,
        currency: tenant.settings.currency,
        dataResidency: tenant.integrations.mixpanel?.dataResidency || 'us',
        retentionDays: tenant.integrations.mixpanel?.retentionDays || this.config.defaultRetentionDays,
        customProperties: tenant.integrations.mixpanel?.customProperties
      };

      const response = await this.createProject(projectConfig);

      // Update tenant with Mixpanel configuration
      const mixpanelConfig: MixpanelConfig = {
        enabled: true,
        projectToken: response.projectToken,
        apiSecret: response.apiSecret,
        serviceAccountUsername: this.config.serviceAccountUsername,
        serviceAccountSecret: this.config.serviceAccountPassword,
        dataResidency: projectConfig.dataResidency,
        customProperties: projectConfig.customProperties,
        retentionDays: projectConfig.retentionDays,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Store the configuration (this would typically update the tenant in the database)
      await this.storeMixpanelConfig(tenant.id, mixpanelConfig);

      // Setup default events and properties
      await this.setupDefaultTracking(response.projectToken, tenant);

      this.logger.info('Mixpanel project created successfully', {
        tenantId: tenant.id,
        projectId: response.projectId,
        projectToken: response.projectToken
      });

      return response;
    } catch (error) {
      this.logger.error('Failed to setup Mixpanel project', {
        tenantId: tenant.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Update Mixpanel project configuration
   */
  async updateTenantProject(tenant: TenantConfig): Promise<void> {
    if (!tenant.integrations.mixpanel?.projectToken) {
      throw new Error('Tenant does not have Mixpanel integration configured');
    }

    this.logger.info('Updating Mixpanel project for tenant', {
      tenantId: tenant.id,
      projectToken: tenant.integrations.mixpanel.projectToken
    });

    try {
      const projectToken = tenant.integrations.mixpanel.projectToken;

      // Update project settings
      await this.updateProjectSettings(projectToken, {
        name: `${tenant.displayName} (${tenant.slug})`,
        timezone: tenant.settings.timezone,
        currency: tenant.settings.currency
      });

      // Update custom properties if changed
      if (tenant.integrations.mixpanel.customProperties) {
        await this.updateCustomProperties(projectToken, tenant.integrations.mixpanel.customProperties);
      }

      // Update retention settings
      if (tenant.integrations.mixpanel.retentionDays) {
        await this.updateRetentionSettings(projectToken, tenant.integrations.mixpanel.retentionDays);
      }

      this.logger.info('Mixpanel project updated successfully', {
        tenantId: tenant.id,
        projectToken: projectToken
      });
    } catch (error) {
      this.logger.error('Failed to update Mixpanel project', {
        tenantId: tenant.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Delete Mixpanel project for a tenant
   */
  async deleteTenantProject(tenant: TenantConfig): Promise<void> {
    if (!tenant.integrations.mixpanel?.projectToken) {
      this.logger.warn('Tenant does not have Mixpanel integration to delete', {
        tenantId: tenant.id
      });
      return;
    }

    this.logger.info('Deleting Mixpanel project for tenant', {
      tenantId: tenant.id,
      projectToken: tenant.integrations.mixpanel.projectToken
    });

    try {
      const projectToken = tenant.integrations.mixpanel.projectToken;

      // Export data before deletion if required
      if (tenant.features.dataExport) {
        await this.exportProjectData(projectToken, tenant);
      }

      // Delete the project
      await this.deleteProject(projectToken);

      // Remove Mixpanel configuration from tenant
      await this.removeMixpanelConfig(tenant.id);

      this.logger.info('Mixpanel project deleted successfully', {
        tenantId: tenant.id,
        projectToken: projectToken
      });
    } catch (error) {
      this.logger.error('Failed to delete Mixpanel project', {
        tenantId: tenant.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get Mixpanel project analytics for tenant
   */
  async getTenantAnalytics(tenant: TenantConfig, options: {
    fromDate: string;
    toDate: string;
    events?: string[];
    properties?: string[];
  }): Promise<any> {
    if (!tenant.integrations.mixpanel?.projectToken) {
      throw new Error('Tenant does not have Mixpanel integration configured');
    }

    try {
      const projectToken = tenant.integrations.mixpanel.projectToken;
      const apiSecret = tenant.integrations.mixpanel.apiSecret;

      // Get event data
      const eventData = await this.getEventData(projectToken, apiSecret!, options);

      // Get user data
      const userData = await this.getUserData(projectToken, apiSecret!, options);

      // Get funnel data
      const funnelData = await this.getFunnelData(projectToken, apiSecret!, options);

      return {
        events: eventData,
        users: userData,
        funnels: funnelData,
        dateRange: {
          from: options.fromDate,
          to: options.toDate
        }
      };
    } catch (error) {
      this.logger.error('Failed to get tenant analytics', {
        tenantId: tenant.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Track custom event for tenant
   */
  async trackTenantEvent(tenant: TenantConfig, eventName: string, properties: Record<string, any>): Promise<void> {
    if (!tenant.integrations.mixpanel?.projectToken) {
      return; // Silently skip if Mixpanel not configured
    }

    try {
      const projectToken = tenant.integrations.mixpanel.projectToken;

      const eventData = {
        event: eventName,
        properties: {
          token: projectToken,
          tenant_id: tenant.id,
          tenant_slug: tenant.slug,
          tenant_tier: tenant.tier,
          ...properties,
          time: Date.now()
        }
      };

      await this.sendEvent(eventData);

      this.logger.debug('Event tracked successfully', {
        tenantId: tenant.id,
        eventName: eventName
      });
    } catch (error) {
      this.logger.error('Failed to track tenant event', {
        tenantId: tenant.id,
        eventName: eventName,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      // Don't throw error for tracking failures
    }
  }

  /**
   * Update user profile for tenant
   */
  async updateTenantUserProfile(tenant: TenantConfig, userId: string, properties: Record<string, any>): Promise<void> {
    if (!tenant.integrations.mixpanel?.projectToken) {
      return;
    }

    try {
      const projectToken = tenant.integrations.mixpanel.projectToken;

      const profileData = {
        $token: projectToken,
        $distinct_id: userId,
        $set: {
          tenant_id: tenant.id,
          tenant_slug: tenant.slug,
          tenant_tier: tenant.tier,
          ...properties
        }
      };

      await this.updateProfile(profileData);

      this.logger.debug('User profile updated successfully', {
        tenantId: tenant.id,
        userId: userId
      });
    } catch (error) {
      this.logger.error('Failed to update user profile', {
        tenantId: tenant.id,
        userId: userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Private methods for Mixpanel API interactions
   */
  private async createProject(config: MixpanelProjectConfig): Promise<MixpanelProjectResponse> {
    const response = await this.apiClient.post('/projects', {
      organization_id: this.config.organizationId,
      name: config.name,
      timezone: config.timezone,
      currency: config.currency,
      data_residency: config.dataResidency
    });

    return {
      projectId: response.data.project_id,
      projectToken: response.data.token,
      apiSecret: response.data.api_secret,
      name: response.data.name,
      timezone: response.data.timezone,
      created: response.data.created
    };
  }

  private async updateProjectSettings(projectToken: string, settings: any): Promise<void> {
    await this.apiClient.patch(`/projects/${projectToken}`, settings);
  }

  private async updateCustomProperties(projectToken: string, properties: Record<string, any>): Promise<void> {
    await this.apiClient.post(`/projects/${projectToken}/properties`, {
      properties: properties
    });
  }

  private async updateRetentionSettings(projectToken: string, retentionDays: number): Promise<void> {
    await this.apiClient.patch(`/projects/${projectToken}/retention`, {
      retention_days: retentionDays
    });
  }

  private async deleteProject(projectToken: string): Promise<void> {
    await this.apiClient.delete(`/projects/${projectToken}`);
  }

  private async exportProjectData(projectToken: string, tenant: TenantConfig): Promise<void> {
    // Implementation for data export
    this.logger.info('Exporting Mixpanel data for tenant', {
      tenantId: tenant.id,
      projectToken: projectToken
    });
    // This would typically trigger an export job
  }

  private async getEventData(projectToken: string, apiSecret: string, options: any): Promise<any> {
    const response = await this.apiClient.get(`/events`, {
      params: {
        project_id: projectToken,
        from_date: options.fromDate,
        to_date: options.toDate,
        events: options.events
      },
      auth: {
        username: apiSecret,
        password: ''
      }
    });

    return response.data;
  }

  private async getUserData(projectToken: string, apiSecret: string, options: any): Promise<any> {
    const response = await this.apiClient.get(`/engage`, {
      params: {
        project_id: projectToken,
        from_date: options.fromDate,
        to_date: options.toDate
      },
      auth: {
        username: apiSecret,
        password: ''
      }
    });

    return response.data;
  }

  private async getFunnelData(projectToken: string, apiSecret: string, options: any): Promise<any> {
    const response = await this.apiClient.get(`/funnels`, {
      params: {
        project_id: projectToken,
        from_date: options.fromDate,
        to_date: options.toDate
      },
      auth: {
        username: apiSecret,
        password: ''
      }
    });

    return response.data;
  }

  private async sendEvent(eventData: any): Promise<void> {
    await this.apiClient.post('/track', eventData);
  }

  private async updateProfile(profileData: any): Promise<void> {
    await this.apiClient.post('/engage', profileData);
  }

  private async setupDefaultTracking(projectToken: string, tenant: TenantConfig): Promise<void> {
    // Setup default events and properties for the tenant
    const defaultEvents = [
      'User Signup',
      'User Login',
      'Feature Used',
      'Subscription Changed',
      'Payment Processed'
    ];

    // This would typically configure default tracking in Mixpanel
    this.logger.info('Setting up default tracking', {
      tenantId: tenant.id,
      projectToken: projectToken,
      defaultEvents: defaultEvents
    });
  }

  private async storeMixpanelConfig(tenantId: string, config: MixpanelConfig): Promise<void> {
    // This would typically update the tenant record in the database
    this.logger.debug('Storing Mixpanel configuration', {
      tenantId: tenantId,
      projectToken: config.projectToken
    });
  }

  private async removeMixpanelConfig(tenantId: string): Promise<void> {
    // This would typically remove Mixpanel configuration from tenant record
    this.logger.debug('Removing Mixpanel configuration', {
      tenantId: tenantId
    });
  }

  private setupInterceptors(): void {
    this.apiClient.interceptors.request.use(
      (config) => {
        this.logger.debug('Mixpanel API request', {
          method: config.method,
          url: config.url,
          params: config.params
        });
        return config;
      },
      (error) => {
        this.logger.error('Mixpanel API request error', { error });
        return Promise.reject(error);
      }
    );

    this.apiClient.interceptors.response.use(
      (response) => {
        this.logger.debug('Mixpanel API response', {
          status: response.status,
          url: response.config.url
        });
        return response;
      },
      (error) => {
        this.logger.error('Mixpanel API response error', {
          status: error.response?.status,
          url: error.config?.url,
          error: error.message
        });
        return Promise.reject(error);
      }
    );
  }
}

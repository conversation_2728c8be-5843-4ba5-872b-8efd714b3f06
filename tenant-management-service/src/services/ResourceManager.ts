import { TenantConfig, ResourceAllocation, TenantResourceUsage, TenantTier } from '../types/tenant';
import { Logger } from '../utils/Logger';

export interface ResourceManagerConfig {
  quotas: Record<TenantTier, ResourceAllocation>;
  monitoring: {
    enabled: boolean;
    alertThresholds: {
      storage: number;
      bandwidth: number;
      apiCalls: number;
      cpu: number;
      memory: number;
    };
  };
  provisioning: {
    autoScale: boolean;
    scaleThresholds: {
      cpu: number;
      memory: number;
    };
  };
}

export interface ResourceProvider {
  provisionStorage(tenantId: string, allocation: number): Promise<string>;
  provisionDatabase(tenantId: string, config: any): Promise<string>;
  provisionCompute(tenantId: string, resources: any): Promise<string>;
  deprovisionResources(tenantId: string): Promise<void>;
  updateResourceLimits(tenantId: string, resources: ResourceAllocation): Promise<void>;
  getResourceUsage(tenantId: string): Promise<TenantResourceUsage>;
  suspendResources(tenantId: string): Promise<void>;
  resumeResources(tenantId: string): Promise<void>;
}

export class ResourceManager {
  private config: ResourceManagerConfig;
  private logger: Logger;
  private providers: Map<string, ResourceProvider> = new Map();
  private usageCache: Map<string, { usage: TenantResourceUsage; timestamp: number }> = new Map();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  constructor(config: ResourceManagerConfig, logger: Logger) {
    this.config = config;
    this.logger = logger;
  }

  /**
   * Register a resource provider
   */
  registerProvider(name: string, provider: ResourceProvider): void {
    this.providers.set(name, provider);
    this.logger.info('Resource provider registered', { provider: name });
  }

  /**
   * Provision resources for a new tenant
   */
  async provisionTenant(tenant: TenantConfig): Promise<void> {
    this.logger.info('Provisioning resources for tenant', {
      tenantId: tenant.id,
      tier: tenant.tier
    });

    try {
      const resources = tenant.resources;

      // Provision storage
      if (resources.storage.limit > 0) {
        await this.provisionStorage(tenant.id, resources.storage.limit);
      }

      // Provision databases
      if (resources.databases.limit > 0) {
        await this.provisionDatabases(tenant);
      }

      // Provision compute resources
      if (resources.compute.instances.limit > 0) {
        await this.provisionCompute(tenant);
      }

      // Setup monitoring
      if (this.config.monitoring.enabled) {
        await this.setupResourceMonitoring(tenant);
      }

      this.logger.info('Resources provisioned successfully', {
        tenantId: tenant.id
      });
    } catch (error) {
      this.logger.error('Failed to provision tenant resources', {
        tenantId: tenant.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Update tenant resource allocation
   */
  async updateTenantResources(tenant: TenantConfig): Promise<void> {
    this.logger.info('Updating tenant resources', {
      tenantId: tenant.id,
      tier: tenant.tier
    });

    try {
      for (const [providerName, provider] of this.providers) {
        await provider.updateResourceLimits(tenant.id, tenant.resources);
      }

      // Clear usage cache to force refresh
      this.usageCache.delete(tenant.id);

      this.logger.info('Tenant resources updated successfully', {
        tenantId: tenant.id
      });
    } catch (error) {
      this.logger.error('Failed to update tenant resources', {
        tenantId: tenant.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Suspend tenant resources
   */
  async suspendTenant(tenant: TenantConfig): Promise<void> {
    this.logger.info('Suspending tenant resources', {
      tenantId: tenant.id
    });

    try {
      for (const [providerName, provider] of this.providers) {
        await provider.suspendResources(tenant.id);
      }

      this.logger.info('Tenant resources suspended successfully', {
        tenantId: tenant.id
      });
    } catch (error) {
      this.logger.error('Failed to suspend tenant resources', {
        tenantId: tenant.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Resume tenant resources
   */
  async resumeTenant(tenant: TenantConfig): Promise<void> {
    this.logger.info('Resuming tenant resources', {
      tenantId: tenant.id
    });

    try {
      for (const [providerName, provider] of this.providers) {
        await provider.resumeResources(tenant.id);
      }

      this.logger.info('Tenant resources resumed successfully', {
        tenantId: tenant.id
      });
    } catch (error) {
      this.logger.error('Failed to resume tenant resources', {
        tenantId: tenant.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Deprovision tenant resources
   */
  async deprovisionTenant(tenant: TenantConfig): Promise<void> {
    this.logger.info('Deprovisioning tenant resources', {
      tenantId: tenant.id
    });

    try {
      for (const [providerName, provider] of this.providers) {
        await provider.deprovisionResources(tenant.id);
      }

      // Remove from cache
      this.usageCache.delete(tenant.id);

      this.logger.info('Tenant resources deprovisioned successfully', {
        tenantId: tenant.id
      });
    } catch (error) {
      this.logger.error('Failed to deprovision tenant resources', {
        tenantId: tenant.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get tenant resource usage
   */
  async getTenantUsage(tenantId: string): Promise<TenantResourceUsage> {
    // Check cache first
    const cached = this.usageCache.get(tenantId);
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      return cached.usage;
    }

    try {
      // Aggregate usage from all providers
      const usageData: any = {
        storage: 0,
        bandwidth: 0,
        apiCalls: 0,
        users: 0,
        projects: 0,
        databases: 0,
        cpu: 0,
        memory: 0
      };

      for (const [providerName, provider] of this.providers) {
        const providerUsage = await provider.getResourceUsage(tenantId);
        
        // Aggregate the usage
        usageData.storage += providerUsage.resources.storage || 0;
        usageData.bandwidth += providerUsage.resources.bandwidth || 0;
        usageData.apiCalls += providerUsage.resources.apiCalls || 0;
        usageData.users += providerUsage.resources.users || 0;
        usageData.projects += providerUsage.resources.projects || 0;
        usageData.databases += providerUsage.resources.databases || 0;
        usageData.cpu += providerUsage.resources.cpu || 0;
        usageData.memory += providerUsage.resources.memory || 0;
      }

      const usage: TenantResourceUsage = {
        tenantId: tenantId,
        timestamp: new Date(),
        resources: usageData
      };

      // Cache the result
      this.usageCache.set(tenantId, {
        usage: usage,
        timestamp: Date.now()
      });

      return usage;
    } catch (error) {
      this.logger.error('Failed to get tenant usage', {
        tenantId: tenantId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Check if tenant is within resource limits
   */
  async checkResourceLimits(tenant: TenantConfig): Promise<{
    withinLimits: boolean;
    violations: Array<{
      resource: string;
      limit: number;
      used: number;
      percentage: number;
    }>;
  }> {
    const usage = await this.getTenantUsage(tenant.id);
    const resources = tenant.resources;
    const violations: any[] = [];

    // Check each resource limit
    const resourceChecks = [
      { name: 'storage', limit: resources.storage.limit, used: usage.resources.storage },
      { name: 'bandwidth', limit: resources.bandwidth.limit, used: usage.resources.bandwidth },
      { name: 'apiCalls', limit: resources.apiCalls.limit, used: usage.resources.apiCalls },
      { name: 'users', limit: resources.users.limit, used: usage.resources.users },
      { name: 'projects', limit: resources.projects.limit, used: usage.resources.projects },
      { name: 'databases', limit: resources.databases.limit, used: usage.resources.databases },
      { name: 'cpu', limit: resources.compute.cpu.limit, used: usage.resources.cpu },
      { name: 'memory', limit: resources.compute.memory.limit, used: usage.resources.memory }
    ];

    for (const check of resourceChecks) {
      if (check.limit > 0 && check.used > check.limit) {
        violations.push({
          resource: check.name,
          limit: check.limit,
          used: check.used,
          percentage: (check.used / check.limit) * 100
        });
      }
    }

    return {
      withinLimits: violations.length === 0,
      violations: violations
    };
  }

  /**
   * Get resource utilization percentage
   */
  async getResourceUtilization(tenant: TenantConfig): Promise<Record<string, number>> {
    const usage = await this.getTenantUsage(tenant.id);
    const resources = tenant.resources;

    const utilization: Record<string, number> = {};

    const resourceChecks = [
      { name: 'storage', limit: resources.storage.limit, used: usage.resources.storage },
      { name: 'bandwidth', limit: resources.bandwidth.limit, used: usage.resources.bandwidth },
      { name: 'apiCalls', limit: resources.apiCalls.limit, used: usage.resources.apiCalls },
      { name: 'users', limit: resources.users.limit, used: usage.resources.users },
      { name: 'projects', limit: resources.projects.limit, used: usage.resources.projects },
      { name: 'databases', limit: resources.databases.limit, used: usage.resources.databases },
      { name: 'cpu', limit: resources.compute.cpu.limit, used: usage.resources.cpu },
      { name: 'memory', limit: resources.compute.memory.limit, used: usage.resources.memory }
    ];

    for (const check of resourceChecks) {
      if (check.limit > 0) {
        utilization[check.name] = Math.min((check.used / check.limit) * 100, 100);
      } else {
        utilization[check.name] = 0; // Unlimited
      }
    }

    return utilization;
  }

  /**
   * Scale tenant resources automatically
   */
  async autoScaleTenant(tenant: TenantConfig): Promise<boolean> {
    if (!this.config.provisioning.autoScale) {
      return false;
    }

    const utilization = await this.getResourceUtilization(tenant);
    const thresholds = this.config.provisioning.scaleThresholds;
    let scaled = false;

    // Check CPU utilization
    if (utilization.cpu > thresholds.cpu) {
      await this.scaleResource(tenant, 'cpu', 1.5);
      scaled = true;
    }

    // Check memory utilization
    if (utilization.memory > thresholds.memory) {
      await this.scaleResource(tenant, 'memory', 1.5);
      scaled = true;
    }

    if (scaled) {
      this.logger.info('Auto-scaled tenant resources', {
        tenantId: tenant.id,
        utilization: utilization
      });
    }

    return scaled;
  }

  /**
   * Private helper methods
   */
  private async provisionStorage(tenantId: string, allocation: number): Promise<void> {
    for (const [providerName, provider] of this.providers) {
      await provider.provisionStorage(tenantId, allocation);
    }
  }

  private async provisionDatabases(tenant: TenantConfig): Promise<void> {
    const dbConfig = {
      tier: tenant.tier,
      maxConnections: this.getDatabaseConnections(tenant.tier),
      storage: tenant.resources.storage.limit
    };

    for (const [providerName, provider] of this.providers) {
      await provider.provisionDatabase(tenant.id, dbConfig);
    }
  }

  private async provisionCompute(tenant: TenantConfig): Promise<void> {
    const computeConfig = {
      cpu: tenant.resources.compute.cpu.limit,
      memory: tenant.resources.compute.memory.limit,
      instances: tenant.resources.compute.instances.limit
    };

    for (const [providerName, provider] of this.providers) {
      await provider.provisionCompute(tenant.id, computeConfig);
    }
  }

  private async setupResourceMonitoring(tenant: TenantConfig): Promise<void> {
    // Setup monitoring alerts and dashboards
    this.logger.info('Setting up resource monitoring', {
      tenantId: tenant.id
    });
  }

  private async scaleResource(tenant: TenantConfig, resource: string, factor: number): Promise<void> {
    // Scale specific resource by factor
    this.logger.info('Scaling tenant resource', {
      tenantId: tenant.id,
      resource: resource,
      factor: factor
    });
  }

  private getDatabaseConnections(tier: TenantTier): number {
    const connections = {
      [TenantTier.FREE]: 5,
      [TenantTier.STARTER]: 20,
      [TenantTier.PRO]: 100,
      [TenantTier.ENTERPRISE]: 500,
      [TenantTier.CUSTOM]: 1000
    };

    return connections[tier] || 20;
  }

  /**
   * Cleanup expired cache entries
   */
  private cleanupCache(): void {
    const now = Date.now();
    for (const [tenantId, cached] of this.usageCache) {
      if (now - cached.timestamp > this.CACHE_TTL) {
        this.usageCache.delete(tenantId);
      }
    }
  }

  /**
   * Start periodic cache cleanup
   */
  startCacheCleanup(): void {
    setInterval(() => {
      this.cleanupCache();
    }, this.CACHE_TTL);
  }
}

import { TenantConfig } from '../types/tenant';
import { Logger } from '../utils/Logger';

export interface NotificationServiceConfig {
  email: {
    enabled: boolean;
    provider: 'sendgrid' | 'mailgun' | 'ses';
    apiKey: string;
    fromEmail: string;
    fromName: string;
  };
  slack: {
    enabled: boolean;
    webhookUrl?: string;
    botToken?: string;
  };
  webhook: {
    enabled: boolean;
    endpoints: Array<{
      url: string;
      secret: string;
      events: string[];
    }>;
  };
  templates: {
    tenantCreated: string;
    tenantUpdated: string;
    tenantDeleted: string;
    billingUpdated: string;
    resourceLimitExceeded: string;
  };
}

export interface NotificationPayload {
  type: string;
  tenant: TenantConfig;
  data?: any;
  timestamp: Date;
}

export class NotificationService {
  private config: NotificationServiceConfig;
  private logger: Logger;

  constructor(config: NotificationServiceConfig, logger: Logger) {
    this.config = config;
    this.logger = logger;
  }

  /**
   * Send tenant created notification
   */
  async sendTenantCreatedNotification(tenant: TenantConfig): Promise<void> {
    const payload: NotificationPayload = {
      type: 'tenant.created',
      tenant: tenant,
      timestamp: new Date()
    };

    await this.sendNotifications(payload);
  }

  /**
   * Send tenant updated notification
   */
  async sendTenantUpdatedNotification(
    updatedTenant: TenantConfig,
    previousTenant: TenantConfig
  ): Promise<void> {
    const payload: NotificationPayload = {
      type: 'tenant.updated',
      tenant: updatedTenant,
      data: {
        previous: previousTenant,
        changes: this.getChanges(previousTenant, updatedTenant)
      },
      timestamp: new Date()
    };

    await this.sendNotifications(payload);
  }

  /**
   * Send tenant deleted notification
   */
  async sendTenantDeletedNotification(tenant: TenantConfig): Promise<void> {
    const payload: NotificationPayload = {
      type: 'tenant.deleted',
      tenant: tenant,
      timestamp: new Date()
    };

    await this.sendNotifications(payload);
  }

  /**
   * Send billing updated notification
   */
  async sendBillingUpdatedNotification(tenant: TenantConfig, billingData: any): Promise<void> {
    const payload: NotificationPayload = {
      type: 'billing.updated',
      tenant: tenant,
      data: billingData,
      timestamp: new Date()
    };

    await this.sendNotifications(payload);
  }

  /**
   * Send resource limit exceeded notification
   */
  async sendResourceLimitExceededNotification(
    tenant: TenantConfig,
    resourceData: any
  ): Promise<void> {
    const payload: NotificationPayload = {
      type: 'resource.limit_exceeded',
      tenant: tenant,
      data: resourceData,
      timestamp: new Date()
    };

    await this.sendNotifications(payload);
  }

  /**
   * Send custom notification
   */
  async sendCustomNotification(
    type: string,
    tenant: TenantConfig,
    data?: any
  ): Promise<void> {
    const payload: NotificationPayload = {
      type: type,
      tenant: tenant,
      data: data,
      timestamp: new Date()
    };

    await this.sendNotifications(payload);
  }

  /**
   * Send notifications to all configured channels
   */
  private async sendNotifications(payload: NotificationPayload): Promise<void> {
    const promises: Promise<void>[] = [];

    // Send email notification
    if (this.config.email.enabled && this.shouldSendEmail(payload)) {
      promises.push(this.sendEmailNotification(payload));
    }

    // Send Slack notification
    if (this.config.slack.enabled && this.shouldSendSlack(payload)) {
      promises.push(this.sendSlackNotification(payload));
    }

    // Send webhook notifications
    if (this.config.webhook.enabled) {
      promises.push(this.sendWebhookNotifications(payload));
    }

    try {
      await Promise.allSettled(promises);
      this.logger.info('Notifications sent', {
        type: payload.type,
        tenantId: payload.tenant.id
      });
    } catch (error) {
      this.logger.error('Failed to send notifications', {
        type: payload.type,
        tenantId: payload.tenant.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Send email notification
   */
  private async sendEmailNotification(payload: NotificationPayload): Promise<void> {
    try {
      const emailContent = this.generateEmailContent(payload);
      
      // Implementation would depend on email provider
      switch (this.config.email.provider) {
        case 'sendgrid':
          await this.sendSendGridEmail(emailContent);
          break;
        case 'mailgun':
          await this.sendMailgunEmail(emailContent);
          break;
        case 'ses':
          await this.sendSESEmail(emailContent);
          break;
      }

      this.logger.debug('Email notification sent', {
        type: payload.type,
        tenantId: payload.tenant.id,
        recipient: payload.tenant.owner.email
      });
    } catch (error) {
      this.logger.error('Failed to send email notification', {
        type: payload.type,
        tenantId: payload.tenant.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Send Slack notification
   */
  private async sendSlackNotification(payload: NotificationPayload): Promise<void> {
    try {
      const slackMessage = this.generateSlackMessage(payload);
      
      if (this.config.slack.webhookUrl) {
        await this.sendSlackWebhook(slackMessage);
      } else if (this.config.slack.botToken) {
        await this.sendSlackBotMessage(slackMessage);
      }

      this.logger.debug('Slack notification sent', {
        type: payload.type,
        tenantId: payload.tenant.id
      });
    } catch (error) {
      this.logger.error('Failed to send Slack notification', {
        type: payload.type,
        tenantId: payload.tenant.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Send webhook notifications
   */
  private async sendWebhookNotifications(payload: NotificationPayload): Promise<void> {
    const promises = this.config.webhook.endpoints
      .filter(endpoint => endpoint.events.includes(payload.type))
      .map(endpoint => this.sendWebhookNotification(endpoint, payload));

    await Promise.allSettled(promises);
  }

  /**
   * Send individual webhook notification
   */
  private async sendWebhookNotification(
    endpoint: { url: string; secret: string; events: string[] },
    payload: NotificationPayload
  ): Promise<void> {
    try {
      const webhookPayload = {
        ...payload,
        webhook: {
          id: this.generateWebhookId(),
          timestamp: payload.timestamp.toISOString(),
          signature: this.generateWebhookSignature(payload, endpoint.secret)
        }
      };

      const response = await fetch(endpoint.url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Webhook-Signature': webhookPayload.webhook.signature,
          'X-Webhook-ID': webhookPayload.webhook.id,
          'X-Webhook-Timestamp': webhookPayload.webhook.timestamp
        },
        body: JSON.stringify(webhookPayload)
      });

      if (!response.ok) {
        throw new Error(`Webhook failed with status: ${response.status}`);
      }

      this.logger.debug('Webhook notification sent', {
        type: payload.type,
        tenantId: payload.tenant.id,
        endpoint: endpoint.url
      });
    } catch (error) {
      this.logger.error('Failed to send webhook notification', {
        type: payload.type,
        tenantId: payload.tenant.id,
        endpoint: endpoint.url,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Generate email content
   */
  private generateEmailContent(payload: NotificationPayload): {
    to: string;
    subject: string;
    html: string;
    text: string;
  } {
    const tenant = payload.tenant;
    
    let subject = '';
    let content = '';

    switch (payload.type) {
      case 'tenant.created':
        subject = `Welcome to ${tenant.displayName}!`;
        content = `Your tenant "${tenant.displayName}" has been successfully created.`;
        break;
      
      case 'tenant.updated':
        subject = `Tenant Updated: ${tenant.displayName}`;
        content = `Your tenant "${tenant.displayName}" has been updated.`;
        if (payload.data?.changes) {
          content += `\n\nChanges: ${payload.data.changes.join(', ')}`;
        }
        break;
      
      case 'tenant.deleted':
        subject = `Tenant Deleted: ${tenant.displayName}`;
        content = `Your tenant "${tenant.displayName}" has been deleted.`;
        break;
      
      case 'billing.updated':
        subject = `Billing Updated: ${tenant.displayName}`;
        content = `Billing information for "${tenant.displayName}" has been updated.`;
        break;
      
      case 'resource.limit_exceeded':
        subject = `Resource Limit Exceeded: ${tenant.displayName}`;
        content = `Resource limits have been exceeded for "${tenant.displayName}".`;
        break;
      
      default:
        subject = `Notification: ${tenant.displayName}`;
        content = `A notification has been generated for "${tenant.displayName}".`;
    }

    return {
      to: tenant.owner.email,
      subject: subject,
      html: this.generateHTMLEmail(subject, content, tenant),
      text: content
    };
  }

  /**
   * Generate Slack message
   */
  private generateSlackMessage(payload: NotificationPayload): any {
    const tenant = payload.tenant;
    
    let color = '#36a64f'; // Green
    let title = '';
    let text = '';

    switch (payload.type) {
      case 'tenant.created':
        color = '#36a64f';
        title = 'New Tenant Created';
        text = `Tenant "${tenant.displayName}" (${tenant.slug}) has been created`;
        break;
      
      case 'tenant.updated':
        color = '#ffaa00';
        title = 'Tenant Updated';
        text = `Tenant "${tenant.displayName}" (${tenant.slug}) has been updated`;
        break;
      
      case 'tenant.deleted':
        color = '#ff0000';
        title = 'Tenant Deleted';
        text = `Tenant "${tenant.displayName}" (${tenant.slug}) has been deleted`;
        break;
      
      case 'resource.limit_exceeded':
        color = '#ff0000';
        title = 'Resource Limit Exceeded';
        text = `Tenant "${tenant.displayName}" (${tenant.slug}) has exceeded resource limits`;
        break;
    }

    return {
      attachments: [
        {
          color: color,
          title: title,
          text: text,
          fields: [
            {
              title: 'Tenant ID',
              value: tenant.id,
              short: true
            },
            {
              title: 'Tier',
              value: tenant.tier,
              short: true
            },
            {
              title: 'Status',
              value: tenant.status,
              short: true
            },
            {
              title: 'Owner',
              value: tenant.owner.email,
              short: true
            }
          ],
          timestamp: payload.timestamp.toISOString()
        }
      ]
    };
  }

  /**
   * Helper methods
   */
  private shouldSendEmail(payload: NotificationPayload): boolean {
    return payload.tenant.settings.notifications.email;
  }

  private shouldSendSlack(payload: NotificationPayload): boolean {
    return payload.tenant.settings.notifications.slack;
  }

  private getChanges(previous: TenantConfig, current: TenantConfig): string[] {
    const changes: string[] = [];
    
    if (previous.name !== current.name) changes.push('name');
    if (previous.tier !== current.tier) changes.push('tier');
    if (previous.status !== current.status) changes.push('status');
    if (previous.domain !== current.domain) changes.push('domain');
    
    return changes;
  }

  private generateHTMLEmail(subject: string, content: string, tenant: TenantConfig): string {
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>${subject}</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h1 style="color: #2c3e50;">${subject}</h1>
            <p>${content}</p>
            <hr style="border: 1px solid #eee; margin: 20px 0;">
            <p style="font-size: 12px; color: #666;">
              This notification was sent for tenant: ${tenant.displayName} (${tenant.slug})
            </p>
          </div>
        </body>
      </html>
    `;
  }

  private generateWebhookId(): string {
    return `wh_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateWebhookSignature(payload: NotificationPayload, secret: string): string {
    // Implementation would use HMAC-SHA256
    const crypto = require('crypto');
    const hmac = crypto.createHmac('sha256', secret);
    hmac.update(JSON.stringify(payload));
    return `sha256=${hmac.digest('hex')}`;
  }

  private async sendSendGridEmail(content: any): Promise<void> {
    // SendGrid implementation
  }

  private async sendMailgunEmail(content: any): Promise<void> {
    // Mailgun implementation
  }

  private async sendSESEmail(content: any): Promise<void> {
    // AWS SES implementation
  }

  private async sendSlackWebhook(message: any): Promise<void> {
    // Slack webhook implementation
  }

  private async sendSlackBotMessage(message: any): Promise<void> {
    // Slack bot API implementation
  }
}

# Tenant Management Service

A comprehensive TypeScript service for managing multi-tenant applications with tenant provisioning, configuration, resource allocation, and Mixpanel integration.

## Features

- **🏢 Tenant Provisioning**: Complete tenant lifecycle management
- **⚙️ Configuration Management**: Flexible tenant settings and features
- **📊 Resource Allocation**: CPU, memory, storage, and API quotas
- **📈 Mixpanel Integration**: Automated analytics project setup
- **💳 Billing Integration**: Stripe-based subscription management
- **🔔 Notifications**: Email, Slack, and webhook notifications
- **📝 Audit Logging**: Complete event tracking and history
- **🎯 TypeScript**: Full type safety and IntelliSense support

## Installation

```bash
npm install @yourorg/tenant-management-service
```

## Quick Start

### Basic Setup

```typescript
import { Pool } from 'pg';
import { createTenantManagementService, TenantTier } from '@yourorg/tenant-management-service';

// Database connection
const pool = new Pool({
  host: 'localhost',
  database: 'tenant_management',
  user: 'postgres',
  password: 'password'
});

// Create service
const tenantService = createTenantManagementService({
  database: pool,
  config: {
    tenantManagement: {
      defaultTier: TenantTier.FREE,
      autoActivate: true,
      enableTrials: true,
      trialDurationDays: 14
    }
  }
});
```

### Create a Tenant

```typescript
const tenant = await tenantService.createTenant({
  name: 'Acme Corporation',
  slug: 'acme-corp',
  displayName: 'Acme Corp',
  tier: TenantTier.PRO,
  owner: {
    userId: 'user_123',
    email: '<EMAIL>',
    name: 'John Doe'
  },
  integrations: {
    mixpanel: {
      enabled: true,
      dataResidency: 'us',
      retentionDays: 365
    }
  }
});
```

## Core Components

### TenantManagementService

The main service that orchestrates all tenant operations:

```typescript
// Create tenant
const tenant = await tenantService.createTenant(request);

// Update tenant
const updated = await tenantService.updateTenant(tenantId, updates);

// Get tenant
const tenant = await tenantService.getTenant(tenantId);

// List tenants with filtering
const result = await tenantService.listTenants({
  status: [TenantStatus.ACTIVE],
  tier: [TenantTier.PRO, TenantTier.ENTERPRISE],
  search: 'acme',
  page: 1,
  limit: 20
});

// Activate/suspend tenant
await tenantService.activateTenant(tenantId);
await tenantService.suspendTenant(tenantId, 'Payment failed');

// Feature management
await tenantService.enableTenantFeature(tenantId, 'sso');
await tenantService.disableTenantFeature(tenantId, 'customDomain');
```

### MixpanelService

Automated Mixpanel project management:

```typescript
// Setup Mixpanel project for tenant
await mixpanelService.setupTenantProject(tenant);

// Update project configuration
await mixpanelService.updateTenantProject(tenant);

// Get analytics data
const analytics = await mixpanelService.getTenantAnalytics(tenant, {
  fromDate: '2023-01-01',
  toDate: '2023-12-31',
  events: ['User Signup', 'Feature Used']
});

// Track custom events
await mixpanelService.trackTenantEvent(tenant, 'Custom Event', {
  property1: 'value1',
  property2: 'value2'
});

// Delete project
await mixpanelService.deleteTenantProject(tenant);
```

### ResourceManager

Resource allocation and monitoring:

```typescript
// Provision resources for tenant
await resourceManager.provisionTenant(tenant);

// Get resource usage
const usage = await resourceManager.getTenantUsage(tenantId);

// Check resource limits
const check = await resourceManager.checkResourceLimits(tenant);
if (!check.withinLimits) {
  console.log('Resource violations:', check.violations);
}

// Get utilization percentages
const utilization = await resourceManager.getResourceUtilization(tenant);

// Auto-scale if needed
const scaled = await resourceManager.autoScaleTenant(tenant);
```

### BillingService

Stripe integration for subscription management:

```typescript
// Setup billing for tenant
await billingService.setupBilling(tenant);

// Get subscription info
const subscription = await billingService.getSubscriptionInfo(tenant);

// Get upcoming invoice
const invoice = await billingService.getUpcomingInvoice(tenant);

// Get invoice history
const invoices = await billingService.getInvoiceHistory(tenant, 10);

// Create payment setup intent
const { clientSecret } = await billingService.createSetupIntent(tenant);

// Handle webhooks
await billingService.handleWebhook(payload, signature);
```

## Configuration

### Tenant Tiers and Features

```typescript
const config = {
  tenantManagement: {
    defaultTier: TenantTier.FREE,
    autoActivate: true,
    enableTrials: true,
    trialDurationDays: 14,
    featureMatrix: {
      [TenantTier.FREE]: {
        analytics: true,
        apiAccess: false,
        customDomain: false,
        sso: false
      },
      [TenantTier.PRO]: {
        analytics: true,
        apiAccess: true,
        customDomain: true,
        sso: false
      },
      [TenantTier.ENTERPRISE]: {
        analytics: true,
        apiAccess: true,
        customDomain: true,
        sso: true
      }
    }
  }
};
```

### Resource Quotas

```typescript
const resourceConfig = {
  quotas: {
    [TenantTier.FREE]: {
      storage: { limit: 1000, unit: 'MB' },
      apiCalls: { limit: 1000, unit: 'requests', resetPeriod: 'monthly' },
      users: { limit: 5, unit: 'users' }
    },
    [TenantTier.PRO]: {
      storage: { limit: 100000, unit: 'MB' },
      apiCalls: { limit: 100000, unit: 'requests', resetPeriod: 'monthly' },
      users: { limit: 100, unit: 'users' }
    }
  }
};
```

### Mixpanel Configuration

```typescript
const mixpanelConfig = {
  apiBaseUrl: 'https://mixpanel.com/api',
  serviceAccountUsername: process.env.MIXPANEL_USERNAME,
  serviceAccountPassword: process.env.MIXPANEL_PASSWORD,
  organizationId: process.env.MIXPANEL_ORG_ID,
  defaultTimezone: 'UTC',
  defaultRetentionDays: 365
};
```

### Billing Configuration

```typescript
const billingConfig = {
  stripe: {
    secretKey: process.env.STRIPE_SECRET_KEY,
    webhookSecret: process.env.STRIPE_WEBHOOK_SECRET,
    defaultCurrency: 'USD'
  },
  plans: {
    [TenantTier.STARTER]: {
      priceId: 'price_starter_monthly',
      monthlyPrice: 29,
      yearlyPrice: 290,
      features: ['analytics', 'api_access']
    },
    [TenantTier.PRO]: {
      priceId: 'price_pro_monthly',
      monthlyPrice: 99,
      yearlyPrice: 990,
      features: ['analytics', 'api_access', 'custom_domain']
    }
  },
  trialDays: 14
};
```

## Event System

The service emits events for all tenant operations:

```typescript
tenantService.on('tenantEvent', (event) => {
  console.log(`Event: ${event.type} for tenant ${event.tenantId}`);
  
  switch (event.type) {
    case 'tenant.created':
      // Handle tenant creation
      break;
    case 'tenant.tier_changed':
      // Handle tier changes
      break;
    case 'tenant.feature_enabled':
      // Handle feature enablement
      break;
  }
});
```

### Available Events

- `tenant.created` - New tenant created
- `tenant.updated` - Tenant configuration updated
- `tenant.deleted` - Tenant deleted
- `tenant.activated` - Tenant activated
- `tenant.suspended` - Tenant suspended
- `tenant.tier_changed` - Tenant tier changed
- `tenant.feature_enabled` - Feature enabled
- `tenant.feature_disabled` - Feature disabled
- `tenant.integration_added` - Integration added
- `tenant.integration_updated` - Integration updated
- `tenant.integration_removed` - Integration removed
- `tenant.resource_limit_exceeded` - Resource limit exceeded
- `tenant.billing_updated` - Billing information updated

## Database Schema

The service requires the following PostgreSQL tables:

```sql
-- Tenants table
CREATE TABLE tenants (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  slug VARCHAR(255) UNIQUE NOT NULL,
  name VARCHAR(255) NOT NULL,
  display_name VARCHAR(255) NOT NULL,
  description TEXT,
  domain VARCHAR(255),
  subdomain VARCHAR(255),
  status VARCHAR(50) NOT NULL,
  tier VARCHAR(50) NOT NULL,
  owner JSONB NOT NULL,
  billing JSONB NOT NULL,
  features JSONB NOT NULL,
  resources JSONB NOT NULL,
  integrations JSONB NOT NULL,
  settings JSONB NOT NULL,
  metadata JSONB NOT NULL DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMP WITH TIME ZONE
);

-- Tenant events table
CREATE TABLE tenant_events (
  id VARCHAR(255) PRIMARY KEY,
  tenant_id UUID NOT NULL REFERENCES tenants(id),
  type VARCHAR(255) NOT NULL,
  data JSONB NOT NULL,
  timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
  user_id VARCHAR(255),
  source VARCHAR(255) NOT NULL
);

-- Tenant usage table
CREATE TABLE tenant_usage (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id),
  timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
  resources JSONB NOT NULL
);

-- Indexes
CREATE INDEX idx_tenants_slug ON tenants(slug);
CREATE INDEX idx_tenants_status ON tenants(status);
CREATE INDEX idx_tenants_tier ON tenants(tier);
CREATE INDEX idx_tenant_events_tenant_id ON tenant_events(tenant_id);
CREATE INDEX idx_tenant_events_type ON tenant_events(type);
CREATE INDEX idx_tenant_usage_tenant_id ON tenant_usage(tenant_id);
```

## Examples

See the `examples/` directory for complete working examples:

- `basicUsage.ts` - Complete tenant lifecycle demo
- `mixpanelIntegration.ts` - Mixpanel setup and analytics
- `billingManagement.ts` - Subscription and billing operations

## Environment Variables

```bash
# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=tenant_management
DB_USER=postgres
DB_PASSWORD=password

# Mixpanel
MIXPANEL_USERNAME=your_username
MIXPANEL_PASSWORD=your_password
MIXPANEL_ORG_ID=your_org_id

# Stripe
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
STRIPE_STARTER_PRICE_ID=price_...
STRIPE_PRO_PRICE_ID=price_...
STRIPE_ENTERPRISE_PRICE_ID=price_...

# Notifications
SENDGRID_API_KEY=SG...
FROM_EMAIL=<EMAIL>
FROM_NAME=Tenant Management
SLACK_WEBHOOK_URL=https://hooks.slack.com/...
```

## License

MIT

import express from 'express';
import { Pool } from 'pg';
import { Redis } from 'ioredis';
import winston from 'winston';

import { TenantManager } from '../src/core/TenantManager';
import { ConnectionRouter } from '../src/database/ConnectionRouter';
import { createTenantIdentificationMiddleware, requireTenant, validateTenantAccess } from '../src/middleware/tenantIdentification';
import { TenantAwareRateLimiter } from '../src/middleware/rateLimiting';
import { ExperimentRepository } from '../src/services/ExperimentRepository';
import { defaultMultiTenantConfig } from '../src/config/tenantConfig';

// Initialize logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'app.log' })
  ]
});

// Initialize database connection
const defaultPool = new Pool({
  host: defaultMultiTenantConfig.defaultDatabase.host,
  port: defaultMultiTenantConfig.defaultDatabase.port,
  database: defaultMultiTenantConfig.defaultDatabase.database,
  user: defaultMultiTenantConfig.defaultDatabase.username,
  password: defaultMultiTenantConfig.defaultDatabase.password,
  ssl: defaultMultiTenantConfig.defaultDatabase.ssl,
  max: defaultMultiTenantConfig.defaultDatabase.maxConnections
});

// Initialize Redis
const redis = new Redis({
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3
});

// Initialize core services
const tenantManager = new TenantManager(defaultPool, redis, logger);
const connectionRouter = new ConnectionRouter(tenantManager, logger, redis);
const rateLimiter = new TenantAwareRateLimiter(redis, tenantManager, logger);

// Initialize repositories
const experimentRepository = new ExperimentRepository(connectionRouter, logger);

// Create Express app
const app = express();

// Basic middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Request ID middleware
app.use((req, res, next) => {
  req.headers['x-request-id'] = req.headers['x-request-id'] || 
    `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  next();
});

// Tenant identification middleware
const tenantIdentification = createTenantIdentificationMiddleware(
  tenantManager,
  logger,
  {
    methods: defaultMultiTenantConfig.identification.methods,
    headerName: defaultMultiTenantConfig.identification.headerName,
    jwtSecret: defaultMultiTenantConfig.identification.jwtSecret,
    required: defaultMultiTenantConfig.identification.required
  }
);

app.use(tenantIdentification);

// Rate limiting middleware
app.use('/api', rateLimiter.createBurstProtection(20, 1000)); // Burst protection
app.use('/api', rateLimiter.createAdaptiveLimiter('requests')); // Adaptive rate limiting

// Health check endpoint (no tenant required)
app.get('/health', async (req, res) => {
  try {
    // Check database connectivity
    const dbClient = await defaultPool.connect();
    await dbClient.query('SELECT 1');
    dbClient.release();

    // Check Redis connectivity
    await redis.ping();

    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        database: 'healthy',
        redis: 'healthy',
        tenantManager: 'healthy'
      }
    });
  } catch (error) {
    logger.error('Health check failed', { error });
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Tenant-aware API routes
const apiRouter = express.Router();

// Require tenant context for all API routes
apiRouter.use(requireTenant);
apiRouter.use(validateTenantAccess(tenantManager));

// Experiments endpoints
apiRouter.get('/experiments', 
  rateLimiter.createLimiter('experiments'),
  async (req: any, res) => {
    try {
      const { page = 1, limit = 20, status, tags, search } = req.query;
      
      const filters = {
        status: status ? status.split(',') : undefined,
        tags: tags ? tags.split(',') : undefined,
        search
      };

      const result = await experimentRepository.findWithFilters(filters, {
        tenantContext: req.tenant,
        page: parseInt(page),
        limit: parseInt(limit)
      });

      res.json({
        success: true,
        data: result.data,
        pagination: result.pagination,
        meta: {
          tenantId: req.tenant.tenantId,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      logger.error('Failed to fetch experiments', { 
        error,
        tenantId: req.tenant?.tenantId 
      });
      
      res.status(500).json({
        success: false,
        error: {
          code: 'FETCH_EXPERIMENTS_FAILED',
          message: 'Failed to fetch experiments'
        }
      });
    }
  }
);

apiRouter.post('/experiments',
  rateLimiter.createLimiter('experiments'),
  async (req: any, res) => {
    try {
      const experimentData = req.body;
      
      const experiment = await experimentRepository.create(experimentData, {
        tenantContext: req.tenant
      });

      res.status(201).json({
        success: true,
        data: experiment,
        meta: {
          tenantId: req.tenant.tenantId,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      logger.error('Failed to create experiment', { 
        error,
        tenantId: req.tenant?.tenantId 
      });
      
      res.status(500).json({
        success: false,
        error: {
          code: 'CREATE_EXPERIMENT_FAILED',
          message: 'Failed to create experiment'
        }
      });
    }
  }
);

apiRouter.get('/experiments/:id',
  rateLimiter.createLimiter('experiments'),
  async (req: any, res) => {
    try {
      const { id } = req.params;
      
      const experiment = await experimentRepository.findById(id, {
        tenantContext: req.tenant
      });

      if (!experiment) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'EXPERIMENT_NOT_FOUND',
            message: 'Experiment not found'
          }
        });
      }

      res.json({
        success: true,
        data: experiment,
        meta: {
          tenantId: req.tenant.tenantId,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      logger.error('Failed to fetch experiment', { 
        error,
        tenantId: req.tenant?.tenantId,
        experimentId: req.params.id
      });
      
      res.status(500).json({
        success: false,
        error: {
          code: 'FETCH_EXPERIMENT_FAILED',
          message: 'Failed to fetch experiment'
        }
      });
    }
  }
);

apiRouter.put('/experiments/:id/status',
  rateLimiter.createLimiter('experiments'),
  async (req: any, res) => {
    try {
      const { id } = req.params;
      const { status } = req.body;
      
      const experiment = await experimentRepository.updateStatus(id, status, {
        tenantContext: req.tenant
      });

      if (!experiment) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'EXPERIMENT_NOT_FOUND',
            message: 'Experiment not found'
          }
        });
      }

      res.json({
        success: true,
        data: experiment,
        meta: {
          tenantId: req.tenant.tenantId,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      logger.error('Failed to update experiment status', { 
        error,
        tenantId: req.tenant?.tenantId,
        experimentId: req.params.id
      });
      
      res.status(500).json({
        success: false,
        error: {
          code: 'UPDATE_EXPERIMENT_FAILED',
          message: error instanceof Error ? error.message : 'Failed to update experiment'
        }
      });
    }
  }
);

// Tenant management endpoints (admin only)
apiRouter.get('/admin/tenants',
  async (req: any, res) => {
    try {
      // Check if user has admin permissions
      if (!req.tenant.permissions.includes('admin:tenants:read')) {
        return res.status(403).json({
          success: false,
          error: {
            code: 'INSUFFICIENT_PERMISSIONS',
            message: 'Admin permissions required'
          }
        });
      }

      const tenants = tenantManager.getAllTenants();
      
      res.json({
        success: true,
        data: tenants,
        meta: {
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      logger.error('Failed to fetch tenants', { error });
      
      res.status(500).json({
        success: false,
        error: {
          code: 'FETCH_TENANTS_FAILED',
          message: 'Failed to fetch tenants'
        }
      });
    }
  }
);

// Mount API router
app.use('/api/v1', apiRouter);

// Error handling middleware
app.use((error: any, req: any, res: any, next: any) => {
  logger.error('Unhandled error', { 
    error,
    tenantId: req.tenant?.tenantId,
    requestId: req.headers['x-request-id']
  });

  res.status(500).json({
    success: false,
    error: {
      code: 'INTERNAL_SERVER_ERROR',
      message: 'An unexpected error occurred'
    },
    requestId: req.headers['x-request-id']
  });
});

// Initialize and start server
async function startServer() {
  try {
    // Initialize tenant manager
    await tenantManager.initialize();
    
    const port = process.env.PORT || 3000;
    app.listen(port, () => {
      logger.info(`Multi-tenant A/B testing server started on port ${port}`);
    });

    // Graceful shutdown
    process.on('SIGTERM', async () => {
      logger.info('Received SIGTERM, shutting down gracefully');
      await tenantManager.cleanup();
      await connectionRouter.cleanup();
      await defaultPool.end();
      await redis.quit();
      process.exit(0);
    });

  } catch (error) {
    logger.error('Failed to start server', { error });
    process.exit(1);
  }
}

// Start the server
if (require.main === module) {
  startServer();
}

export { app, tenantManager, connectionRouter, rateLimiter };

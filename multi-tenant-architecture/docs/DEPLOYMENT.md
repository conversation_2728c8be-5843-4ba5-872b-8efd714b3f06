# Multi-Tenant A/B Testing Platform Deployment Guide

## Overview

This guide covers deploying the multi-tenant A/B testing platform in various environments, from development to production.

## Environment Setup

### Development Environment

```bash
# Clone the repository
git clone <repository-url>
cd multi-tenant-architecture

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env
# Edit .env with your configuration

# Start PostgreSQL and Redis
docker-compose up -d postgres redis

# Run database migrations
npm run migrate

# Start development server
npm run dev
```

### Production Environment

#### Docker Deployment

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DB_HOST=postgres
      - REDIS_HOST=redis
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: ab_testing
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

#### Kubernetes Deployment

```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: ab-testing

---
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
  namespace: ab-testing
data:
  NODE_ENV: "production"
  DB_HOST: "postgres-service"
  REDIS_HOST: "redis-service"
  ENABLE_RLS: "true"
  METRICS_ENABLED: "true"

---
# k8s/secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: app-secrets
  namespace: ab-testing
type: Opaque
data:
  DB_PASSWORD: <base64-encoded-password>
  JWT_SECRET: <base64-encoded-secret>
  REDIS_PASSWORD: <base64-encoded-password>

---
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ab-testing-app
  namespace: ab-testing
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ab-testing-app
  template:
    metadata:
      labels:
        app: ab-testing-app
    spec:
      containers:
      - name: app
        image: ab-testing:latest
        ports:
        - containerPort: 3000
        envFrom:
        - configMapRef:
            name: app-config
        - secretRef:
            name: app-secrets
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5

---
# k8s/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: ab-testing-service
  namespace: ab-testing
spec:
  selector:
    app: ab-testing-app
  ports:
  - port: 80
    targetPort: 3000
  type: ClusterIP

---
# k8s/ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ab-testing-ingress
  namespace: ab-testing
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/rate-limit: "100"
spec:
  tls:
  - hosts:
    - api.abtesting.com
    secretName: ab-testing-tls
  rules:
  - host: api.abtesting.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ab-testing-service
            port:
              number: 80
```

## Database Configuration

### Shared Database Setup

```sql
-- Create shared database with RLS
CREATE DATABASE ab_testing_shared;

-- Apply schema and RLS policies
\c ab_testing_shared
\i ab_testing_schema.sql
\i src/database/RowLevelSecurity.sql

-- Create tenant-specific users (optional)
CREATE USER tenant_user_1 WITH PASSWORD 'secure_password';
GRANT CONNECT ON DATABASE ab_testing_shared TO tenant_user_1;
GRANT USAGE ON SCHEMA public TO tenant_user_1;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO tenant_user_1;
```

### Dedicated Database Setup

```bash
#!/bin/bash
# scripts/create-tenant-database.sh

TENANT_ID=$1
DB_PASSWORD=$2

if [ -z "$TENANT_ID" ] || [ -z "$DB_PASSWORD" ]; then
    echo "Usage: $0 <tenant_id> <db_password>"
    exit 1
fi

# Create database
createdb "ab_testing_${TENANT_ID}"

# Apply schema
psql -d "ab_testing_${TENANT_ID}" -f ab_testing_schema.sql

# Create tenant user
psql -d "ab_testing_${TENANT_ID}" -c "
    CREATE USER tenant_${TENANT_ID} WITH PASSWORD '${DB_PASSWORD}';
    GRANT ALL PRIVILEGES ON DATABASE ab_testing_${TENANT_ID} TO tenant_${TENANT_ID};
    GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO tenant_${TENANT_ID};
    GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO tenant_${TENANT_ID};
"

echo "Database created for tenant: ${TENANT_ID}"
```

## Load Balancing & High Availability

### Nginx Configuration

```nginx
# nginx.conf
upstream ab_testing_backend {
    least_conn;
    server app1:3000 max_fails=3 fail_timeout=30s;
    server app2:3000 max_fails=3 fail_timeout=30s;
    server app3:3000 max_fails=3 fail_timeout=30s;
}

server {
    listen 80;
    server_name api.abtesting.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.abtesting.com;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $http_x_tenant_id zone=tenant:10m rate=100r/s;

    location / {
        limit_req zone=api burst=20 nodelay;
        limit_req zone=tenant burst=200 nodelay;

        proxy_pass http://ab_testing_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Tenant identification from subdomain
        if ($host ~* "^([^.]+)\.api\.abtesting\.com$") {
            set $tenant_id $1;
            proxy_set_header X-Tenant-ID $tenant_id;
        }
    }

    location /health {
        access_log off;
        proxy_pass http://ab_testing_backend;
    }
}
```

## Monitoring & Observability

### Prometheus Configuration

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'ab-testing'
    static_configs:
      - targets: ['app:3000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
```

### Grafana Dashboard

```json
{
  "dashboard": {
    "title": "Multi-Tenant A/B Testing Platform",
    "panels": [
      {
        "title": "Requests per Tenant",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{tenant_id}}"
          }
        ]
      },
      {
        "title": "Database Connections",
        "type": "graph",
        "targets": [
          {
            "expr": "pg_stat_database_numbackends",
            "legendFormat": "{{datname}}"
          }
        ]
      },
      {
        "title": "Rate Limit Violations",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(rate_limit_exceeded_total[5m])",
            "legendFormat": "{{tenant_id}}"
          }
        ]
      }
    ]
  }
}
```

## Security Considerations

### SSL/TLS Configuration

```bash
# Generate SSL certificates
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout ssl/key.pem \
    -out ssl/cert.pem \
    -subj "/C=US/ST=State/L=City/O=Organization/CN=api.abtesting.com"
```

### Environment Variables

```bash
# .env.production
NODE_ENV=production
PORT=3000

# Database
DB_HOST=postgres-cluster.internal
DB_PORT=5432
DB_NAME=ab_testing
DB_USER=app_user
DB_PASSWORD=<secure-password>
DB_SSL=true
DB_MAX_CONNECTIONS=20

# Redis
REDIS_HOST=redis-cluster.internal
REDIS_PORT=6379
REDIS_PASSWORD=<secure-password>

# Security
JWT_SECRET=<256-bit-secret>
ENCRYPTION_KEY=<256-bit-key>
ENABLE_RLS=true
AUDIT_LOGGING=true

# Rate Limiting
TENANT_REQUIRED=true
CACHE_ENABLED=true

# Monitoring
METRICS_ENABLED=true
HEALTH_CHECK_INTERVAL=30000
```

## Backup & Recovery

### Database Backup

```bash
#!/bin/bash
# scripts/backup-databases.sh

BACKUP_DIR="/backups/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# Backup shared database
pg_dump ab_testing_shared | gzip > "$BACKUP_DIR/shared.sql.gz"

# Backup tenant databases
for db in $(psql -t -c "SELECT datname FROM pg_database WHERE datname LIKE 'ab_testing_%'"); do
    pg_dump $db | gzip > "$BACKUP_DIR/${db}.sql.gz"
done

# Upload to S3 (optional)
aws s3 sync $BACKUP_DIR s3://ab-testing-backups/$(date +%Y%m%d)/
```

### Disaster Recovery

```bash
#!/bin/bash
# scripts/restore-database.sh

BACKUP_FILE=$1
TARGET_DB=$2

if [ -z "$BACKUP_FILE" ] || [ -z "$TARGET_DB" ]; then
    echo "Usage: $0 <backup_file> <target_database>"
    exit 1
fi

# Create database if it doesn't exist
createdb $TARGET_DB

# Restore from backup
gunzip -c $BACKUP_FILE | psql $TARGET_DB

echo "Database restored: $TARGET_DB"
```

## Performance Optimization

### Connection Pooling

```typescript
// config/database.ts
export const poolConfig = {
  // Shared database pool
  shared: {
    min: 5,
    max: 50,
    acquireTimeoutMillis: 30000,
    createTimeoutMillis: 30000,
    destroyTimeoutMillis: 5000,
    idleTimeoutMillis: 300000
  },
  
  // Dedicated database pools
  dedicated: {
    min: 2,
    max: 20,
    acquireTimeoutMillis: 30000,
    createTimeoutMillis: 30000,
    destroyTimeoutMillis: 5000,
    idleTimeoutMillis: 300000
  }
};
```

### Caching Strategy

```typescript
// config/cache.ts
export const cacheConfig = {
  tenantConfig: {
    ttl: 300, // 5 minutes
    maxSize: 1000
  },
  
  experimentData: {
    ttl: 60, // 1 minute
    maxSize: 5000
  },
  
  userAssignments: {
    ttl: 3600, // 1 hour
    maxSize: 10000
  }
};
```

# Tenant Migration Guide

## Overview

This guide covers migrating tenants between different isolation models and managing tenant data lifecycle.

## Migration Scenarios

### 1. Shared to Dedicated Database Migration

When a tenant grows and needs dedicated resources:

```typescript
// src/migration/SharedToDedicated.ts
import { TenantManager } from '../core/TenantManager';
import { ConnectionRouter } from '../database/ConnectionRouter';
import { Logger } from 'winston';

export class SharedToDedicatedMigration {
  private tenantManager: TenantManager;
  private connectionRouter: ConnectionRouter;
  private logger: Logger;

  constructor(
    tenantManager: TenantManager,
    connectionRouter: ConnectionRouter,
    logger: Logger
  ) {
    this.tenantManager = tenantManager;
    this.connectionRouter = connectionRouter;
    this.logger = logger;
  }

  async migrateTenant(tenantId: string, newDatabaseConfig: any): Promise<void> {
    this.logger.info('Starting shared to dedicated migration', { tenantId });

    try {
      // 1. Create new dedicated database
      await this.createDedicatedDatabase(tenantId, newDatabaseConfig);

      // 2. Export data from shared database
      const exportedData = await this.exportTenantData(tenantId);

      // 3. Import data to dedicated database
      await this.importTenantData(tenantId, exportedData, newDatabaseConfig);

      // 4. Verify data integrity
      await this.verifyDataIntegrity(tenantId, exportedData);

      // 5. Update tenant configuration
      await this.updateTenantConfig(tenantId, 'dedicated', newDatabaseConfig);

      // 6. Clean up shared database data
      await this.cleanupSharedData(tenantId);

      this.logger.info('Migration completed successfully', { tenantId });
    } catch (error) {
      this.logger.error('Migration failed', { tenantId, error });
      await this.rollbackMigration(tenantId);
      throw error;
    }
  }

  private async createDedicatedDatabase(
    tenantId: string,
    config: any
  ): Promise<void> {
    // Implementation for creating dedicated database
    // This would typically involve:
    // - Creating new database instance
    // - Applying schema
    // - Setting up user permissions
  }

  private async exportTenantData(tenantId: string): Promise<any> {
    const tables = [
      'experiments',
      'variants',
      'targeting_rules',
      'user_assignments',
      'events',
      'experiment_results'
    ];

    const exportedData: any = {};

    for (const table of tables) {
      const result = await this.connectionRouter.query(
        `SELECT * FROM ${table} WHERE tenant_id = $1`,
        [tenantId],
        { tenantId }
      );
      exportedData[table] = result.rows;
    }

    return exportedData;
  }

  private async importTenantData(
    tenantId: string,
    data: any,
    config: any
  ): Promise<void> {
    // Connect to new dedicated database
    // Import data table by table
    // Handle foreign key constraints
  }

  private async verifyDataIntegrity(
    tenantId: string,
    originalData: any
  ): Promise<void> {
    // Compare record counts
    // Verify key relationships
    // Check data consistency
  }

  private async updateTenantConfig(
    tenantId: string,
    tier: string,
    databaseConfig: any
  ): Promise<void> {
    await this.tenantManager.updateTenant(tenantId, {
      tier: tier as any,
      databaseConfig
    });
  }

  private async cleanupSharedData(tenantId: string): Promise<void> {
    const tables = [
      'experiment_results',
      'events',
      'user_assignments',
      'targeting_rules',
      'variants',
      'experiments'
    ];

    // Delete in reverse order to handle foreign keys
    for (const table of tables.reverse()) {
      await this.connectionRouter.query(
        `DELETE FROM ${table} WHERE tenant_id = $1`,
        [tenantId],
        { tenantId }
      );
    }
  }

  private async rollbackMigration(tenantId: string): Promise<void> {
    // Rollback logic
    // - Remove dedicated database if created
    // - Restore tenant configuration
    // - Log rollback actions
  }
}
```

### 2. Dedicated to Shared Database Migration

For cost optimization or consolidation:

```typescript
// src/migration/DedicatedToShared.ts
export class DedicatedToSharedMigration {
  async migrateTenant(tenantId: string): Promise<void> {
    this.logger.info('Starting dedicated to shared migration', { tenantId });

    try {
      // 1. Export data from dedicated database
      const exportedData = await this.exportFromDedicated(tenantId);

      // 2. Import data to shared database
      await this.importToShared(tenantId, exportedData);

      // 3. Verify data integrity
      await this.verifyDataIntegrity(tenantId, exportedData);

      // 4. Update tenant configuration
      await this.updateTenantConfig(tenantId, 'shared');

      // 5. Archive dedicated database
      await this.archiveDedicatedDatabase(tenantId);

      this.logger.info('Migration completed successfully', { tenantId });
    } catch (error) {
      this.logger.error('Migration failed', { tenantId, error });
      throw error;
    }
  }

  private async exportFromDedicated(tenantId: string): Promise<any> {
    // Export from dedicated database
  }

  private async importToShared(tenantId: string, data: any): Promise<void> {
    // Import to shared database with tenant_id
  }

  private async archiveDedicatedDatabase(tenantId: string): Promise<void> {
    // Create backup
    // Drop database
    // Update connection pools
  }
}
```

## Data Migration Tools

### Bulk Data Transfer

```typescript
// src/migration/BulkDataTransfer.ts
export class BulkDataTransfer {
  async transferTable(
    tableName: string,
    sourceConfig: any,
    targetConfig: any,
    tenantId: string,
    batchSize: number = 1000
  ): Promise<void> {
    let offset = 0;
    let hasMore = true;

    while (hasMore) {
      // Read batch from source
      const batch = await this.readBatch(
        tableName,
        sourceConfig,
        tenantId,
        offset,
        batchSize
      );

      if (batch.length === 0) {
        hasMore = false;
        break;
      }

      // Write batch to target
      await this.writeBatch(tableName, targetConfig, batch);

      offset += batchSize;
      
      this.logger.info('Transferred batch', {
        tableName,
        tenantId,
        offset,
        batchSize: batch.length
      });

      // Add delay to avoid overwhelming the database
      await this.delay(100);
    }
  }

  private async readBatch(
    tableName: string,
    config: any,
    tenantId: string,
    offset: number,
    limit: number
  ): Promise<any[]> {
    // Read batch with pagination
    const sql = `
      SELECT * FROM ${tableName} 
      WHERE tenant_id = $1 
      ORDER BY created_at 
      LIMIT $2 OFFSET $3
    `;
    
    const result = await this.executeQuery(config, sql, [tenantId, limit, offset]);
    return result.rows;
  }

  private async writeBatch(
    tableName: string,
    config: any,
    batch: any[]
  ): Promise<void> {
    if (batch.length === 0) return;

    const columns = Object.keys(batch[0]);
    const placeholders = batch.map((_, i) => 
      `(${columns.map((_, j) => `$${i * columns.length + j + 1}`).join(', ')})`
    ).join(', ');

    const values = batch.flatMap(row => columns.map(col => row[col]));

    const sql = `
      INSERT INTO ${tableName} (${columns.join(', ')}) 
      VALUES ${placeholders}
      ON CONFLICT (id) DO NOTHING
    `;

    await this.executeQuery(config, sql, values);
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
```

### Schema Migration

```typescript
// src/migration/SchemaMigration.ts
export class SchemaMigration {
  async applyMigrations(
    databaseConfig: any,
    migrations: string[]
  ): Promise<void> {
    for (const migration of migrations) {
      try {
        await this.executeMigration(databaseConfig, migration);
        this.logger.info('Applied migration', { migration });
      } catch (error) {
        this.logger.error('Migration failed', { migration, error });
        throw error;
      }
    }
  }

  private async executeMigration(
    config: any,
    migrationSql: string
  ): Promise<void> {
    // Execute migration SQL
    await this.executeQuery(config, migrationSql, []);
  }

  async createMigrationTable(databaseConfig: any): Promise<void> {
    const sql = `
      CREATE TABLE IF NOT EXISTS schema_migrations (
        id SERIAL PRIMARY KEY,
        version VARCHAR(255) NOT NULL UNIQUE,
        applied_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `;
    
    await this.executeQuery(databaseConfig, sql, []);
  }

  async recordMigration(
    databaseConfig: any,
    version: string
  ): Promise<void> {
    const sql = `
      INSERT INTO schema_migrations (version) 
      VALUES ($1) 
      ON CONFLICT (version) DO NOTHING
    `;
    
    await this.executeQuery(databaseConfig, sql, [version]);
  }
}
```

## Migration Monitoring

### Progress Tracking

```typescript
// src/migration/MigrationMonitor.ts
export class MigrationMonitor {
  private redis: Redis;
  private logger: Logger;

  async trackProgress(
    migrationId: string,
    tenantId: string,
    progress: {
      stage: string;
      completed: number;
      total: number;
      errors?: string[];
    }
  ): Promise<void> {
    const key = `migration:${migrationId}:${tenantId}`;
    
    await this.redis.hset(key, {
      stage: progress.stage,
      completed: progress.completed.toString(),
      total: progress.total.toString(),
      percentage: ((progress.completed / progress.total) * 100).toFixed(2),
      errors: JSON.stringify(progress.errors || []),
      updated_at: new Date().toISOString()
    });

    await this.redis.expire(key, 86400); // 24 hours

    this.logger.info('Migration progress updated', {
      migrationId,
      tenantId,
      ...progress
    });
  }

  async getProgress(
    migrationId: string,
    tenantId: string
  ): Promise<any> {
    const key = `migration:${migrationId}:${tenantId}`;
    const data = await this.redis.hgetall(key);
    
    if (!data.stage) {
      return null;
    }

    return {
      stage: data.stage,
      completed: parseInt(data.completed),
      total: parseInt(data.total),
      percentage: parseFloat(data.percentage),
      errors: JSON.parse(data.errors || '[]'),
      updated_at: data.updated_at
    };
  }
}
```

## Migration CLI Tools

### Command Line Interface

```typescript
// src/cli/migration.ts
import { Command } from 'commander';
import { SharedToDedicatedMigration } from '../migration/SharedToDedicated';

const program = new Command();

program
  .name('tenant-migration')
  .description('Tenant migration tools')
  .version('1.0.0');

program
  .command('shared-to-dedicated')
  .description('Migrate tenant from shared to dedicated database')
  .requiredOption('-t, --tenant <tenantId>', 'Tenant ID to migrate')
  .requiredOption('-h, --host <host>', 'New database host')
  .requiredOption('-d, --database <database>', 'New database name')
  .requiredOption('-u, --user <user>', 'Database user')
  .requiredOption('-p, --password <password>', 'Database password')
  .action(async (options) => {
    const migration = new SharedToDedicatedMigration(
      tenantManager,
      connectionRouter,
      logger
    );

    const databaseConfig = {
      host: options.host,
      database: options.database,
      username: options.user,
      password: options.password,
      port: 5432,
      ssl: true
    };

    try {
      await migration.migrateTenant(options.tenant, databaseConfig);
      console.log('Migration completed successfully');
    } catch (error) {
      console.error('Migration failed:', error);
      process.exit(1);
    }
  });

program
  .command('status')
  .description('Check migration status')
  .requiredOption('-m, --migration <migrationId>', 'Migration ID')
  .requiredOption('-t, --tenant <tenantId>', 'Tenant ID')
  .action(async (options) => {
    const monitor = new MigrationMonitor(redis, logger);
    const progress = await monitor.getProgress(options.migration, options.tenant);
    
    if (!progress) {
      console.log('No migration found');
      return;
    }

    console.log('Migration Status:');
    console.log(`  Stage: ${progress.stage}`);
    console.log(`  Progress: ${progress.completed}/${progress.total} (${progress.percentage}%)`);
    console.log(`  Updated: ${progress.updated_at}`);
    
    if (progress.errors.length > 0) {
      console.log('  Errors:');
      progress.errors.forEach((error: string) => {
        console.log(`    - ${error}`);
      });
    }
  });

program.parse();
```

## Best Practices

### Pre-Migration Checklist

1. **Backup Data**: Always create full backups before migration
2. **Test Migration**: Run migration on staging environment first
3. **Monitor Resources**: Ensure sufficient CPU, memory, and disk space
4. **Schedule Downtime**: Plan for maintenance windows
5. **Verify Integrity**: Check data consistency after migration

### Migration Safety

```typescript
// src/migration/SafetyChecks.ts
export class MigrationSafetyChecks {
  async performPreMigrationChecks(tenantId: string): Promise<boolean> {
    const checks = [
      this.checkDiskSpace(),
      this.checkDatabaseConnections(),
      this.checkBackupExists(tenantId),
      this.checkNoActiveExperiments(tenantId),
      this.checkDataConsistency(tenantId)
    ];

    const results = await Promise.all(checks);
    return results.every(result => result);
  }

  private async checkDiskSpace(): Promise<boolean> {
    // Check available disk space
    return true;
  }

  private async checkDatabaseConnections(): Promise<boolean> {
    // Verify database connectivity
    return true;
  }

  private async checkBackupExists(tenantId: string): Promise<boolean> {
    // Verify recent backup exists
    return true;
  }

  private async checkNoActiveExperiments(tenantId: string): Promise<boolean> {
    // Ensure no active experiments during migration
    const result = await this.connectionRouter.query(
      'SELECT COUNT(*) FROM experiments WHERE tenant_id = $1 AND status = $2',
      [tenantId, 'active'],
      { tenantId }
    );
    
    return parseInt(result.rows[0].count) === 0;
  }

  private async checkDataConsistency(tenantId: string): Promise<boolean> {
    // Verify data integrity before migration
    return true;
  }
}
```

### Rollback Procedures

```typescript
// src/migration/RollbackManager.ts
export class RollbackManager {
  async createRollbackPoint(
    tenantId: string,
    migrationId: string
  ): Promise<string> {
    const rollbackId = `rollback_${migrationId}_${Date.now()}`;
    
    // Create backup
    await this.createBackup(tenantId, rollbackId);
    
    // Store rollback metadata
    await this.storeRollbackMetadata(rollbackId, tenantId, migrationId);
    
    return rollbackId;
  }

  async executeRollback(rollbackId: string): Promise<void> {
    const metadata = await this.getRollbackMetadata(rollbackId);
    
    // Restore from backup
    await this.restoreFromBackup(metadata.tenantId, rollbackId);
    
    // Revert configuration changes
    await this.revertConfiguration(metadata.tenantId, metadata.migrationId);
    
    this.logger.info('Rollback completed', { rollbackId });
  }
}
```

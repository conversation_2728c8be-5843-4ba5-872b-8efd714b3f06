import { ConnectionRouter, QueryOptions } from '../database/ConnectionRouter';
import { TenantContext } from '../core/TenantManager';
import { Logger } from 'winston';

export interface RepositoryOptions {
  tenantContext: TenantContext;
  useReadReplica?: boolean;
  timeout?: number;
  retries?: number;
}

export abstract class TenantAwareRepository<T> {
  protected connectionRouter: ConnectionRouter;
  protected logger: Logger;
  protected tableName: string;

  constructor(
    connectionRouter: ConnectionRouter,
    logger: Logger,
    tableName: string
  ) {
    this.connectionRouter = connectionRouter;
    this.logger = logger;
    this.tableName = tableName;
  }

  /**
   * Find records by criteria with tenant isolation
   */
  async find(
    criteria: Partial<T> = {},
    options: RepositoryOptions
  ): Promise<T[]> {
    const whereClause = this.buildWhereClause(criteria, options.tenantContext);
    const sql = `SELECT * FROM ${this.tableName} ${whereClause.sql}`;
    
    const result = await this.connectionRouter.query(
      sql,
      whereClause.params,
      this.buildQueryOptions(options)
    );

    return result.rows;
  }

  /**
   * Find single record by ID with tenant isolation
   */
  async findById(
    id: string,
    options: RepositoryOptions
  ): Promise<T | null> {
    const sql = `
      SELECT * FROM ${this.tableName} 
      WHERE id = $1 AND tenant_id = $2
      LIMIT 1
    `;
    
    const result = await this.connectionRouter.query(
      sql,
      [id, options.tenantContext.tenantId],
      this.buildQueryOptions(options)
    );

    return result.rows[0] || null;
  }

  /**
   * Count records with tenant isolation
   */
  async count(
    criteria: Partial<T> = {},
    options: RepositoryOptions
  ): Promise<number> {
    const whereClause = this.buildWhereClause(criteria, options.tenantContext);
    const sql = `SELECT COUNT(*) as count FROM ${this.tableName} ${whereClause.sql}`;
    
    const result = await this.connectionRouter.query(
      sql,
      whereClause.params,
      this.buildQueryOptions(options)
    );

    return parseInt(result.rows[0].count, 10);
  }

  /**
   * Create new record with automatic tenant assignment
   */
  async create(
    data: Omit<T, 'id' | 'tenant_id' | 'created_at' | 'updated_at'>,
    options: RepositoryOptions
  ): Promise<T> {
    const fields = Object.keys(data);
    const values = Object.values(data);
    
    // Add tenant_id to the data
    fields.push('tenant_id');
    values.push(options.tenantContext.tenantId);

    const placeholders = values.map((_, index) => `$${index + 1}`).join(', ');
    const fieldsList = fields.join(', ');

    const sql = `
      INSERT INTO ${this.tableName} (${fieldsList})
      VALUES (${placeholders})
      RETURNING *
    `;

    const result = await this.connectionRouter.query(
      sql,
      values,
      this.buildQueryOptions(options)
    );

    this.logger.info(`Created ${this.tableName} record`, {
      tenantId: options.tenantContext.tenantId,
      recordId: result.rows[0].id
    });

    return result.rows[0];
  }

  /**
   * Update record with tenant isolation
   */
  async update(
    id: string,
    data: Partial<Omit<T, 'id' | 'tenant_id' | 'created_at'>>,
    options: RepositoryOptions
  ): Promise<T | null> {
    const fields = Object.keys(data);
    const values = Object.values(data);
    
    if (fields.length === 0) {
      throw new Error('No fields to update');
    }

    const setClause = fields.map((field, index) => `${field} = $${index + 1}`).join(', ');
    values.push(id, options.tenantContext.tenantId);

    const sql = `
      UPDATE ${this.tableName} 
      SET ${setClause}, updated_at = CURRENT_TIMESTAMP
      WHERE id = $${values.length - 1} AND tenant_id = $${values.length}
      RETURNING *
    `;

    const result = await this.connectionRouter.query(
      sql,
      values,
      this.buildQueryOptions(options)
    );

    if (result.rows.length === 0) {
      this.logger.warn(`${this.tableName} record not found for update`, {
        tenantId: options.tenantContext.tenantId,
        recordId: id
      });
      return null;
    }

    this.logger.info(`Updated ${this.tableName} record`, {
      tenantId: options.tenantContext.tenantId,
      recordId: id
    });

    return result.rows[0];
  }

  /**
   * Delete record with tenant isolation
   */
  async delete(
    id: string,
    options: RepositoryOptions
  ): Promise<boolean> {
    const sql = `
      DELETE FROM ${this.tableName} 
      WHERE id = $1 AND tenant_id = $2
    `;

    const result = await this.connectionRouter.query(
      sql,
      [id, options.tenantContext.tenantId],
      this.buildQueryOptions(options)
    );

    const deleted = result.rowCount > 0;
    
    if (deleted) {
      this.logger.info(`Deleted ${this.tableName} record`, {
        tenantId: options.tenantContext.tenantId,
        recordId: id
      });
    } else {
      this.logger.warn(`${this.tableName} record not found for deletion`, {
        tenantId: options.tenantContext.tenantId,
        recordId: id
      });
    }

    return deleted;
  }

  /**
   * Execute custom query with tenant context
   */
  async executeQuery<R = any>(
    sql: string,
    params: any[] = [],
    options: RepositoryOptions
  ): Promise<{ rows: R[]; rowCount: number }> {
    return this.connectionRouter.query(
      sql,
      params,
      this.buildQueryOptions(options)
    );
  }

  /**
   * Execute transaction with tenant context
   */
  async executeTransaction<R>(
    callback: (query: (sql: string, params?: any[]) => Promise<any>) => Promise<R>,
    options: RepositoryOptions
  ): Promise<R> {
    return this.connectionRouter.transaction(
      options.tenantContext.tenantId,
      async (client) => {
        // Set tenant context for the transaction
        await client.query('SELECT set_tenant_context($1)', [options.tenantContext.tenantId]);
        
        const query = (sql: string, params: any[] = []) => client.query(sql, params);
        return callback(query);
      }
    );
  }

  /**
   * Build WHERE clause with tenant isolation
   */
  protected buildWhereClause(
    criteria: Partial<T>,
    tenantContext: TenantContext
  ): { sql: string; params: any[] } {
    const conditions: string[] = ['tenant_id = $1'];
    const params: any[] = [tenantContext.tenantId];

    let paramIndex = 2;
    for (const [key, value] of Object.entries(criteria)) {
      if (value !== undefined && value !== null) {
        conditions.push(`${key} = $${paramIndex}`);
        params.push(value);
        paramIndex++;
      }
    }

    const sql = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
    return { sql, params };
  }

  /**
   * Build query options for connection router
   */
  protected buildQueryOptions(options: RepositoryOptions): QueryOptions {
    return {
      tenantId: options.tenantContext.tenantId,
      timeout: options.timeout,
      retries: options.retries,
      useReadReplica: options.useReadReplica
    };
  }

  /**
   * Validate tenant access for operation
   */
  protected validateTenantAccess(
    tenantContext: TenantContext,
    requiredPermissions: string[] = []
  ): void {
    if (!tenantContext.tenantId) {
      throw new Error('Tenant context is required');
    }

    // Check permissions if specified
    if (requiredPermissions.length > 0) {
      const hasPermission = requiredPermissions.some(permission =>
        tenantContext.permissions.includes(permission)
      );

      if (!hasPermission) {
        throw new Error(`Insufficient permissions. Required: ${requiredPermissions.join(', ')}`);
      }
    }
  }

  /**
   * Get paginated results with tenant isolation
   */
  async findPaginated(
    criteria: Partial<T> = {},
    options: RepositoryOptions & {
      page?: number;
      limit?: number;
      orderBy?: string;
      orderDirection?: 'ASC' | 'DESC';
    }
  ): Promise<{
    data: T[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    const page = options.page || 1;
    const limit = options.limit || 20;
    const offset = (page - 1) * limit;
    const orderBy = options.orderBy || 'created_at';
    const orderDirection = options.orderDirection || 'DESC';

    // Get total count
    const total = await this.count(criteria, options);

    // Get paginated data
    const whereClause = this.buildWhereClause(criteria, options.tenantContext);
    const sql = `
      SELECT * FROM ${this.tableName} 
      ${whereClause.sql}
      ORDER BY ${orderBy} ${orderDirection}
      LIMIT $${whereClause.params.length + 1} 
      OFFSET $${whereClause.params.length + 2}
    `;

    const result = await this.connectionRouter.query(
      sql,
      [...whereClause.params, limit, offset],
      this.buildQueryOptions(options)
    );

    return {
      data: result.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }
}

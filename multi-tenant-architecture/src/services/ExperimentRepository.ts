import { TenantAwareRepository, RepositoryOptions } from './TenantAwareRepository';
import { ConnectionRouter } from '../database/ConnectionRouter';
import { Logger } from 'winston';

export interface Experiment {
  id: string;
  tenant_id: string;
  name: string;
  description?: string;
  hypothesis?: string;
  status: 'draft' | 'active' | 'paused' | 'completed' | 'archived';
  traffic_allocation: number;
  assignment_method: 'random' | 'sticky' | 'deterministic';
  start_date?: Date;
  end_date?: Date;
  sample_size?: number;
  confidence_level: number;
  minimum_detectable_effect?: number;
  primary_metric?: string;
  secondary_metrics?: string[];
  tags?: string[];
  metadata: any;
  created_by?: string;
  created_at: Date;
  updated_at: Date;
}

export interface ExperimentFilters {
  status?: string[];
  tags?: string[];
  created_by?: string;
  start_date_from?: Date;
  start_date_to?: Date;
  search?: string;
}

export class ExperimentRepository extends TenantAwareRepository<Experiment> {
  constructor(connectionRouter: ConnectionRouter, logger: Logger) {
    super(connectionRouter, logger, 'experiments');
  }

  /**
   * Find experiments with advanced filtering
   */
  async findWithFilters(
    filters: ExperimentFilters,
    options: RepositoryOptions & {
      page?: number;
      limit?: number;
      orderBy?: string;
      orderDirection?: 'ASC' | 'DESC';
    }
  ): Promise<{
    data: Experiment[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    this.validateTenantAccess(options.tenantContext, ['experiments:read']);

    const page = options.page || 1;
    const limit = options.limit || 20;
    const offset = (page - 1) * limit;
    const orderBy = options.orderBy || 'created_at';
    const orderDirection = options.orderDirection || 'DESC';

    // Build dynamic WHERE clause
    const conditions: string[] = ['tenant_id = $1'];
    const params: any[] = [options.tenantContext.tenantId];
    let paramIndex = 2;

    if (filters.status && filters.status.length > 0) {
      conditions.push(`status = ANY($${paramIndex})`);
      params.push(filters.status);
      paramIndex++;
    }

    if (filters.tags && filters.tags.length > 0) {
      conditions.push(`tags && $${paramIndex}`);
      params.push(filters.tags);
      paramIndex++;
    }

    if (filters.created_by) {
      conditions.push(`created_by = $${paramIndex}`);
      params.push(filters.created_by);
      paramIndex++;
    }

    if (filters.start_date_from) {
      conditions.push(`start_date >= $${paramIndex}`);
      params.push(filters.start_date_from);
      paramIndex++;
    }

    if (filters.start_date_to) {
      conditions.push(`start_date <= $${paramIndex}`);
      params.push(filters.start_date_to);
      paramIndex++;
    }

    if (filters.search) {
      conditions.push(`(name ILIKE $${paramIndex} OR description ILIKE $${paramIndex})`);
      params.push(`%${filters.search}%`);
      paramIndex++;
    }

    const whereClause = `WHERE ${conditions.join(' AND ')}`;

    // Get total count
    const countSql = `SELECT COUNT(*) as count FROM ${this.tableName} ${whereClause}`;
    const countResult = await this.connectionRouter.query(
      countSql,
      params,
      this.buildQueryOptions(options)
    );
    const total = parseInt(countResult.rows[0].count, 10);

    // Get paginated data
    const dataSql = `
      SELECT * FROM ${this.tableName} 
      ${whereClause}
      ORDER BY ${orderBy} ${orderDirection}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    const dataResult = await this.connectionRouter.query(
      dataSql,
      [...params, limit, offset],
      this.buildQueryOptions(options)
    );

    return {
      data: dataResult.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  /**
   * Find active experiments for user assignment
   */
  async findActiveExperiments(
    options: RepositoryOptions
  ): Promise<Experiment[]> {
    this.validateTenantAccess(options.tenantContext, ['experiments:read']);

    const sql = `
      SELECT * FROM ${this.tableName}
      WHERE tenant_id = $1 
        AND status = 'active'
        AND (start_date IS NULL OR start_date <= CURRENT_TIMESTAMP)
        AND (end_date IS NULL OR end_date > CURRENT_TIMESTAMP)
      ORDER BY created_at DESC
    `;

    const result = await this.connectionRouter.query(
      sql,
      [options.tenantContext.tenantId],
      this.buildQueryOptions(options)
    );

    return result.rows;
  }

  /**
   * Get experiment statistics
   */
  async getExperimentStats(
    experimentId: string,
    options: RepositoryOptions
  ): Promise<{
    total_assignments: number;
    unique_users: number;
    total_events: number;
    conversion_rate: number;
    variants: Array<{
      variant_id: string;
      variant_name: string;
      assignments: number;
      events: number;
      conversion_rate: number;
    }>;
  }> {
    this.validateTenantAccess(options.tenantContext, ['experiments:read']);

    const sql = `
      WITH experiment_stats AS (
        SELECT 
          COUNT(ua.id) as total_assignments,
          COUNT(DISTINCT ua.user_id) as unique_users
        FROM user_assignments ua
        WHERE ua.experiment_id = $1 AND ua.tenant_id = $2
      ),
      event_stats AS (
        SELECT 
          COUNT(e.id) as total_events,
          COUNT(DISTINCT e.user_id)::DECIMAL / NULLIF(COUNT(DISTINCT ua.user_id), 0) as conversion_rate
        FROM events e
        LEFT JOIN user_assignments ua ON ua.experiment_id = e.experiment_id AND ua.user_id = e.user_id
        WHERE e.experiment_id = $1 AND e.tenant_id = $2
      ),
      variant_stats AS (
        SELECT 
          v.id as variant_id,
          v.name as variant_name,
          COUNT(ua.id) as assignments,
          COUNT(e.id) as events,
          COUNT(DISTINCT e.user_id)::DECIMAL / NULLIF(COUNT(DISTINCT ua.user_id), 0) as conversion_rate
        FROM variants v
        LEFT JOIN user_assignments ua ON ua.variant_id = v.id
        LEFT JOIN events e ON e.variant_id = v.id
        WHERE v.experiment_id = $1 AND v.tenant_id = $2
        GROUP BY v.id, v.name
      )
      SELECT 
        es.total_assignments,
        es.unique_users,
        evs.total_events,
        COALESCE(evs.conversion_rate, 0) as conversion_rate,
        json_agg(
          json_build_object(
            'variant_id', vs.variant_id,
            'variant_name', vs.variant_name,
            'assignments', vs.assignments,
            'events', vs.events,
            'conversion_rate', COALESCE(vs.conversion_rate, 0)
          )
        ) as variants
      FROM experiment_stats es
      CROSS JOIN event_stats evs
      CROSS JOIN variant_stats vs
      GROUP BY es.total_assignments, es.unique_users, evs.total_events, evs.conversion_rate
    `;

    const result = await this.connectionRouter.query(
      sql,
      [experimentId, options.tenantContext.tenantId],
      this.buildQueryOptions(options)
    );

    return result.rows[0] || {
      total_assignments: 0,
      unique_users: 0,
      total_events: 0,
      conversion_rate: 0,
      variants: []
    };
  }

  /**
   * Update experiment status with validation
   */
  async updateStatus(
    experimentId: string,
    newStatus: Experiment['status'],
    options: RepositoryOptions
  ): Promise<Experiment | null> {
    this.validateTenantAccess(options.tenantContext, ['experiments:write']);

    // Validate status transition
    const experiment = await this.findById(experimentId, options);
    if (!experiment) {
      throw new Error('Experiment not found');
    }

    const validTransitions: Record<string, string[]> = {
      'draft': ['active', 'archived'],
      'active': ['paused', 'completed', 'archived'],
      'paused': ['active', 'completed', 'archived'],
      'completed': ['archived'],
      'archived': []
    };

    if (!validTransitions[experiment.status].includes(newStatus)) {
      throw new Error(`Invalid status transition from ${experiment.status} to ${newStatus}`);
    }

    // Update status with timestamp tracking
    const updateData: any = { status: newStatus };
    
    if (newStatus === 'active' && !experiment.start_date) {
      updateData.start_date = new Date();
    }
    
    if (newStatus === 'completed' && !experiment.end_date) {
      updateData.end_date = new Date();
    }

    return this.update(experimentId, updateData, options);
  }

  /**
   * Duplicate experiment
   */
  async duplicate(
    experimentId: string,
    newName: string,
    options: RepositoryOptions
  ): Promise<Experiment> {
    this.validateTenantAccess(options.tenantContext, ['experiments:write']);

    return this.executeTransaction(async (query) => {
      // Get original experiment
      const originalResult = await query(
        `SELECT * FROM ${this.tableName} WHERE id = $1 AND tenant_id = $2`,
        [experimentId, options.tenantContext.tenantId]
      );

      if (originalResult.rows.length === 0) {
        throw new Error('Experiment not found');
      }

      const original = originalResult.rows[0];

      // Create new experiment
      const newExperiment = {
        ...original,
        name: newName,
        status: 'draft' as const,
        start_date: null,
        end_date: null
      };

      delete newExperiment.id;
      delete newExperiment.created_at;
      delete newExperiment.updated_at;

      const experimentResult = await query(
        `INSERT INTO ${this.tableName} (${Object.keys(newExperiment).join(', ')})
         VALUES (${Object.keys(newExperiment).map((_, i) => `$${i + 1}`).join(', ')})
         RETURNING *`,
        Object.values(newExperiment)
      );

      const newExperimentId = experimentResult.rows[0].id;

      // Duplicate variants
      await query(`
        INSERT INTO variants (tenant_id, experiment_id, name, description, is_control, traffic_weight, configuration)
        SELECT tenant_id, $1, name, description, is_control, traffic_weight, configuration
        FROM variants
        WHERE experiment_id = $2 AND tenant_id = $3
      `, [newExperimentId, experimentId, options.tenantContext.tenantId]);

      // Duplicate targeting rules
      await query(`
        INSERT INTO targeting_rules (tenant_id, experiment_id, name, attribute_name, operator, value_text, value_number, value_boolean, value_list, is_active, priority)
        SELECT tenant_id, $1, name, attribute_name, operator, value_text, value_number, value_boolean, value_list, is_active, priority
        FROM targeting_rules
        WHERE experiment_id = $2 AND tenant_id = $3
      `, [newExperimentId, experimentId, options.tenantContext.tenantId]);

      return experimentResult.rows[0];
    }, options);
  }

  /**
   * Archive old experiments
   */
  async archiveOldExperiments(
    daysOld: number,
    options: RepositoryOptions
  ): Promise<number> {
    this.validateTenantAccess(options.tenantContext, ['experiments:write']);

    const sql = `
      UPDATE ${this.tableName}
      SET status = 'archived', updated_at = CURRENT_TIMESTAMP
      WHERE tenant_id = $1 
        AND status = 'completed'
        AND end_date < CURRENT_TIMESTAMP - INTERVAL '${daysOld} days'
    `;

    const result = await this.connectionRouter.query(
      sql,
      [options.tenantContext.tenantId],
      this.buildQueryOptions(options)
    );

    this.logger.info(`Archived ${result.rowCount} old experiments`, {
      tenantId: options.tenantContext.tenantId,
      daysOld,
      archivedCount: result.rowCount
    });

    return result.rowCount;
  }
}

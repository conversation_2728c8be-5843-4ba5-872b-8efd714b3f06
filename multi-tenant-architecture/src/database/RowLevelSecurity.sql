-- Row-Level Security (RLS) Policies for Multi-Tenant A/B Testing Platform
-- This file contains PostgreSQL RLS policies to ensure tenant isolation

-- Enable RLS on all tenant-aware tables
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;
ALTER TABLE experiments ENABLE ROW LEVEL SECURITY;
ALTER TABLE variants ENABLE ROW LEVEL SECURITY;
ALTER TABLE targeting_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE events ENABLE ROW LEVEL SECURITY;
ALTER TABLE experiment_results ENABLE ROW LEVEL SECURITY;

-- Create function to get current tenant ID from application context
CREATE OR R<PERSON>LACE FUNCTION current_tenant_id() RETURNS UUID AS $$
BEGIN
  RETURN NULLIF(current_setting('app.current_tenant_id', true), '')::UUID;
EXCEPTION
  WHEN OTHERS THEN
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- <PERSON>reate function to check if user is super admin
CREATE OR REPLACE FUNCTION is_super_admin() R<PERSON>URNS BOOLEAN AS $$
BEGIN
  RETURN COALESCE(current_setting('app.is_super_admin', true)::BOOLEAN, false);
EXCEPTION
  WHEN OTHERS THEN
    RETURN false;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Tenants table policies
-- Super admins can see all tenants, regular users can only see their own tenant
CREATE POLICY tenant_isolation_policy ON tenants
  FOR ALL
  TO PUBLIC
  USING (
    is_super_admin() OR 
    id = current_tenant_id()
  );

-- Experiments table policies
CREATE POLICY experiment_tenant_isolation ON experiments
  FOR ALL
  TO PUBLIC
  USING (
    is_super_admin() OR 
    tenant_id = current_tenant_id()
  );

-- Variants table policies
CREATE POLICY variant_tenant_isolation ON variants
  FOR ALL
  TO PUBLIC
  USING (
    is_super_admin() OR 
    tenant_id = current_tenant_id()
  );

-- Targeting rules table policies
CREATE POLICY targeting_rule_tenant_isolation ON targeting_rules
  FOR ALL
  TO PUBLIC
  USING (
    is_super_admin() OR 
    tenant_id = current_tenant_id()
  );

-- User assignments table policies
CREATE POLICY user_assignment_tenant_isolation ON user_assignments
  FOR ALL
  TO PUBLIC
  USING (
    is_super_admin() OR 
    tenant_id = current_tenant_id()
  );

-- Events table policies
CREATE POLICY event_tenant_isolation ON events
  FOR ALL
  TO PUBLIC
  USING (
    is_super_admin() OR 
    tenant_id = current_tenant_id()
  );

-- Experiment results table policies
CREATE POLICY experiment_result_tenant_isolation ON experiment_results
  FOR ALL
  TO PUBLIC
  USING (
    is_super_admin() OR 
    tenant_id = current_tenant_id()
  );

-- Create indexes to optimize RLS policy performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_experiments_tenant_rls ON experiments(tenant_id) WHERE tenant_id IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_variants_tenant_rls ON variants(tenant_id) WHERE tenant_id IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_targeting_rules_tenant_rls ON targeting_rules(tenant_id) WHERE tenant_id IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_assignments_tenant_rls ON user_assignments(tenant_id) WHERE tenant_id IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_events_tenant_rls ON events(tenant_id) WHERE tenant_id IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_experiment_results_tenant_rls ON experiment_results(tenant_id) WHERE tenant_id IS NOT NULL;

-- Create function to set tenant context for a session
CREATE OR REPLACE FUNCTION set_tenant_context(tenant_uuid UUID, is_admin BOOLEAN DEFAULT false)
RETURNS VOID AS $$
BEGIN
  PERFORM set_config('app.current_tenant_id', tenant_uuid::TEXT, false);
  PERFORM set_config('app.is_super_admin', is_admin::TEXT, false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to clear tenant context
CREATE OR REPLACE FUNCTION clear_tenant_context()
RETURNS VOID AS $$
BEGIN
  PERFORM set_config('app.current_tenant_id', '', false);
  PERFORM set_config('app.is_super_admin', 'false', false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create view for tenant-aware experiment statistics
CREATE OR REPLACE VIEW tenant_experiment_stats AS
SELECT 
  e.tenant_id,
  t.name as tenant_name,
  t.slug as tenant_slug,
  COUNT(*) as total_experiments,
  COUNT(*) FILTER (WHERE e.status = 'active') as active_experiments,
  COUNT(*) FILTER (WHERE e.status = 'completed') as completed_experiments,
  COUNT(*) FILTER (WHERE e.status = 'draft') as draft_experiments,
  COUNT(*) FILTER (WHERE e.status = 'paused') as paused_experiments,
  COUNT(*) FILTER (WHERE e.status = 'archived') as archived_experiments,
  MIN(e.created_at) as first_experiment_date,
  MAX(e.created_at) as latest_experiment_date
FROM experiments e
JOIN tenants t ON e.tenant_id = t.id
WHERE (is_super_admin() OR e.tenant_id = current_tenant_id())
GROUP BY e.tenant_id, t.name, t.slug;

-- Create view for tenant-aware user assignment statistics
CREATE OR REPLACE VIEW tenant_assignment_stats AS
SELECT 
  ua.tenant_id,
  t.name as tenant_name,
  COUNT(*) as total_assignments,
  COUNT(DISTINCT ua.user_id) as unique_users,
  COUNT(DISTINCT ua.experiment_id) as experiments_with_assignments,
  MIN(ua.assignment_timestamp) as first_assignment_date,
  MAX(ua.assignment_timestamp) as latest_assignment_date
FROM user_assignments ua
JOIN tenants t ON ua.tenant_id = t.id
WHERE (is_super_admin() OR ua.tenant_id = current_tenant_id())
GROUP BY ua.tenant_id, t.name;

-- Create view for tenant-aware event statistics
CREATE OR REPLACE VIEW tenant_event_stats AS
SELECT 
  e.tenant_id,
  t.name as tenant_name,
  COUNT(*) as total_events,
  COUNT(DISTINCT e.user_id) as unique_users_with_events,
  COUNT(DISTINCT e.experiment_id) as experiments_with_events,
  COUNT(DISTINCT e.event_name) as unique_event_types,
  MIN(e.timestamp) as first_event_date,
  MAX(e.timestamp) as latest_event_date
FROM events e
JOIN tenants t ON e.tenant_id = t.id
WHERE (is_super_admin() OR e.tenant_id = current_tenant_id())
GROUP BY e.tenant_id, t.name;

-- Create function to validate tenant ownership of experiments
CREATE OR REPLACE FUNCTION validate_experiment_tenant_ownership(experiment_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
  experiment_tenant_id UUID;
BEGIN
  SELECT tenant_id INTO experiment_tenant_id 
  FROM experiments 
  WHERE id = experiment_uuid;
  
  IF experiment_tenant_id IS NULL THEN
    RETURN false;
  END IF;
  
  RETURN is_super_admin() OR experiment_tenant_id = current_tenant_id();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get tenant-specific configuration
CREATE OR REPLACE FUNCTION get_tenant_config(config_key TEXT)
RETURNS JSONB AS $$
DECLARE
  tenant_settings JSONB;
BEGIN
  SELECT settings INTO tenant_settings
  FROM tenants
  WHERE id = current_tenant_id();
  
  IF tenant_settings IS NULL THEN
    RETURN NULL;
  END IF;
  
  RETURN tenant_settings->config_key;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger function to automatically set tenant_id on insert
CREATE OR REPLACE FUNCTION set_tenant_id_on_insert()
RETURNS TRIGGER AS $$
BEGIN
  -- Only set tenant_id if it's not already provided and we have a current tenant
  IF NEW.tenant_id IS NULL AND current_tenant_id() IS NOT NULL THEN
    NEW.tenant_id := current_tenant_id();
  END IF;
  
  -- Validate that the tenant_id matches current context (unless super admin)
  IF NOT is_super_admin() AND NEW.tenant_id != current_tenant_id() THEN
    RAISE EXCEPTION 'Cannot insert record for different tenant. Expected: %, Got: %', 
      current_tenant_id(), NEW.tenant_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers to automatically set tenant_id on insert
CREATE TRIGGER set_tenant_id_experiments
  BEFORE INSERT ON experiments
  FOR EACH ROW
  EXECUTE FUNCTION set_tenant_id_on_insert();

CREATE TRIGGER set_tenant_id_variants
  BEFORE INSERT ON variants
  FOR EACH ROW
  EXECUTE FUNCTION set_tenant_id_on_insert();

CREATE TRIGGER set_tenant_id_targeting_rules
  BEFORE INSERT ON targeting_rules
  FOR EACH ROW
  EXECUTE FUNCTION set_tenant_id_on_insert();

CREATE TRIGGER set_tenant_id_user_assignments
  BEFORE INSERT ON user_assignments
  FOR EACH ROW
  EXECUTE FUNCTION set_tenant_id_on_insert();

CREATE TRIGGER set_tenant_id_events
  BEFORE INSERT ON events
  FOR EACH ROW
  EXECUTE FUNCTION set_tenant_id_on_insert();

CREATE TRIGGER set_tenant_id_experiment_results
  BEFORE INSERT ON experiment_results
  FOR EACH ROW
  EXECUTE FUNCTION set_tenant_id_on_insert();

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION current_tenant_id() TO PUBLIC;
GRANT EXECUTE ON FUNCTION is_super_admin() TO PUBLIC;
GRANT EXECUTE ON FUNCTION set_tenant_context(UUID, BOOLEAN) TO PUBLIC;
GRANT EXECUTE ON FUNCTION clear_tenant_context() TO PUBLIC;
GRANT EXECUTE ON FUNCTION validate_experiment_tenant_ownership(UUID) TO PUBLIC;
GRANT EXECUTE ON FUNCTION get_tenant_config(TEXT) TO PUBLIC;

-- Grant access to views
GRANT SELECT ON tenant_experiment_stats TO PUBLIC;
GRANT SELECT ON tenant_assignment_stats TO PUBLIC;
GRANT SELECT ON tenant_event_stats TO PUBLIC;

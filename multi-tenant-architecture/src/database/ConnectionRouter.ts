import { Pool, PoolClient } from 'pg';
import { TenantManager, TenantConfig } from '../core/TenantManager';
import { Logger } from 'winston';
import { Redis } from 'ioredis';

export interface QueryOptions {
  tenantId: string;
  timeout?: number;
  retries?: number;
  useReadReplica?: boolean;
}

export interface ConnectionMetrics {
  totalConnections: number;
  activeConnections: number;
  idleConnections: number;
  waitingClients: number;
}

export class ConnectionRouter {
  private tenantManager: TenantManager;
  private logger: Logger;
  private redis: Redis;
  private metrics: Map<string, ConnectionMetrics> = new Map();
  private healthCheckInterval: NodeJS.Timeout;

  constructor(
    tenantManager: TenantManager,
    logger: Logger,
    redis: Redis
  ) {
    this.tenantManager = tenantManager;
    this.logger = logger;
    this.redis = redis;

    // Start health check monitoring
    this.healthCheckInterval = setInterval(() => {
      this.performHealthChecks();
    }, 30000); // Every 30 seconds
  }

  /**
   * Execute query with automatic tenant routing
   */
  async query<T = any>(
    sql: string,
    params: any[] = [],
    options: QueryOptions
  ): Promise<{ rows: T[]; rowCount: number }> {
    const client = await this.getConnection(options.tenantId);
    
    try {
      const startTime = Date.now();
      
      // Set query timeout if specified
      if (options.timeout) {
        await client.query(`SET statement_timeout = ${options.timeout}`);
      }

      const result = await client.query(sql, params);
      
      const duration = Date.now() - startTime;
      this.logger.debug('Query executed', {
        tenantId: options.tenantId,
        duration,
        rowCount: result.rowCount
      });

      return result;
    } catch (error) {
      this.logger.error('Query execution failed', {
        tenantId: options.tenantId,
        error: error instanceof Error ? error.message : 'Unknown error',
        sql: sql.substring(0, 100) + '...'
      });

      // Retry logic for transient errors
      if (options.retries && options.retries > 0 && this.isRetryableError(error)) {
        this.logger.info('Retrying query', { tenantId: options.tenantId, retriesLeft: options.retries });
        return this.query(sql, params, { ...options, retries: options.retries - 1 });
      }

      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Execute transaction with automatic tenant routing
   */
  async transaction<T>(
    tenantId: string,
    callback: (client: PoolClient) => Promise<T>
  ): Promise<T> {
    const client = await this.getConnection(tenantId);
    
    try {
      await client.query('BEGIN');
      const result = await callback(client);
      await client.query('COMMIT');
      
      this.logger.debug('Transaction completed successfully', { tenantId });
      return result;
    } catch (error) {
      await client.query('ROLLBACK');
      this.logger.error('Transaction failed and rolled back', {
        tenantId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Get connection for specific tenant
   */
  private async getConnection(tenantId: string): Promise<PoolClient> {
    const config = this.tenantManager.getTenantConfig(tenantId);
    if (!config) {
      throw new Error(`Tenant configuration not found: ${tenantId}`);
    }

    // Check if tenant is active
    if (!config.isActive) {
      throw new Error(`Tenant is inactive: ${tenantId}`);
    }

    // Get connection from tenant manager
    return this.tenantManager.getConnection(tenantId);
  }

  /**
   * Get connection metrics for tenant
   */
  async getConnectionMetrics(tenantId: string): Promise<ConnectionMetrics> {
    const cached = this.metrics.get(tenantId);
    if (cached) {
      return cached;
    }

    const config = this.tenantManager.getTenantConfig(tenantId);
    if (!config) {
      throw new Error(`Tenant not found: ${tenantId}`);
    }

    // For shared database, get metrics from default pool
    // For dedicated database, get metrics from tenant-specific pool
    const metrics: ConnectionMetrics = {
      totalConnections: 0,
      activeConnections: 0,
      idleConnections: 0,
      waitingClients: 0
    };

    this.metrics.set(tenantId, metrics);
    return metrics;
  }

  /**
   * Perform health checks on all tenant connections
   */
  private async performHealthChecks(): Promise<void> {
    const tenants = this.tenantManager.getAllTenants();
    
    for (const tenant of tenants) {
      if (!tenant.isActive) continue;

      try {
        const client = await this.getConnection(tenant.id);
        await client.query('SELECT 1');
        client.release();

        // Update health status in Redis
        await this.redis.setex(`tenant:${tenant.id}:health`, 60, 'healthy');
        
        this.logger.debug('Health check passed', { tenantId: tenant.id });
      } catch (error) {
        this.logger.error('Health check failed', {
          tenantId: tenant.id,
          error: error instanceof Error ? error.message : 'Unknown error'
        });

        // Mark as unhealthy in Redis
        await this.redis.setex(`tenant:${tenant.id}:health`, 60, 'unhealthy');
      }
    }
  }

  /**
   * Check if error is retryable
   */
  private isRetryableError(error: any): boolean {
    if (!error || typeof error.code !== 'string') {
      return false;
    }

    // PostgreSQL error codes that are retryable
    const retryableCodes = [
      '53300', // too_many_connections
      '53400', // configuration_limit_exceeded
      '08000', // connection_exception
      '08003', // connection_does_not_exist
      '08006', // connection_failure
      '08001', // sqlclient_unable_to_establish_sqlconnection
      '08004', // sqlserver_rejected_establishment_of_sqlconnection
    ];

    return retryableCodes.includes(error.code);
  }

  /**
   * Get tenant health status
   */
  async getTenantHealth(tenantId: string): Promise<'healthy' | 'unhealthy' | 'unknown'> {
    try {
      const status = await this.redis.get(`tenant:${tenantId}:health`);
      return (status as 'healthy' | 'unhealthy') || 'unknown';
    } catch (error) {
      this.logger.error('Failed to get tenant health status', { tenantId, error });
      return 'unknown';
    }
  }

  /**
   * Execute batch queries for multiple tenants
   */
  async batchQuery<T = any>(
    queries: Array<{ sql: string; params: any[]; tenantId: string }>
  ): Promise<Array<{ tenantId: string; result?: { rows: T[]; rowCount: number }; error?: Error }>> {
    const results = await Promise.allSettled(
      queries.map(async ({ sql, params, tenantId }) => {
        const result = await this.query(sql, params, { tenantId });
        return { tenantId, result };
      })
    );

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          tenantId: queries[index].tenantId,
          error: result.reason
        };
      }
    });
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
    this.metrics.clear();
    this.logger.info('ConnectionRouter cleanup completed');
  }
}

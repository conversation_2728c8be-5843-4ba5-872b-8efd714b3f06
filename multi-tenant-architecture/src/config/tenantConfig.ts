export interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  ssl?: boolean;
  maxConnections: number;
  connectionTimeout: number;
  queryTimeout: number;
}

export interface TenantTierConfig {
  name: string;
  maxExperiments: number;
  maxVariants: number;
  maxEvents: number;
  maxUsers: number;
  retentionDays: number;
  features: string[];
  rateLimits: {
    requests: number;
    experiments: number;
    events: number;
  };
  sla: {
    uptime: number;
    responseTime: number;
    support: string;
  };
}

export interface MultiTenantConfig {
  // Database configuration
  defaultDatabase: DatabaseConfig;
  
  // Tenant identification
  identification: {
    methods: ('subdomain' | 'header' | 'jwt' | 'path')[];
    headerName: string;
    jwtSecret: string;
    defaultTenant?: string;
    required: boolean;
  };

  // Tenant tiers
  tiers: {
    shared: TenantTierConfig;
    dedicated: TenantTierConfig;
    enterprise: TenantTierConfig;
  };

  // Connection pooling
  connectionPool: {
    min: number;
    max: number;
    acquireTimeoutMillis: number;
    createTimeoutMillis: number;
    destroyTimeoutMillis: number;
    idleTimeoutMillis: number;
    reapIntervalMillis: number;
    createRetryIntervalMillis: number;
  };

  // Caching
  cache: {
    enabled: boolean;
    ttl: number;
    maxSize: number;
    tenantConfigTtl: number;
  };

  // Security
  security: {
    enableRLS: boolean;
    encryptionKey: string;
    auditLogging: boolean;
    ipWhitelist?: string[];
  };

  // Monitoring
  monitoring: {
    healthCheckInterval: number;
    metricsEnabled: boolean;
    alerting: {
      enabled: boolean;
      thresholds: {
        errorRate: number;
        responseTime: number;
        connectionPool: number;
      };
    };
  };

  // Migration
  migration: {
    enabled: boolean;
    batchSize: number;
    parallelJobs: number;
    retryAttempts: number;
  };
}

export const defaultMultiTenantConfig: MultiTenantConfig = {
  defaultDatabase: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    database: process.env.DB_NAME || 'ab_testing',
    username: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || '',
    ssl: process.env.DB_SSL === 'true',
    maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || '20'),
    connectionTimeout: parseInt(process.env.DB_CONNECTION_TIMEOUT || '30000'),
    queryTimeout: parseInt(process.env.DB_QUERY_TIMEOUT || '60000')
  },

  identification: {
    methods: ['jwt', 'header', 'subdomain'],
    headerName: 'X-Tenant-ID',
    jwtSecret: process.env.JWT_SECRET || 'your-secret-key',
    defaultTenant: process.env.DEFAULT_TENANT,
    required: process.env.TENANT_REQUIRED !== 'false'
  },

  tiers: {
    shared: {
      name: 'Shared',
      maxExperiments: 10,
      maxVariants: 5,
      maxEvents: 100000,
      maxUsers: 10000,
      retentionDays: 90,
      features: ['basic_analytics', 'email_support'],
      rateLimits: {
        requests: 1000,
        experiments: 50,
        events: 5000
      },
      sla: {
        uptime: 99.0,
        responseTime: 500,
        support: 'email'
      }
    },

    dedicated: {
      name: 'Dedicated',
      maxExperiments: 100,
      maxVariants: 10,
      maxEvents: 1000000,
      maxUsers: 100000,
      retentionDays: 365,
      features: ['advanced_analytics', 'api_access', 'priority_support'],
      rateLimits: {
        requests: 5000,
        experiments: 200,
        events: 25000
      },
      sla: {
        uptime: 99.5,
        responseTime: 200,
        support: 'chat'
      }
    },

    enterprise: {
      name: 'Enterprise',
      maxExperiments: -1, // unlimited
      maxVariants: -1,
      maxEvents: -1,
      maxUsers: -1,
      retentionDays: -1, // unlimited
      features: [
        'advanced_analytics',
        'api_access',
        'priority_support',
        'custom_integrations',
        'sso',
        'audit_logs',
        'white_label'
      ],
      rateLimits: {
        requests: 25000,
        experiments: 1000,
        events: 100000
      },
      sla: {
        uptime: 99.9,
        responseTime: 100,
        support: 'phone'
      }
    }
  },

  connectionPool: {
    min: 2,
    max: 20,
    acquireTimeoutMillis: 30000,
    createTimeoutMillis: 30000,
    destroyTimeoutMillis: 5000,
    idleTimeoutMillis: 300000,
    reapIntervalMillis: 1000,
    createRetryIntervalMillis: 200
  },

  cache: {
    enabled: process.env.CACHE_ENABLED !== 'false',
    ttl: parseInt(process.env.CACHE_TTL || '3600'),
    maxSize: parseInt(process.env.CACHE_MAX_SIZE || '1000'),
    tenantConfigTtl: parseInt(process.env.TENANT_CONFIG_TTL || '300')
  },

  security: {
    enableRLS: process.env.ENABLE_RLS !== 'false',
    encryptionKey: process.env.ENCRYPTION_KEY || 'your-encryption-key',
    auditLogging: process.env.AUDIT_LOGGING === 'true',
    ipWhitelist: process.env.IP_WHITELIST?.split(',')
  },

  monitoring: {
    healthCheckInterval: parseInt(process.env.HEALTH_CHECK_INTERVAL || '30000'),
    metricsEnabled: process.env.METRICS_ENABLED === 'true',
    alerting: {
      enabled: process.env.ALERTING_ENABLED === 'true',
      thresholds: {
        errorRate: parseFloat(process.env.ERROR_RATE_THRESHOLD || '0.05'),
        responseTime: parseInt(process.env.RESPONSE_TIME_THRESHOLD || '1000'),
        connectionPool: parseFloat(process.env.CONNECTION_POOL_THRESHOLD || '0.8')
      }
    }
  },

  migration: {
    enabled: process.env.MIGRATION_ENABLED === 'true',
    batchSize: parseInt(process.env.MIGRATION_BATCH_SIZE || '1000'),
    parallelJobs: parseInt(process.env.MIGRATION_PARALLEL_JOBS || '4'),
    retryAttempts: parseInt(process.env.MIGRATION_RETRY_ATTEMPTS || '3')
  }
};

/**
 * Validate multi-tenant configuration
 */
export function validateConfig(config: MultiTenantConfig): string[] {
  const errors: string[] = [];

  // Validate database config
  if (!config.defaultDatabase.host) {
    errors.push('Database host is required');
  }
  if (!config.defaultDatabase.database) {
    errors.push('Database name is required');
  }
  if (!config.defaultDatabase.username) {
    errors.push('Database username is required');
  }

  // Validate identification config
  if (config.identification.methods.length === 0) {
    errors.push('At least one tenant identification method is required');
  }
  if (config.identification.methods.includes('jwt') && !config.identification.jwtSecret) {
    errors.push('JWT secret is required when using JWT identification');
  }

  // Validate tier configs
  for (const [tierName, tierConfig] of Object.entries(config.tiers)) {
    if (!tierConfig.name) {
      errors.push(`Tier ${tierName} name is required`);
    }
    if (tierConfig.rateLimits.requests <= 0) {
      errors.push(`Tier ${tierName} request rate limit must be positive`);
    }
  }

  // Validate connection pool config
  if (config.connectionPool.min < 0) {
    errors.push('Connection pool min must be non-negative');
  }
  if (config.connectionPool.max <= config.connectionPool.min) {
    errors.push('Connection pool max must be greater than min');
  }

  return errors;
}

/**
 * Get tenant tier configuration
 */
export function getTierConfig(tier: string, config: MultiTenantConfig): TenantTierConfig | null {
  return config.tiers[tier as keyof typeof config.tiers] || null;
}

/**
 * Check if feature is enabled for tenant tier
 */
export function isFeatureEnabled(feature: string, tier: string, config: MultiTenantConfig): boolean {
  const tierConfig = getTierConfig(tier, config);
  return tierConfig?.features.includes(feature) || false;
}

/**
 * Get effective rate limits for tenant
 */
export function getEffectiveRateLimits(
  tier: string,
  customLimits: Partial<{ requests: number; experiments: number; events: number }>,
  config: MultiTenantConfig
): { requests: number; experiments: number; events: number } {
  const tierConfig = getTierConfig(tier, config);
  if (!tierConfig) {
    throw new Error(`Unknown tier: ${tier}`);
  }

  return {
    requests: customLimits.requests || tierConfig.rateLimits.requests,
    experiments: customLimits.experiments || tierConfig.rateLimits.experiments,
    events: customLimits.events || tierConfig.rateLimits.events
  };
}

/**
 * Create database configuration for tenant
 */
export function createTenantDatabaseConfig(
  tenantId: string,
  tier: string,
  customConfig?: Partial<DatabaseConfig>,
  baseConfig: MultiTenantConfig = defaultMultiTenantConfig
): DatabaseConfig {
  const base = baseConfig.defaultDatabase;
  
  if (tier === 'shared') {
    // Shared database - use default config
    return {
      ...base,
      ...customConfig
    };
  }

  // Dedicated database - create tenant-specific config
  return {
    ...base,
    database: `ab_testing_${tenantId}`,
    ...customConfig
  };
}

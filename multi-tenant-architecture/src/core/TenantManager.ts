import { Pool, PoolClient } from 'pg';
import { Redis } from 'ioredis';
import { Logger } from 'winston';

export interface TenantConfig {
  id: string;
  name: string;
  slug: string;
  tier: 'shared' | 'dedicated' | 'enterprise';
  databaseConfig?: {
    host: string;
    port: number;
    database: string;
    username: string;
    password: string;
    ssl?: boolean;
  };
  settings: {
    maxConnections: number;
    connectionTimeout: number;
    queryTimeout: number;
    enableRLS: boolean;
    cacheEnabled: boolean;
    rateLimits: {
      requests: number;
      experiments: number;
      events: number;
    };
  };
  features: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface TenantContext {
  tenantId: string;
  tenantSlug: string;
  tier: string;
  userId?: string;
  permissions: string[];
  features: string[];
}

export class TenantManager {
  private tenantConfigs: Map<string, TenantConfig> = new Map();
  private connectionPools: Map<string, Pool> = new Map();
  private redis: Redis;
  private logger: Logger;
  private defaultPool: Pool;

  constructor(
    defaultPool: Pool,
    redis: Redis,
    logger: Logger
  ) {
    this.defaultPool = defaultPool;
    this.redis = redis;
    this.logger = logger;
  }

  /**
   * Initialize tenant manager with tenant configurations
   */
  async initialize(): Promise<void> {
    try {
      await this.loadTenantConfigurations();
      await this.initializeConnectionPools();
      this.logger.info('TenantManager initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize TenantManager', { error });
      throw error;
    }
  }

  /**
   * Load tenant configurations from database
   */
  private async loadTenantConfigurations(): Promise<void> {
    const client = await this.defaultPool.connect();
    try {
      const result = await client.query(`
        SELECT id, name, slug, settings, is_active, created_at, updated_at
        FROM tenants 
        WHERE is_active = true
      `);

      for (const row of result.rows) {
        const config: TenantConfig = {
          id: row.id,
          name: row.name,
          slug: row.slug,
          tier: row.settings.tier || 'shared',
          databaseConfig: row.settings.database,
          settings: {
            maxConnections: row.settings.maxConnections || 10,
            connectionTimeout: row.settings.connectionTimeout || 30000,
            queryTimeout: row.settings.queryTimeout || 60000,
            enableRLS: row.settings.enableRLS !== false,
            cacheEnabled: row.settings.cacheEnabled !== false,
            rateLimits: row.settings.rateLimits || {
              requests: 1000,
              experiments: 100,
              events: 10000
            }
          },
          features: row.settings.features || [],
          isActive: row.is_active,
          createdAt: row.created_at,
          updatedAt: row.updated_at
        };

        this.tenantConfigs.set(config.id, config);
        this.tenantConfigs.set(config.slug, config);
      }

      this.logger.info(`Loaded ${result.rows.length} tenant configurations`);
    } finally {
      client.release();
    }
  }

  /**
   * Initialize connection pools for dedicated database tenants
   */
  private async initializeConnectionPools(): Promise<void> {
    for (const [tenantId, config] of this.tenantConfigs) {
      if (config.tier === 'dedicated' && config.databaseConfig) {
        const pool = new Pool({
          host: config.databaseConfig.host,
          port: config.databaseConfig.port,
          database: config.databaseConfig.database,
          user: config.databaseConfig.username,
          password: config.databaseConfig.password,
          ssl: config.databaseConfig.ssl,
          max: config.settings.maxConnections,
          connectionTimeoutMillis: config.settings.connectionTimeout,
          query_timeout: config.settings.queryTimeout,
          application_name: `ab-testing-${config.slug}`
        });

        // Test connection
        try {
          const client = await pool.connect();
          client.release();
          this.connectionPools.set(tenantId, pool);
          this.logger.info(`Initialized dedicated pool for tenant ${config.slug}`);
        } catch (error) {
          this.logger.error(`Failed to initialize pool for tenant ${config.slug}`, { error });
        }
      }
    }
  }

  /**
   * Get tenant configuration by ID or slug
   */
  getTenantConfig(identifier: string): TenantConfig | null {
    return this.tenantConfigs.get(identifier) || null;
  }

  /**
   * Get database connection for tenant
   */
  async getConnection(tenantId: string): Promise<PoolClient> {
    const config = this.getTenantConfig(tenantId);
    if (!config) {
      throw new Error(`Tenant not found: ${tenantId}`);
    }

    if (config.tier === 'dedicated') {
      const pool = this.connectionPools.get(tenantId);
      if (!pool) {
        throw new Error(`No connection pool for tenant: ${tenantId}`);
      }
      return pool.connect();
    }

    // Use shared database with tenant context
    const client = await this.defaultPool.connect();
    
    // Set tenant context for RLS
    if (config.settings.enableRLS) {
      await client.query('SET app.current_tenant_id = $1', [tenantId]);
    }

    return client;
  }

  /**
   * Create tenant context from request
   */
  createTenantContext(
    tenantId: string,
    userId?: string,
    permissions: string[] = []
  ): TenantContext {
    const config = this.getTenantConfig(tenantId);
    if (!config) {
      throw new Error(`Tenant not found: ${tenantId}`);
    }

    return {
      tenantId: config.id,
      tenantSlug: config.slug,
      tier: config.tier,
      userId,
      permissions,
      features: config.features
    };
  }

  /**
   * Validate tenant access
   */
  validateTenantAccess(tenantId: string, userId: string): boolean {
    const config = this.getTenantConfig(tenantId);
    if (!config || !config.isActive) {
      return false;
    }

    // Add additional validation logic here
    // e.g., check user membership, subscription status, etc.
    
    return true;
  }

  /**
   * Get tenant rate limits
   */
  getTenantRateLimits(tenantId: string): any {
    const config = this.getTenantConfig(tenantId);
    return config?.settings.rateLimits || {
      requests: 100,
      experiments: 10,
      events: 1000
    };
  }

  /**
   * Refresh tenant configurations
   */
  async refreshTenantConfigurations(): Promise<void> {
    this.tenantConfigs.clear();
    await this.loadTenantConfigurations();
    this.logger.info('Tenant configurations refreshed');
  }

  /**
   * Add new tenant configuration
   */
  async addTenant(config: Omit<TenantConfig, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const client = await this.defaultPool.connect();
    try {
      const result = await client.query(`
        INSERT INTO tenants (name, slug, settings, is_active)
        VALUES ($1, $2, $3, $4)
        RETURNING id, created_at, updated_at
      `, [
        config.name,
        config.slug,
        JSON.stringify({
          tier: config.tier,
          database: config.databaseConfig,
          ...config.settings,
          features: config.features
        }),
        config.isActive
      ]);

      const tenantId = result.rows[0].id;
      const fullConfig: TenantConfig = {
        ...config,
        id: tenantId,
        createdAt: result.rows[0].created_at,
        updatedAt: result.rows[0].updated_at
      };

      this.tenantConfigs.set(tenantId, fullConfig);
      this.tenantConfigs.set(config.slug, fullConfig);

      // Initialize connection pool if dedicated
      if (config.tier === 'dedicated' && config.databaseConfig) {
        await this.initializeTenantPool(tenantId, fullConfig);
      }

      this.logger.info(`Added new tenant: ${config.slug}`, { tenantId });
      return tenantId;
    } finally {
      client.release();
    }
  }

  /**
   * Initialize connection pool for a specific tenant
   */
  private async initializeTenantPool(tenantId: string, config: TenantConfig): Promise<void> {
    if (!config.databaseConfig) return;

    const pool = new Pool({
      host: config.databaseConfig.host,
      port: config.databaseConfig.port,
      database: config.databaseConfig.database,
      user: config.databaseConfig.username,
      password: config.databaseConfig.password,
      ssl: config.databaseConfig.ssl,
      max: config.settings.maxConnections,
      connectionTimeoutMillis: config.settings.connectionTimeout,
      query_timeout: config.settings.queryTimeout,
      application_name: `ab-testing-${config.slug}`
    });

    try {
      const client = await pool.connect();
      client.release();
      this.connectionPools.set(tenantId, pool);
      this.logger.info(`Initialized dedicated pool for tenant ${config.slug}`);
    } catch (error) {
      this.logger.error(`Failed to initialize pool for tenant ${config.slug}`, { error });
      throw error;
    }
  }

  /**
   * Update tenant configuration
   */
  async updateTenant(tenantId: string, updates: Partial<TenantConfig>): Promise<void> {
    const client = await this.defaultPool.connect();
    try {
      const currentConfig = this.getTenantConfig(tenantId);
      if (!currentConfig) {
        throw new Error(`Tenant not found: ${tenantId}`);
      }

      const updatedConfig = { ...currentConfig, ...updates };

      await client.query(`
        UPDATE tenants
        SET name = $1, slug = $2, settings = $3, is_active = $4, updated_at = CURRENT_TIMESTAMP
        WHERE id = $5
      `, [
        updatedConfig.name,
        updatedConfig.slug,
        JSON.stringify({
          tier: updatedConfig.tier,
          database: updatedConfig.databaseConfig,
          ...updatedConfig.settings,
          features: updatedConfig.features
        }),
        updatedConfig.isActive,
        tenantId
      ]);

      // Update in-memory cache
      this.tenantConfigs.set(tenantId, updatedConfig);
      this.tenantConfigs.set(updatedConfig.slug, updatedConfig);

      this.logger.info(`Updated tenant: ${updatedConfig.slug}`, { tenantId });
    } finally {
      client.release();
    }
  }

  /**
   * Remove tenant
   */
  async removeTenant(tenantId: string): Promise<void> {
    const config = this.getTenantConfig(tenantId);
    if (!config) {
      throw new Error(`Tenant not found: ${tenantId}`);
    }

    // Close connection pool if exists
    const pool = this.connectionPools.get(tenantId);
    if (pool) {
      await pool.end();
      this.connectionPools.delete(tenantId);
    }

    // Remove from database
    const client = await this.defaultPool.connect();
    try {
      await client.query('DELETE FROM tenants WHERE id = $1', [tenantId]);
    } finally {
      client.release();
    }

    // Remove from cache
    this.tenantConfigs.delete(tenantId);
    this.tenantConfigs.delete(config.slug);

    this.logger.info(`Removed tenant: ${config.slug}`, { tenantId });
  }

  /**
   * Get all tenant configurations
   */
  getAllTenants(): TenantConfig[] {
    const tenants: TenantConfig[] = [];
    const seen = new Set<string>();

    for (const [key, config] of this.tenantConfigs) {
      if (!seen.has(config.id)) {
        tenants.push(config);
        seen.add(config.id);
      }
    }

    return tenants;
  }

  /**
   * Get tenant statistics
   */
  async getTenantStats(tenantId: string): Promise<any> {
    const client = await this.getConnection(tenantId);
    try {
      const stats = await client.query(`
        SELECT
          (SELECT COUNT(*) FROM experiments WHERE tenant_id = $1) as total_experiments,
          (SELECT COUNT(*) FROM experiments WHERE tenant_id = $1 AND status = 'active') as active_experiments,
          (SELECT COUNT(*) FROM user_assignments WHERE tenant_id = $1) as total_assignments,
          (SELECT COUNT(*) FROM events WHERE tenant_id = $1) as total_events
      `, [tenantId]);

      return stats.rows[0];
    } finally {
      client.release();
    }
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    for (const [tenantId, pool] of this.connectionPools) {
      await pool.end();
      this.logger.info(`Closed connection pool for tenant ${tenantId}`);
    }
    this.connectionPools.clear();
  }
}

import { Request, Response, NextFunction } from 'express';
import { Redis } from 'ioredis';
import { TenantManager } from '../core/TenantManager';
import { Logger } from 'winston';

export interface TenantRequest extends Request {
  tenant?: {
    tenantId: string;
    tenantSlug: string;
    tier: string;
  };
}

export interface RateLimitOptions {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  keyGenerator?: (req: TenantRequest) => string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  onLimitReached?: (req: TenantRequest, res: Response) => void;
}

export class TenantAwareRateLimiter {
  private redis: Redis;
  private tenantManager: TenantManager;
  private logger: Logger;

  constructor(redis: Redis, tenantManager: TenantManager, logger: Logger) {
    this.redis = redis;
    this.tenantManager = tenantManager;
    this.logger = logger;
  }

  /**
   * Create rate limiting middleware with tenant-specific limits
   */
  createLimiter(
    limitType: 'requests' | 'experiments' | 'events',
    options: Partial<RateLimitOptions> = {}
  ) {
    return async (req: TenantRequest, res: Response, next: NextFunction): Promise<void> => {
      try {
        if (!req.tenant) {
          // If no tenant context, apply global rate limit
          return this.applyGlobalRateLimit(req, res, next, limitType, options);
        }

        const tenantLimits = this.tenantManager.getTenantRateLimits(req.tenant.tenantId);
        const limit = tenantLimits[limitType] || this.getDefaultLimit(limitType);
        
        const windowMs = options.windowMs || 3600000; // 1 hour default
        const key = this.generateKey(req, limitType, options.keyGenerator);
        
        const isAllowed = await this.checkRateLimit(key, limit, windowMs);
        
        if (!isAllowed) {
          this.logger.warn('Rate limit exceeded', {
            tenantId: req.tenant.tenantId,
            limitType,
            key,
            limit
          });

          if (options.onLimitReached) {
            options.onLimitReached(req, res);
            return;
          }

          return this.sendRateLimitResponse(res, limit, windowMs);
        }

        // Add rate limit headers
        const remaining = await this.getRemainingRequests(key, limit, windowMs);
        const resetTime = await this.getResetTime(key, windowMs);

        res.set({
          'X-RateLimit-Limit': limit.toString(),
          'X-RateLimit-Remaining': remaining.toString(),
          'X-RateLimit-Reset': resetTime.toString(),
          'X-RateLimit-Window': windowMs.toString()
        });

        next();
      } catch (error) {
        this.logger.error('Rate limiting error', { error });
        // On error, allow the request to proceed
        next();
      }
    };
  }

  /**
   * Check if request is within rate limit
   */
  private async checkRateLimit(
    key: string,
    limit: number,
    windowMs: number
  ): Promise<boolean> {
    const pipeline = this.redis.pipeline();
    const now = Date.now();
    const windowStart = now - windowMs;

    // Remove old entries
    pipeline.zremrangebyscore(key, 0, windowStart);
    
    // Count current requests
    pipeline.zcard(key);
    
    // Add current request
    pipeline.zadd(key, now, `${now}-${Math.random()}`);
    
    // Set expiration
    pipeline.expire(key, Math.ceil(windowMs / 1000));

    const results = await pipeline.exec();
    
    if (!results) {
      throw new Error('Redis pipeline execution failed');
    }

    const currentCount = results[1][1] as number;
    return currentCount < limit;
  }

  /**
   * Get remaining requests in current window
   */
  private async getRemainingRequests(
    key: string,
    limit: number,
    windowMs: number
  ): Promise<number> {
    const now = Date.now();
    const windowStart = now - windowMs;
    
    const currentCount = await this.redis.zcount(key, windowStart, now);
    return Math.max(0, limit - currentCount);
  }

  /**
   * Get reset time for current window
   */
  private async getResetTime(key: string, windowMs: number): Promise<number> {
    const oldestEntry = await this.redis.zrange(key, 0, 0, 'WITHSCORES');
    
    if (oldestEntry.length === 0) {
      return Date.now() + windowMs;
    }

    const oldestTimestamp = parseInt(oldestEntry[1], 10);
    return oldestTimestamp + windowMs;
  }

  /**
   * Generate cache key for rate limiting
   */
  private generateKey(
    req: TenantRequest,
    limitType: string,
    customGenerator?: (req: TenantRequest) => string
  ): string {
    if (customGenerator) {
      return customGenerator(req);
    }

    const tenantId = req.tenant?.tenantId || 'global';
    const ip = req.ip || req.connection.remoteAddress || 'unknown';
    const userId = (req as any).user?.id || 'anonymous';

    return `rate_limit:${limitType}:${tenantId}:${userId}:${ip}`;
  }

  /**
   * Apply global rate limit when no tenant context
   */
  private async applyGlobalRateLimit(
    req: TenantRequest,
    res: Response,
    next: NextFunction,
    limitType: string,
    options: Partial<RateLimitOptions>
  ): Promise<void> {
    const globalLimits = {
      requests: 100,
      experiments: 10,
      events: 1000
    };

    const limit = globalLimits[limitType as keyof typeof globalLimits];
    const windowMs = options.windowMs || 3600000;
    const key = `global_rate_limit:${limitType}:${req.ip}`;

    const isAllowed = await this.checkRateLimit(key, limit, windowMs);

    if (!isAllowed) {
      this.logger.warn('Global rate limit exceeded', {
        limitType,
        ip: req.ip,
        limit
      });

      return this.sendRateLimitResponse(res, limit, windowMs);
    }

    next();
  }

  /**
   * Send rate limit exceeded response
   */
  private sendRateLimitResponse(
    res: Response,
    limit: number,
    windowMs: number
  ): void {
    res.status(429).json({
      success: false,
      error: {
        code: 'RATE_LIMIT_EXCEEDED',
        message: `Rate limit exceeded. Maximum ${limit} requests per ${windowMs / 1000} seconds.`,
        details: {
          limit,
          windowMs,
          retryAfter: Math.ceil(windowMs / 1000)
        }
      },
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Get default rate limit for limit type
   */
  private getDefaultLimit(limitType: string): number {
    const defaults = {
      requests: 1000,
      experiments: 100,
      events: 10000
    };

    return defaults[limitType as keyof typeof defaults] || 100;
  }

  /**
   * Create burst protection middleware
   */
  createBurstProtection(
    maxBurst: number = 10,
    burstWindowMs: number = 1000
  ) {
    return async (req: TenantRequest, res: Response, next: NextFunction): Promise<void> => {
      try {
        const tenantId = req.tenant?.tenantId || 'global';
        const key = `burst_protection:${tenantId}:${req.ip}`;
        
        const isAllowed = await this.checkRateLimit(key, maxBurst, burstWindowMs);
        
        if (!isAllowed) {
          this.logger.warn('Burst protection triggered', {
            tenantId,
            ip: req.ip,
            maxBurst,
            burstWindowMs
          });

          res.status(429).json({
            success: false,
            error: {
              code: 'BURST_PROTECTION',
              message: 'Too many requests in a short time period',
              details: {
                maxBurst,
                burstWindowMs,
                retryAfter: Math.ceil(burstWindowMs / 1000)
              }
            },
            timestamp: new Date().toISOString()
          });
          return;
        }

        next();
      } catch (error) {
        this.logger.error('Burst protection error', { error });
        next();
      }
    };
  }

  /**
   * Create adaptive rate limiting based on tenant tier
   */
  createAdaptiveLimiter(limitType: 'requests' | 'experiments' | 'events') {
    return async (req: TenantRequest, res: Response, next: NextFunction): Promise<void> => {
      if (!req.tenant) {
        return this.applyGlobalRateLimit(req, res, next, limitType, {});
      }

      const config = this.tenantManager.getTenantConfig(req.tenant.tenantId);
      if (!config) {
        return next();
      }

      // Adjust limits based on tenant tier
      const tierMultipliers = {
        shared: 1,
        dedicated: 2,
        enterprise: 5
      };

      const baseLimit = config.settings.rateLimits[limitType];
      const multiplier = tierMultipliers[config.tier as keyof typeof tierMultipliers] || 1;
      const adjustedLimit = baseLimit * multiplier;

      // Use shorter windows for higher tiers
      const windowMs = config.tier === 'enterprise' ? 900000 : 3600000; // 15 min vs 1 hour

      const key = this.generateKey(req, limitType);
      const isAllowed = await this.checkRateLimit(key, adjustedLimit, windowMs);

      if (!isAllowed) {
        this.logger.warn('Adaptive rate limit exceeded', {
          tenantId: req.tenant.tenantId,
          tier: config.tier,
          limitType,
          adjustedLimit
        });

        return this.sendRateLimitResponse(res, adjustedLimit, windowMs);
      }

      // Add tier-specific headers
      res.set({
        'X-RateLimit-Tier': config.tier,
        'X-RateLimit-Limit': adjustedLimit.toString(),
        'X-RateLimit-Window': windowMs.toString()
      });

      next();
    };
  }

  /**
   * Reset rate limits for a tenant (admin function)
   */
  async resetTenantLimits(tenantId: string, limitType?: string): Promise<void> {
    const pattern = limitType 
      ? `rate_limit:${limitType}:${tenantId}:*`
      : `rate_limit:*:${tenantId}:*`;

    const keys = await this.redis.keys(pattern);
    
    if (keys.length > 0) {
      await this.redis.del(...keys);
      this.logger.info('Reset rate limits for tenant', {
        tenantId,
        limitType,
        keysDeleted: keys.length
      });
    }
  }

  /**
   * Get rate limit statistics for a tenant
   */
  async getTenantRateLimitStats(tenantId: string): Promise<{
    requests: { current: number; limit: number; remaining: number };
    experiments: { current: number; limit: number; remaining: number };
    events: { current: number; limit: number; remaining: number };
  }> {
    const limits = this.tenantManager.getTenantRateLimits(tenantId);
    const windowMs = 3600000; // 1 hour

    const stats = {
      requests: { current: 0, limit: limits.requests, remaining: limits.requests },
      experiments: { current: 0, limit: limits.experiments, remaining: limits.experiments },
      events: { current: 0, limit: limits.events, remaining: limits.events }
    };

    for (const limitType of ['requests', 'experiments', 'events']) {
      const pattern = `rate_limit:${limitType}:${tenantId}:*`;
      const keys = await this.redis.keys(pattern);
      
      let totalCurrent = 0;
      for (const key of keys) {
        const count = await this.redis.zcard(key);
        totalCurrent += count;
      }

      const limit = limits[limitType as keyof typeof limits];
      stats[limitType as keyof typeof stats] = {
        current: totalCurrent,
        limit,
        remaining: Math.max(0, limit - totalCurrent)
      };
    }

    return stats;
  }
}

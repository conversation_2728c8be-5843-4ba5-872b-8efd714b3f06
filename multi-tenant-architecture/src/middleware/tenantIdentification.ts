import { Request, Response, NextFunction } from 'express';
import { TenantManager, TenantContext } from '../core/TenantManager';
import { Logger } from 'winston';
import jwt from 'jsonwebtoken';

export interface TenantRequest extends Request {
  tenant?: TenantContext;
  tenantId?: string;
}

export interface TenantIdentificationOptions {
  methods: ('subdomain' | 'header' | 'jwt' | 'path')[];
  headerName?: string;
  jwtSecret?: string;
  defaultTenant?: string;
  required?: boolean;
}

export class TenantIdentificationMiddleware {
  private tenantManager: TenantManager;
  private logger: Logger;
  private options: TenantIdentificationOptions;

  constructor(
    tenantManager: TenantManager,
    logger: Logger,
    options: TenantIdentificationOptions
  ) {
    this.tenantManager = tenantManager;
    this.logger = logger;
    this.options = {
      headerName: 'X-Tenant-ID',
      required: true,
      ...options
    };
  }

  /**
   * Main tenant identification middleware
   */
  identify = async (req: TenantRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      let tenantId: string | null = null;

      // Try each identification method in order
      for (const method of this.options.methods) {
        tenantId = await this.identifyByMethod(req, method);
        if (tenantId) {
          this.logger.debug(`Tenant identified by ${method}`, { tenantId });
          break;
        }
      }

      // Use default tenant if none found and not required
      if (!tenantId && this.options.defaultTenant && !this.options.required) {
        tenantId = this.options.defaultTenant;
        this.logger.debug('Using default tenant', { tenantId });
      }

      // Validate tenant exists and is active
      if (tenantId) {
        const tenantConfig = this.tenantManager.getTenantConfig(tenantId);
        if (!tenantConfig || !tenantConfig.isActive) {
          return this.handleTenantError(res, 'TENANT_NOT_FOUND', `Tenant not found or inactive: ${tenantId}`);
        }

        // Create tenant context
        const userId = this.extractUserId(req);
        const permissions = this.extractPermissions(req);
        
        req.tenant = this.tenantManager.createTenantContext(tenantId, userId, permissions);
        req.tenantId = tenantConfig.id;

        this.logger.info('Tenant context established', {
          tenantId: req.tenant.tenantId,
          tenantSlug: req.tenant.tenantSlug,
          tier: req.tenant.tier,
          userId: req.tenant.userId
        });
      } else if (this.options.required) {
        return this.handleTenantError(res, 'TENANT_REQUIRED', 'Tenant identification required');
      }

      next();
    } catch (error) {
      this.logger.error('Tenant identification failed', { error });
      return this.handleTenantError(res, 'TENANT_IDENTIFICATION_ERROR', 'Failed to identify tenant');
    }
  };

  /**
   * Identify tenant by specific method
   */
  private async identifyByMethod(req: TenantRequest, method: string): Promise<string | null> {
    switch (method) {
      case 'subdomain':
        return this.identifyBySubdomain(req);
      case 'header':
        return this.identifyByHeader(req);
      case 'jwt':
        return this.identifyByJWT(req);
      case 'path':
        return this.identifyByPath(req);
      default:
        return null;
    }
  }

  /**
   * Identify tenant by subdomain
   */
  private identifyBySubdomain(req: TenantRequest): string | null {
    const host = req.get('host');
    if (!host) return null;

    const subdomain = host.split('.')[0];
    if (subdomain && subdomain !== 'www' && subdomain !== 'api') {
      return subdomain;
    }

    return null;
  }

  /**
   * Identify tenant by header
   */
  private identifyByHeader(req: TenantRequest): string | null {
    return req.get(this.options.headerName!) || null;
  }

  /**
   * Identify tenant by JWT token
   */
  private identifyByJWT(req: TenantRequest): string | null {
    const authHeader = req.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }

    try {
      const token = authHeader.substring(7);
      const decoded = jwt.verify(token, this.options.jwtSecret!) as any;
      return decoded.tenant_id || decoded.tenantId || null;
    } catch (error) {
      this.logger.warn('Failed to decode JWT for tenant identification', { error });
      return null;
    }
  }

  /**
   * Identify tenant by URL path
   */
  private identifyByPath(req: TenantRequest): string | null {
    const pathMatch = req.path.match(/^\/api\/v\d+\/([^\/]+)/);
    return pathMatch ? pathMatch[1] : null;
  }

  /**
   * Extract user ID from request
   */
  private extractUserId(req: TenantRequest): string | undefined {
    // Try to get from JWT token
    const authHeader = req.get('Authorization');
    if (authHeader && authHeader.startsWith('Bearer ') && this.options.jwtSecret) {
      try {
        const token = authHeader.substring(7);
        const decoded = jwt.verify(token, this.options.jwtSecret) as any;
        return decoded.user_id || decoded.userId || decoded.sub;
      } catch (error) {
        // Ignore JWT errors for user extraction
      }
    }

    // Try to get from custom header
    return req.get('X-User-ID') || undefined;
  }

  /**
   * Extract permissions from request
   */
  private extractPermissions(req: TenantRequest): string[] {
    // Try to get from JWT token
    const authHeader = req.get('Authorization');
    if (authHeader && authHeader.startsWith('Bearer ') && this.options.jwtSecret) {
      try {
        const token = authHeader.substring(7);
        const decoded = jwt.verify(token, this.options.jwtSecret) as any;
        return decoded.permissions || decoded.scopes || [];
      } catch (error) {
        // Ignore JWT errors for permission extraction
      }
    }

    return [];
  }

  /**
   * Handle tenant identification errors
   */
  private handleTenantError(res: Response, code: string, message: string): void {
    res.status(400).json({
      success: false,
      error: {
        code,
        message,
        timestamp: new Date().toISOString()
      }
    });
  }
}

/**
 * Factory function to create tenant identification middleware
 */
export function createTenantIdentificationMiddleware(
  tenantManager: TenantManager,
  logger: Logger,
  options: TenantIdentificationOptions
) {
  const middleware = new TenantIdentificationMiddleware(tenantManager, logger, options);
  return middleware.identify;
}

/**
 * Middleware to ensure tenant context exists
 */
export function requireTenant(req: TenantRequest, res: Response, next: NextFunction): void {
  if (!req.tenant) {
    res.status(400).json({
      success: false,
      error: {
        code: 'TENANT_REQUIRED',
        message: 'Tenant context is required for this operation',
        timestamp: new Date().toISOString()
      }
    });
    return;
  }
  next();
}

/**
 * Middleware to validate tenant access for user
 */
export function validateTenantAccess(tenantManager: TenantManager) {
  return (req: TenantRequest, res: Response, next: NextFunction): void => {
    if (!req.tenant || !req.tenant.userId) {
      return next();
    }

    const hasAccess = tenantManager.validateTenantAccess(req.tenant.tenantId, req.tenant.userId);
    if (!hasAccess) {
      res.status(403).json({
        success: false,
        error: {
          code: 'TENANT_ACCESS_DENIED',
          message: 'Access denied for this tenant',
          timestamp: new Date().toISOString()
        }
      });
      return;
    }

    next();
  };
}

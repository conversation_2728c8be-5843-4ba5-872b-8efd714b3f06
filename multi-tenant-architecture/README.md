# Multi-Tenant Architecture for A/B Testing Platform

## Overview

This document outlines a comprehensive multi-tenant architecture that supports both **shared database with tenant isolation** and **separate database per tenant** approaches for the A/B testing platform.

## Architecture Patterns

### 1. Shared Database with Tenant Isolation (Row-Level Security)
- Single database with `tenant_id` column in all tables
- PostgreSQL Row-Level Security (RLS) policies
- Connection pooling with tenant context
- Cost-effective for smaller tenants

### 2. Separate Database Per Tenant
- Dedicated database instance per tenant
- Complete data isolation
- Independent scaling and backup strategies
- Suitable for enterprise customers

### 3. Hybrid Approach
- Small/medium tenants: Shared database
- Enterprise tenants: Dedicated databases
- Dynamic routing based on tenant configuration

## Key Components

### Tenant Identification
- **Subdomain-based**: `{tenant}.platform.com`
- **Header-based**: `X-Tenant-ID` header
- **JWT-based**: Tenant ID embedded in authentication token
- **Path-based**: `/api/v1/{tenant}/experiments`

### Connection Management
- **Connection Pool Manager**: Manages multiple database connections
- **Tenant Router**: Routes requests to appropriate database
- **Connection Factory**: Creates tenant-specific connections
- **Health Monitoring**: Monitors database health per tenant

### Security & Isolation
- **Row-Level Security**: PostgreSQL RLS policies
- **Schema Isolation**: Separate schemas per tenant (optional)
- **API Gateway**: Tenant routing and rate limiting
- **Audit Logging**: Tenant-specific audit trails

## Implementation Strategy

### Phase 1: Enhanced Shared Database
1. Implement comprehensive RLS policies
2. Add tenant context middleware
3. Enhance connection pooling
4. Add tenant-aware caching

### Phase 2: Multi-Database Support
1. Database abstraction layer
2. Tenant configuration management
3. Dynamic connection routing
4. Migration tools

### Phase 3: Hybrid Architecture
1. Tenant tier management
2. Migration between tiers
3. Cost optimization
4. Performance monitoring

## Benefits

- **Scalability**: Support from startup to enterprise
- **Flexibility**: Choose appropriate isolation level
- **Cost Efficiency**: Optimize costs per tenant tier
- **Security**: Strong data isolation guarantees
- **Performance**: Optimized for different tenant sizes

## Quick Start

### 1. Installation

```bash
npm install
npm run build
```

### 2. Database Setup

```bash
# Run the main schema
psql -d ab_testing -f ab_testing_schema.sql

# Apply RLS policies
psql -d ab_testing -f src/database/RowLevelSecurity.sql

# Insert sample data
psql -d ab_testing -f sample_data.sql
```

### 3. Configuration

```typescript
import { defaultMultiTenantConfig } from './src/config/tenantConfig';

// Customize configuration
const config = {
  ...defaultMultiTenantConfig,
  identification: {
    methods: ['jwt', 'header'],
    headerName: 'X-Tenant-ID',
    jwtSecret: process.env.JWT_SECRET,
    required: true
  }
};
```

### 4. Start the Server

```typescript
import { app } from './examples/expressApp';

// Server starts automatically with tenant management
```

## Architecture Components

### Core Services

1. **TenantManager**: Central tenant configuration and connection management
2. **ConnectionRouter**: Database routing and query execution
3. **TenantAwareRepository**: Base repository with automatic tenant isolation
4. **RateLimiter**: Tenant-specific rate limiting

### Middleware

1. **Tenant Identification**: Multiple identification strategies
2. **Rate Limiting**: Adaptive limits based on tenant tier
3. **Access Validation**: Tenant access control
4. **Audit Logging**: Tenant-aware audit trails

### Database Features

1. **Row-Level Security**: PostgreSQL RLS policies
2. **Connection Pooling**: Efficient connection management
3. **Health Monitoring**: Real-time database health checks
4. **Migration Tools**: Tenant data migration utilities

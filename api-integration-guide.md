# A/B Testing API Integration Guide

This guide provides best practices and patterns for integrating with the A/B Testing Experiment Management API.

## Quick Start

### 1. Authentication Setup

```javascript
// JavaScript/Node.js example
const API_BASE_URL = 'https://api.example.com/v1';
const API_TOKEN = 'your-jwt-token';

const apiClient = {
  async request(method, endpoint, data = null) {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method,
      headers: {
        'Authorization': `Bearer ${API_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: data ? JSON.stringify(data) : null,
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(`API Error: ${error.error.message}`);
    }
    
    return response.json();
  }
};
```

### 2. Basic Integration Pattern

```javascript
class ABTestingClient {
  constructor(apiToken, baseUrl = 'https://api.example.com/v1') {
    this.apiToken = apiToken;
    this.baseUrl = baseUrl;
  }

  async assignUser(experimentId, userId, userAttributes = {}) {
    try {
      const response = await this.request('POST', `/experiments/${experimentId}/assignments`, {
        user_id: userId,
        user_attributes: userAttributes
      });
      
      return response.data;
    } catch (error) {
      console.error('User assignment failed:', error);
      return { variant_id: null, is_excluded: true, exclusion_reason: 'API Error' };
    }
  }

  async trackEvent(experimentId, userId, eventName, eventValue = null, eventProperties = {}) {
    try {
      await this.request('POST', `/experiments/${experimentId}/events`, {
        user_id: userId,
        event_name: eventName,
        event_value: eventValue,
        event_properties: eventProperties
      });
    } catch (error) {
      console.error('Event tracking failed:', error);
      // Don't throw - event tracking should be non-blocking
    }
  }

  async request(method, endpoint, data = null) {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method,
      headers: {
        'Authorization': `Bearer ${this.apiToken}`,
        'Content-Type': 'application/json',
      },
      body: data ? JSON.stringify(data) : null,
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error.message);
    }
    
    return response.json();
  }
}
```

## Integration Patterns

### Client-Side Integration (Frontend)

```javascript
// React Hook Example
import { useState, useEffect } from 'react';

function useABTest(experimentId, userId, userAttributes) {
  const [assignment, setAssignment] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function assignUser() {
      try {
        const client = new ABTestingClient(process.env.REACT_APP_API_TOKEN);
        const result = await client.assignUser(experimentId, userId, userAttributes);
        setAssignment(result);
      } catch (error) {
        console.error('A/B test assignment failed:', error);
        setAssignment({ variant_id: null, is_excluded: true });
      } finally {
        setLoading(false);
      }
    }

    if (experimentId && userId) {
      assignUser();
    }
  }, [experimentId, userId, userAttributes]);

  const trackEvent = async (eventName, eventValue, eventProperties) => {
    if (assignment && !assignment.is_excluded) {
      const client = new ABTestingClient(process.env.REACT_APP_API_TOKEN);
      await client.trackEvent(experimentId, userId, eventName, eventValue, eventProperties);
    }
  };

  return { assignment, loading, trackEvent };
}

// Usage in component
function HomePage() {
  const userId = getCurrentUserId();
  const userAttributes = { country: 'US', device_type: 'desktop' };
  const { assignment, loading, trackEvent } = useABTest('exp-123', userId, userAttributes);

  const handleButtonClick = () => {
    trackEvent('button_click', 1, { button_location: 'hero' });
    // Handle actual button click logic
  };

  if (loading) return <div>Loading...</div>;

  const buttonColor = assignment?.assignment?.variant?.configuration?.button_color || '#007bff';

  return (
    <div>
      <button 
        style={{ backgroundColor: buttonColor }}
        onClick={handleButtonClick}
      >
        Get Started
      </button>
    </div>
  );
}
```

### Server-Side Integration (Backend)

```python
# Python/Django Example
import requests
import logging
from django.conf import settings

class ABTestingService:
    def __init__(self):
        self.base_url = settings.AB_TESTING_API_URL
        self.api_token = settings.AB_TESTING_API_TOKEN
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {self.api_token}',
            'Content-Type': 'application/json'
        })

    def assign_user(self, experiment_id, user_id, user_attributes=None):
        """Assign user to experiment variant"""
        try:
            response = self.session.post(
                f'{self.base_url}/experiments/{experiment_id}/assignments',
                json={
                    'user_id': user_id,
                    'user_attributes': user_attributes or {}
                }
            )
            response.raise_for_status()
            return response.json()['data']
        except requests.RequestException as e:
            logging.error(f'User assignment failed: {e}')
            return {'variant_id': None, 'is_excluded': True, 'exclusion_reason': 'API Error'}

    def track_event(self, experiment_id, user_id, event_name, event_value=None, event_properties=None):
        """Track event for experiment analytics"""
        try:
            self.session.post(
                f'{self.base_url}/experiments/{experiment_id}/events',
                json={
                    'user_id': user_id,
                    'event_name': event_name,
                    'event_value': event_value,
                    'event_properties': event_properties or {}
                }
            )
        except requests.RequestException as e:
            logging.error(f'Event tracking failed: {e}')
            # Don't raise - event tracking should be non-blocking

# Usage in Django view
from django.shortcuts import render
from .services import ABTestingService

def homepage_view(request):
    ab_service = ABTestingService()
    user_id = str(request.user.id) if request.user.is_authenticated else request.session.session_key
    
    user_attributes = {
        'country': get_user_country(request),
        'device_type': get_device_type(request),
        'is_authenticated': request.user.is_authenticated
    }
    
    # Assign user to experiment
    assignment = ab_service.assign_user('homepage-button-test', user_id, user_attributes)
    
    # Get variant configuration
    button_config = {}
    if not assignment['is_excluded'] and assignment['assignment']:
        button_config = assignment['assignment']['variant']['configuration']
    
    return render(request, 'homepage.html', {
        'button_config': button_config,
        'experiment_assignment': assignment
    })
```

### Mobile App Integration

```swift
// iOS Swift Example
import Foundation

class ABTestingClient {
    private let baseURL = "https://api.example.com/v1"
    private let apiToken: String
    
    init(apiToken: String) {
        self.apiToken = apiToken
    }
    
    func assignUser(experimentId: String, userId: String, userAttributes: [String: Any] = [:], completion: @escaping (Result<AssignmentResult, Error>) -> Void) {
        let url = URL(string: "\(baseURL)/experiments/\(experimentId)/assignments")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("Bearer \(apiToken)", forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let body = [
            "user_id": userId,
            "user_attributes": userAttributes
        ]
        
        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: body)
        } catch {
            completion(.failure(error))
            return
        }
        
        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            guard let data = data else {
                completion(.failure(ABTestingError.noData))
                return
            }
            
            do {
                let result = try JSONDecoder().decode(AssignmentResponse.self, from: data)
                completion(.success(result.data))
            } catch {
                completion(.failure(error))
            }
        }.resume()
    }
    
    func trackEvent(experimentId: String, userId: String, eventName: String, eventValue: Double? = nil, eventProperties: [String: Any] = [:]) {
        let url = URL(string: "\(baseURL)/experiments/\(experimentId)/events")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("Bearer \(apiToken)", forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        var body: [String: Any] = [
            "user_id": userId,
            "event_name": eventName,
            "event_properties": eventProperties
        ]
        
        if let eventValue = eventValue {
            body["event_value"] = eventValue
        }
        
        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: body)
            URLSession.shared.dataTask(with: request).resume()
        } catch {
            print("Event tracking failed: \(error)")
        }
    }
}

// Usage in ViewController
class HomeViewController: UIViewController {
    private let abClient = ABTestingClient(apiToken: "your-token")
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupABTest()
    }
    
    private func setupABTest() {
        let userId = UserDefaults.standard.string(forKey: "user_id") ?? UUID().uuidString
        let userAttributes = [
            "country": Locale.current.regionCode ?? "US",
            "device_type": "mobile",
            "app_version": Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0"
        ]
        
        abClient.assignUser(experimentId: "homepage-button-test", userId: userId, userAttributes: userAttributes) { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let assignment):
                    self?.configureUI(with: assignment)
                case .failure(let error):
                    print("A/B test assignment failed: \(error)")
                    self?.configureUI(with: nil)
                }
            }
        }
    }
    
    private func configureUI(with assignment: AssignmentResult?) {
        guard let assignment = assignment,
              !assignment.isExcluded,
              let config = assignment.assignment?.variant.configuration else {
            // Use default configuration
            return
        }
        
        // Apply variant configuration
        if let buttonColor = config["button_color"] as? String {
            ctaButton.backgroundColor = UIColor(hex: buttonColor)
        }
    }
    
    @IBAction func ctaButtonTapped(_ sender: UIButton) {
        abClient.trackEvent(experimentId: "homepage-button-test", userId: getCurrentUserId(), eventName: "button_click", eventValue: 1, eventProperties: ["button_location": "hero"])
        
        // Handle button tap logic
    }
}
```

## Best Practices

### 1. Error Handling and Fallbacks

```javascript
// Always provide fallbacks for A/B test failures
async function getExperimentVariant(experimentId, userId, userAttributes) {
  try {
    const assignment = await abClient.assignUser(experimentId, userId, userAttributes);
    
    if (assignment.is_excluded) {
      return getDefaultVariant();
    }
    
    return assignment.assignment.variant;
  } catch (error) {
    console.error('A/B test failed, using default:', error);
    return getDefaultVariant();
  }
}

function getDefaultVariant() {
  return {
    name: 'Default',
    configuration: {
      button_color: '#007bff',
      button_text: 'Get Started'
    }
  };
}
```

### 2. Caching and Performance

```javascript
// Cache assignments to avoid repeated API calls
class ABTestingClientWithCache {
  constructor(apiToken) {
    this.client = new ABTestingClient(apiToken);
    this.cache = new Map();
    this.cacheExpiry = 5 * 60 * 1000; // 5 minutes
  }

  async assignUser(experimentId, userId, userAttributes) {
    const cacheKey = `${experimentId}:${userId}`;
    const cached = this.cache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      return cached.assignment;
    }

    const assignment = await this.client.assignUser(experimentId, userId, userAttributes);
    
    this.cache.set(cacheKey, {
      assignment,
      timestamp: Date.now()
    });

    return assignment;
  }
}
```

### 3. Event Batching

```javascript
// Batch events for better performance
class EventBatcher {
  constructor(abClient, batchSize = 10, flushInterval = 5000) {
    this.abClient = abClient;
    this.batchSize = batchSize;
    this.flushInterval = flushInterval;
    this.events = [];
    
    setInterval(() => this.flush(), flushInterval);
  }

  trackEvent(experimentId, userId, eventName, eventValue, eventProperties) {
    this.events.push({
      experimentId,
      userId,
      eventName,
      eventValue,
      eventProperties,
      timestamp: new Date().toISOString()
    });

    if (this.events.length >= this.batchSize) {
      this.flush();
    }
  }

  async flush() {
    if (this.events.length === 0) return;

    const eventsToSend = this.events.splice(0);
    
    try {
      await Promise.all(
        eventsToSend.map(event =>
          this.abClient.trackEvent(
            event.experimentId,
            event.userId,
            event.eventName,
            event.eventValue,
            event.eventProperties
          )
        )
      );
    } catch (error) {
      console.error('Batch event tracking failed:', error);
      // Could implement retry logic here
    }
  }
}
```

### 4. Testing and Development

```javascript
// Mock client for testing
class MockABTestingClient {
  constructor(mockAssignments = {}) {
    this.mockAssignments = mockAssignments;
  }

  async assignUser(experimentId, userId, userAttributes) {
    const assignment = this.mockAssignments[experimentId] || {
      variant_id: 'control',
      is_excluded: false,
      assignment: {
        variant: {
          name: 'Control',
          configuration: {}
        }
      }
    };

    return assignment;
  }

  async trackEvent() {
    // Mock implementation - just log
    console.log('Mock event tracked');
  }
}

// Use in tests
const mockClient = new MockABTestingClient({
  'homepage-button-test': {
    variant_id: 'red-button',
    is_excluded: false,
    assignment: {
      variant: {
        name: 'Red Button',
        configuration: { button_color: '#dc3545' }
      }
    }
  }
});
```

## Security Considerations

1. **API Token Security**: Store API tokens securely and rotate them regularly
2. **User Privacy**: Ensure user attributes comply with privacy regulations
3. **Rate Limiting**: Implement client-side rate limiting to avoid hitting API limits
4. **HTTPS Only**: Always use HTTPS for API communications
5. **Input Validation**: Validate all user inputs before sending to API

## Monitoring and Observability

```javascript
// Add monitoring to your A/B testing integration
class MonitoredABTestingClient {
  constructor(apiToken, metrics) {
    this.client = new ABTestingClient(apiToken);
    this.metrics = metrics;
  }

  async assignUser(experimentId, userId, userAttributes) {
    const startTime = Date.now();
    
    try {
      const result = await this.client.assignUser(experimentId, userId, userAttributes);
      
      this.metrics.increment('ab_test.assignment.success', {
        experiment_id: experimentId
      });
      
      this.metrics.histogram('ab_test.assignment.duration', Date.now() - startTime);
      
      return result;
    } catch (error) {
      this.metrics.increment('ab_test.assignment.error', {
        experiment_id: experimentId,
        error_type: error.constructor.name
      });
      
      throw error;
    }
  }
}
```

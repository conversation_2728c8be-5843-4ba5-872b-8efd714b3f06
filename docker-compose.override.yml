# Docker Compose override for development
version: '3.8'

services:
  # Development overrides for the app service
  app:
    build:
      target: development
    environment:
      NODE_ENV: development
      LOG_LEVEL: debug
    volumes:
      - .:/app
      - /app/node_modules
      - app_logs:/app/logs
    command: ["dumb-init", "npm", "run", "dev"]
    ports:
      - "3003:3003"
      - "9229:9229"  # Node.js debugger port

  # Development database with exposed port
  postgres:
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: ab_testing_platform_dev
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./docker/postgres/init:/docker-entrypoint-initdb.d

  # Development Redis with exposed port
  redis:
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data

  # Hot reload for Nginx in development
  nginx:
    volumes:
      - ./public:/usr/share/nginx/html
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
    ports:
      - "80:80"
      - "8080:8080"

volumes:
  postgres_dev_data:
  redis_dev_data:

# A/B Testing Dashboard - React UI

A comprehensive React dashboard for managing A/B testing experiments with a modern, responsive interface.

## 🚀 Features

### Core Components
- **Experiment List** - Paginated list with filtering, sorting, and search
- **Experiment Detail** - Comprehensive view with analytics and controls
- **Experiment Form** - Create/edit experiments with validation
- **Variant Editor** - Drag-and-drop variant management with configuration
- **Targeting Rules Editor** - Visual rule builder with priority ordering
- **Status Controls** - Experiment lifecycle management with confirmations
- **Analytics View** - Charts, statistics, and performance metrics

### Key Capabilities
- 📊 **Real-time Analytics** - Live experiment performance data
- 🎯 **Advanced Targeting** - Complex user targeting rules
- 🔄 **Drag & Drop** - Intuitive variant and rule reordering
- 📱 **Responsive Design** - Works on desktop, tablet, and mobile
- 🎨 **Modern UI** - Clean, professional interface with Tailwind CSS
- ⚡ **Performance** - Optimized with React Query and lazy loading
- 🔒 **Type Safety** - Full TypeScript implementation
- 🧪 **Form Validation** - Comprehensive validation with Zod schemas

## 🏗️ Architecture

### Component Hierarchy
```
App
├── DashboardLayout
│   ├── Navigation
│   └── ExperimentsPage
│       ├── ExperimentList
│       │   ├── Filters
│       │   ├── Search
│       │   └── Pagination
│       ├── ExperimentDetail
│       │   ├── StatusControls
│       │   ├── VariantList
│       │   ├── TargetingRulesList
│       │   └── ExperimentAnalyticsView
│       └── ExperimentForm
│           ├── VariantEditor
│           └── TargetingRulesEditor
```

### State Management
- **React Query** - Server state management and caching
- **React Hook Form** - Form state and validation
- **React Router** - URL-based navigation state
- **Local State** - Component-specific UI state

### Data Flow
```
API Service → React Query → Custom Hooks → Components
     ↓              ↓            ↓           ↓
  HTTP Calls    Caching    Business Logic   UI State
```

## 🛠️ Technology Stack

### Core Framework
- **React 18** - Latest React with concurrent features
- **TypeScript** - Type safety and developer experience
- **React Router 6** - Modern routing with data loading

### State & Data
- **TanStack Query** - Server state management
- **React Hook Form** - Form handling and validation
- **Zod** - Schema validation and type inference

### UI & Styling
- **Tailwind CSS** - Utility-first CSS framework
- **Headless UI** - Unstyled, accessible UI components
- **Heroicons** - Beautiful SVG icons
- **Framer Motion** - Smooth animations and transitions

### Charts & Visualization
- **Recharts** - Composable charting library
- **React Beautiful DnD** - Drag and drop functionality

### Development Tools
- **Vite** - Fast build tool and dev server
- **ESLint** - Code linting and quality
- **Prettier** - Code formatting
- **React Query DevTools** - Development debugging

## 📦 Installation

```bash
# Install dependencies
npm install

# Install additional Tailwind plugins
npm install @tailwindcss/forms @tailwindcss/typography

# Start development server
npm start
```

## 🔧 Configuration

### Environment Variables
```bash
# .env.local
REACT_APP_API_URL=http://localhost:3001/api/v1
REACT_APP_ENVIRONMENT=development
```

### API Configuration
The app expects the API to be running on `http://localhost:3001` by default. Update the proxy in `package.json` or set `REACT_APP_API_URL` to point to your API server.

## 🎨 Component Documentation

### ExperimentList
Displays paginated experiments with filtering and search capabilities.

**Props:**
- `experiments` - Array of experiment objects
- `loading` - Loading state boolean
- `onExperimentSelect` - Callback for experiment selection
- `filters` - Current filter state
- `pagination` - Pagination parameters

**Features:**
- Search by name/description
- Filter by status, tags, dates
- Sort by various fields
- Bulk actions
- Status quick actions

### ExperimentDetail
Comprehensive experiment view with tabs for overview, analytics, and assignments.

**Props:**
- `experiment` - Experiment object
- `analytics` - Analytics data
- `onEdit` - Edit callback
- `onStatusChange` - Status change callback

**Features:**
- Tabbed interface
- Real-time analytics
- Status controls
- Variant and targeting rule display

### ExperimentForm
Modal form for creating and editing experiments with comprehensive validation.

**Props:**
- `experiment` - Experiment to edit (optional)
- `onSubmit` - Form submission callback
- `onCancel` - Cancel callback

**Features:**
- Multi-step form layout
- Real-time validation
- Variant management
- Targeting rules configuration

### VariantEditor
Drag-and-drop editor for managing experiment variants.

**Props:**
- `variants` - Array of variant objects
- `onChange` - Change callback
- `readonly` - Read-only mode

**Features:**
- Drag-and-drop reordering
- Traffic weight management
- Configuration editor (JSON/key-value)
- Validation and error display

### TargetingRulesEditor
Visual editor for creating and managing targeting rules.

**Props:**
- `rules` - Array of targeting rule objects
- `onChange` - Change callback
- `readonly` - Read-only mode

**Features:**
- Priority-based ordering
- Visual rule builder
- Common attribute suggestions
- Rule preview

### StatusControls
Experiment lifecycle management with confirmation dialogs.

**Props:**
- `experiment` - Experiment object
- `onStatusChange` - Status change callback
- `loading` - Loading state

**Features:**
- Context-aware actions
- Confirmation dialogs
- Input collection for status changes
- Visual status indicators

## 🎯 Usage Examples

### Basic Experiment List
```tsx
import { ExperimentList } from './components/experiments/ExperimentList';

function MyPage() {
  const { experiments, loading } = useExperiments();
  
  return (
    <ExperimentList
      experiments={experiments}
      loading={loading}
      onExperimentSelect={(exp) => navigate(`/experiments/${exp.id}`)}
      onExperimentCreate={() => navigate('/experiments/new')}
      // ... other props
    />
  );
}
```

### Experiment Form with Validation
```tsx
import { ExperimentForm } from './components/experiments/ExperimentForm';

function CreateExperiment() {
  const { createExperiment } = useExperiments();
  
  const handleSubmit = async (data) => {
    try {
      await createExperiment(data);
      navigate('/experiments');
    } catch (error) {
      // Handle error
    }
  };
  
  return (
    <ExperimentForm
      onSubmit={handleSubmit}
      onCancel={() => navigate('/experiments')}
    />
  );
}
```

### Custom Hook Usage
```tsx
import { useExperiments } from './hooks/useExperiments';

function ExperimentManager() {
  const {
    experiments,
    loading,
    createExperiment,
    updateExperiment,
    deleteExperiment,
  } = useExperiments(filters, pagination);
  
  // Use the hook data and methods
}
```

## 🔍 Testing

### Component Testing
```bash
# Run tests
npm test

# Run tests with coverage
npm test -- --coverage

# Run tests in watch mode
npm test -- --watch
```

### Testing Strategy
- **Unit Tests** - Individual component testing
- **Integration Tests** - Component interaction testing
- **E2E Tests** - Full user workflow testing
- **Visual Tests** - UI regression testing

## 🚀 Deployment

### Build for Production
```bash
# Create production build
npm run build

# Preview production build
npm run preview
```

### Environment-Specific Builds
```bash
# Development build
npm run build:dev

# Staging build
npm run build:staging

# Production build
npm run build:prod
```

## 🎨 Customization

### Theming
Customize the design system by modifying `tailwind.config.js`:

```javascript
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: {
          // Your brand colors
        },
      },
    },
  },
};
```

### Component Styling
Components use Tailwind utility classes and can be customized by:
1. Modifying the utility classes
2. Adding custom CSS classes
3. Using CSS-in-JS solutions
4. Extending the Tailwind theme

## 📈 Performance

### Optimization Features
- **Code Splitting** - Lazy loading of routes and components
- **React Query** - Intelligent caching and background updates
- **Memoization** - React.memo and useMemo for expensive operations
- **Virtual Scrolling** - For large lists (when needed)
- **Image Optimization** - Lazy loading and responsive images

### Bundle Analysis
```bash
# Analyze bundle size
npm run analyze

# Check bundle composition
npm run bundle-analyzer
```

## 🔧 Development

### Code Quality
- **ESLint** - Code linting with React and TypeScript rules
- **Prettier** - Consistent code formatting
- **Husky** - Git hooks for quality checks
- **TypeScript** - Static type checking

### Development Workflow
1. Create feature branch
2. Implement component with TypeScript
3. Add comprehensive tests
4. Update documentation
5. Submit pull request

## 📚 Additional Resources

- [React Documentation](https://react.dev/)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [React Query Documentation](https://tanstack.com/query/latest)
- [React Hook Form Documentation](https://react-hook-form.com/)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

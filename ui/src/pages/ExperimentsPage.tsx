// Main experiments page component
import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ExperimentList } from '../components/experiments/ExperimentList';
import { ExperimentDetail } from '../components/experiments/ExperimentDetail';
import { ExperimentForm } from '../components/experiments/ExperimentForm';
import { 
  Experiment, 
  ExperimentFilters, 
  PaginationParams, 
  CreateExperimentForm,
  ExperimentStatus,
  ExperimentAnalytics
} from '../types/experiment';
import { useExperiments } from '../hooks/useExperiments';
import { useExperimentAnalytics } from '../hooks/useExperimentAnalytics';

export const ExperimentsPage: React.FC = () => {
  const navigate = useNavigate();
  const { experimentId } = useParams();
  
  const [view, setView] = useState<'list' | 'detail' | 'create' | 'edit'>('list');
  const [selectedExperiment, setSelectedExperiment] = useState<Experiment | null>(null);
  const [filters, setFilters] = useState<ExperimentFilters>({});
  const [pagination, setPagination] = useState<PaginationParams>({
    page: 1,
    limit: 20,
    sortBy: 'createdAt',
    sortOrder: 'desc',
  });

  const {
    experiments,
    totalCount,
    loading: experimentsLoading,
    error: experimentsError,
    createExperiment,
    updateExperiment,
    deleteExperiment,
    updateExperimentStatus,
    duplicateExperiment,
    refetch,
  } = useExperiments(filters, pagination);

  const {
    analytics,
    loading: analyticsLoading,
    error: analyticsError,
    refetch: refetchAnalytics,
  } = useExperimentAnalytics(selectedExperiment?.id);

  // Handle URL-based navigation
  useEffect(() => {
    if (experimentId && experimentId !== 'new') {
      const experiment = experiments.find(exp => exp.id === experimentId);
      if (experiment) {
        setSelectedExperiment(experiment);
        setView('detail');
      }
    } else if (experimentId === 'new') {
      setView('create');
    } else {
      setView('list');
      setSelectedExperiment(null);
    }
  }, [experimentId, experiments]);

  const handleExperimentSelect = (experiment: Experiment) => {
    setSelectedExperiment(experiment);
    setView('detail');
    navigate(`/experiments/${experiment.id}`);
  };

  const handleExperimentCreate = () => {
    setView('create');
    navigate('/experiments/new');
  };

  const handleExperimentEdit = (experiment: Experiment) => {
    setSelectedExperiment(experiment);
    setView('edit');
  };

  const handleExperimentDelete = async (experiment: Experiment) => {
    if (window.confirm(`Are you sure you want to delete "${experiment.name}"?`)) {
      try {
        await deleteExperiment(experiment.id);
        if (selectedExperiment?.id === experiment.id) {
          setView('list');
          setSelectedExperiment(null);
          navigate('/experiments');
        }
      } catch (error) {
        console.error('Failed to delete experiment:', error);
      }
    }
  };

  const handleExperimentDuplicate = async (experiment: Experiment) => {
    try {
      const duplicated = await duplicateExperiment(experiment.id);
      setSelectedExperiment(duplicated);
      setView('edit');
    } catch (error) {
      console.error('Failed to duplicate experiment:', error);
    }
  };

  const handleStatusChange = async (experiment: Experiment, status: ExperimentStatus, data?: any) => {
    try {
      const updated = await updateExperimentStatus(experiment.id, status, data);
      setSelectedExperiment(updated);
      refetchAnalytics();
    } catch (error) {
      console.error('Failed to update experiment status:', error);
    }
  };

  const handleFormSubmit = async (data: CreateExperimentForm) => {
    try {
      if (view === 'create') {
        const created = await createExperiment(data);
        setSelectedExperiment(created);
        setView('detail');
        navigate(`/experiments/${created.id}`);
      } else if (view === 'edit' && selectedExperiment) {
        const updated = await updateExperiment(selectedExperiment.id, data);
        setSelectedExperiment(updated);
        setView('detail');
      }
    } catch (error) {
      console.error('Failed to save experiment:', error);
    }
  };

  const handleFormCancel = () => {
    if (view === 'create') {
      setView('list');
      navigate('/experiments');
    } else if (view === 'edit') {
      setView('detail');
    }
  };

  const handleBack = () => {
    setView('list');
    setSelectedExperiment(null);
    navigate('/experiments');
  };

  const renderContent = () => {
    switch (view) {
      case 'list':
        return (
          <ExperimentList
            experiments={experiments}
            loading={experimentsLoading}
            error={experimentsError}
            onExperimentSelect={handleExperimentSelect}
            onExperimentCreate={handleExperimentCreate}
            onExperimentEdit={handleExperimentEdit}
            onExperimentDelete={handleExperimentDelete}
            onExperimentDuplicate={handleExperimentDuplicate}
            onStatusChange={handleStatusChange}
            filters={filters}
            onFiltersChange={setFilters}
            pagination={pagination}
            onPaginationChange={setPagination}
            totalCount={totalCount}
          />
        );

      case 'detail':
        if (!selectedExperiment) return null;
        return (
          <ExperimentDetail
            experiment={selectedExperiment}
            analytics={analytics}
            loading={analyticsLoading}
            error={analyticsError}
            onEdit={() => handleExperimentEdit(selectedExperiment)}
            onDelete={() => handleExperimentDelete(selectedExperiment)}
            onDuplicate={() => handleExperimentDuplicate(selectedExperiment)}
            onBack={handleBack}
            onStatusChange={(status, data) => handleStatusChange(selectedExperiment, status, data)}
          />
        );

      case 'create':
        return (
          <ExperimentForm
            onSubmit={handleFormSubmit}
            onCancel={handleFormCancel}
            loading={experimentsLoading}
            error={experimentsError}
          />
        );

      case 'edit':
        if (!selectedExperiment) return null;
        return (
          <ExperimentForm
            experiment={selectedExperiment}
            onSubmit={handleFormSubmit}
            onCancel={handleFormCancel}
            loading={experimentsLoading}
            error={experimentsError}
          />
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {renderContent()}
    </div>
  );
};

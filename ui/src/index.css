@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900;
  }
}

@layer components {
  /* Custom button styles */
  .btn-primary {
    @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-secondary {
    @apply inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-danger {
    @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  /* Form input styles */
  .form-input {
    @apply block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm;
  }
  
  .form-select {
    @apply block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm;
  }
  
  .form-textarea {
    @apply block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm;
  }

  /* Card styles */
  .card {
    @apply bg-white shadow rounded-lg;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
  }

  /* Status badge styles */
  .status-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .status-draft {
    @apply bg-gray-100 text-gray-800;
  }
  
  .status-active {
    @apply bg-green-100 text-green-800;
  }
  
  .status-paused {
    @apply bg-yellow-100 text-yellow-800;
  }
  
  .status-completed {
    @apply bg-blue-100 text-blue-800;
  }
  
  .status-archived {
    @apply bg-gray-100 text-gray-600;
  }

  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-b-2 border-indigo-600;
  }

  /* Hover effects */
  .hover-lift {
    @apply transition-transform duration-200 hover:-translate-y-1;
  }
  
  .hover-shadow {
    @apply transition-shadow duration-200 hover:shadow-lg;
  }

  /* Text truncation */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
  
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  
  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
}

@layer utilities {
  /* Custom scrollbar */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(156 163 175) transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(156 163 175);
    border-radius: 3px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgb(107 114 128);
  }

  /* Focus ring utilities */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500;
  }
  
  .focus-ring-inset {
    @apply focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500;
  }

  /* Animation delays */
  .animate-delay-75 {
    animation-delay: 75ms;
  }
  
  .animate-delay-100 {
    animation-delay: 100ms;
  }
  
  .animate-delay-150 {
    animation-delay: 150ms;
  }
  
  .animate-delay-200 {
    animation-delay: 200ms;
  }
  
  .animate-delay-300 {
    animation-delay: 300ms;
  }
}

/* React Beautiful DnD styles */
.react-beautiful-dnd-dragging {
  @apply shadow-lg;
}

.react-beautiful-dnd-drag-handle {
  @apply cursor-move;
}

/* React Select custom styles */
.react-select-container {
  @apply text-sm;
}

.react-select__control {
  @apply border-gray-300 shadow-sm;
}

.react-select__control--is-focused {
  @apply border-indigo-500 ring-1 ring-indigo-500;
  box-shadow: 0 0 0 1px rgb(99 102 241);
}

.react-select__menu {
  @apply shadow-lg border border-gray-200;
}

.react-select__option--is-focused {
  @apply bg-indigo-50;
}

.react-select__option--is-selected {
  @apply bg-indigo-600;
}

/* Chart tooltip styles */
.recharts-tooltip-wrapper {
  @apply shadow-lg;
}

.recharts-default-tooltip {
  @apply bg-white border border-gray-200 rounded-lg shadow-lg;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break-before {
    page-break-before: always;
  }
  
  .print-break-after {
    page-break-after: always;
  }
}

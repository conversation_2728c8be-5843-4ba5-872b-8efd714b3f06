// Custom hook for experiment management
import { useState, useEffect, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  Experiment, 
  ExperimentFilters, 
  PaginationParams, 
  CreateExperimentForm,
  ExperimentStatus,
  PaginatedResponse,
  ApiResponse
} from '../types/experiment';
import { experimentsApi } from '../services/api';

export const useExperiments = (
  filters: ExperimentFilters,
  pagination: PaginationParams
) => {
  const queryClient = useQueryClient();

  // Fetch experiments with filters and pagination
  const {
    data: experimentsData,
    isLoading: loading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['experiments', filters, pagination],
    queryFn: () => experimentsApi.getExperiments(filters, pagination),
    keepPreviousData: true,
  });

  const experiments = experimentsData?.data || [];
  const totalCount = experimentsData?.pagination?.total || 0;

  // Create experiment mutation
  const createMutation = useMutation({
    mutationFn: (data: CreateExperimentForm) => experimentsApi.createExperiment(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['experiments'] });
    },
  });

  // Update experiment mutation
  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<CreateExperimentForm> }) =>
      experimentsApi.updateExperiment(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['experiments'] });
    },
  });

  // Delete experiment mutation
  const deleteMutation = useMutation({
    mutationFn: (id: string) => experimentsApi.deleteExperiment(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['experiments'] });
    },
  });

  // Update experiment status mutation
  const statusMutation = useMutation({
    mutationFn: ({ id, status, data }: { id: string; status: ExperimentStatus; data?: any }) =>
      experimentsApi.updateExperimentStatus(id, status, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['experiments'] });
    },
  });

  // Duplicate experiment mutation
  const duplicateMutation = useMutation({
    mutationFn: (id: string) => experimentsApi.duplicateExperiment(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['experiments'] });
    },
  });

  const createExperiment = useCallback(
    async (data: CreateExperimentForm): Promise<Experiment> => {
      const result = await createMutation.mutateAsync(data);
      return result.data;
    },
    [createMutation]
  );

  const updateExperiment = useCallback(
    async (id: string, data: Partial<CreateExperimentForm>): Promise<Experiment> => {
      const result = await updateMutation.mutateAsync({ id, data });
      return result.data;
    },
    [updateMutation]
  );

  const deleteExperiment = useCallback(
    async (id: string): Promise<void> => {
      await deleteMutation.mutateAsync(id);
    },
    [deleteMutation]
  );

  const updateExperimentStatus = useCallback(
    async (id: string, status: ExperimentStatus, data?: any): Promise<Experiment> => {
      const result = await statusMutation.mutateAsync({ id, status, data });
      return result.data;
    },
    [statusMutation]
  );

  const duplicateExperiment = useCallback(
    async (id: string): Promise<Experiment> => {
      const result = await duplicateMutation.mutateAsync(id);
      return result.data;
    },
    [duplicateMutation]
  );

  return {
    experiments,
    totalCount,
    loading,
    error: error?.message || 
           createMutation.error?.message || 
           updateMutation.error?.message || 
           deleteMutation.error?.message ||
           statusMutation.error?.message ||
           duplicateMutation.error?.message,
    createExperiment,
    updateExperiment,
    deleteExperiment,
    updateExperimentStatus,
    duplicateExperiment,
    refetch,
    // Loading states for individual operations
    creating: createMutation.isLoading,
    updating: updateMutation.isLoading,
    deleting: deleteMutation.isLoading,
    updatingStatus: statusMutation.isLoading,
    duplicating: duplicateMutation.isLoading,
  };
};

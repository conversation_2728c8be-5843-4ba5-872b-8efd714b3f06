// Custom hook for experiment analytics
import { useQuery } from '@tanstack/react-query';
import { ExperimentAnalytics } from '../types/experiment';
import { experimentsApi } from '../services/api';

export const useExperimentAnalytics = (experimentId?: string) => {
  const {
    data: analyticsData,
    isLoading: loading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['experiment-analytics', experimentId],
    queryFn: () => experimentsApi.getExperimentAnalytics(experimentId!),
    enabled: !!experimentId,
    refetchInterval: 30000, // Refetch every 30 seconds for live data
  });

  const analytics = analyticsData?.data;

  return {
    analytics,
    loading,
    error: error?.message,
    refetch,
  };
};

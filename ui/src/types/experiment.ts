// TypeScript types for experiment management
export type ExperimentStatus = 'DRAFT' | 'ACTIVE' | 'PAUSED' | 'COMPLETED' | 'ARCHIVED';

export type AssignmentMethod = 'RANDOM' | 'STICKY' | 'DETERMINISTIC';

export type TargetingOperator = 
  | 'EQUALS' 
  | 'NOT_EQUALS' 
  | 'IN' 
  | 'NOT_IN' 
  | 'GREATER_THAN' 
  | 'LESS_THAN' 
  | 'CONTAINS' 
  | 'REGEX';

export interface Experiment {
  id: string;
  tenantId: string;
  name: string;
  description?: string;
  hypothesis?: string;
  status: ExperimentStatus;
  trafficAllocation: number;
  assignmentMethod: AssignmentMethod;
  startDate?: string;
  endDate?: string;
  sampleSize?: number;
  confidenceLevel: number;
  minimumDetectableEffect?: number;
  primaryMetric?: string;
  secondaryMetrics: string[];
  tags: string[];
  metadata?: Record<string, any>;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  variants: Variant[];
  targetingRules: TargetingRule[];
  _count?: {
    variants: number;
    userAssignments: number;
    events: number;
  };
}

export interface Variant {
  id: string;
  experimentId: string;
  tenantId: string;
  name: string;
  description?: string;
  isControl: boolean;
  trafficWeight: number;
  configuration: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface TargetingRule {
  id: string;
  experimentId: string;
  tenantId: string;
  name: string;
  attributeName: string;
  operator: TargetingOperator;
  value: string | number | boolean | string[];
  isActive: boolean;
  priority: number;
  createdAt: string;
  updatedAt: string;
}

export interface UserAssignment {
  id: string;
  experimentId: string;
  variantId?: string;
  tenantId: string;
  userId: string;
  sessionId?: string;
  assignmentTimestamp: string;
  userAttributes: Record<string, any>;
  isExcluded: boolean;
  exclusionReason?: string;
  variant?: Variant;
}

export interface Event {
  id: string;
  experimentId: string;
  variantId?: string;
  tenantId: string;
  userId: string;
  sessionId?: string;
  eventName: string;
  eventValue?: number;
  eventProperties: Record<string, any>;
  timestamp: string;
}

export interface ExperimentAnalytics {
  experimentId: string;
  experimentName: string;
  status: ExperimentStatus;
  startDate?: string;
  endDate?: string;
  totalUsers: number;
  variants: VariantAnalytics[];
}

export interface VariantAnalytics {
  variantId: string;
  variantName: string;
  isControl: boolean;
  metrics: ConversionMetrics;
  statistical_significance: boolean;
  p_value?: number;
  confidence_interval?: {
    lower: number;
    upper: number;
  };
}

export interface ConversionMetrics {
  totalUsers: number;
  conversions: number;
  conversionRate: number;
  averageValue?: number;
  totalValue?: number;
}

// Form types
export interface CreateExperimentForm {
  name: string;
  description?: string;
  hypothesis?: string;
  trafficAllocation: number;
  assignmentMethod: AssignmentMethod;
  startDate?: Date;
  endDate?: Date;
  sampleSize?: number;
  confidenceLevel: number;
  minimumDetectableEffect?: number;
  primaryMetric?: string;
  secondaryMetrics: string[];
  tags: string[];
  variants: CreateVariantForm[];
  targetingRules: CreateTargetingRuleForm[];
}

export interface CreateVariantForm {
  name: string;
  description?: string;
  isControl: boolean;
  trafficWeight: number;
  configuration: Record<string, any>;
}

export interface CreateTargetingRuleForm {
  name: string;
  attributeName: string;
  operator: TargetingOperator;
  value: string | number | boolean | string[];
  isActive: boolean;
  priority: number;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  meta: {
    timestamp: string;
    request_id: string;
    tenant_id?: string;
  };
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
}

export interface ApiError {
  error: {
    code: string;
    message: string;
    details?: any;
    field_errors?: Array<{
      field: string;
      message: string;
    }>;
  };
  meta: {
    timestamp: string;
    request_id: string;
    tenant_id?: string;
  };
}

// Filter and query types
export interface ExperimentFilters {
  status?: ExperimentStatus[];
  tags?: string[];
  createdBy?: string;
  search?: string;
  startDateFrom?: Date;
  startDateTo?: Date;
}

export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Component prop types
export interface ExperimentListProps {
  experiments: Experiment[];
  loading: boolean;
  error?: string;
  onExperimentSelect: (experiment: Experiment) => void;
  onExperimentCreate: () => void;
  onExperimentEdit: (experiment: Experiment) => void;
  onExperimentDelete: (experiment: Experiment) => void;
  filters: ExperimentFilters;
  onFiltersChange: (filters: ExperimentFilters) => void;
  pagination: PaginationParams;
  onPaginationChange: (pagination: PaginationParams) => void;
}

export interface ExperimentDetailProps {
  experiment: Experiment;
  analytics?: ExperimentAnalytics;
  loading: boolean;
  error?: string;
  onEdit: () => void;
  onDelete: () => void;
  onStatusChange: (status: ExperimentStatus) => void;
  onDuplicate: () => void;
}

export interface ExperimentFormProps {
  experiment?: Experiment;
  onSubmit: (data: CreateExperimentForm) => void;
  onCancel: () => void;
  loading: boolean;
  error?: string;
}

export interface VariantEditorProps {
  variants: CreateVariantForm[];
  onChange: (variants: CreateVariantForm[]) => void;
  errors?: Record<string, string>;
}

export interface TargetingRulesEditorProps {
  rules: CreateTargetingRuleForm[];
  onChange: (rules: CreateTargetingRuleForm[]) => void;
  errors?: Record<string, string>;
}

export interface StatusControlsProps {
  experiment: Experiment;
  onStatusChange: (status: ExperimentStatus, data?: any) => void;
  loading: boolean;
  disabled?: boolean;
}

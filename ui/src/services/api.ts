// API service for experiment management
import {
  Experiment,
  ExperimentFilters,
  PaginationParams,
  CreateExperimentForm,
  ExperimentStatus,
  ExperimentAnalytics,
  PaginatedResponse,
  ApiResponse,
  ApiError
} from '../types/experiment';

// Mock data for development
const mockExperiments: Experiment[] = [
  {
    id: '1',
    tenantId: 'tenant-1',
    name: 'Homepage Button Color Test',
    description: 'Testing different button colors to improve conversion rates',
    hypothesis: 'A green button will perform better than the current blue button',
    status: 'ACTIVE',
    trafficAllocation: 0.5,
    assignmentMethod: 'STICKY',
    startDate: '2024-01-15T00:00:00Z',
    endDate: '2024-02-15T00:00:00Z',
    sampleSize: 10000,
    confidenceLevel: 0.95,
    minimumDetectableEffect: 0.05,
    primaryMetric: 'conversion_rate',
    secondaryMetrics: ['click_through_rate', 'bounce_rate'],
    tags: ['homepage', 'ui', 'conversion'],
    metadata: {},
    createdBy: 'user-1',
    createdAt: '2024-01-10T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z',
    variants: [
      {
        id: 'v1',
        experimentId: '1',
        tenantId: 'tenant-1',
        name: 'Control (Blue)',
        description: 'Current blue button',
        isControl: true,
        trafficWeight: 0.5,
        configuration: { buttonColor: '#3B82F6' },
        createdAt: '2024-01-10T00:00:00Z',
        updatedAt: '2024-01-10T00:00:00Z',
      },
      {
        id: 'v2',
        experimentId: '1',
        tenantId: 'tenant-1',
        name: 'Treatment (Green)',
        description: 'New green button',
        isControl: false,
        trafficWeight: 0.5,
        configuration: { buttonColor: '#10B981' },
        createdAt: '2024-01-10T00:00:00Z',
        updatedAt: '2024-01-10T00:00:00Z',
      },
    ],
    targetingRules: [
      {
        id: 'r1',
        experimentId: '1',
        tenantId: 'tenant-1',
        name: 'Desktop Users Only',
        attributeName: 'device_type',
        operator: 'EQUALS',
        value: 'desktop',
        isActive: true,
        priority: 1,
        createdAt: '2024-01-10T00:00:00Z',
        updatedAt: '2024-01-10T00:00:00Z',
      },
    ],
    _count: {
      variants: 2,
      userAssignments: 1250,
      events: 3420,
    },
  },
  {
    id: '2',
    tenantId: 'tenant-1',
    name: 'Checkout Flow Optimization',
    description: 'Testing a simplified checkout process',
    hypothesis: 'Reducing checkout steps will increase completion rates',
    status: 'DRAFT',
    trafficAllocation: 1.0,
    assignmentMethod: 'RANDOM',
    confidenceLevel: 0.95,
    primaryMetric: 'checkout_completion',
    secondaryMetrics: ['cart_abandonment'],
    tags: ['checkout', 'ux', 'conversion'],
    metadata: {},
    createdBy: 'user-2',
    createdAt: '2024-01-12T00:00:00Z',
    updatedAt: '2024-01-12T00:00:00Z',
    variants: [
      {
        id: 'v3',
        experimentId: '2',
        tenantId: 'tenant-1',
        name: 'Current Flow',
        description: '3-step checkout process',
        isControl: true,
        trafficWeight: 0.5,
        configuration: { steps: 3 },
        createdAt: '2024-01-12T00:00:00Z',
        updatedAt: '2024-01-12T00:00:00Z',
      },
      {
        id: 'v4',
        experimentId: '2',
        tenantId: 'tenant-1',
        name: 'Simplified Flow',
        description: '2-step checkout process',
        isControl: false,
        trafficWeight: 0.5,
        configuration: { steps: 2 },
        createdAt: '2024-01-12T00:00:00Z',
        updatedAt: '2024-01-12T00:00:00Z',
      },
    ],
    targetingRules: [],
    _count: {
      variants: 2,
      userAssignments: 0,
      events: 0,
    },
  },
];

const mockAnalytics: ExperimentAnalytics = {
  experimentId: '1',
  experimentName: 'Homepage Button Color Test',
  status: 'ACTIVE',
  startDate: '2024-01-15T00:00:00Z',
  totalUsers: 1250,
  variants: [
    {
      variantId: 'v1',
      variantName: 'Control (Blue)',
      isControl: true,
      metrics: {
        totalUsers: 625,
        conversions: 87,
        conversionRate: 0.1392,
        averageValue: 45.67,
        totalValue: 3973.29,
      },
      statistical_significance: false,
      p_value: 0.23,
      confidence_interval: {
        lower: 0.115,
        upper: 0.163,
      },
    },
    {
      variantId: 'v2',
      variantName: 'Treatment (Green)',
      isControl: false,
      metrics: {
        totalUsers: 625,
        conversions: 103,
        conversionRate: 0.1648,
        averageValue: 47.23,
        totalValue: 4864.69,
      },
      statistical_significance: true,
      p_value: 0.04,
      confidence_interval: {
        lower: 0.138,
        upper: 0.192,
      },
    },
  ],
};

class ExperimentsApiService {
  constructor() {
    // Mock service - no axios needed for demo
  }

  // Simulate API delay
  private delay(ms: number = 500): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Get experiments with filters and pagination
  async getExperiments(
    filters: ExperimentFilters,
    pagination: PaginationParams
  ): Promise<PaginatedResponse<Experiment>> {
    await this.delay();

    let filteredExperiments = [...mockExperiments];

    // Apply filters
    if (filters.status?.length) {
      filteredExperiments = filteredExperiments.filter(exp =>
        filters.status!.includes(exp.status)
      );
    }

    if (filters.search) {
      const search = filters.search.toLowerCase();
      filteredExperiments = filteredExperiments.filter(exp =>
        exp.name.toLowerCase().includes(search) ||
        exp.description?.toLowerCase().includes(search)
      );
    }

    if (filters.tags?.length) {
      filteredExperiments = filteredExperiments.filter(exp =>
        filters.tags!.some(tag => exp.tags.includes(tag))
      );
    }

    // Apply pagination
    const total = filteredExperiments.length;
    const start = (pagination.page - 1) * pagination.limit;
    const end = start + pagination.limit;
    const paginatedExperiments = filteredExperiments.slice(start, end);

    return {
      data: paginatedExperiments,
      pagination: {
        page: pagination.page,
        limit: pagination.limit,
        total,
        total_pages: Math.ceil(total / pagination.limit),
        has_next: end < total,
        has_prev: pagination.page > 1,
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: Math.random().toString(36),
        tenant_id: 'tenant-1',
      },
    };
  }

  // Get single experiment
  async getExperiment(id: string): Promise<ApiResponse<Experiment>> {
    await this.delay();
    const experiment = mockExperiments.find(exp => exp.id === id);
    if (!experiment) {
      throw new Error('Experiment not found');
    }

    return {
      data: experiment,
      meta: {
        timestamp: new Date().toISOString(),
        request_id: Math.random().toString(36),
        tenant_id: 'tenant-1',
      },
    };
  }

  // Create experiment
  async createExperiment(data: CreateExperimentForm): Promise<ApiResponse<Experiment>> {
    await this.delay();

    const newExperiment: Experiment = {
      id: Math.random().toString(36),
      tenantId: 'tenant-1',
      ...data,
      status: 'DRAFT',
      createdBy: 'current-user',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      variants: data.variants.map(v => ({
        id: Math.random().toString(36),
        experimentId: '',
        tenantId: 'tenant-1',
        ...v,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      })),
      targetingRules: data.targetingRules.map(r => ({
        id: Math.random().toString(36),
        experimentId: '',
        tenantId: 'tenant-1',
        ...r,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      })),
      _count: {
        variants: data.variants.length,
        userAssignments: 0,
        events: 0,
      },
    };

    newExperiment.variants.forEach(v => v.experimentId = newExperiment.id);
    newExperiment.targetingRules.forEach(r => r.experimentId = newExperiment.id);

    mockExperiments.push(newExperiment);

    return {
      data: newExperiment,
      meta: {
        timestamp: new Date().toISOString(),
        request_id: Math.random().toString(36),
        tenant_id: 'tenant-1',
      },
    };
  }

  // Update experiment
  async updateExperiment(
    id: string,
    data: Partial<CreateExperimentForm>
  ): Promise<ApiResponse<Experiment>> {
    await this.delay();

    const experimentIndex = mockExperiments.findIndex(exp => exp.id === id);
    if (experimentIndex === -1) {
      throw new Error('Experiment not found');
    }

    const updatedExperiment = {
      ...mockExperiments[experimentIndex],
      ...data,
      updatedAt: new Date().toISOString(),
    };

    mockExperiments[experimentIndex] = updatedExperiment;

    return {
      data: updatedExperiment,
      meta: {
        timestamp: new Date().toISOString(),
        request_id: Math.random().toString(36),
        tenant_id: 'tenant-1',
      },
    };
  }

  // Delete experiment
  async deleteExperiment(id: string): Promise<void> {
    await this.delay();
    const index = mockExperiments.findIndex(exp => exp.id === id);
    if (index !== -1) {
      mockExperiments.splice(index, 1);
    }
  }

  // Update experiment status
  async updateExperimentStatus(
    id: string,
    status: ExperimentStatus,
    data?: any
  ): Promise<ApiResponse<Experiment>> {
    await this.delay();

    const experimentIndex = mockExperiments.findIndex(exp => exp.id === id);
    if (experimentIndex === -1) {
      throw new Error('Experiment not found');
    }

    const updatedExperiment = {
      ...mockExperiments[experimentIndex],
      status,
      updatedAt: new Date().toISOString(),
    };

    if (status === 'ACTIVE' && !updatedExperiment.startDate) {
      updatedExperiment.startDate = new Date().toISOString();
    }

    mockExperiments[experimentIndex] = updatedExperiment;

    return {
      data: updatedExperiment,
      meta: {
        timestamp: new Date().toISOString(),
        request_id: Math.random().toString(36),
        tenant_id: 'tenant-1',
      },
    };
  }

  // Duplicate experiment
  async duplicateExperiment(id: string): Promise<ApiResponse<Experiment>> {
    await this.delay();

    const original = mockExperiments.find(exp => exp.id === id);
    if (!original) {
      throw new Error('Experiment not found');
    }

    const duplicated: Experiment = {
      ...original,
      id: Math.random().toString(36),
      name: `${original.name} (Copy)`,
      status: 'DRAFT',
      startDate: undefined,
      endDate: undefined,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      variants: original.variants.map(v => ({
        ...v,
        id: Math.random().toString(36),
        experimentId: '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      })),
      targetingRules: original.targetingRules.map(r => ({
        ...r,
        id: Math.random().toString(36),
        experimentId: '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      })),
      _count: {
        variants: original.variants.length,
        userAssignments: 0,
        events: 0,
      },
    };

    duplicated.variants.forEach(v => v.experimentId = duplicated.id);
    duplicated.targetingRules.forEach(r => r.experimentId = duplicated.id);

    mockExperiments.push(duplicated);

    return {
      data: duplicated,
      meta: {
        timestamp: new Date().toISOString(),
        request_id: Math.random().toString(36),
        tenant_id: 'tenant-1',
      },
    };
  }

  // Get experiment analytics
  async getExperimentAnalytics(id: string): Promise<ApiResponse<ExperimentAnalytics>> {
    await this.delay();

    return {
      data: mockAnalytics,
      meta: {
        timestamp: new Date().toISOString(),
        request_id: Math.random().toString(36),
        tenant_id: 'tenant-1',
      },
    };
  }

  // Get experiment assignments (mock)
  async getExperimentAssignments(
    id: string,
    params?: { page?: number; limit?: number; variant_id?: string; user_id?: string }
  ): Promise<PaginatedResponse<any>> {
    await this.delay();
    return {
      data: [],
      pagination: {
        page: 1,
        limit: 20,
        total: 0,
        total_pages: 0,
        has_next: false,
        has_prev: false,
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: Math.random().toString(36),
        tenant_id: 'tenant-1',
      },
    };
  }

  // Assign user to experiment (mock)
  async assignUserToExperiment(
    id: string,
    data: {
      user_id: string;
      user_attributes?: Record<string, any>;
    }
  ): Promise<ApiResponse<any>> {
    await this.delay();
    return {
      data: { success: true },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: Math.random().toString(36),
        tenant_id: 'tenant-1',
      },
    };
  }

  // Get experiment events (mock)
  async getExperimentEvents(
    id: string,
    params?: {
      page?: number;
      limit?: number;
      event_name?: string;
      variant_id?: string;
      user_id?: string;
      start_date?: Date;
      end_date?: Date;
    }
  ): Promise<PaginatedResponse<any>> {
    await this.delay();
    return {
      data: [],
      pagination: {
        page: 1,
        limit: 20,
        total: 0,
        total_pages: 0,
        has_next: false,
        has_prev: false,
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: Math.random().toString(36),
        tenant_id: 'tenant-1',
      },
    };
  }

  // Track event (mock)
  async trackEvent(
    id: string,
    data: {
      user_id: string;
      event_name: string;
      event_value?: number;
      event_properties?: Record<string, any>;
      timestamp?: Date;
    }
  ): Promise<ApiResponse<any>> {
    await this.delay();
    return {
      data: { success: true },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: Math.random().toString(36),
        tenant_id: 'tenant-1',
      },
    };
  }
}

export const experimentsApi = new ExperimentsApiService();

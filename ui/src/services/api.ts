// API service for experiment management
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { 
  Experiment, 
  ExperimentFilters, 
  PaginationParams, 
  CreateExperimentForm,
  ExperimentStatus,
  ExperimentAnalytics,
  PaginatedResponse,
  ApiResponse,
  ApiError
} from '../types/experiment';

class ExperimentsApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: process.env.REACT_APP_API_URL || '/api/v1',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor for auth
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Handle unauthorized - redirect to login
          localStorage.removeItem('auth_token');
          window.location.href = '/login';
        }
        return Promise.reject(error.response?.data || error);
      }
    );
  }

  // Get experiments with filters and pagination
  async getExperiments(
    filters: ExperimentFilters,
    pagination: PaginationParams
  ): Promise<PaginatedResponse<Experiment>> {
    const params = new URLSearchParams();
    
    // Add pagination params
    params.append('page', pagination.page.toString());
    params.append('limit', pagination.limit.toString());
    if (pagination.sortBy) params.append('sort_by', pagination.sortBy);
    if (pagination.sortOrder) params.append('sort_order', pagination.sortOrder);

    // Add filter params
    if (filters.status?.length) {
      filters.status.forEach(status => params.append('status', status));
    }
    if (filters.tags?.length) {
      filters.tags.forEach(tag => params.append('tags', tag));
    }
    if (filters.search) params.append('search', filters.search);
    if (filters.createdBy) params.append('created_by', filters.createdBy);
    if (filters.startDateFrom) {
      params.append('start_date_from', filters.startDateFrom.toISOString());
    }
    if (filters.startDateTo) {
      params.append('start_date_to', filters.startDateTo.toISOString());
    }

    const response = await this.api.get<PaginatedResponse<Experiment>>(
      `/experiments?${params.toString()}`
    );
    return response.data;
  }

  // Get single experiment
  async getExperiment(id: string): Promise<ApiResponse<Experiment>> {
    const response = await this.api.get<ApiResponse<Experiment>>(`/experiments/${id}`);
    return response.data;
  }

  // Create experiment
  async createExperiment(data: CreateExperimentForm): Promise<ApiResponse<Experiment>> {
    const response = await this.api.post<ApiResponse<Experiment>>('/experiments', data);
    return response.data;
  }

  // Update experiment
  async updateExperiment(
    id: string, 
    data: Partial<CreateExperimentForm>
  ): Promise<ApiResponse<Experiment>> {
    const response = await this.api.put<ApiResponse<Experiment>>(`/experiments/${id}`, data);
    return response.data;
  }

  // Delete experiment
  async deleteExperiment(id: string): Promise<void> {
    await this.api.delete(`/experiments/${id}`);
  }

  // Update experiment status
  async updateExperimentStatus(
    id: string, 
    status: ExperimentStatus, 
    data?: any
  ): Promise<ApiResponse<Experiment>> {
    const endpoint = this.getStatusEndpoint(status);
    const response = await this.api.post<ApiResponse<Experiment>>(
      `/experiments/${id}/${endpoint}`,
      data || {}
    );
    return response.data;
  }

  // Duplicate experiment
  async duplicateExperiment(id: string): Promise<ApiResponse<Experiment>> {
    const response = await this.api.post<ApiResponse<Experiment>>(`/experiments/${id}/duplicate`);
    return response.data;
  }

  // Get experiment analytics
  async getExperimentAnalytics(id: string): Promise<ApiResponse<ExperimentAnalytics>> {
    const response = await this.api.get<ApiResponse<ExperimentAnalytics>>(
      `/experiments/${id}/analytics`
    );
    return response.data;
  }

  // Get experiment assignments
  async getExperimentAssignments(
    id: string,
    params?: { page?: number; limit?: number; variant_id?: string; user_id?: string }
  ): Promise<PaginatedResponse<any>> {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.variant_id) searchParams.append('variant_id', params.variant_id);
    if (params?.user_id) searchParams.append('user_id', params.user_id);

    const response = await this.api.get<PaginatedResponse<any>>(
      `/experiments/${id}/assignments?${searchParams.toString()}`
    );
    return response.data;
  }

  // Assign user to experiment
  async assignUserToExperiment(
    id: string,
    data: {
      user_id: string;
      user_attributes?: Record<string, any>;
    }
  ): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>(
      `/experiments/${id}/assignments`,
      data
    );
    return response.data;
  }

  // Get experiment events
  async getExperimentEvents(
    id: string,
    params?: {
      page?: number;
      limit?: number;
      event_name?: string;
      variant_id?: string;
      user_id?: string;
      start_date?: Date;
      end_date?: Date;
    }
  ): Promise<PaginatedResponse<any>> {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.event_name) searchParams.append('event_name', params.event_name);
    if (params?.variant_id) searchParams.append('variant_id', params.variant_id);
    if (params?.user_id) searchParams.append('user_id', params.user_id);
    if (params?.start_date) searchParams.append('start_date', params.start_date.toISOString());
    if (params?.end_date) searchParams.append('end_date', params.end_date.toISOString());

    const response = await this.api.get<PaginatedResponse<any>>(
      `/experiments/${id}/events?${searchParams.toString()}`
    );
    return response.data;
  }

  // Track event
  async trackEvent(
    id: string,
    data: {
      user_id: string;
      event_name: string;
      event_value?: number;
      event_properties?: Record<string, any>;
      timestamp?: Date;
    }
  ): Promise<ApiResponse<any>> {
    const response = await this.api.post<ApiResponse<any>>(
      `/experiments/${id}/events`,
      data
    );
    return response.data;
  }

  private getStatusEndpoint(status: ExperimentStatus): string {
    switch (status) {
      case 'ACTIVE':
        return 'start';
      case 'PAUSED':
        return 'pause';
      case 'COMPLETED':
        return 'complete';
      case 'ARCHIVED':
        return 'archive';
      default:
        throw new Error(`Invalid status: ${status}`);
    }
  }
}

export const experimentsApi = new ExperimentsApiService();

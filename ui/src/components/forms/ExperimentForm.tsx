// Comprehensive experiment form with dynamic variant editor
import React, { useState, useEffect } from 'react';
import { useForm, useField<PERSON><PERSON><PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { 
  XMarkIcon, 
  PlusIcon, 
  TrashIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import DatePicker from 'react-datepicker';
import "react-datepicker/dist/react-datepicker.css";

// Validation schema
const variantSchema = z.object({
  name: z.string().min(1, 'Variant name is required').max(50, 'Name too long'),
  description: z.string().optional(),
  isControl: z.boolean(),
  allocation: z.number().min(0, 'Allocation must be positive').max(100, 'Cannot exceed 100%'),
  configuration: z.record(z.any()).optional(),
});

const experimentSchema = z.object({
  name: z.string().min(1, 'Experiment name is required').max(100, 'Name too long'),
  description: z.string().optional(),
  hypothesis: z.string().optional(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  status: z.enum(['DRAFT', 'ACTIVE', 'PAUSED', 'COMPLETED', 'ARCHIVED']),
  trafficAllocation: z.number().min(1, 'Must be at least 1%').max(100, 'Cannot exceed 100%'),
  variants: z.array(variantSchema).min(2, 'At least 2 variants required'),
  primaryMetric: z.string().optional(),
  tags: z.array(z.string()),
}).refine((data) => {
  // Validate total allocation equals 100%
  const totalAllocation = data.variants.reduce((sum, variant) => sum + variant.allocation, 0);
  return Math.abs(totalAllocation - 100) < 0.01;
}, {
  message: 'Variant allocations must sum to exactly 100%',
  path: ['variants'],
}).refine((data) => {
  // Only one control variant allowed
  const controlCount = data.variants.filter(v => v.isControl).length;
  return controlCount <= 1;
}, {
  message: 'Only one variant can be marked as control',
  path: ['variants'],
}).refine((data) => {
  // End date must be after start date
  if (data.startDate && data.endDate) {
    return data.endDate > data.startDate;
  }
  return true;
}, {
  message: 'End date must be after start date',
  path: ['endDate'],
});

type FormData = z.infer<typeof experimentSchema>;

interface ExperimentFormProps {
  experiment?: any; // Existing experiment for editing
  onSubmit: (data: FormData) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
}

export const ExperimentForm: React.FC<ExperimentFormProps> = ({
  experiment,
  onSubmit,
  onCancel,
  loading = false,
}) => {
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [allocationError, setAllocationError] = useState<string | null>(null);

  const {
    register,
    control,
    handleSubmit,
    watch,
    setValue,
    trigger,
    formState: { errors, isValid },
  } = useForm<FormData>({
    resolver: zodResolver(experimentSchema),
    defaultValues: {
      name: experiment?.name || '',
      description: experiment?.description || '',
      hypothesis: experiment?.hypothesis || '',
      startDate: experiment?.startDate ? new Date(experiment.startDate) : undefined,
      endDate: experiment?.endDate ? new Date(experiment.endDate) : undefined,
      status: experiment?.status || 'DRAFT',
      trafficAllocation: experiment?.trafficAllocation || 100,
      variants: experiment?.variants?.map((v: any) => ({
        name: v.name,
        description: v.description || '',
        isControl: v.isControl,
        allocation: v.trafficWeight * 100, // Convert to percentage
        configuration: v.configuration || {},
      })) || [
        { name: 'Control', description: '', isControl: true, allocation: 50, configuration: {} },
        { name: 'Treatment', description: '', isControl: false, allocation: 50, configuration: {} },
      ],
      primaryMetric: experiment?.primaryMetric || '',
      tags: experiment?.tags || [],
    },
    mode: 'onChange',
  });

  const { fields, append, remove, move } = useFieldArray({
    control,
    name: 'variants',
  });

  const variants = watch('variants');
  const status = watch('status');

  // Calculate total allocation and validate
  useEffect(() => {
    const total = variants.reduce((sum, variant) => sum + (variant.allocation || 0), 0);
    const diff = Math.abs(total - 100);
    
    if (diff > 0.01) {
      setAllocationError(`Total allocation is ${total.toFixed(1)}%. Must equal 100%.`);
    } else {
      setAllocationError(null);
    }
  }, [variants]);

  const handleFormSubmit = async (data: FormData) => {
    try {
      // Convert allocations back to weights (0-1)
      const processedData = {
        ...data,
        variants: data.variants.map(v => ({
          ...v,
          trafficWeight: v.allocation / 100,
        })),
        trafficAllocation: data.trafficAllocation / 100,
      };
      await onSubmit(processedData);
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  const addVariant = () => {
    const currentTotal = variants.reduce((sum, v) => sum + v.allocation, 0);
    const remainingAllocation = Math.max(0, 100 - currentTotal);
    
    append({
      name: `Variant ${variants.length + 1}`,
      description: '',
      isControl: false,
      allocation: remainingAllocation,
      configuration: {},
    });
  };

  const redistributeEqually = () => {
    const equalAllocation = 100 / variants.length;
    variants.forEach((_, index) => {
      setValue(`variants.${index}.allocation`, equalAllocation);
    });
    trigger('variants');
  };

  const handleControlChange = (index: number, isControl: boolean) => {
    if (isControl) {
      // Unset other controls
      variants.forEach((_, i) => {
        if (i !== index) {
          setValue(`variants.${i}.isControl`, false);
        }
      });
    }
    setValue(`variants.${index}.isControl`, isControl);
    trigger('variants');
  };

  const handleDragEnd = (result: any) => {
    if (!result.destination) return;
    move(result.source.index, result.destination.index);
  };

  const canEdit = status === 'DRAFT' || !experiment;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onCancel} />
        
        <div className="relative w-full max-w-4xl bg-white rounded-lg shadow-xl max-h-[90vh] overflow-hidden">
          <form onSubmit={handleSubmit(handleFormSubmit)} className="flex flex-col h-full">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">
                {experiment ? 'Edit Experiment' : 'Create New Experiment'}
              </h2>
              <button
                type="button"
                onClick={onCancel}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            {/* Form Content */}
            <div className="flex-1 overflow-y-auto p-6 space-y-6">
              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Basic Information</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Experiment Name *
                    </label>
                    <input
                      type="text"
                      {...register('name')}
                      disabled={!canEdit}
                      className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 disabled:bg-gray-100"
                      placeholder="Enter experiment name"
                    />
                    {errors.name && (
                      <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Status
                    </label>
                    <select
                      {...register('status')}
                      className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                    >
                      <option value="DRAFT">Draft</option>
                      <option value="ACTIVE">Active</option>
                      <option value="PAUSED">Paused</option>
                      <option value="COMPLETED">Completed</option>
                      <option value="ARCHIVED">Archived</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    {...register('description')}
                    disabled={!canEdit}
                    rows={3}
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 disabled:bg-gray-100"
                    placeholder="Describe what this experiment tests"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Hypothesis
                  </label>
                  <textarea
                    {...register('hypothesis')}
                    disabled={!canEdit}
                    rows={3}
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 disabled:bg-gray-100"
                    placeholder="State your hypothesis for this experiment"
                  />
                </div>
              </div>

              {/* Dates and Configuration */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Schedule & Configuration</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Start Date
                    </label>
                    <Controller
                      name="startDate"
                      control={control}
                      render={({ field }) => (
                        <DatePicker
                          selected={field.value}
                          onChange={field.onChange}
                          disabled={!canEdit}
                          showTimeSelect
                          dateFormat="MMM d, yyyy h:mm aa"
                          className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 disabled:bg-gray-100"
                          placeholderText="Select start date"
                        />
                      )}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      End Date
                    </label>
                    <Controller
                      name="endDate"
                      control={control}
                      render={({ field }) => (
                        <DatePicker
                          selected={field.value}
                          onChange={field.onChange}
                          disabled={!canEdit}
                          showTimeSelect
                          dateFormat="MMM d, yyyy h:mm aa"
                          className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 disabled:bg-gray-100"
                          placeholderText="Select end date"
                        />
                      )}
                    />
                    {errors.endDate && (
                      <p className="mt-1 text-sm text-red-600">{errors.endDate.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Traffic Allocation (%)
                    </label>
                    <input
                      type="number"
                      min="1"
                      max="100"
                      {...register('trafficAllocation', { valueAsNumber: true })}
                      disabled={!canEdit}
                      className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 disabled:bg-gray-100"
                    />
                  </div>
                </div>
              </div>

              {/* Variants Section */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-gray-900">Variants *</h3>
                  <div className="flex items-center space-x-2">
                    {allocationError && (
                      <div className="flex items-center text-red-600">
                        <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                        <span className="text-sm">{allocationError}</span>
                      </div>
                    )}
                    {canEdit && (
                      <>
                        <button
                          type="button"
                          onClick={redistributeEqually}
                          className="text-sm text-indigo-600 hover:text-indigo-800"
                        >
                          Distribute Equally
                        </button>
                        <button
                          type="button"
                          onClick={addVariant}
                          className="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-sm font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                        >
                          <PlusIcon className="h-4 w-4 mr-1" />
                          Add Variant
                        </button>
                      </>
                    )}
                  </div>
                </div>

                <DragDropContext onDragEnd={handleDragEnd}>
                  <Droppable droppableId="variants" isDropDisabled={!canEdit}>
                    {(provided) => (
                      <div {...provided.droppableProps} ref={provided.innerRef} className="space-y-3">
                        {fields.map((field, index) => (
                          <Draggable 
                            key={field.id} 
                            draggableId={field.id} 
                            index={index}
                            isDragDisabled={!canEdit}
                          >
                            {(provided, snapshot) => (
                              <div
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                className={`border border-gray-200 rounded-lg p-4 bg-white ${
                                  snapshot.isDragging ? 'shadow-lg' : ''
                                }`}
                              >
                                <div className="flex items-start space-x-4">
                                  {canEdit && (
                                    <div
                                      {...provided.dragHandleProps}
                                      className="mt-2 text-gray-400 hover:text-gray-600 cursor-move"
                                    >
                                      <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M7 2a1 1 0 000 2h6a1 1 0 100-2H7zM7 8a1 1 0 000 2h6a1 1 0 100-2H7zM7 14a1 1 0 000 2h6a1 1 0 100-2H7z" />
                                      </svg>
                                    </div>
                                  )}

                                  <div className="flex-1 space-y-3">
                                    <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
                                      <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                          Variant Name
                                        </label>
                                        <input
                                          type="text"
                                          {...register(`variants.${index}.name`)}
                                          disabled={!canEdit}
                                          className="block w-full text-sm border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 disabled:bg-gray-100"
                                        />
                                        {errors.variants?.[index]?.name && (
                                          <p className="mt-1 text-xs text-red-600">
                                            {errors.variants[index]?.name?.message}
                                          </p>
                                        )}
                                      </div>

                                      <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                          Allocation (%)
                                        </label>
                                        <input
                                          type="number"
                                          min="0"
                                          max="100"
                                          step="0.1"
                                          {...register(`variants.${index}.allocation`, { valueAsNumber: true })}
                                          disabled={!canEdit}
                                          className="block w-full text-sm border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 disabled:bg-gray-100"
                                        />
                                      </div>

                                      <div className="flex items-center justify-center">
                                        <label className="flex items-center">
                                          <input
                                            type="checkbox"
                                            checked={watch(`variants.${index}.isControl`)}
                                            onChange={(e) => handleControlChange(index, e.target.checked)}
                                            disabled={!canEdit}
                                            className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 disabled:opacity-50"
                                          />
                                          <span className="ml-2 text-sm text-gray-700">Control</span>
                                        </label>
                                      </div>

                                      <div className="flex items-center justify-end">
                                        {canEdit && fields.length > 2 && (
                                          <button
                                            type="button"
                                            onClick={() => remove(index)}
                                            className="text-red-500 hover:text-red-700"
                                          >
                                            <TrashIcon className="h-4 w-4" />
                                          </button>
                                        )}
                                      </div>
                                    </div>

                                    <div>
                                      <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Description
                                      </label>
                                      <input
                                        type="text"
                                        {...register(`variants.${index}.description`)}
                                        disabled={!canEdit}
                                        className="block w-full text-sm border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 disabled:bg-gray-100"
                                        placeholder="Optional description"
                                      />
                                    </div>
                                  </div>
                                </div>
                              </div>
                            )}
                          </Draggable>
                        ))}
                        {provided.placeholder}
                      </div>
                    )}
                  </Droppable>
                </DragDropContext>

                {errors.variants && (
                  <div className="flex items-center text-red-600">
                    <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                    <span className="text-sm">{errors.variants.message}</span>
                  </div>
                )}
              </div>

              {/* Advanced Settings */}
              <div className="space-y-4">
                <button
                  type="button"
                  onClick={() => setShowAdvanced(!showAdvanced)}
                  className="flex items-center text-sm text-indigo-600 hover:text-indigo-800"
                >
                  <InformationCircleIcon className="h-4 w-4 mr-1" />
                  {showAdvanced ? 'Hide' : 'Show'} Advanced Settings
                </button>

                {showAdvanced && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Primary Metric
                      </label>
                      <input
                        type="text"
                        {...register('primaryMetric')}
                        disabled={!canEdit}
                        className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 disabled:bg-gray-100"
                        placeholder="e.g., conversion_rate"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Tags
                      </label>
                      <input
                        type="text"
                        disabled={!canEdit}
                        className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 disabled:bg-gray-100"
                        placeholder="Enter tags separated by commas"
                        onChange={(e) => {
                          const tags = e.target.value.split(',').map(tag => tag.trim()).filter(Boolean);
                          setValue('tags', tags);
                        }}
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Footer */}
            <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
              <div className="flex items-center text-sm text-gray-500">
                {!isValid && (
                  <div className="flex items-center text-red-600">
                    <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                    Please fix validation errors before submitting
                  </div>
                )}
              </div>
              
              <div className="flex items-center space-x-3">
                <button
                  type="button"
                  onClick={onCancel}
                  className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={loading || !isValid || !!allocationError}
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? 'Saving...' : experiment ? 'Update Experiment' : 'Create Experiment'}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

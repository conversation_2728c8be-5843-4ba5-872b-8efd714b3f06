// Comprehensive experiment data table with sorting, filtering, and actions
import React, { useState, useMemo, useCallback } from 'react';
import {
  ChevronUpIcon,
  ChevronDownIcon,
  FunnelIcon,
  MagnifyingGlassIcon,
  PlayIcon,
  PauseIcon,
  StopIcon,
  ArchiveBoxIcon,
  EllipsisVerticalIcon,
  EyeIcon,
  PencilIcon,
  DocumentDuplicateIcon,
  TrashIcon,
  CalendarIcon,
  UserGroupIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';
import { Menu, Transition } from '@headlessui/react';
import { format, formatDistanceToNow, isValid } from 'date-fns';
import { clsx } from 'clsx';

// Types
interface Experiment {
  id: string;
  tenantId: string;
  name: string;
  description?: string;
  status: 'DRAFT' | 'ACTIVE' | 'PAUSED' | 'COMPLETED' | 'ARCHIVED';
  startDate?: string;
  endDate?: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  variants: Array<{ id: string; name: string; trafficWeight: number }>;
  tags: string[];
  _count?: {
    userAssignments: number;
    events: number;
  };
}

interface SortConfig {
  key: keyof Experiment | 'variantCount' | 'assignmentCount' | 'eventCount';
  direction: 'asc' | 'desc';
}

interface FilterConfig {
  status: string[];
  tags: string[];
  search: string;
  dateRange: {
    start?: Date;
    end?: Date;
  };
  createdBy: string[];
}

interface ExperimentDataTableProps {
  experiments: Experiment[];
  loading?: boolean;
  currentTenant?: string;
  onExperimentClick: (experiment: Experiment) => void;
  onStatusChange: (experiment: Experiment, newStatus: string) => void;
  onEdit: (experiment: Experiment) => void;
  onDuplicate: (experiment: Experiment) => void;
  onDelete: (experiment: Experiment) => void;
  onBulkAction?: (action: string, experiments: Experiment[]) => void;
}

// Status configuration
const statusConfig = {
  DRAFT: {
    label: 'Draft',
    color: 'bg-gray-100 text-gray-800',
    icon: PencilIcon,
    actions: ['start']
  },
  ACTIVE: {
    label: 'Active',
    color: 'bg-green-100 text-green-800',
    icon: PlayIcon,
    actions: ['pause', 'complete']
  },
  PAUSED: {
    label: 'Paused',
    color: 'bg-yellow-100 text-yellow-800',
    icon: PauseIcon,
    actions: ['resume', 'complete']
  },
  COMPLETED: {
    label: 'Completed',
    color: 'bg-blue-100 text-blue-800',
    icon: StopIcon,
    actions: ['archive']
  },
  ARCHIVED: {
    label: 'Archived',
    color: 'bg-gray-100 text-gray-600',
    icon: ArchiveBoxIcon,
    actions: []
  }
};

const actionConfig = {
  start: { label: 'Start', icon: PlayIcon, status: 'ACTIVE' },
  pause: { label: 'Pause', icon: PauseIcon, status: 'PAUSED' },
  resume: { label: 'Resume', icon: PlayIcon, status: 'ACTIVE' },
  complete: { label: 'Complete', icon: StopIcon, status: 'COMPLETED' },
  archive: { label: 'Archive', icon: ArchiveBoxIcon, status: 'ARCHIVED' }
};

export const ExperimentDataTable: React.FC<ExperimentDataTableProps> = ({
  experiments,
  loading = false,
  currentTenant,
  onExperimentClick,
  onStatusChange,
  onEdit,
  onDuplicate,
  onDelete,
  onBulkAction
}) => {
  const [sortConfig, setSortConfig] = useState<SortConfig>({ key: 'createdAt', direction: 'desc' });
  const [filters, setFilters] = useState<FilterConfig>({
    status: [],
    tags: [],
    search: '',
    dateRange: {},
    createdBy: []
  });
  const [showFilters, setShowFilters] = useState(false);
  const [selectedExperiments, setSelectedExperiments] = useState<Set<string>>(new Set());
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  // Filter and sort experiments
  const filteredAndSortedExperiments = useMemo(() => {
    let filtered = experiments;

    // Apply tenant filter if specified
    if (currentTenant) {
      filtered = filtered.filter(exp => exp.tenantId === currentTenant);
    }

    // Apply search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(exp =>
        exp.name.toLowerCase().includes(searchLower) ||
        exp.description?.toLowerCase().includes(searchLower) ||
        exp.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    // Apply status filter
    if (filters.status.length > 0) {
      filtered = filtered.filter(exp => filters.status.includes(exp.status));
    }

    // Apply tags filter
    if (filters.tags.length > 0) {
      filtered = filtered.filter(exp =>
        filters.tags.some(tag => exp.tags.includes(tag))
      );
    }

    // Apply date range filter
    if (filters.dateRange.start || filters.dateRange.end) {
      filtered = filtered.filter(exp => {
        const createdDate = new Date(exp.createdAt);
        if (filters.dateRange.start && createdDate < filters.dateRange.start) return false;
        if (filters.dateRange.end && createdDate > filters.dateRange.end) return false;
        return true;
      });
    }

    // Apply created by filter
    if (filters.createdBy.length > 0) {
      filtered = filtered.filter(exp => filters.createdBy.includes(exp.createdBy));
    }

    // Sort experiments
    const sorted = [...filtered].sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (sortConfig.key) {
        case 'variantCount':
          aValue = a.variants.length;
          bValue = b.variants.length;
          break;
        case 'assignmentCount':
          aValue = a._count?.userAssignments || 0;
          bValue = b._count?.userAssignments || 0;
          break;
        case 'eventCount':
          aValue = a._count?.events || 0;
          bValue = b._count?.events || 0;
          break;
        default:
          aValue = a[sortConfig.key as keyof Experiment];
          bValue = b[sortConfig.key as keyof Experiment];
      }

      if (aValue === bValue) return 0;

      const comparison = aValue < bValue ? -1 : 1;
      return sortConfig.direction === 'desc' ? -comparison : comparison;
    });

    return sorted;
  }, [experiments, filters, sortConfig, currentTenant]);

  // Pagination
  const paginatedExperiments = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    return filteredAndSortedExperiments.slice(startIndex, startIndex + pageSize);
  }, [filteredAndSortedExperiments, currentPage, pageSize]);

  const totalPages = Math.ceil(filteredAndSortedExperiments.length / pageSize);

  // Handlers
  const handleSort = useCallback((key: SortConfig['key']) => {
    setSortConfig(prev => ({
      key,
      direction: prev.key === key && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  }, []);

  const handleFilterChange = useCallback((newFilters: Partial<FilterConfig>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setCurrentPage(1); // Reset to first page when filters change
  }, []);

  const handleSelectExperiment = useCallback((experimentId: string, selected: boolean) => {
    setSelectedExperiments(prev => {
      const newSet = new Set(prev);
      if (selected) {
        newSet.add(experimentId);
      } else {
        newSet.delete(experimentId);
      }
      return newSet;
    });
  }, []);

  const handleSelectAll = useCallback((selected: boolean) => {
    if (selected) {
      setSelectedExperiments(new Set(paginatedExperiments.map(exp => exp.id)));
    } else {
      setSelectedExperiments(new Set());
    }
  }, [paginatedExperiments]);

  const handleBulkAction = useCallback((action: string) => {
    const selectedExps = experiments.filter(exp => selectedExperiments.has(exp.id));
    onBulkAction?.(action, selectedExps);
    setSelectedExperiments(new Set());
  }, [experiments, selectedExperiments, onBulkAction]);

  // Utility functions
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    if (!isValid(date)) return 'Invalid date';
    return format(date, 'MMM d, yyyy');
  };

  const formatRelativeDate = (dateString: string) => {
    const date = new Date(dateString);
    if (!isValid(date)) return 'Invalid date';
    return formatDistanceToNow(date, { addSuffix: true });
  };

  const getAvailableActions = (experiment: Experiment) => {
    return statusConfig[experiment.status].actions.map(action => actionConfig[action]);
  };

  const SortableHeader: React.FC<{
    sortKey: SortConfig['key'];
    children: React.ReactNode;
    className?: string;
  }> = ({ sortKey, children, className = '' }) => (
    <th
      className={clsx(
        'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100',
        className
      )}
      onClick={() => handleSort(sortKey)}
    >
      <div className="flex items-center space-x-1">
        <span>{children}</span>
        {sortConfig.key === sortKey && (
          sortConfig.direction === 'asc' ?
            <ChevronUpIcon className="h-4 w-4" /> :
            <ChevronDownIcon className="h-4 w-4" />
        )}
      </div>
    </th>
  );

  if (loading) {
    return (
      <div className="bg-white shadow rounded-lg">
        <div className="animate-pulse">
          <div className="h-16 bg-gray-200 rounded-t-lg"></div>
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-20 bg-gray-100 border-t border-gray-200"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search experiments..."
              value={filters.search}
              onChange={(e) => handleFilterChange({ search: e.target.value })}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
            />
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={clsx(
              'inline-flex items-center px-4 py-2 border rounded-md shadow-sm text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500',
              showFilters
                ? 'border-indigo-300 text-indigo-700 bg-indigo-50 hover:bg-indigo-100'
                : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
            )}
          >
            <FunnelIcon className="h-4 w-4 mr-2" />
            Filters
            {(filters.status.length > 0 || filters.tags.length > 0 || filters.createdBy.length > 0) && (
              <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                {filters.status.length + filters.tags.length + filters.createdBy.length}
              </span>
            )}
          </button>

          {selectedExperiments.size > 0 && onBulkAction && (
            <Menu as="div" className="relative">
              <Menu.Button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                Bulk Actions ({selectedExperiments.size})
                <ChevronDownIcon className="ml-2 h-4 w-4" />
              </Menu.Button>
              <Transition
                enter="transition ease-out duration-100"
                enterFrom="transform opacity-0 scale-95"
                enterTo="transform opacity-100 scale-100"
                leave="transition ease-in duration-75"
                leaveFrom="transform opacity-100 scale-100"
                leaveTo="transform opacity-0 scale-95"
              >
                <Menu.Items className="absolute right-0 z-10 mt-2 w-48 origin-top-right bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                  <div className="py-1">
                    <Menu.Item>
                      {({ active }) => (
                        <button
                          onClick={() => handleBulkAction('archive')}
                          className={clsx(
                            active ? 'bg-gray-100 text-gray-900' : 'text-gray-700',
                            'group flex items-center px-4 py-2 text-sm w-full text-left'
                          )}
                        >
                          <ArchiveBoxIcon className="mr-3 h-4 w-4" />
                          Archive Selected
                        </button>
                      )}
                    </Menu.Item>
                    <Menu.Item>
                      {({ active }) => (
                        <button
                          onClick={() => handleBulkAction('delete')}
                          className={clsx(
                            active ? 'bg-gray-100 text-red-900' : 'text-red-700',
                            'group flex items-center px-4 py-2 text-sm w-full text-left'
                          )}
                        >
                          <TrashIcon className="mr-3 h-4 w-4" />
                          Delete Selected
                        </button>
                      )}
                    </Menu.Item>
                  </div>
                </Menu.Items>
              </Transition>
            </Menu>
          )}
        </div>
      </div>

      {/* Filter Panel */}
      {showFilters && (
        <div className="bg-gray-50 p-4 rounded-lg border">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
              <div className="space-y-2">
                {Object.entries(statusConfig).map(([status, config]) => (
                  <label key={status} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={filters.status.includes(status)}
                      onChange={(e) => {
                        const newStatus = e.target.checked
                          ? [...filters.status, status]
                          : filters.status.filter(s => s !== status);
                        handleFilterChange({ status: newStatus });
                      }}
                      className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">{config.label}</span>
                  </label>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
              <div className="space-y-2">
                <input
                  type="date"
                  value={filters.dateRange.start ? format(filters.dateRange.start, 'yyyy-MM-dd') : ''}
                  onChange={(e) => handleFilterChange({
                    dateRange: { ...filters.dateRange, start: e.target.value ? new Date(e.target.value) : undefined }
                  })}
                  className="block w-full text-sm border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                />
                <input
                  type="date"
                  value={filters.dateRange.end ? format(filters.dateRange.end, 'yyyy-MM-dd') : ''}
                  onChange={(e) => handleFilterChange({
                    dateRange: { ...filters.dateRange, end: e.target.value ? new Date(e.target.value) : undefined }
                  })}
                  className="block w-full text-sm border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Tags</label>
              <input
                type="text"
                placeholder="Enter tags..."
                onChange={(e) => {
                  const tags = e.target.value.split(',').map(tag => tag.trim()).filter(Boolean);
                  handleFilterChange({ tags });
                }}
                className="block w-full text-sm border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>

            <div className="flex items-end">
              <button
                onClick={() => setFilters({
                  status: [],
                  tags: [],
                  search: '',
                  dateRange: {},
                  createdBy: []
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Clear Filters
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Data Table */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {onBulkAction && (
                <th className="px-6 py-3 text-left">
                  <input
                    type="checkbox"
                    checked={selectedExperiments.size === paginatedExperiments.length && paginatedExperiments.length > 0}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                    className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                  />
                </th>
              )}
              <SortableHeader sortKey="name">Name</SortableHeader>
              <SortableHeader sortKey="status">Status</SortableHeader>
              <SortableHeader sortKey="variantCount">Variants</SortableHeader>
              <SortableHeader sortKey="assignmentCount">Assignments</SortableHeader>
              <SortableHeader sortKey="eventCount">Events</SortableHeader>
              <SortableHeader sortKey="startDate">Start Date</SortableHeader>
              <SortableHeader sortKey="createdAt">Created</SortableHeader>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {paginatedExperiments.map((experiment) => {
              const StatusIcon = statusConfig[experiment.status].icon;
              const availableActions = getAvailableActions(experiment);

              return (
                <tr
                  key={experiment.id}
                  className="hover:bg-gray-50 cursor-pointer"
                  onClick={() => onExperimentClick(experiment)}
                >
                  {onBulkAction && (
                    <td className="px-6 py-4 whitespace-nowrap" onClick={(e) => e.stopPropagation()}>
                      <input
                        type="checkbox"
                        checked={selectedExperiments.has(experiment.id)}
                        onChange={(e) => handleSelectExperiment(experiment.id, e.target.checked)}
                        className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                      />
                    </td>
                  )}

                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex flex-col">
                      <div className="text-sm font-medium text-gray-900 truncate max-w-xs">
                        {experiment.name}
                      </div>
                      {experiment.description && (
                        <div className="text-sm text-gray-500 truncate max-w-xs">
                          {experiment.description}
                        </div>
                      )}
                      {experiment.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-1">
                          {experiment.tags.slice(0, 3).map(tag => (
                            <span
                              key={tag}
                              className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800"
                            >
                              {tag}
                            </span>
                          ))}
                          {experiment.tags.length > 3 && (
                            <span className="text-xs text-gray-500">
                              +{experiment.tags.length - 3} more
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={clsx(
                      'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                      statusConfig[experiment.status].color
                    )}>
                      <StatusIcon className="h-3 w-3 mr-1" />
                      {statusConfig[experiment.status].label}
                    </span>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="flex items-center">
                      <ChartBarIcon className="h-4 w-4 text-gray-400 mr-1" />
                      {experiment.variants.length}
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="flex items-center">
                      <UserGroupIcon className="h-4 w-4 text-gray-400 mr-1" />
                      {(experiment._count?.userAssignments || 0).toLocaleString()}
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="flex items-center">
                      <ChartBarIcon className="h-4 w-4 text-gray-400 mr-1" />
                      {(experiment._count?.events || 0).toLocaleString()}
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {experiment.startDate ? (
                      <div className="flex items-center">
                        <CalendarIcon className="h-4 w-4 text-gray-400 mr-1" />
                        <div>
                          <div>{formatDate(experiment.startDate)}</div>
                          <div className="text-xs text-gray-400">
                            {formatRelativeDate(experiment.startDate)}
                          </div>
                        </div>
                      </div>
                    ) : (
                      <span className="text-gray-400">Not started</span>
                    )}
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="flex items-center">
                      <CalendarIcon className="h-4 w-4 text-gray-400 mr-1" />
                      <div>
                        <div>{formatDate(experiment.createdAt)}</div>
                        <div className="text-xs text-gray-400">
                          {formatRelativeDate(experiment.createdAt)}
                        </div>
                      </div>
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium" onClick={(e) => e.stopPropagation()}>
                    <div className="flex items-center space-x-2">
                      {/* Quick Actions */}
                      {availableActions.slice(0, 1).map((action) => (
                        <button
                          key={action.label}
                          onClick={() => onStatusChange(experiment, action.status)}
                          className="inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                        >
                          <action.icon className="h-3 w-3 mr-1" />
                          {action.label}
                        </button>
                      ))}

                      {/* More Actions Menu */}
                      <Menu as="div" className="relative">
                        <Menu.Button className="inline-flex items-center p-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50">
                          <EllipsisVerticalIcon className="h-4 w-4" />
                        </Menu.Button>
                        <Transition
                          enter="transition ease-out duration-100"
                          enterFrom="transform opacity-0 scale-95"
                          enterTo="transform opacity-100 scale-100"
                          leave="transition ease-in duration-75"
                          leaveFrom="transform opacity-100 scale-100"
                          leaveTo="transform opacity-0 scale-95"
                        >
                          <Menu.Items className="absolute right-0 z-10 mt-2 w-48 origin-top-right bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                            <div className="py-1">
                              <Menu.Item>
                                {({ active }) => (
                                  <button
                                    onClick={() => onExperimentClick(experiment)}
                                    className={clsx(
                                      active ? 'bg-gray-100 text-gray-900' : 'text-gray-700',
                                      'group flex items-center px-4 py-2 text-sm w-full text-left'
                                    )}
                                  >
                                    <EyeIcon className="mr-3 h-4 w-4" />
                                    View Details
                                  </button>
                                )}
                              </Menu.Item>
                              <Menu.Item>
                                {({ active }) => (
                                  <button
                                    onClick={() => onEdit(experiment)}
                                    className={clsx(
                                      active ? 'bg-gray-100 text-gray-900' : 'text-gray-700',
                                      'group flex items-center px-4 py-2 text-sm w-full text-left'
                                    )}
                                  >
                                    <PencilIcon className="mr-3 h-4 w-4" />
                                    Edit
                                  </button>
                                )}
                              </Menu.Item>
                              <Menu.Item>
                                {({ active }) => (
                                  <button
                                    onClick={() => onDuplicate(experiment)}
                                    className={clsx(
                                      active ? 'bg-gray-100 text-gray-900' : 'text-gray-700',
                                      'group flex items-center px-4 py-2 text-sm w-full text-left'
                                    )}
                                  >
                                    <DocumentDuplicateIcon className="mr-3 h-4 w-4" />
                                    Duplicate
                                  </button>
                                )}
                              </Menu.Item>

                              {/* Additional Status Actions */}
                              {availableActions.slice(1).map((action) => (
                                <Menu.Item key={action.label}>
                                  {({ active }) => (
                                    <button
                                      onClick={() => onStatusChange(experiment, action.status)}
                                      className={clsx(
                                        active ? 'bg-gray-100 text-gray-900' : 'text-gray-700',
                                        'group flex items-center px-4 py-2 text-sm w-full text-left'
                                      )}
                                    >
                                      <action.icon className="mr-3 h-4 w-4" />
                                      {action.label}
                                    </button>
                                  )}
                                </Menu.Item>
                              ))}

                              <Menu.Item>
                                {({ active }) => (
                                  <button
                                    onClick={() => onDelete(experiment)}
                                    className={clsx(
                                      active ? 'bg-gray-100 text-red-900' : 'text-red-700',
                                      'group flex items-center px-4 py-2 text-sm w-full text-left'
                                    )}
                                  >
                                    <TrashIcon className="mr-3 h-4 w-4" />
                                    Delete
                                  </button>
                                )}
                              </Menu.Item>
                            </div>
                          </Menu.Items>
                        </Transition>
                      </Menu>
                    </div>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>

        {/* Empty State */}
        {filteredAndSortedExperiments.length === 0 && (
          <div className="text-center py-12">
            <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No experiments found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {filters.search || filters.status.length > 0 || filters.tags.length > 0
                ? 'Try adjusting your search or filter criteria.'
                : 'Get started by creating your first experiment.'}
            </p>
          </div>
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
          <div className="flex flex-1 justify-between sm:hidden">
            <button
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
              className="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button
              onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
              disabled={currentPage === totalPages}
              className="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
            <div className="flex items-center space-x-2">
              <p className="text-sm text-gray-700">
                Showing{' '}
                <span className="font-medium">{(currentPage - 1) * pageSize + 1}</span>
                {' '}to{' '}
                <span className="font-medium">
                  {Math.min(currentPage * pageSize, filteredAndSortedExperiments.length)}
                </span>
                {' '}of{' '}
                <span className="font-medium">{filteredAndSortedExperiments.length}</span>
                {' '}results
              </p>
              <select
                value={pageSize}
                onChange={(e) => {
                  setPageSize(Number(e.target.value));
                  setCurrentPage(1);
                }}
                className="border-gray-300 rounded-md text-sm"
              >
                <option value={10}>10 per page</option>
                <option value={20}>20 per page</option>
                <option value={50}>50 per page</option>
                <option value={100}>100 per page</option>
              </select>
            </div>
            <div>
              <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>

                {/* Page Numbers */}
                {[...Array(Math.min(5, totalPages))].map((_, i) => {
                  const pageNum = i + 1;
                  return (
                    <button
                      key={pageNum}
                      onClick={() => setCurrentPage(pageNum)}
                      className={clsx(
                        'relative inline-flex items-center px-4 py-2 text-sm font-semibold ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0',
                        currentPage === pageNum
                          ? 'bg-indigo-600 text-white ring-indigo-600'
                          : 'text-gray-900'
                      )}
                    >
                      {pageNum}
                    </button>
                  );
                })}

                <button
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                  className="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
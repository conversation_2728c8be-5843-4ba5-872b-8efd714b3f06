// Experiment list component with filtering and pagination
import React, { useState } from 'react';
import { 
  PlusIcon, 
  FunnelIcon, 
  MagnifyingGlassIcon,
  EllipsisVerticalIcon,
  PlayIcon,
  PauseIcon,
  StopIcon,
  PencilIcon,
  TrashIcon,
  DocumentDuplicateIcon
} from '@heroicons/react/24/outline';
import { Menu, Transition } from '@headlessui/react';
import { clsx } from 'clsx';
import { format } from 'date-fns';
import { Experiment, ExperimentFilters, PaginationParams, ExperimentStatus } from '../../types/experiment';

interface ExperimentListProps {
  experiments: Experiment[];
  loading: boolean;
  error?: string;
  onExperimentSelect: (experiment: Experiment) => void;
  onExperimentCreate: () => void;
  onExperimentEdit: (experiment: Experiment) => void;
  onExperimentDelete: (experiment: Experiment) => void;
  onExperimentDuplicate: (experiment: Experiment) => void;
  onStatusChange: (experiment: Experiment, status: ExperimentStatus) => void;
  filters: ExperimentFilters;
  onFiltersChange: (filters: ExperimentFilters) => void;
  pagination: PaginationParams;
  onPaginationChange: (pagination: PaginationParams) => void;
  totalCount: number;
}

const statusColors: Record<ExperimentStatus, string> = {
  DRAFT: 'bg-gray-100 text-gray-800',
  ACTIVE: 'bg-green-100 text-green-800',
  PAUSED: 'bg-yellow-100 text-yellow-800',
  COMPLETED: 'bg-blue-100 text-blue-800',
  ARCHIVED: 'bg-gray-100 text-gray-600',
};

const statusIcons: Record<ExperimentStatus, React.ComponentType<{ className?: string }>> = {
  DRAFT: PencilIcon,
  ACTIVE: PlayIcon,
  PAUSED: PauseIcon,
  COMPLETED: StopIcon,
  ARCHIVED: StopIcon,
};

export const ExperimentList: React.FC<ExperimentListProps> = ({
  experiments,
  loading,
  error,
  onExperimentSelect,
  onExperimentCreate,
  onExperimentEdit,
  onExperimentDelete,
  onExperimentDuplicate,
  onStatusChange,
  filters,
  onFiltersChange,
  pagination,
  onPaginationChange,
  totalCount,
}) => {
  const [showFilters, setShowFilters] = useState(false);

  const handleSearchChange = (search: string) => {
    onFiltersChange({ ...filters, search });
  };

  const handleStatusFilter = (status: ExperimentStatus) => {
    const currentStatuses = filters.status || [];
    const newStatuses = currentStatuses.includes(status)
      ? currentStatuses.filter(s => s !== status)
      : [...currentStatuses, status];
    
    onFiltersChange({ ...filters, status: newStatuses });
  };

  const getStatusActions = (experiment: Experiment) => {
    const actions = [];
    
    switch (experiment.status) {
      case 'DRAFT':
        actions.push({ label: 'Start', action: () => onStatusChange(experiment, 'ACTIVE'), icon: PlayIcon });
        break;
      case 'ACTIVE':
        actions.push({ label: 'Pause', action: () => onStatusChange(experiment, 'PAUSED'), icon: PauseIcon });
        actions.push({ label: 'Complete', action: () => onStatusChange(experiment, 'COMPLETED'), icon: StopIcon });
        break;
      case 'PAUSED':
        actions.push({ label: 'Resume', action: () => onStatusChange(experiment, 'ACTIVE'), icon: PlayIcon });
        actions.push({ label: 'Complete', action: () => onStatusChange(experiment, 'COMPLETED'), icon: StopIcon });
        break;
      case 'COMPLETED':
        actions.push({ label: 'Archive', action: () => onStatusChange(experiment, 'ARCHIVED'), icon: StopIcon });
        break;
    }
    
    return actions;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">Error loading experiments</div>
        <div className="text-gray-500">{error}</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Experiments</h1>
          <p className="text-gray-500">Manage your A/B testing experiments</p>
        </div>
        <button
          onClick={onExperimentCreate}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          New Experiment
        </button>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search experiments..."
              value={filters.search || ''}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
            />
          </div>
        </div>
        <button
          onClick={() => setShowFilters(!showFilters)}
          className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          <FunnelIcon className="h-4 w-4 mr-2" />
          Filters
        </button>
      </div>

      {/* Filter Panel */}
      {showFilters && (
        <div className="bg-gray-50 p-4 rounded-lg border">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
              <div className="space-y-2">
                {(['DRAFT', 'ACTIVE', 'PAUSED', 'COMPLETED', 'ARCHIVED'] as ExperimentStatus[]).map(status => (
                  <label key={status} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={filters.status?.includes(status) || false}
                      onChange={() => handleStatusFilter(status)}
                      className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">{status}</span>
                  </label>
                ))}
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Tags</label>
              <input
                type="text"
                placeholder="Enter tags..."
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
              <div className="space-y-2">
                <input
                  type="date"
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                />
                <input
                  type="date"
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Experiment Table */}
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        <ul className="divide-y divide-gray-200">
          {experiments.map((experiment) => {
            const StatusIcon = statusIcons[experiment.status];
            const statusActions = getStatusActions(experiment);
            
            return (
              <li key={experiment.id} className="hover:bg-gray-50">
                <div className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-3">
                        <button
                          onClick={() => onExperimentSelect(experiment)}
                          className="text-lg font-medium text-indigo-600 hover:text-indigo-900 truncate"
                        >
                          {experiment.name}
                        </button>
                        <span className={clsx(
                          'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                          statusColors[experiment.status]
                        )}>
                          <StatusIcon className="h-3 w-3 mr-1" />
                          {experiment.status}
                        </span>
                      </div>
                      <div className="mt-2 flex items-center text-sm text-gray-500 space-x-4">
                        <span>{experiment.variants?.length || 0} variants</span>
                        <span>{experiment._count?.userAssignments || 0} assignments</span>
                        <span>{experiment._count?.events || 0} events</span>
                        {experiment.startDate && (
                          <span>Started {format(new Date(experiment.startDate), 'MMM d, yyyy')}</span>
                        )}
                      </div>
                      {experiment.description && (
                        <p className="mt-1 text-sm text-gray-600 line-clamp-2">{experiment.description}</p>
                      )}
                      {experiment.tags.length > 0 && (
                        <div className="mt-2 flex flex-wrap gap-1">
                          {experiment.tags.map(tag => (
                            <span
                              key={tag}
                              className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      {/* Quick Status Actions */}
                      {statusActions.slice(0, 1).map((action, index) => (
                        <button
                          key={index}
                          onClick={action.action}
                          className="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        >
                          <action.icon className="h-3 w-3 mr-1" />
                          {action.label}
                        </button>
                      ))}
                      
                      {/* More Actions Menu */}
                      <Menu as="div" className="relative">
                        <Menu.Button className="inline-flex items-center p-2 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                          <EllipsisVerticalIcon className="h-4 w-4" />
                        </Menu.Button>
                        <Transition
                          enter="transition ease-out duration-100"
                          enterFrom="transform opacity-0 scale-95"
                          enterTo="transform opacity-100 scale-100"
                          leave="transition ease-in duration-75"
                          leaveFrom="transform opacity-100 scale-100"
                          leaveTo="transform opacity-0 scale-95"
                        >
                          <Menu.Items className="absolute right-0 z-10 mt-2 w-48 origin-top-right bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                            <div className="py-1">
                              <Menu.Item>
                                {({ active }) => (
                                  <button
                                    onClick={() => onExperimentEdit(experiment)}
                                    className={clsx(
                                      active ? 'bg-gray-100 text-gray-900' : 'text-gray-700',
                                      'group flex items-center px-4 py-2 text-sm w-full text-left'
                                    )}
                                  >
                                    <PencilIcon className="mr-3 h-4 w-4" />
                                    Edit
                                  </button>
                                )}
                              </Menu.Item>
                              <Menu.Item>
                                {({ active }) => (
                                  <button
                                    onClick={() => onExperimentDuplicate(experiment)}
                                    className={clsx(
                                      active ? 'bg-gray-100 text-gray-900' : 'text-gray-700',
                                      'group flex items-center px-4 py-2 text-sm w-full text-left'
                                    )}
                                  >
                                    <DocumentDuplicateIcon className="mr-3 h-4 w-4" />
                                    Duplicate
                                  </button>
                                )}
                              </Menu.Item>
                              {statusActions.slice(1).map((action, index) => (
                                <Menu.Item key={index}>
                                  {({ active }) => (
                                    <button
                                      onClick={action.action}
                                      className={clsx(
                                        active ? 'bg-gray-100 text-gray-900' : 'text-gray-700',
                                        'group flex items-center px-4 py-2 text-sm w-full text-left'
                                      )}
                                    >
                                      <action.icon className="mr-3 h-4 w-4" />
                                      {action.label}
                                    </button>
                                  )}
                                </Menu.Item>
                              ))}
                              <Menu.Item>
                                {({ active }) => (
                                  <button
                                    onClick={() => onExperimentDelete(experiment)}
                                    className={clsx(
                                      active ? 'bg-gray-100 text-red-900' : 'text-red-700',
                                      'group flex items-center px-4 py-2 text-sm w-full text-left'
                                    )}
                                  >
                                    <TrashIcon className="mr-3 h-4 w-4" />
                                    Delete
                                  </button>
                                )}
                              </Menu.Item>
                            </div>
                          </Menu.Items>
                        </Transition>
                      </Menu>
                    </div>
                  </div>
                </div>
              </li>
            );
          })}
        </ul>
      </div>

      {/* Pagination */}
      {totalCount > pagination.limit && (
        <div className="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
          <div className="flex flex-1 justify-between sm:hidden">
            <button
              onClick={() => onPaginationChange({ ...pagination, page: pagination.page - 1 })}
              disabled={pagination.page === 1}
              className="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button
              onClick={() => onPaginationChange({ ...pagination, page: pagination.page + 1 })}
              disabled={pagination.page * pagination.limit >= totalCount}
              className="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing{' '}
                <span className="font-medium">{(pagination.page - 1) * pagination.limit + 1}</span>
                {' '}to{' '}
                <span className="font-medium">
                  {Math.min(pagination.page * pagination.limit, totalCount)}
                </span>
                {' '}of{' '}
                <span className="font-medium">{totalCount}</span>
                {' '}results
              </p>
            </div>
            <div>
              <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                <button
                  onClick={() => onPaginationChange({ ...pagination, page: pagination.page - 1 })}
                  disabled={pagination.page === 1}
                  className="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>
                <button
                  onClick={() => onPaginationChange({ ...pagination, page: pagination.page + 1 })}
                  disabled={pagination.page * pagination.limit >= totalCount}
                  className="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

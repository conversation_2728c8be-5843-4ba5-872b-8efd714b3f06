// Targeting rules editor component
import React, { useState } from 'react';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { 
  PlusIcon, 
  TrashIcon, 
  GripVerticalIcon,
  EyeIcon,
  EyeSlashIcon
} from '@heroicons/react/24/outline';
import { clsx } from 'clsx';
import { CreateTargetingRuleForm, TargetingOperator } from '../../types/experiment';

interface TargetingRulesEditorProps {
  rules: CreateTargetingRuleForm[];
  onChange: (rules: CreateTargetingRuleForm[]) => void;
  errors?: Record<string, string>;
  readonly?: boolean;
}

const operatorLabels: Record<TargetingOperator, string> = {
  EQUALS: 'Equals',
  NOT_EQUALS: 'Not Equals',
  IN: 'In List',
  NOT_IN: 'Not In List',
  GREATER_THAN: 'Greater Than',
  LESS_THAN: 'Less Than',
  CONTAINS: 'Contains',
  REGEX: 'Matches Regex',
};

const commonAttributes = [
  'country',
  'region',
  'city',
  'device_type',
  'browser',
  'os',
  'user_agent',
  'subscription_tier',
  'user_type',
  'age',
  'gender',
  'language',
  'timezone',
];

const ValueEditor: React.FC<{
  operator: TargetingOperator;
  value: string | number | boolean | string[];
  onChange: (value: string | number | boolean | string[]) => void;
}> = ({ operator, value, onChange }) => {
  const [listValue, setListValue] = useState(
    Array.isArray(value) ? value.join(', ') : ''
  );

  const handleListChange = (newValue: string) => {
    setListValue(newValue);
    const items = newValue.split(',').map(item => item.trim()).filter(Boolean);
    onChange(items);
  };

  switch (operator) {
    case 'IN':
    case 'NOT_IN':
      return (
        <input
          type="text"
          value={listValue}
          onChange={(e) => handleListChange(e.target.value)}
          placeholder="value1, value2, value3"
          className="block w-full text-sm border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
        />
      );

    case 'GREATER_THAN':
    case 'LESS_THAN':
      return (
        <input
          type="number"
          value={typeof value === 'number' ? value : ''}
          onChange={(e) => onChange(parseFloat(e.target.value) || 0)}
          className="block w-full text-sm border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
        />
      );

    default:
      return (
        <input
          type="text"
          value={typeof value === 'string' ? value : String(value)}
          onChange={(e) => onChange(e.target.value)}
          className="block w-full text-sm border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
        />
      );
  }
};

export const TargetingRulesEditor: React.FC<TargetingRulesEditorProps> = ({
  rules,
  onChange,
  errors,
  readonly = false,
}) => {
  const [showInactive, setShowInactive] = useState(true);

  const handleRuleChange = (index: number, field: keyof CreateTargetingRuleForm, value: any) => {
    const newRules = [...rules];
    newRules[index] = { ...newRules[index], [field]: value };
    
    // Reset value when operator changes
    if (field === 'operator') {
      switch (value) {
        case 'IN':
        case 'NOT_IN':
          newRules[index].value = [];
          break;
        case 'GREATER_THAN':
        case 'LESS_THAN':
          newRules[index].value = 0;
          break;
        default:
          newRules[index].value = '';
      }
    }
    
    onChange(newRules);
  };

  const handleAddRule = () => {
    const newRule: CreateTargetingRuleForm = {
      name: `Rule ${rules.length + 1}`,
      attributeName: '',
      operator: 'EQUALS',
      value: '',
      isActive: true,
      priority: rules.length + 1,
    };
    onChange([...rules, newRule]);
  };

  const handleRemoveRule = (index: number) => {
    const newRules = rules.filter((_, i) => i !== index);
    // Reorder priorities
    newRules.forEach((rule, i) => {
      rule.priority = i + 1;
    });
    onChange(newRules);
  };

  const handleDragEnd = (result: any) => {
    if (!result.destination || readonly) return;

    const newRules = Array.from(rules);
    const [reorderedItem] = newRules.splice(result.source.index, 1);
    newRules.splice(result.destination.index, 0, reorderedItem);

    // Update priorities based on new order
    newRules.forEach((rule, index) => {
      rule.priority = index + 1;
    });

    onChange(newRules);
  };

  const visibleRules = showInactive ? rules : rules.filter(rule => rule.isActive);

  if (readonly) {
    return (
      <div className="space-y-3">
        {rules.length === 0 ? (
          <p className="text-sm text-gray-500 italic">No targeting rules configured</p>
        ) : (
          rules
            .sort((a, b) => a.priority - b.priority)
            .map((rule, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-3">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900">{rule.name}</h4>
                  <div className="flex items-center space-x-2">
                    <span className="text-xs text-gray-500">Priority {rule.priority}</span>
                    <span className={clsx(
                      'inline-flex items-center px-2 py-0.5 rounded text-xs font-medium',
                      rule.isActive 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-gray-100 text-gray-800'
                    )}>
                      {rule.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </div>
                <div className="text-sm text-gray-600">
                  <span className="font-medium">{rule.attributeName}</span>
                  {' '}
                  <span className="text-gray-500">{operatorLabels[rule.operator]}</span>
                  {' '}
                  <span className="font-medium">
                    {Array.isArray(rule.value) ? rule.value.join(', ') : String(rule.value)}
                  </span>
                </div>
              </div>
            ))
        )}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {rules.length > 0 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-600">
            Rules are evaluated in priority order. Higher priority rules are checked first.
          </p>
          <button
            type="button"
            onClick={() => setShowInactive(!showInactive)}
            className="inline-flex items-center text-xs text-gray-500 hover:text-gray-700"
          >
            {showInactive ? (
              <>
                <EyeSlashIcon className="h-3 w-3 mr-1" />
                Hide Inactive
              </>
            ) : (
              <>
                <EyeIcon className="h-3 w-3 mr-1" />
                Show All
              </>
            )}
          </button>
        </div>
      )}

      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="targeting-rules">
          {(provided) => (
            <div {...provided.droppableProps} ref={provided.innerRef} className="space-y-3">
              {visibleRules.map((rule, index) => (
                <Draggable key={index} draggableId={`rule-${index}`} index={index}>
                  {(provided, snapshot) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      className={clsx(
                        'border border-gray-200 rounded-lg p-4 bg-white',
                        snapshot.isDragging && 'shadow-lg',
                        !rule.isActive && 'opacity-60'
                      )}
                    >
                      <div className="flex items-start space-x-4">
                        {/* Drag Handle */}
                        <div
                          {...provided.dragHandleProps}
                          className="mt-2 text-gray-400 hover:text-gray-600 cursor-move"
                        >
                          <GripVerticalIcon className="h-5 w-5" />
                        </div>

                        <div className="flex-1 space-y-4">
                          {/* Rule Header */}
                          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                              <label className="block text-sm font-medium text-gray-700">
                                Rule Name
                              </label>
                              <input
                                type="text"
                                value={rule.name}
                                onChange={(e) => handleRuleChange(index, 'name', e.target.value)}
                                className="mt-1 block w-full text-sm border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                                placeholder="Enter rule name"
                              />
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700">
                                Priority
                              </label>
                              <input
                                type="number"
                                min="1"
                                value={rule.priority}
                                onChange={(e) => handleRuleChange(index, 'priority', parseInt(e.target.value) || 1)}
                                className="mt-1 block w-full text-sm border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                              />
                            </div>

                            <div className="flex items-center">
                              <label className="flex items-center mt-6">
                                <input
                                  type="checkbox"
                                  checked={rule.isActive}
                                  onChange={(e) => handleRuleChange(index, 'isActive', e.target.checked)}
                                  className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                                />
                                <span className="ml-2 text-sm text-gray-700">Active</span>
                              </label>
                            </div>

                            <div className="flex items-center justify-end">
                              <button
                                type="button"
                                onClick={() => handleRemoveRule(index)}
                                className="mt-6 text-red-500 hover:text-red-700"
                              >
                                <TrashIcon className="h-4 w-4" />
                              </button>
                            </div>
                          </div>

                          {/* Rule Condition */}
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                              <label className="block text-sm font-medium text-gray-700">
                                Attribute
                              </label>
                              <div className="mt-1 relative">
                                <input
                                  type="text"
                                  list={`attributes-${index}`}
                                  value={rule.attributeName}
                                  onChange={(e) => handleRuleChange(index, 'attributeName', e.target.value)}
                                  className="block w-full text-sm border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                                  placeholder="e.g., country"
                                />
                                <datalist id={`attributes-${index}`}>
                                  {commonAttributes.map(attr => (
                                    <option key={attr} value={attr} />
                                  ))}
                                </datalist>
                              </div>
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700">
                                Operator
                              </label>
                              <select
                                value={rule.operator}
                                onChange={(e) => handleRuleChange(index, 'operator', e.target.value as TargetingOperator)}
                                className="mt-1 block w-full text-sm border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                              >
                                {Object.entries(operatorLabels).map(([value, label]) => (
                                  <option key={value} value={value}>
                                    {label}
                                  </option>
                                ))}
                              </select>
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700">
                                Value
                              </label>
                              <div className="mt-1">
                                <ValueEditor
                                  operator={rule.operator}
                                  value={rule.value}
                                  onChange={(value) => handleRuleChange(index, 'value', value)}
                                />
                              </div>
                            </div>
                          </div>

                          {/* Rule Preview */}
                          <div className="bg-gray-50 rounded p-3">
                            <p className="text-sm text-gray-700">
                              <span className="font-medium">Rule:</span> Include users where{' '}
                              <span className="font-medium text-indigo-600">{rule.attributeName || '[attribute]'}</span>
                              {' '}
                              <span className="text-gray-500">{operatorLabels[rule.operator].toLowerCase()}</span>
                              {' '}
                              <span className="font-medium text-indigo-600">
                                {Array.isArray(rule.value) 
                                  ? `[${rule.value.join(', ')}]`
                                  : rule.value || '[value]'
                                }
                              </span>
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>

      {/* Add Rule Button */}
      <button
        type="button"
        onClick={handleAddRule}
        className="w-full flex items-center justify-center px-4 py-2 border-2 border-dashed border-gray-300 rounded-lg text-sm font-medium text-gray-600 hover:text-gray-800 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
      >
        <PlusIcon className="h-5 w-5 mr-2" />
        Add Targeting Rule
      </button>

      {rules.length === 0 && (
        <div className="text-center py-6">
          <p className="text-sm text-gray-500">
            No targeting rules configured. All users will be eligible for this experiment.
          </p>
        </div>
      )}

      {/* Errors */}
      {errors && (
        <div className="text-sm text-red-600">
          {Object.values(errors).map((error, index) => (
            <div key={index}>{error}</div>
          ))}
        </div>
      )}
    </div>
  );
};

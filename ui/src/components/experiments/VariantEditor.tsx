// Variant editor component for managing experiment variants
import React, { useState } from 'react';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { 
  PlusIcon, 
  TrashIcon, 
  GripVerticalIcon,
  EyeIcon,
  EyeSlashIcon
} from '@heroicons/react/24/outline';
import { clsx } from 'clsx';
import { CreateVariantForm } from '../../types/experiment';

interface VariantEditorProps {
  variants: CreateVariantForm[];
  onChange: (variants: CreateVariantForm[]) => void;
  errors?: Record<string, string>;
  readonly?: boolean;
}

interface ConfigurationEditorProps {
  configuration: Record<string, any>;
  onChange: (configuration: Record<string, any>) => void;
  variantName: string;
}

const ConfigurationEditor: React.FC<ConfigurationEditorProps> = ({
  configuration,
  onChange,
  variantName,
}) => {
  const [showJson, setShowJson] = useState(false);
  const [jsonValue, setJsonValue] = useState(JSON.stringify(configuration, null, 2));

  const handleKeyValueChange = (key: string, value: any, oldKey?: string) => {
    const newConfig = { ...configuration };
    
    if (oldKey && oldKey !== key) {
      delete newConfig[oldKey];
    }
    
    if (key) {
      newConfig[key] = value;
    }
    
    onChange(newConfig);
  };

  const handleAddKeyValue = () => {
    const newConfig = { ...configuration, '': '' };
    onChange(newConfig);
  };

  const handleRemoveKeyValue = (key: string) => {
    const newConfig = { ...configuration };
    delete newConfig[key];
    onChange(newConfig);
  };

  const handleJsonChange = (value: string) => {
    setJsonValue(value);
    try {
      const parsed = JSON.parse(value);
      onChange(parsed);
    } catch (e) {
      // Invalid JSON, don't update
    }
  };

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <label className="block text-sm font-medium text-gray-700">
          Configuration
        </label>
        <button
          type="button"
          onClick={() => setShowJson(!showJson)}
          className="inline-flex items-center text-xs text-gray-500 hover:text-gray-700"
        >
          {showJson ? (
            <>
              <EyeSlashIcon className="h-3 w-3 mr-1" />
              Hide JSON
            </>
          ) : (
            <>
              <EyeIcon className="h-3 w-3 mr-1" />
              Show JSON
            </>
          )}
        </button>
      </div>

      {showJson ? (
        <textarea
          value={jsonValue}
          onChange={(e) => handleJsonChange(e.target.value)}
          rows={6}
          className="block w-full text-sm font-mono border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
          placeholder="Enter JSON configuration"
        />
      ) : (
        <div className="space-y-2">
          {Object.entries(configuration).map(([key, value], index) => (
            <div key={index} className="flex items-center space-x-2">
              <input
                type="text"
                value={key}
                onChange={(e) => handleKeyValueChange(e.target.value, value, key)}
                placeholder="Key"
                className="flex-1 text-sm border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
              />
              <input
                type="text"
                value={typeof value === 'string' ? value : JSON.stringify(value)}
                onChange={(e) => {
                  let newValue: any = e.target.value;
                  // Try to parse as JSON for non-string values
                  try {
                    if (newValue.startsWith('{') || newValue.startsWith('[') || newValue === 'true' || newValue === 'false' || !isNaN(Number(newValue))) {
                      newValue = JSON.parse(newValue);
                    }
                  } catch (e) {
                    // Keep as string if parsing fails
                  }
                  handleKeyValueChange(key, newValue);
                }}
                placeholder="Value"
                className="flex-1 text-sm border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
              />
              <button
                type="button"
                onClick={() => handleRemoveKeyValue(key)}
                className="text-red-500 hover:text-red-700"
              >
                <TrashIcon className="h-4 w-4" />
              </button>
            </div>
          ))}
          <button
            type="button"
            onClick={handleAddKeyValue}
            className="inline-flex items-center text-sm text-indigo-600 hover:text-indigo-800"
          >
            <PlusIcon className="h-4 w-4 mr-1" />
            Add Property
          </button>
        </div>
      )}
    </div>
  );
};

export const VariantEditor: React.FC<VariantEditorProps> = ({
  variants,
  onChange,
  errors,
  readonly = false,
}) => {
  const handleVariantChange = (index: number, field: keyof CreateVariantForm, value: any) => {
    const newVariants = [...variants];
    newVariants[index] = { ...newVariants[index], [field]: value };
    
    // If setting a variant as control, unset others
    if (field === 'isControl' && value === true) {
      newVariants.forEach((variant, i) => {
        if (i !== index) {
          variant.isControl = false;
        }
      });
    }
    
    onChange(newVariants);
  };

  const handleAddVariant = () => {
    const newVariant: CreateVariantForm = {
      name: `Variant ${variants.length + 1}`,
      description: '',
      isControl: false,
      trafficWeight: 0,
      configuration: {},
    };
    onChange([...variants, newVariant]);
  };

  const handleRemoveVariant = (index: number) => {
    if (variants.length <= 2) return; // Minimum 2 variants required
    const newVariants = variants.filter((_, i) => i !== index);
    onChange(newVariants);
  };

  const handleDragEnd = (result: any) => {
    if (!result.destination || readonly) return;

    const newVariants = Array.from(variants);
    const [reorderedItem] = newVariants.splice(result.source.index, 1);
    newVariants.splice(result.destination.index, 0, reorderedItem);

    onChange(newVariants);
  };

  const redistributeTraffic = () => {
    const equalWeight = 1 / variants.length;
    const newVariants = variants.map(variant => ({
      ...variant,
      trafficWeight: equalWeight,
    }));
    onChange(newVariants);
  };

  const totalWeight = variants.reduce((sum, variant) => sum + variant.trafficWeight, 0);
  const isWeightValid = Math.abs(totalWeight - 1) < 0.001;

  if (readonly) {
    return (
      <div className="space-y-4">
        {variants.map((variant, index) => (
          <div key={index} className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-3">
                <h4 className="font-medium text-gray-900">{variant.name}</h4>
                {variant.isControl && (
                  <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                    Control
                  </span>
                )}
              </div>
              <span className="text-sm text-gray-500">
                {(variant.trafficWeight * 100).toFixed(1)}%
              </span>
            </div>
            {variant.description && (
              <p className="text-sm text-gray-600 mb-3">{variant.description}</p>
            )}
            {Object.keys(variant.configuration).length > 0 && (
              <div className="bg-gray-50 rounded p-3">
                <pre className="text-xs text-gray-700 overflow-x-auto">
                  {JSON.stringify(variant.configuration, null, 2)}
                </pre>
              </div>
            )}
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Traffic Weight Summary */}
      <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
        <div className="flex items-center space-x-4">
          <span className="text-sm font-medium text-gray-700">
            Total Traffic: {(totalWeight * 100).toFixed(1)}%
          </span>
          {!isWeightValid && (
            <span className="text-sm text-red-600">
              ⚠️ Traffic weights must sum to 100%
            </span>
          )}
        </div>
        <button
          type="button"
          onClick={redistributeTraffic}
          className="text-sm text-indigo-600 hover:text-indigo-800"
        >
          Distribute Equally
        </button>
      </div>

      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="variants">
          {(provided) => (
            <div {...provided.droppableProps} ref={provided.innerRef} className="space-y-4">
              {variants.map((variant, index) => (
                <Draggable key={index} draggableId={`variant-${index}`} index={index}>
                  {(provided, snapshot) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      className={clsx(
                        'border border-gray-200 rounded-lg p-4 bg-white',
                        snapshot.isDragging && 'shadow-lg'
                      )}
                    >
                      <div className="flex items-start space-x-4">
                        {/* Drag Handle */}
                        <div
                          {...provided.dragHandleProps}
                          className="mt-2 text-gray-400 hover:text-gray-600 cursor-move"
                        >
                          <GripVerticalIcon className="h-5 w-5" />
                        </div>

                        <div className="flex-1 space-y-4">
                          {/* Variant Header */}
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                              <label className="block text-sm font-medium text-gray-700">
                                Variant Name
                              </label>
                              <input
                                type="text"
                                value={variant.name}
                                onChange={(e) => handleVariantChange(index, 'name', e.target.value)}
                                className="mt-1 block w-full text-sm border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                                placeholder="Enter variant name"
                              />
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700">
                                Traffic Weight (%)
                              </label>
                              <input
                                type="number"
                                step="0.1"
                                min="0"
                                max="100"
                                value={(variant.trafficWeight * 100).toFixed(1)}
                                onChange={(e) => handleVariantChange(index, 'trafficWeight', parseFloat(e.target.value) / 100)}
                                className="mt-1 block w-full text-sm border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                              />
                            </div>

                            <div className="flex items-center justify-between">
                              <label className="flex items-center">
                                <input
                                  type="checkbox"
                                  checked={variant.isControl}
                                  onChange={(e) => handleVariantChange(index, 'isControl', e.target.checked)}
                                  className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                                />
                                <span className="ml-2 text-sm text-gray-700">Control Variant</span>
                              </label>

                              {variants.length > 2 && (
                                <button
                                  type="button"
                                  onClick={() => handleRemoveVariant(index)}
                                  className="text-red-500 hover:text-red-700"
                                >
                                  <TrashIcon className="h-4 w-4" />
                                </button>
                              )}
                            </div>
                          </div>

                          {/* Description */}
                          <div>
                            <label className="block text-sm font-medium text-gray-700">
                              Description
                            </label>
                            <input
                              type="text"
                              value={variant.description || ''}
                              onChange={(e) => handleVariantChange(index, 'description', e.target.value)}
                              className="mt-1 block w-full text-sm border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                              placeholder="Optional description"
                            />
                          </div>

                          {/* Configuration */}
                          <ConfigurationEditor
                            configuration={variant.configuration}
                            onChange={(config) => handleVariantChange(index, 'configuration', config)}
                            variantName={variant.name}
                          />
                        </div>
                      </div>
                    </div>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>

      {/* Add Variant Button */}
      <button
        type="button"
        onClick={handleAddVariant}
        className="w-full flex items-center justify-center px-4 py-2 border-2 border-dashed border-gray-300 rounded-lg text-sm font-medium text-gray-600 hover:text-gray-800 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
      >
        <PlusIcon className="h-5 w-5 mr-2" />
        Add Variant
      </button>

      {/* Errors */}
      {errors && (
        <div className="text-sm text-red-600">
          {Object.values(errors).map((error, index) => (
            <div key={index}>{error}</div>
          ))}
        </div>
      )}
    </div>
  );
};

// Experiment detail view component
import React, { useState } from 'react';
import { 
  PencilIcon, 
  TrashIcon, 
  DocumentDuplicateIcon,
  ChartBarIcon,
  UserGroupIcon,
  CalendarIcon,
  TagIcon,
  BeakerIcon,
  ArrowLeftIcon
} from '@heroicons/react/24/outline';
import { Tab } from '@headlessui/react';
import { clsx } from 'clsx';
import { format } from 'date-fns';
import { Experiment, ExperimentAnalytics } from '../../types/experiment';
import { StatusControls } from './StatusControls';
import { VariantList } from './VariantList';
import { TargetingRulesList } from './TargetingRulesList';
import { ExperimentAnalyticsView } from './ExperimentAnalyticsView';

interface ExperimentDetailProps {
  experiment: Experiment;
  analytics?: ExperimentAnalytics;
  loading: boolean;
  error?: string;
  onEdit: () => void;
  onDelete: () => void;
  onDuplicate: () => void;
  onBack: () => void;
  onStatusChange: (status: string, data?: any) => void;
}

const tabs = [
  { name: 'Overview', icon: BeakerIcon },
  { name: 'Analytics', icon: ChartBarIcon },
  { name: 'Assignments', icon: UserGroupIcon },
];

export const ExperimentDetail: React.FC<ExperimentDetailProps> = ({
  experiment,
  analytics,
  loading,
  error,
  onEdit,
  onDelete,
  onDuplicate,
  onBack,
  onStatusChange,
}) => {
  const [selectedTab, setSelectedTab] = useState(0);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">Error loading experiment</div>
        <div className="text-gray-500">{error}</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={onBack}
            className="inline-flex items-center text-gray-500 hover:text-gray-700"
          >
            <ArrowLeftIcon className="h-5 w-5 mr-1" />
            Back to Experiments
          </button>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={onEdit}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <PencilIcon className="h-4 w-4 mr-2" />
            Edit
          </button>
          <button
            onClick={onDuplicate}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <DocumentDuplicateIcon className="h-4 w-4 mr-2" />
            Duplicate
          </button>
          <button
            onClick={onDelete}
            className="inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            <TrashIcon className="h-4 w-4 mr-2" />
            Delete
          </button>
        </div>
      </div>

      {/* Experiment Header */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <h1 className="text-2xl font-bold text-gray-900">{experiment.name}</h1>
              {experiment.description && (
                <p className="mt-2 text-gray-600">{experiment.description}</p>
              )}
              {experiment.hypothesis && (
                <div className="mt-3">
                  <h3 className="text-sm font-medium text-gray-700">Hypothesis</h3>
                  <p className="mt-1 text-sm text-gray-600">{experiment.hypothesis}</p>
                </div>
              )}
            </div>
            <div className="ml-6">
              <StatusControls
                experiment={experiment}
                onStatusChange={onStatusChange}
                loading={false}
              />
            </div>
          </div>

          {/* Metadata */}
          <div className="mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="flex items-center text-sm text-gray-500">
              <CalendarIcon className="h-4 w-4 mr-2" />
              <span>
                Created {format(new Date(experiment.createdAt), 'MMM d, yyyy')}
              </span>
            </div>
            {experiment.startDate && (
              <div className="flex items-center text-sm text-gray-500">
                <CalendarIcon className="h-4 w-4 mr-2" />
                <span>
                  Started {format(new Date(experiment.startDate), 'MMM d, yyyy')}
                </span>
              </div>
            )}
            {experiment.endDate && (
              <div className="flex items-center text-sm text-gray-500">
                <CalendarIcon className="h-4 w-4 mr-2" />
                <span>
                  Ends {format(new Date(experiment.endDate), 'MMM d, yyyy')}
                </span>
              </div>
            )}
            <div className="flex items-center text-sm text-gray-500">
              <UserGroupIcon className="h-4 w-4 mr-2" />
              <span>{experiment._count?.userAssignments || 0} assignments</span>
            </div>
          </div>

          {/* Tags */}
          {experiment.tags.length > 0 && (
            <div className="mt-4">
              <div className="flex items-center">
                <TagIcon className="h-4 w-4 text-gray-400 mr-2" />
                <div className="flex flex-wrap gap-2">
                  {experiment.tags.map(tag => (
                    <span
                      key={tag}
                      className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Tabs */}
      <Tab.Group selectedIndex={selectedTab} onChange={setSelectedTab}>
        <Tab.List className="flex space-x-1 rounded-xl bg-blue-900/20 p-1">
          {tabs.map((tab, index) => (
            <Tab
              key={tab.name}
              className={({ selected }) =>
                clsx(
                  'w-full rounded-lg py-2.5 text-sm font-medium leading-5',
                  'ring-white ring-opacity-60 ring-offset-2 ring-offset-blue-400 focus:outline-none focus:ring-2',
                  selected
                    ? 'bg-white text-blue-700 shadow'
                    : 'text-blue-100 hover:bg-white/[0.12] hover:text-white'
                )
              }
            >
              <div className="flex items-center justify-center space-x-2">
                <tab.icon className="h-4 w-4" />
                <span>{tab.name}</span>
              </div>
            </Tab>
          ))}
        </Tab.List>
        <Tab.Panels className="mt-6">
          {/* Overview Tab */}
          <Tab.Panel className="space-y-6">
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <BeakerIcon className="h-8 w-8 text-indigo-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Variants</p>
                    <p className="text-2xl font-semibold text-gray-900">
                      {experiment.variants?.length || 0}
                    </p>
                  </div>
                </div>
              </div>
              <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <UserGroupIcon className="h-8 w-8 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Assignments</p>
                    <p className="text-2xl font-semibold text-gray-900">
                      {experiment._count?.userAssignments || 0}
                    </p>
                  </div>
                </div>
              </div>
              <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <ChartBarIcon className="h-8 w-8 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Events</p>
                    <p className="text-2xl font-semibold text-gray-900">
                      {experiment._count?.events || 0}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Configuration */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Experiment Settings */}
              <div className="bg-white shadow rounded-lg">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">Configuration</h3>
                </div>
                <div className="px-6 py-4 space-y-4">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Traffic Allocation</dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {(experiment.trafficAllocation * 100).toFixed(0)}%
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Assignment Method</dt>
                    <dd className="mt-1 text-sm text-gray-900">{experiment.assignmentMethod}</dd>
                  </div>
                  {experiment.sampleSize && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Sample Size</dt>
                      <dd className="mt-1 text-sm text-gray-900">
                        {experiment.sampleSize.toLocaleString()}
                      </dd>
                    </div>
                  )}
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Confidence Level</dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {(experiment.confidenceLevel * 100).toFixed(0)}%
                    </dd>
                  </div>
                  {experiment.primaryMetric && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Primary Metric</dt>
                      <dd className="mt-1 text-sm text-gray-900">{experiment.primaryMetric}</dd>
                    </div>
                  )}
                  {experiment.secondaryMetrics.length > 0 && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Secondary Metrics</dt>
                      <dd className="mt-1 text-sm text-gray-900">
                        {experiment.secondaryMetrics.join(', ')}
                      </dd>
                    </div>
                  )}
                </div>
              </div>

              {/* Variants */}
              <div className="bg-white shadow rounded-lg">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">Variants</h3>
                </div>
                <div className="px-6 py-4">
                  <VariantList variants={experiment.variants || []} readonly />
                </div>
              </div>
            </div>

            {/* Targeting Rules */}
            {experiment.targetingRules && experiment.targetingRules.length > 0 && (
              <div className="bg-white shadow rounded-lg">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">Targeting Rules</h3>
                </div>
                <div className="px-6 py-4">
                  <TargetingRulesList rules={experiment.targetingRules} readonly />
                </div>
              </div>
            )}
          </Tab.Panel>

          {/* Analytics Tab */}
          <Tab.Panel>
            <ExperimentAnalyticsView
              experiment={experiment}
              analytics={analytics}
              loading={loading}
            />
          </Tab.Panel>

          {/* Assignments Tab */}
          <Tab.Panel>
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">User Assignments</h3>
              </div>
              <div className="px-6 py-4">
                <p className="text-gray-500">User assignment details will be displayed here.</p>
              </div>
            </div>
          </Tab.Panel>
        </Tab.Panels>
      </Tab.Group>
    </div>
  );
};

// Status controls component for experiment lifecycle management
import React, { useState } from 'react';
import { 
  PlayIcon, 
  PauseIcon, 
  StopIcon,
  ArchiveBoxIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { Dialog, Transition } from '@headlessui/react';
import { clsx } from 'clsx';
import { Experiment, ExperimentStatus } from '../../types/experiment';

interface StatusControlsProps {
  experiment: Experiment;
  onStatusChange: (status: ExperimentStatus, data?: any) => void;
  loading: boolean;
  disabled?: boolean;
}

interface StatusAction {
  status: ExperimentStatus;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  requiresConfirmation: boolean;
  confirmationTitle?: string;
  confirmationMessage?: string;
  requiresInput?: boolean;
  inputLabel?: string;
  inputPlaceholder?: string;
}

const statusActions: Record<ExperimentStatus, StatusAction[]> = {
  DRAFT: [
    {
      status: 'ACTIVE',
      label: 'Start Experiment',
      icon: PlayIcon,
      color: 'bg-green-600 hover:bg-green-700',
      requiresConfirmation: true,
      confirmationTitle: 'Start Experiment',
      confirmationMessage: 'Are you sure you want to start this experiment? Once started, users will begin being assigned to variants.',
    },
  ],
  ACTIVE: [
    {
      status: 'PAUSED',
      label: 'Pause',
      icon: PauseIcon,
      color: 'bg-yellow-600 hover:bg-yellow-700',
      requiresConfirmation: true,
      confirmationTitle: 'Pause Experiment',
      confirmationMessage: 'Are you sure you want to pause this experiment? New users will not be assigned to variants, but existing assignments will remain.',
      requiresInput: true,
      inputLabel: 'Reason for pausing',
      inputPlaceholder: 'Enter reason for pausing the experiment...',
    },
    {
      status: 'COMPLETED',
      label: 'Complete',
      icon: StopIcon,
      color: 'bg-blue-600 hover:bg-blue-700',
      requiresConfirmation: true,
      confirmationTitle: 'Complete Experiment',
      confirmationMessage: 'Are you sure you want to complete this experiment? This action cannot be undone.',
    },
  ],
  PAUSED: [
    {
      status: 'ACTIVE',
      label: 'Resume',
      icon: PlayIcon,
      color: 'bg-green-600 hover:bg-green-700',
      requiresConfirmation: true,
      confirmationTitle: 'Resume Experiment',
      confirmationMessage: 'Are you sure you want to resume this experiment? Users will begin being assigned to variants again.',
    },
    {
      status: 'COMPLETED',
      label: 'Complete',
      icon: StopIcon,
      color: 'bg-blue-600 hover:bg-blue-700',
      requiresConfirmation: true,
      confirmationTitle: 'Complete Experiment',
      confirmationMessage: 'Are you sure you want to complete this experiment? This action cannot be undone.',
    },
  ],
  COMPLETED: [
    {
      status: 'ARCHIVED',
      label: 'Archive',
      icon: ArchiveBoxIcon,
      color: 'bg-gray-600 hover:bg-gray-700',
      requiresConfirmation: true,
      confirmationTitle: 'Archive Experiment',
      confirmationMessage: 'Are you sure you want to archive this experiment? Archived experiments are moved out of the main view but data is preserved.',
    },
  ],
  ARCHIVED: [],
};

const statusColors: Record<ExperimentStatus, string> = {
  DRAFT: 'bg-gray-100 text-gray-800',
  ACTIVE: 'bg-green-100 text-green-800',
  PAUSED: 'bg-yellow-100 text-yellow-800',
  COMPLETED: 'bg-blue-100 text-blue-800',
  ARCHIVED: 'bg-gray-100 text-gray-600',
};

const statusIcons: Record<ExperimentStatus, React.ComponentType<{ className?: string }>> = {
  DRAFT: StopIcon,
  ACTIVE: PlayIcon,
  PAUSED: PauseIcon,
  COMPLETED: StopIcon,
  ARCHIVED: ArchiveBoxIcon,
};

export const StatusControls: React.FC<StatusControlsProps> = ({
  experiment,
  onStatusChange,
  loading,
  disabled = false,
}) => {
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [selectedAction, setSelectedAction] = useState<StatusAction | null>(null);
  const [inputValue, setInputValue] = useState('');

  const availableActions = statusActions[experiment.status] || [];
  const StatusIcon = statusIcons[experiment.status];

  const handleActionClick = (action: StatusAction) => {
    if (action.requiresConfirmation) {
      setSelectedAction(action);
      setInputValue('');
      setShowConfirmation(true);
    } else {
      onStatusChange(action.status);
    }
  };

  const handleConfirm = () => {
    if (selectedAction) {
      const data = selectedAction.requiresInput ? { reason: inputValue } : undefined;
      onStatusChange(selectedAction.status, data);
      setShowConfirmation(false);
      setSelectedAction(null);
      setInputValue('');
    }
  };

  const handleCancel = () => {
    setShowConfirmation(false);
    setSelectedAction(null);
    setInputValue('');
  };

  const canConfirm = !selectedAction?.requiresInput || inputValue.trim().length > 0;

  return (
    <>
      <div className="flex items-center space-x-4">
        {/* Current Status */}
        <div className="flex items-center space-x-2">
          <span className={clsx(
            'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium',
            statusColors[experiment.status]
          )}>
            <StatusIcon className="h-4 w-4 mr-1" />
            {experiment.status}
          </span>
        </div>

        {/* Action Buttons */}
        {availableActions.length > 0 && (
          <div className="flex items-center space-x-2">
            {availableActions.map((action) => (
              <button
                key={action.status}
                onClick={() => handleActionClick(action)}
                disabled={disabled || loading}
                className={clsx(
                  'inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed',
                  action.color
                )}
              >
                <action.icon className="h-4 w-4 mr-2" />
                {loading ? 'Processing...' : action.label}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Confirmation Dialog */}
      <Transition appear show={showConfirmation}>
        <Dialog as="div" className="relative z-50" onClose={handleCancel}>
          <Transition.Child
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-25" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="flex-shrink-0">
                      <ExclamationTriangleIcon className="h-6 w-6 text-amber-600" />
                    </div>
                    <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
                      {selectedAction?.confirmationTitle}
                    </Dialog.Title>
                  </div>

                  <div className="mb-4">
                    <p className="text-sm text-gray-500">
                      {selectedAction?.confirmationMessage}
                    </p>
                  </div>

                  {selectedAction?.requiresInput && (
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {selectedAction.inputLabel}
                      </label>
                      <textarea
                        value={inputValue}
                        onChange={(e) => setInputValue(e.target.value)}
                        rows={3}
                        className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                        placeholder={selectedAction.inputPlaceholder}
                      />
                    </div>
                  )}

                  <div className="flex items-center justify-end space-x-3">
                    <button
                      type="button"
                      onClick={handleCancel}
                      className="inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                    >
                      Cancel
                    </button>
                    <button
                      type="button"
                      onClick={handleConfirm}
                      disabled={!canConfirm || loading}
                      className={clsx(
                        'inline-flex justify-center rounded-md border border-transparent px-4 py-2 text-sm font-medium text-white shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed',
                        selectedAction?.color || 'bg-indigo-600 hover:bg-indigo-700 focus:ring-indigo-500'
                      )}
                    >
                      {loading ? 'Processing...' : 'Confirm'}
                    </button>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
};

// Experiment creation/edit form component
import React, { useEffect } from 'react';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { Experiment, CreateExperimentForm, AssignmentMethod } from '../../types/experiment';
import { VariantEditor } from './VariantEditor';
import { TargetingRulesEditor } from './TargetingRulesEditor';

const experimentSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters'),
  description: z.string().optional(),
  hypothesis: z.string().optional(),
  trafficAllocation: z.number().min(0.01, 'Must be at least 1%').max(1, 'Cannot exceed 100%'),
  assignmentMethod: z.enum(['RANDOM', 'STICKY', 'DETERMINISTIC']),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  sampleSize: z.number().min(1).optional(),
  confidenceLevel: z.number().min(0.8).max(0.99),
  minimumDetectableEffect: z.number().min(0.01).max(1).optional(),
  primaryMetric: z.string().optional(),
  secondaryMetrics: z.array(z.string()),
  tags: z.array(z.string()),
  variants: z.array(z.object({
    name: z.string().min(1, 'Variant name is required'),
    description: z.string().optional(),
    isControl: z.boolean(),
    trafficWeight: z.number().min(0).max(1),
    configuration: z.record(z.any()),
  })).min(2, 'At least 2 variants are required'),
  targetingRules: z.array(z.object({
    name: z.string().min(1, 'Rule name is required'),
    attributeName: z.string().min(1, 'Attribute name is required'),
    operator: z.enum(['EQUALS', 'NOT_EQUALS', 'IN', 'NOT_IN', 'GREATER_THAN', 'LESS_THAN', 'CONTAINS', 'REGEX']),
    value: z.union([z.string(), z.number(), z.boolean(), z.array(z.string())]),
    isActive: z.boolean(),
    priority: z.number().min(1),
  })),
}).refine((data) => {
  // Validate traffic weights sum to 1
  const totalWeight = data.variants.reduce((sum, variant) => sum + variant.trafficWeight, 0);
  return Math.abs(totalWeight - 1) < 0.001;
}, {
  message: 'Variant traffic weights must sum to 100%',
  path: ['variants'],
}).refine((data) => {
  // Validate only one control variant
  const controlCount = data.variants.filter(v => v.isControl).length;
  return controlCount <= 1;
}, {
  message: 'Only one variant can be marked as control',
  path: ['variants'],
}).refine((data) => {
  // Validate end date is after start date
  if (data.startDate && data.endDate) {
    return data.endDate > data.startDate;
  }
  return true;
}, {
  message: 'End date must be after start date',
  path: ['endDate'],
});

interface ExperimentFormProps {
  experiment?: Experiment;
  onSubmit: (data: CreateExperimentForm) => void;
  onCancel: () => void;
  loading: boolean;
  error?: string;
}

export const ExperimentForm: React.FC<ExperimentFormProps> = ({
  experiment,
  onSubmit,
  onCancel,
  loading,
  error,
}) => {
  const {
    register,
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<CreateExperimentForm>({
    resolver: zodResolver(experimentSchema),
    defaultValues: {
      name: experiment?.name || '',
      description: experiment?.description || '',
      hypothesis: experiment?.hypothesis || '',
      trafficAllocation: experiment?.trafficAllocation || 1,
      assignmentMethod: experiment?.assignmentMethod || 'STICKY',
      startDate: experiment?.startDate ? new Date(experiment.startDate) : undefined,
      endDate: experiment?.endDate ? new Date(experiment.endDate) : undefined,
      sampleSize: experiment?.sampleSize || undefined,
      confidenceLevel: experiment?.confidenceLevel || 0.95,
      minimumDetectableEffect: experiment?.minimumDetectableEffect || undefined,
      primaryMetric: experiment?.primaryMetric || '',
      secondaryMetrics: experiment?.secondaryMetrics || [],
      tags: experiment?.tags || [],
      variants: experiment?.variants?.map(v => ({
        name: v.name,
        description: v.description || '',
        isControl: v.isControl,
        trafficWeight: v.trafficWeight,
        configuration: v.configuration,
      })) || [
        { name: 'Control', description: '', isControl: true, trafficWeight: 0.5, configuration: {} },
        { name: 'Treatment', description: '', isControl: false, trafficWeight: 0.5, configuration: {} },
      ],
      targetingRules: experiment?.targetingRules?.map(r => ({
        name: r.name,
        attributeName: r.attributeName,
        operator: r.operator,
        value: r.value,
        isActive: r.isActive,
        priority: r.priority,
      })) || [],
    },
  });

  const variants = watch('variants');
  const targetingRules = watch('targetingRules');

  const handleFormSubmit = (data: CreateExperimentForm) => {
    onSubmit(data);
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onCancel} />
        
        <div className="relative w-full max-w-4xl bg-white rounded-lg shadow-xl">
          <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">
                {experiment ? 'Edit Experiment' : 'Create New Experiment'}
              </h2>
              <button
                type="button"
                onClick={onCancel}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            {/* Error Message */}
            {error && (
              <div className="mx-6 p-4 bg-red-50 border border-red-200 rounded-md">
                <div className="text-sm text-red-600">{error}</div>
              </div>
            )}

            <div className="px-6 space-y-6 max-h-96 overflow-y-auto">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Experiment Name *
                  </label>
                  <input
                    type="text"
                    {...register('name')}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="Enter experiment name"
                  />
                  {errors.name && (
                    <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Assignment Method
                  </label>
                  <select
                    {...register('assignmentMethod')}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                  >
                    <option value="STICKY">Sticky (Consistent)</option>
                    <option value="RANDOM">Random</option>
                    <option value="DETERMINISTIC">Deterministic</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Description
                </label>
                <textarea
                  {...register('description')}
                  rows={3}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Describe what this experiment tests"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Hypothesis
                </label>
                <textarea
                  {...register('hypothesis')}
                  rows={3}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="State your hypothesis for this experiment"
                />
              </div>

              {/* Configuration */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Traffic Allocation (%)
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    min="1"
                    max="100"
                    {...register('trafficAllocation', { 
                      setValueAs: (value) => parseFloat(value) / 100 
                    })}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                  />
                  {errors.trafficAllocation && (
                    <p className="mt-1 text-sm text-red-600">{errors.trafficAllocation.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Confidence Level (%)
                  </label>
                  <select
                    {...register('confidenceLevel', { 
                      setValueAs: (value) => parseFloat(value) 
                    })}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                  >
                    <option value="0.90">90%</option>
                    <option value="0.95">95%</option>
                    <option value="0.99">99%</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Sample Size
                  </label>
                  <input
                    type="number"
                    min="1"
                    {...register('sampleSize', { 
                      setValueAs: (value) => value ? parseInt(value) : undefined 
                    })}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="Optional"
                  />
                </div>
              </div>

              {/* Dates */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Start Date
                  </label>
                  <input
                    type="datetime-local"
                    {...register('startDate', { 
                      setValueAs: (value) => value ? new Date(value) : undefined 
                    })}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    End Date
                  </label>
                  <input
                    type="datetime-local"
                    {...register('endDate', { 
                      setValueAs: (value) => value ? new Date(value) : undefined 
                    })}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                  />
                  {errors.endDate && (
                    <p className="mt-1 text-sm text-red-600">{errors.endDate.message}</p>
                  )}
                </div>
              </div>

              {/* Metrics */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Primary Metric
                  </label>
                  <input
                    type="text"
                    {...register('primaryMetric')}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="e.g., conversion_rate"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Minimum Detectable Effect (%)
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    min="0.01"
                    max="100"
                    {...register('minimumDetectableEffect', { 
                      setValueAs: (value) => value ? parseFloat(value) / 100 : undefined 
                    })}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="Optional"
                  />
                </div>
              </div>

              {/* Tags */}
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Tags
                </label>
                <input
                  type="text"
                  placeholder="Enter tags separated by commas"
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                  onChange={(e) => {
                    const tags = e.target.value.split(',').map(tag => tag.trim()).filter(Boolean);
                    setValue('tags', tags);
                  }}
                />
              </div>

              {/* Variants */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-4">
                  Variants *
                </label>
                <Controller
                  name="variants"
                  control={control}
                  render={({ field }) => (
                    <VariantEditor
                      variants={field.value}
                      onChange={field.onChange}
                      errors={errors.variants as any}
                    />
                  )}
                />
                {errors.variants && (
                  <p className="mt-1 text-sm text-red-600">{errors.variants.message}</p>
                )}
              </div>

              {/* Targeting Rules */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-4">
                  Targeting Rules
                </label>
                <Controller
                  name="targetingRules"
                  control={control}
                  render={({ field }) => (
                    <TargetingRulesEditor
                      rules={field.value}
                      onChange={field.onChange}
                      errors={errors.targetingRules as any}
                    />
                  )}
                />
              </div>
            </div>

            {/* Footer */}
            <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
              <button
                type="button"
                onClick={onCancel}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Saving...' : experiment ? 'Update Experiment' : 'Create Experiment'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

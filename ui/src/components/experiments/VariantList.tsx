// Variant list component for displaying variants
import React from 'react';
import { clsx } from 'clsx';
import { Variant } from '../../types/experiment';

interface VariantListProps {
  variants: Variant[];
  readonly?: boolean;
  showConfiguration?: boolean;
}

export const VariantList: React.FC<VariantListProps> = ({
  variants,
  readonly = true,
  showConfiguration = true,
}) => {
  if (variants.length === 0) {
    return (
      <div className="text-center py-6">
        <p className="text-sm text-gray-500">No variants configured</p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {variants.map((variant, index) => (
        <div key={variant.id || index} className="border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-3">
              <h4 className="font-medium text-gray-900">{variant.name}</h4>
              {variant.isControl && (
                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                  Control
                </span>
              )}
            </div>
            <span className="text-sm text-gray-500">
              {(variant.trafficWeight * 100).toFixed(1)}%
            </span>
          </div>
          
          {variant.description && (
            <p className="text-sm text-gray-600 mb-3">{variant.description}</p>
          )}
          
          {showConfiguration && Object.keys(variant.configuration).length > 0 && (
            <div className="bg-gray-50 rounded p-3">
              <h5 className="text-xs font-medium text-gray-700 mb-2">Configuration</h5>
              <pre className="text-xs text-gray-700 overflow-x-auto">
                {JSON.stringify(variant.configuration, null, 2)}
              </pre>
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

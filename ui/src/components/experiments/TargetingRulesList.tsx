// Targeting rules list component for displaying rules
import React from 'react';
import { clsx } from 'clsx';
import { TargetingRule, TargetingOperator } from '../../types/experiment';

interface TargetingRulesListProps {
  rules: TargetingRule[];
  readonly?: boolean;
}

const operatorLabels: Record<TargetingOperator, string> = {
  EQUALS: 'equals',
  NOT_EQUALS: 'does not equal',
  IN: 'is in',
  NOT_IN: 'is not in',
  GREATER_THAN: 'is greater than',
  LESS_THAN: 'is less than',
  CONTAINS: 'contains',
  REGEX: 'matches pattern',
};

export const TargetingRulesList: React.FC<TargetingRulesListProps> = ({
  rules,
  readonly = true,
}) => {
  if (rules.length === 0) {
    return (
      <div className="text-center py-6">
        <p className="text-sm text-gray-500">No targeting rules configured</p>
        <p className="text-xs text-gray-400 mt-1">All users will be eligible for this experiment</p>
      </div>
    );
  }

  const sortedRules = [...rules].sort((a, b) => a.priority - b.priority);

  return (
    <div className="space-y-3">
      {sortedRules.map((rule, index) => (
        <div key={rule.id || index} className="border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <h4 className="font-medium text-gray-900">{rule.name}</h4>
            <div className="flex items-center space-x-2">
              <span className="text-xs text-gray-500">Priority {rule.priority}</span>
              <span className={clsx(
                'inline-flex items-center px-2 py-0.5 rounded text-xs font-medium',
                rule.isActive 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-gray-100 text-gray-800'
              )}>
                {rule.isActive ? 'Active' : 'Inactive'}
              </span>
            </div>
          </div>
          
          <div className="text-sm text-gray-600">
            Include users where{' '}
            <span className="font-medium text-indigo-600">{rule.attributeName}</span>
            {' '}
            <span className="text-gray-500">{operatorLabels[rule.operator]}</span>
            {' '}
            <span className="font-medium text-indigo-600">
              {Array.isArray(rule.value) 
                ? `[${rule.value.join(', ')}]`
                : String(rule.value)
              }
            </span>
          </div>
        </div>
      ))}
    </div>
  );
};

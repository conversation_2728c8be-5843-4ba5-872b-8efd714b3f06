export interface Permission {
  id: string;
  name: string;
  description: string;
  resource: string;
  action: string;
  conditions?: PermissionCondition[];
  createdAt: Date;
  updatedAt: Date;
}

export interface PermissionCondition {
  field: string;
  operator: 'equals' | 'not_equals' | 'in' | 'not_in' | 'contains' | 'starts_with' | 'ends_with';
  value: any;
  description?: string;
}

export interface Role {
  id: string;
  name: string;
  displayName: string;
  description: string;
  type: RoleType;
  isSystem: boolean;
  tenantId?: string; // null for system roles, specific for tenant roles
  permissions: string[]; // Permission IDs
  inheritsFrom?: string[]; // Role IDs to inherit permissions from
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  createdBy?: string;
}

export enum RoleType {
  SYSTEM = 'system',
  TENANT = 'tenant',
  CUSTOM = 'custom'
}

export interface UserRole {
  id: string;
  userId: string;
  roleId: string;
  tenantId: string;
  assignedBy: string;
  assignedAt: Date;
  expiresAt?: Date;
  isActive: boolean;
  conditions?: UserRoleCondition[];
}

export interface UserRoleCondition {
  field: string;
  operator: string;
  value: any;
  description?: string;
}

export interface RolePermission {
  roleId: string;
  permissionId: string;
  grantedBy: string;
  grantedAt: Date;
  conditions?: PermissionCondition[];
}

export interface AccessRequest {
  userId: string;
  tenantId: string;
  resource: string;
  action: string;
  context?: AccessContext;
}

export interface AccessContext {
  experimentId?: string;
  projectId?: string;
  teamId?: string;
  resourceOwnerId?: string;
  ipAddress?: string;
  userAgent?: string;
  timestamp?: Date;
  metadata?: Record<string, any>;
}

export interface AccessResult {
  granted: boolean;
  reason: string;
  matchedPermissions: Permission[];
  appliedConditions: PermissionCondition[];
  denyReasons?: string[];
}

export interface RoleHierarchy {
  roleId: string;
  parentRoleId: string;
  level: number;
  path: string[];
}

export interface PermissionGroup {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  category: string;
}

// Predefined system roles
export enum SystemRole {
  SUPER_ADMIN = 'super_admin',
  TENANT_ADMIN = 'tenant_admin',
  ADMIN = 'admin',
  EXPERIMENTER = 'experimenter',
  VIEWER = 'viewer',
  GUEST = 'guest'
}

// Resource types for permissions
export enum Resource {
  EXPERIMENTS = 'experiments',
  VARIANTS = 'variants',
  AUDIENCES = 'audiences',
  METRICS = 'metrics',
  ANALYTICS = 'analytics',
  USERS = 'users',
  ROLES = 'roles',
  PERMISSIONS = 'permissions',
  TENANTS = 'tenants',
  PROJECTS = 'projects',
  TEAMS = 'teams',
  SETTINGS = 'settings',
  BILLING = 'billing',
  INTEGRATIONS = 'integrations'
}

// Action types for permissions
export enum Action {
  CREATE = 'create',
  READ = 'read',
  UPDATE = 'update',
  DELETE = 'delete',
  EXECUTE = 'execute',
  APPROVE = 'approve',
  PUBLISH = 'publish',
  ARCHIVE = 'archive',
  RESTORE = 'restore',
  ASSIGN = 'assign',
  UNASSIGN = 'unassign',
  INVITE = 'invite',
  MANAGE = 'manage',
  VIEW_ANALYTICS = 'view_analytics',
  EXPORT = 'export',
  IMPORT = 'import'
}

export interface RoleTemplate {
  name: string;
  displayName: string;
  description: string;
  permissions: string[];
  inheritsFrom?: string[];
  defaultForTenant?: boolean;
}

export interface PermissionCheck {
  resource: string;
  action: string;
  conditions?: PermissionCondition[];
}

export interface BulkPermissionCheck {
  userId: string;
  tenantId: string;
  checks: PermissionCheck[];
}

export interface BulkPermissionResult {
  results: Record<string, AccessResult>;
  summary: {
    totalChecks: number;
    granted: number;
    denied: number;
  };
}

export interface RoleAssignment {
  userId: string;
  roleId: string;
  tenantId: string;
  assignedBy: string;
  expiresAt?: Date;
  conditions?: UserRoleCondition[];
}

export interface RoleRevocation {
  userId: string;
  roleId: string;
  tenantId: string;
  revokedBy: string;
  reason?: string;
}

export interface PermissionAuditLog {
  id: string;
  userId: string;
  tenantId: string;
  action: 'grant' | 'revoke' | 'check' | 'deny';
  resource: string;
  permission: string;
  result: boolean;
  reason?: string;
  context?: AccessContext;
  timestamp: Date;
  performedBy?: string;
}

export interface RoleAuditLog {
  id: string;
  userId: string;
  tenantId: string;
  roleId: string;
  action: 'assign' | 'revoke' | 'modify';
  previousRoles?: string[];
  newRoles?: string[];
  reason?: string;
  timestamp: Date;
  performedBy: string;
}

export interface RBACConfig {
  enableRoleHierarchy: boolean;
  enableConditionalPermissions: boolean;
  enableRoleExpiration: boolean;
  enableAuditLogging: boolean;
  defaultRoleForNewUsers: string;
  maxRolesPerUser: number;
  permissionCacheTTL: number; // seconds
  roleCacheTTL: number; // seconds
}

export interface EffectivePermissions {
  userId: string;
  tenantId: string;
  roles: Role[];
  permissions: Permission[];
  computedAt: Date;
  cacheKey: string;
}

export interface RoleStats {
  roleId: string;
  roleName: string;
  userCount: number;
  permissionCount: number;
  lastUsed?: Date;
  createdAt: Date;
}

export interface PermissionStats {
  permissionId: string;
  permissionName: string;
  roleCount: number;
  userCount: number;
  usageCount: number;
  lastUsed?: Date;
}

export interface RBACMetrics {
  totalRoles: number;
  totalPermissions: number;
  totalUserRoles: number;
  systemRoles: number;
  tenantRoles: number;
  customRoles: number;
  activeUsers: number;
  permissionChecksToday: number;
  topPermissions: PermissionStats[];
  topRoles: RoleStats[];
}
